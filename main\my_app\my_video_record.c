#include "my_video_record.h"

#include "my_config.h"
#include "my_camera.h"
#include "my_monitor.h"
#include "my_time.h"
#include "my_nvs_ds.h"

#include "my_sdcard.h"

#include <esp_log.h>

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"
#include "freertos/semphr.h"
#include "freertos/timers.h"

#include "rtc.h"
#include "time.h"

#include <stdint.h>
#include <stdbool.h>
#include <stdio.h>
#include <string.h>
#include <dirent.h>
#include <sys/unistd.h>
#include <sys/stat.h>
//--------------------
// Input Pins/Misc
#define REC_QUEUE_SIZE  8
#define REC_QUALITY     12
#define REC_EN_PIN      16
#define REC_HI_WATER    3800000000
//#define REC_HI_WATER    1000000


#define MYSD_MOUNT_POINT "/sdcard"

#define VIDEO_FNAME     "video_%d.mjpg"


typedef struct {
    int64_t timestamp_us;
    camera_fb_t * fb;
} cam_frame_t;

typedef struct {
    QueueHandle_t rtos_fb_queue;
    char * video_path;
    struct {
        uint32_t cam_ready : 1;
        uint32_t cam_recording : 1;
    };
} cam_sys_t;

char my_video_file_name[128];
void sd_open_next(FILE ** fjpg, int * fjpg_fd) {
    
    char fjpg_path[256] = "\0";
    
    sprintf(fjpg_path, "%s/%s", MYSD_MOUNT_POINT, my_video_file_name);
    
    ESP_LOGI("sd_open_next", "opening %s for writing...", fjpg_path);
    
    *fjpg = fopen(fjpg_path, "w");
            
    if (*fjpg == NULL) {
        ESP_LOGE("sd_open_next", "could not open %s for writing, panicing!", fjpg_path);
        esp_restart();
    }

    *fjpg_fd = fileno(*fjpg);

	printf("fjpg_path=%s, fjpg_fd=%d\n", fjpg_path, *fjpg_fd);
}

/*
SD task

recieves frames from a framebuffer queue and writes them to the SD card.
starts with a closed video file.

if the video file is null, it will read the latest video path and attempt to open the video file.

if a recieved framebuffer is null (end of stream), it will close the current video file.
otherwise, it will write the framebuffer contents to the file.

*/
void sd_write_task(void * arg) {
    cam_sys_t * cam_sys = (cam_sys_t *)arg;
    cam_frame_t frame;
    FILE * fjpg = NULL; 
    int fjpg_fd = -1;
    size_t fjpg_size = 0;
    
    ESP_LOGI("sd_write_task", "sd write task ready!");
    
    while (xQueueReceive(cam_sys->rtos_fb_queue, (void *)&frame, portMAX_DELAY)) {
        
        if (fjpg == NULL) { // null file
            sd_open_next(&fjpg, &fjpg_fd);
            fjpg_size = 0;
			ESP_LOGI("--", "ok1");
        }
        
        if (frame.fb) { // valid frame
            fjpg_size += fwrite(frame.fb->buf, 1, frame.fb->len, fjpg);
            fsync(fjpg_fd);
            esp_camera_fb_return(frame.fb);
        } 
        
        if (!frame.fb || fjpg_size > REC_HI_WATER) { // if the frame is null or the high water mark has been reached
            fclose(fjpg);
            fjpg = NULL;
            fjpg_fd = -1;
			ESP_LOGI("--", "ok3");
        }
    }
}
//--------------------
typedef struct
{
    int busy;

    StackType_t* p_task_stack_maintask;
	StaticTask_t* p_task_data_maintask;
    TaskHandle_t maintaskTaskHandle;
    StackType_t* p_task_stack_sd_write_task;
	StaticTask_t* p_task_data_sd_write_task;
    TaskHandle_t write_taskTaskHandle;

    int record_time;

    cam_sys_t cam_sys;
}my_videorecord_info_t;

my_videorecord_info_t my_videorecord_info;


void My_VideoRecord_MainTask(void* param)
{
    int record_cnt = 0;
    time_t record_start_time = MyTime_GetTime();

    ESP_LOGI("My_VideoRecord_MainTask", "starting recording...");

    while (true) {
        cam_frame_t frame = {
            .timestamp_us = esp_timer_get_time(),
            .fb = esp_camera_fb_get()
        };
        
        if (!frame.fb) {
            ESP_LOGW("My_VideoRecord_MainTask()", "camera framebuffer is null!");
            continue;
        }
        
        if (xQueueSendToBack(my_videorecord_info.cam_sys.rtos_fb_queue, (void *)&frame, 10) != pdPASS) {
            ESP_LOGW("My_VideoRecord_MainTask()", "dropped frame!");
        }

		record_cnt++;
		ESP_LOGI("My_VideoRecord_MainTask()", "record_cnt=%d", record_cnt);
        // vTaskDelay(200 / portTICK_PERIOD_MS);

        if(MyTime_GetTime()-record_start_time >= my_videorecord_info.record_time)
        {
            ESP_LOGI("My_VideoRecord_MainTask()", "record finished");
			vTaskDelay(5000 / portTICK_PERIOD_MS);
            if(my_videorecord_info.write_taskTaskHandle != NULL)
            {
                vTaskDelete(my_videorecord_info.write_taskTaskHandle);
            }

            // if(my_videorecord_info.p_task_stack_sd_write_task!=NULL)free(my_videorecord_info.p_task_stack_sd_write_task);
            // my_videorecord_info.p_task_stack_sd_write_task=NULL;
            // if(my_videorecord_info.p_task_data_sd_write_task!=NULL)free(my_videorecord_info.p_task_data_sd_write_task);
            // my_videorecord_info.p_task_data_sd_write_task=NULL;

            // if(my_videorecord_info.p_task_stack_maintask!=NULL)free(my_videorecord_info.p_task_stack_maintask);
            // my_videorecord_info.p_task_stack_maintask=NULL;
            // if(my_videorecord_info.p_task_data_maintask!=NULL)free(my_videorecord_info.p_task_data_maintask);
            // my_videorecord_info.p_task_data_maintask=NULL;

            if(my_videorecord_info.cam_sys.rtos_fb_queue != NULL)
            {
                vQueueDelete(my_videorecord_info.cam_sys.rtos_fb_queue);
                my_videorecord_info.cam_sys.rtos_fb_queue = NULL;
            }
            

            my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
            MyCamera_ReInit(&p_my_conf->cam.conf);

            my_videorecord_info.busy = 0;

            My_SDcard_Unmount(MYSD_MOUNT_POINT);

            ESP_LOGI("My_VideoRecord_MainTask", "freed resources");
            break;
		}
    }

    ESP_LOGI("My_VideoRecord_MainTask", "freed resources_1");
    vTaskDelete(NULL);
}

int My_VideoRecord_For(int image_size_x, int image_size_y, int record_time, char* file_name)
{
    if(!my_videorecord_info.busy)
    {
        ESP_LOGI("My_VideoRecord_For", "Initialize recording...");
        memset(&my_videorecord_info, 0, sizeof(my_videorecord_info));
        my_videorecord_info.busy = 1;

        memset(my_video_file_name, 0, sizeof(my_video_file_name));
        memcpy(my_video_file_name, file_name, strlen(file_name));

        ESP_LOGI("My_VideoRecord_For", "file_name=%s", file_name);
        ESP_LOGI("My_VideoRecord_For", "my_video_file_name=%s", my_video_file_name);
        ESP_LOGI("My_VideoRecord_For", "record_time=%ds", record_time);


        my_config_nvs_t* p_my_conf = MyConfig_LoadFromNVS();

        my_camera_conf_t my_camera_conf;
        memset(&my_camera_conf, 0, sizeof(my_camera_conf));
        memcpy(&my_camera_conf, &p_my_conf->cam.conf, sizeof(my_camera_conf));
        if((image_size_x!=0)&&(image_size_y!=0))
        {
            my_camera_conf.photo_imagesize_x = image_size_x;
            my_camera_conf.photo_imagesize_y = image_size_y;
        }

        MyCamera_ReInit(&my_camera_conf);


        My_SDcard_Mount(MYSD_MOUNT_POINT);


        // create framebuffer queue
        my_videorecord_info.cam_sys.rtos_fb_queue = xQueueCreate(REC_QUEUE_SIZE, sizeof(cam_frame_t));
        
        if (my_videorecord_info.cam_sys.rtos_fb_queue == NULL) {
            ESP_LOGE("My_VideoRecord_For", "failed to create framebuffer queue!");
        }

        my_videorecord_info.record_time = record_time;

        //xTaskCreate(sd_write_task,  "SDWrite",    8192,    NULL, 14, NULL);
        my_videorecord_info.p_task_stack_sd_write_task = calloc(1, 8192*sizeof(StackType_t));
        if(my_videorecord_info.p_task_stack_sd_write_task!=NULL)
        {
            my_videorecord_info.p_task_data_sd_write_task = calloc(1, sizeof(StaticTask_t));
            if(my_videorecord_info.p_task_data_sd_write_task!=NULL)
            {
                //xTaskCreateStatic(&sd_write_task, "SDWrite", 8192, NULL, 8, my_videorecord_info.p_task_stack_sd_write_task, my_videorecord_info.p_task_data_sd_write_task);
                my_videorecord_info.write_taskTaskHandle = xTaskCreateStaticPinnedToCore(&sd_write_task, "SDWrite", 8192, &my_videorecord_info.cam_sys, 8, my_videorecord_info.p_task_stack_sd_write_task, my_videorecord_info.p_task_data_sd_write_task, 0);
            }
        }

        my_videorecord_info.p_task_stack_maintask = calloc(1, 8192*sizeof(StackType_t));
        if(my_videorecord_info.p_task_stack_maintask!=NULL)
        {
            my_videorecord_info.p_task_data_maintask = calloc(1, sizeof(StaticTask_t));
            if(my_videorecord_info.p_task_data_maintask!=NULL)
            {
                //xTaskCreateStatic(&My_VideoRecord_MainTask, "MVR_MainTask", 8192, NULL, 8, my_videorecord_info.p_task_stack_maintask, my_videorecord_info.p_task_data_maintask);
                my_videorecord_info.maintaskTaskHandle = xTaskCreateStaticPinnedToCore(&My_VideoRecord_MainTask, "MVR_MainTask", 8192, NULL, 8, my_videorecord_info.p_task_stack_maintask, my_videorecord_info.p_task_data_maintask, 1);
            }
        }
        
        ESP_LOGI("My_VideoRecord_For", "starting recording...");

        return 0;
    }

    ESP_LOGW("My_VideoRecord_For", "busy!, ignore");
    return 1;
}