/* LEDC (LED Controller) fade example

   This example code is in the Public Domain (or CC0 licensed, at your option.)

   Unless required by applicable law or agreed to in writing, this
   software is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
   CONDITIONS OF ANY KIND, either express or implied.
*/

#include "my_ledc.h"

#if MY_LEDC_FUNC

#include "my_debug.h"

#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/ledc.h"
#include "esp_err.h"

#include "esp_log.h"


#if defined MY_DEBUG_PRINT_FUNC
#define DEBUG_PRINT_LEVEL_my_ledc_PRINT   MY_DEBUG_PRINT_LEVEL_0
#else
#define DEBUG_PRINT_LEVEL_my_ledc_PRINT   DEBUG_PRINT_LEVEL_0
#endif


/*
 * About this example
 *
 * 1. Start with initializing LEDC module:
 *    a. Set the timer of LEDC first, this determines the frequency
 *       and resolution of PWM.
 *    b. Then set the LEDC channel you want to use,
 *       and bind with one of the timers.
 *
 * 2. You need first to install a default fade function,
 *    then you can use fade APIs.
 *
 * 3. You can also set a target duty directly without fading.
 *
 * 4. This example uses GPIO18/19/4/5 as LEDC output,
 *    and it will change the duty repeatedly.
 *
 * 5. GPIO18/19 are from high speed channel group.
 *    GPIO4/5 are from low speed channel group.
 *
 */
// #define LEDC_HS_TIMER          LEDC_TIMER_0
// #define LEDC_HS_MODE           LEDC_HIGH_SPEED_MODE
#define LEDC_LS_CH0_GPIO       (39)
#define LEDC_LS_CH0_CHANNEL    LEDC_CHANNEL_0

#define LEDC_LS_TIMER          LEDC_TIMER_1
#define LEDC_LS_MODE           LEDC_LOW_SPEED_MODE

#define LEDC_TEST_CH_NUM       (1)
#define LEDC_TEST_DUTY         (4000)
#define LEDC_TEST_FADE_TIME    (1500)


#define LED_MODE_1  1
#define LED_MODE_2  2
#define LED_MODE_3  3
#define LED_MODE_4  4




static int ch;

my_ledc_info_t my_ledc_info;

    /*
     * Prepare and set configuration of timers
     * that will be used by LED Controller
     */
    ledc_timer_config_t ledc_timer = {
        .duty_resolution = LEDC_TIMER_13_BIT, // resolution of PWM duty
        .freq_hz = 5000,                      // frequency of PWM signal
        .speed_mode = LEDC_LS_MODE,           // timer mode
        .timer_num = LEDC_LS_TIMER,            // timer index
        .clk_cfg = LEDC_AUTO_CLK,              // Auto select the source clock
    };
    
    /*
     * Prepare individual configuration
     * for each channel of LED Controller
     * by selecting:
     * - controller's channel number
     * - output duty cycle, set initially to 0
     * - GPIO number where LED is connected to
     * - speed mode, either high or low
     * - timer servicing selected channel
     *   Note: if different channels use one timer,
     *         then frequency and bit_num of these channels
     *         will be the same
     */
    ledc_channel_config_t ledc_channel[LEDC_TEST_CH_NUM] = {
        {
            .channel    = LEDC_LS_CH0_CHANNEL,
            .duty       = 0,
            .gpio_num   = LEDC_LS_CH0_GPIO,
            .speed_mode = LEDC_LS_MODE,
            .hpoint     = 0,
            .timer_sel  = LEDC_LS_TIMER
        },
    };

TaskHandle_t MyLedTaskHandle = NULL;
static void MyLedTask(void* param)
{
    for(;;)
    {
        switch(my_ledc_info.mode)
        {
            case(ledmode_fade_flash):
                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_fade_with_time(ledc_channel[ch].speed_mode,
                            ledc_channel[ch].channel, LEDC_TEST_DUTY, LEDC_TEST_FADE_TIME);
                    ledc_fade_start(ledc_channel[ch].speed_mode,
                            ledc_channel[ch].channel, LEDC_FADE_NO_WAIT);
                }
                vTaskDelay(LEDC_TEST_FADE_TIME / portTICK_PERIOD_MS);

                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_fade_with_time(ledc_channel[ch].speed_mode,
                            ledc_channel[ch].channel, 0, LEDC_TEST_FADE_TIME);
                    ledc_fade_start(ledc_channel[ch].speed_mode,
                            ledc_channel[ch].channel, LEDC_FADE_NO_WAIT);
                }
                vTaskDelay(LEDC_TEST_FADE_TIME / portTICK_PERIOD_MS);

                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, LEDC_TEST_DUTY);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(200 / portTICK_PERIOD_MS);

                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, 0);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(500 / portTICK_PERIOD_MS);
            break;
            case(ledmode_on):
                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, LEDC_TEST_DUTY);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(1000 / portTICK_PERIOD_MS);
            break;
            case(ledmode_flash):
                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, LEDC_TEST_DUTY);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(200 / portTICK_PERIOD_MS);

                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, 0);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(200 / portTICK_PERIOD_MS);
            break;
            case(ledmode_flash1):
                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, LEDC_TEST_DUTY);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(200 / portTICK_PERIOD_MS);

                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, 0);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(1500 / portTICK_PERIOD_MS);
            break;
            case(ledmode_flash2):
                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, LEDC_TEST_DUTY);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(200 / portTICK_PERIOD_MS);

                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, 0);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(200 / portTICK_PERIOD_MS);

                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, LEDC_TEST_DUTY);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(200 / portTICK_PERIOD_MS);

                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, 0);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(1500 / portTICK_PERIOD_MS);
            break;
            case(ledmode_flash3):
                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, LEDC_TEST_DUTY);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(200 / portTICK_PERIOD_MS);

                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, 0);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(200 / portTICK_PERIOD_MS);

                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, LEDC_TEST_DUTY);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(200 / portTICK_PERIOD_MS);

                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, 0);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(200 / portTICK_PERIOD_MS);

                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, LEDC_TEST_DUTY);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(200 / portTICK_PERIOD_MS);

                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, 0);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(1500 / portTICK_PERIOD_MS);
            break;
            case(ledmode_flash_find_dev):
                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, LEDC_TEST_DUTY);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(80 / portTICK_PERIOD_MS);

                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, 0);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(80 / portTICK_PERIOD_MS);
            break;
            case(ledmode_fade):
                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_fade_with_time(ledc_channel[ch].speed_mode,
                            ledc_channel[ch].channel, LEDC_TEST_DUTY, LEDC_TEST_FADE_TIME);
                    ledc_fade_start(ledc_channel[ch].speed_mode,
                            ledc_channel[ch].channel, LEDC_FADE_NO_WAIT);
                }
                vTaskDelay(LEDC_TEST_FADE_TIME / portTICK_PERIOD_MS);

                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_fade_with_time(ledc_channel[ch].speed_mode,
                            ledc_channel[ch].channel, 0, LEDC_TEST_FADE_TIME);
                    ledc_fade_start(ledc_channel[ch].speed_mode,
                            ledc_channel[ch].channel, LEDC_FADE_NO_WAIT);
                }
                vTaskDelay(LEDC_TEST_FADE_TIME / portTICK_PERIOD_MS);
            break;
            case(ledmode_off):
                for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
                    ledc_set_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel, 0);
                    ledc_update_duty(ledc_channel[ch].speed_mode, ledc_channel[ch].channel);
                }
                vTaskDelay(1000 / portTICK_PERIOD_MS);
            break;

            default:
                vTaskDelay(1000 / portTICK_PERIOD_MS);
            break;
        }
    }
}

void MyLed_SetMode(uint8_t new_mode)
{
    if(my_ledc_info.led_mng_enable==false)
    {
        my_ledc_info.mode = new_mode;
    }
}

void MyLed_SetMode_Super(uint8_t new_mode)
{
    my_ledc_info.mode = new_mode;
}

uint8_t MyLed_GetMode(void)
{
    return my_ledc_info.mode;
}

void MyLed_Mng_Enable(void)
{
    my_ledc_info.led_mng_enable = true;
}

void MyLed_Mng_Disable(void)
{
    my_ledc_info.led_mng_enable = false;
}

void MyLedInit(void)
{
    BaseType_t ret = pdPASS;

    my_ledc_info.led_mng_enable = false;

    #if DEBUG_PRINT_LEVEL_my_ledc_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
    printf("sizeof(my_ledc_info)=%d\n", sizeof(my_ledc_info));
    #endif

    #if DEBUG_PRINT_LEVEL_my_ledc_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
    printf("my_ledc_info.mode=%d\n", my_ledc_info.mode);
    #endif
    // Set configuration of timer0 for high speed channels
    ledc_timer_config(&ledc_timer);

    // Set LED Controller with previously prepared configuration
    for (ch = 0; ch < LEDC_TEST_CH_NUM; ch++) {
        ledc_channel_config(&ledc_channel[ch]);
    }

    // Initialize fade service.
    ledc_fade_func_install(ESP_INTR_FLAG_LEVEL2);

    #if MyLedTask_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    p_task_stack = calloc(1, MyLedTask_task_stack_size*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
			if(xTaskCreateStaticPinnedToCore(MyLedTask, "MyLedTask", MyLedTask_task_stack_size, NULL, MyLedTask_priority, p_task_stack, p_task_data, MyLedTask_COREID)!=p_task_data)
			{
				ESP_LOGE("MyEspChipInfoMng_Init()", "creat MyLedTask use xTaskCreateStaticPinnedToCore() error\n");
			}
        }
    }
	#elif MyLedTask_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
	ret = xTaskCreatePinnedToCore(MyLedTask, "MyLedTask", MyLedTask_task_stack_size, NULL, MyLedTask_priority, &MyLedTaskHandle, MyLedTask_COREID);
	if(ret!=pdPASS)
	{
		ESP_LOGE("MyEspChipInfoMng_Init()", "creat MyLedTask use xTaskCreatePinnedToCore() error=%d\n", ret);
	}
	#endif
}

#endif