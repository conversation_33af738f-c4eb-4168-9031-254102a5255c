#include "sw_uart_config.h"

#if SW_UART_ENABLE

#include "myesp_sw_uart.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "string.h"

static const char* TAG = "SW_UART_DEBUG";

// 调试用的引脚定义
#define DEBUG_SW_UART_TX_PIN    (GPIO_NUM_4)
#define DEBUG_SW_UART_RX_PIN    (GPIO_NUM_5)

// 调试统计
static uint32_t debug_rx_count = 0;
static uint32_t debug_tx_count = 0;
static uint32_t debug_error_count = 0;

/**
 * @brief 软串口接收调试任务
 */
static void sw_uart_debug_rx_task(void *pvParameters) {
    char rx_buffer[256];
    int bytes_received;
    
    ESP_LOGI(TAG, "SW_UART debug RX task started");
    
    while (1) {
        // 接收数据，超时时间100ms
        bytes_received = sw_uart_read_bytes(SW_UART_NUM_0, rx_buffer, 
                                           sizeof(rx_buffer) - 1, 
                                           100 / portTICK_PERIOD_MS);
        
        if (bytes_received > 0) {
            rx_buffer[bytes_received] = '\0';  // 添加字符串结束符
            debug_rx_count++;
            
            ESP_LOGI(TAG, "RX[%d] %d bytes:", debug_rx_count, bytes_received);
            
            // 以十六进制格式打印接收到的数据
            printf("HEX: ");
            for (int i = 0; i < bytes_received; i++) {
                printf("%02X ", (uint8_t)rx_buffer[i]);
            }
            printf("\n");
            
            // 以ASCII格式打印（如果是可打印字符）
            printf("ASCII: ");
            for (int i = 0; i < bytes_received; i++) {
                if (rx_buffer[i] >= 32 && rx_buffer[i] <= 126) {
                    printf("%c", rx_buffer[i]);
                } else {
                    printf(".");
                }
            }
            printf("\n");
            
            // 检查是否收到0xFF
            bool has_0xff = false;
            for (int i = 0; i < bytes_received; i++) {
                if ((uint8_t)rx_buffer[i] == 0xFF) {
                    has_0xff = true;
                    break;
                }
            }
            
            if (has_0xff) {
                debug_error_count++;
                ESP_LOGW(TAG, "⚠️  Received 0xFF - possible timing issue! Error count: %d", debug_error_count);
            }
            
            // 回显数据
            char echo_msg[300];
            snprintf(echo_msg, sizeof(echo_msg), "ECHO[%d]: ", debug_rx_count);
            strncat(echo_msg, rx_buffer, sizeof(echo_msg) - strlen(echo_msg) - 1);
            
            int echo_len = strlen(echo_msg);
            int sent = sw_uart_write_bytes(SW_UART_NUM_0, echo_msg, echo_len);
            if (sent > 0) {
                debug_tx_count++;
                ESP_LOGI(TAG, "TX[%d] echoed %d bytes", debug_tx_count, sent);
            }
        }
    }
}

/**
 * @brief 软串口发送测试任务
 */
static void sw_uart_debug_tx_task(void *pvParameters) {
    int counter = 0;
    char test_msg[100];
    
    ESP_LOGI(TAG, "SW_UART debug TX task started");
    
    // 等待一段时间让系统稳定
    vTaskDelay(3000 / portTICK_PERIOD_MS);
    
    while (1) {
        // 每10秒发送一次测试数据
        snprintf(test_msg, sizeof(test_msg), "SW_UART_TEST_%04d\r\n", counter++);
        
        int bytes_sent = sw_uart_write_bytes(SW_UART_NUM_0, test_msg, strlen(test_msg));
        if (bytes_sent > 0) {
            debug_tx_count++;
            ESP_LOGI(TAG, "TX[%d] sent %d bytes: %s", debug_tx_count, bytes_sent, test_msg);
        } else {
            ESP_LOGE(TAG, "TX failed: %d", bytes_sent);
        }
        
        vTaskDelay(10000 / portTICK_PERIOD_MS);
    }
}

/**
 * @brief 状态监控任务
 */
static void sw_uart_debug_status_task(void *pvParameters) {
    ESP_LOGI(TAG, "SW_UART debug status task started");
    
    while (1) {
        // 每30秒打印一次统计信息
        vTaskDelay(30000 / portTICK_PERIOD_MS);
        
        size_t buffered_data = 0;
        sw_uart_get_buffered_data_len(SW_UART_NUM_0, &buffered_data);
        
        ESP_LOGI(TAG, "=== SW_UART Status ===");
        ESP_LOGI(TAG, "RX Count: %d", debug_rx_count);
        ESP_LOGI(TAG, "TX Count: %d", debug_tx_count);
        ESP_LOGI(TAG, "Error Count: %d", debug_error_count);
        ESP_LOGI(TAG, "Buffered Data: %d bytes", buffered_data);
        ESP_LOGI(TAG, "Error Rate: %.2f%%", 
                debug_rx_count > 0 ? (float)debug_error_count * 100.0f / debug_rx_count : 0.0f);
    }
}

/**
 * @brief 初始化软串口调试
 */
int sw_uart_debug_init(void) {
    ESP_LOGI(TAG, "Initializing SW_UART debug with TX=%d, RX=%d", 
             DEBUG_SW_UART_TX_PIN, DEBUG_SW_UART_RX_PIN);
    
    // 1. 配置软串口参数
    sw_uart_config_t sw_uart_config = {
        .baud_rate = 9600,  // 使用较低的波特率以提高稳定性
        .data_bits = SW_UART_DATA_8_BITS,
        .parity = SW_UART_PARITY_DISABLE,
        .stop_bits = SW_UART_STOP_BITS_1,
        .flow_ctrl_enable = false
    };
    
    // 2. 应用配置
    int ret = sw_uart_param_config(SW_UART_NUM_0, &sw_uart_config);
    if (ret != SW_UART_OK) {
        ESP_LOGE(TAG, "Failed to configure SW_UART: %d", ret);
        return ret;
    }
    
    // 3. 设置引脚
    ret = sw_uart_set_pin(SW_UART_NUM_0, DEBUG_SW_UART_TX_PIN, DEBUG_SW_UART_RX_PIN, 
                         SW_UART_PIN_NO_CHANGE, SW_UART_PIN_NO_CHANGE);
    if (ret != SW_UART_OK) {
        ESP_LOGE(TAG, "Failed to set SW_UART pins: %d", ret);
        return ret;
    }
    
    // 4. 安装驱动
    ret = sw_uart_driver_install(SW_UART_NUM_0, 1024, 0, 0, NULL, 0);
    if (ret != SW_UART_OK) {
        ESP_LOGE(TAG, "Failed to install SW_UART driver: %d", ret);
        return ret;
    }
    
    ESP_LOGI(TAG, "SW_UART debug initialized successfully");
    return SW_UART_OK;
}

/**
 * @brief 启动软串口调试测试
 */
void sw_uart_debug_start(void) {
    // 初始化软串口
    if (sw_uart_debug_init() != SW_UART_OK) {
        ESP_LOGE(TAG, "Failed to initialize SW_UART debug");
        return;
    }
    
    // 创建调试任务
    BaseType_t task_ret;
    
    // 创建接收任务
    task_ret = xTaskCreate(sw_uart_debug_rx_task, "sw_uart_debug_rx", 
                          4096, NULL, 6, NULL);
    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create SW_UART debug RX task");
        return;
    }
    
    // 创建发送任务
    task_ret = xTaskCreate(sw_uart_debug_tx_task, "sw_uart_debug_tx", 
                          4096, NULL, 5, NULL);
    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create SW_UART debug TX task");
        return;
    }
    
    // 创建状态监控任务
    task_ret = xTaskCreate(sw_uart_debug_status_task, "sw_uart_debug_status", 
                          3072, NULL, 4, NULL);
    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create SW_UART debug status task");
        return;
    }
    
    ESP_LOGI(TAG, "SW_UART debug test started");
    ESP_LOGI(TAG, "Connect your device to:");
    ESP_LOGI(TAG, "  TX (ESP32 -> Device): GPIO%d", DEBUG_SW_UART_TX_PIN);
    ESP_LOGI(TAG, "  RX (Device -> ESP32): GPIO%d", DEBUG_SW_UART_RX_PIN);
    ESP_LOGI(TAG, "Baud rate: 9600, 8N1");
}

/**
 * @brief 手动发送测试数据
 */
int sw_uart_debug_send_test(const char* data) {
    if (data == NULL) {
        return SW_UART_ERR_INVALID_ARG;
    }
    
    ESP_LOGI(TAG, "Manual test send: %s", data);
    return sw_uart_write_bytes(SW_UART_NUM_0, data, strlen(data));
}

/**
 * @brief 清空接收缓冲区
 */
void sw_uart_debug_flush(void) {
    sw_uart_flush(SW_UART_NUM_0);
    ESP_LOGI(TAG, "RX buffer flushed");
}

/**
 * @brief 重置调试统计
 */
void sw_uart_debug_reset_stats(void) {
    debug_rx_count = 0;
    debug_tx_count = 0;
    debug_error_count = 0;
    ESP_LOGI(TAG, "Debug statistics reset");
}

#endif // SW_UART_ENABLE
