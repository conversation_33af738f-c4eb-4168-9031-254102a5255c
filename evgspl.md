# 1. 注册
**数据格式(请求)**
```json
    {
        "type": "regist",
        "data": {
            "deviceNo": "",
            "iccid": "",
            "imei": "",
            "ble": "",
            "model": "",
            "userName": "",
            "password": ""
        }
    }
```
**数据格式(响应格式)**
```json
    {
        "type": "regist_response",
        "data": {
            "password": ""
        }
    }
```

# 2. 登录
**数据格式(请求)**
```json
    {
        "type": "auth",
        "data": {
            "password": ""
        }
    }
```
**数据格式(响应)**
```json
    {
        "type": "auth_response",
        "data": {
            "deviceNo": ""
        }
    }
```

# 3. 心跳
**数据格式(请求)**
```json
    {
        "type": "heartbeat",
        "data": {
            "deviceNo": ""
        }
    }
```
**数据格式(响应)**
```json
    {
        "type": "heartbeat_response",
        "data": {
            "deviceNo": ""
        }
    }
```

# 4. 数据上报
**数据格式(请求)**
```json
    {
        "type": "data",
        "data": {
            "deviceNo": "",
            "...": ""
        }
    }
```
**数据格式(响应)**
```json
    {
        "type": "data_response",
        "data": {
            "deviceNo": ""
        }
    }
```

# 4. 参数设置
**数据格式(请求)**
```json
{
    "msgType": "param_setting",
    "data": {
        "restore": "true",
        "channeldoor_mode": 0,
        "channeldoor_mode2_plan":[
            {
                "start_time": "08:00",
                "end_time": "17:00"
            },
            {
                "start_time": "18:00",
                "end_time": "23:00"
            }
        ]
    }
}
```
**数据格式(响应)**
```json
    {
        "type": "param_setting",
        "data": {
            "deviceNo": ""
        }
    }
```