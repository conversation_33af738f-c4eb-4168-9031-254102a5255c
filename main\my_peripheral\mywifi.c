#include "mywifi.h"

#if WIFI_FUNC
#include "my_tcp_server.h"
#include "my_nvs.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"
#include "freertos/semphr.h"

#include <stdio.h>
#include <string.h>
#include "esp_wifi.h"
#include "esp_log.h"
#include "esp_event_loop.h"


int wifi_state = 0;

my_wifi_info_t my_wifi_info;

static esp_err_t event_handler(void *ctx, system_event_t *event)
{
	wifi_ap_record_t wifi_ap_record;
	
    switch(event->event_id) 
	{
		case SYSTEM_EVENT_STA_START:                         // wifi STA 开始
			printf("[WIFI AP+STA] STA start event!\r\n");
			ESP_ERROR_CHECK(esp_wifi_connect());                 // 连接wifi
			break;
		case SYSTEM_EVENT_STA_GOT_IP:                        // 分配到了IP
			printf("[WIFI AP+STA] Get IP event!\r\n");
			printf("[WIFI AP+STA] ESP32 IP: %s !\r\n", ip4addr_ntoa(&event->event_info.got_ip.ip_info.ip));
			my_wifi_info.wifi_state = WIFI_STATE_ONLINE;
			break;
		case SYSTEM_EVENT_STA_CONNECTED:                     // 连接上了wifi	
			printf("[WIFI AP+STA] Wifi STA connect event!\r\n");
			esp_wifi_sta_get_ap_info(&wifi_ap_record);             // STA模式下，获取模块连接上的wifi热点的信息
			printf("[WIFI AP+STA] Connect to : %s!\r\n", wifi_ap_record.ssid);
			break;
		case SYSTEM_EVENT_STA_DISCONNECTED:                  // 断开了与wifi的连接
			printf("[WIFI AP+STA] Wifi STA disconnect event, reconnect!\r\n");
			my_wifi_info.wifi_state = WIFI_STATE_OFFLINE; 
			ESP_ERROR_CHECK( esp_wifi_connect() );                // 重新连接wifi         
			break;
		case SYSTEM_EVENT_AP_START:                          // wifi AP 开始
			printf("[WIFI AP+STA] AP start event!\r\n");

			//ESP_LOGI(TAG, "station:"MACSTR" join,AID=%d\n",
			//MAC2STR(event->event_info.sta_connected.mac),
			//event->event_info.sta_connected.aid);
			//xEventGroupSetBits(udp_event_group, WIFI_CONNECTED_BIT);
			break;
		case SYSTEM_EVENT_AP_STACONNECTED:                   // 有站点（STA）连接上ESP32的AP
			printf("[WIFI AP+STA] A station connected to ESP32 soft-AP!\r\n");
			//ESP_LOGI(TAG, "station:"MACSTR" join,AID=%d\n",
			//MAC2STR(event->event_info.sta_connected.mac),
			//event->event_info.sta_connected.aid);
			//xEventGroupSetBits(udp_event_group, WIFI_CONNECTED_BIT);
			break;
		case SYSTEM_EVENT_AP_STADISCONNECTED:                // 有站点（STA）与ESP32的AP断开连接
			printf("[WIFI AP+STA] A station disconnected from ESP32 soft-AP!\r\n");
			//ESP_LOGI(TAG, "station:"MACSTR"leave,AID=%d\n",
			//MAC2STR(event->event_info.sta_disconnected.mac),
			//event->event_info.sta_disconnected.aid);
			//xEventGroupClearBits(udp_event_group, WIFI_CONNECTED_BIT);
			break;
		default:
			break;
    }
	
    return ESP_OK;	
}

int _MyWifi_Init(my_wifi_conf_t* conf)
{
	if(conf==NULL)
	{
		return -1;
	}

    tcpip_adapter_init();
	
    ESP_ERROR_CHECK( esp_event_loop_init(event_handler, NULL) );
	
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK( esp_wifi_init(&cfg) );
	ESP_ERROR_CHECK( esp_wifi_set_storage(WIFI_STORAGE_RAM) );
	
	wifi_config_t wifi_sta_config;
	memset(&wifi_sta_config, 0, sizeof(wifi_sta_config));
	memcpy(wifi_sta_config.sta.ssid, conf->sta_ssid, strlen(conf->sta_ssid));
	memcpy(wifi_sta_config.sta.password, conf->sta_password, strlen(conf->sta_password));
	printf("wifi_sta_config.sta.ssid=%s\n", wifi_sta_config.sta.ssid);
	printf("wifi_sta_config.sta.password=%s\n", wifi_sta_config.sta.password);
	
	// AP配置
	wifi_config_t wifi_ap_config;
	memset(&wifi_ap_config, 0, sizeof(wifi_ap_config));
	memcpy(wifi_ap_config.ap.ssid, conf->ap_ssid, strlen(conf->ap_ssid));
	memcpy(wifi_ap_config.ap.password, conf->ap_password, strlen(conf->ap_password));
	wifi_ap_config.ap.ssid_len = strlen(conf->ap_ssid);
	wifi_ap_config.ap.channel = ESP32_AP_CHANNEL;
	wifi_ap_config.ap.max_connection = ESP32_MAX_CONN;
	wifi_ap_config.ap.authmode = WIFI_AUTH_WPA_WPA2_PSK;
	printf("wifi_ap_config.ap.ssid=%s\n", wifi_ap_config.ap.ssid);
	printf("wifi_ap_config.ap.password=%s\n", wifi_ap_config.ap.password);
	
	switch(conf->mode)
	{
		case(0):
			
			break;
		case(1):
			ESP_ERROR_CHECK( esp_wifi_set_mode(WIFI_MODE_STA) );                      // softAP+station模式
			ESP_ERROR_CHECK( esp_wifi_set_config(ESP_IF_WIFI_STA, &wifi_sta_config) );  // STA配置
			ESP_ERROR_CHECK( esp_wifi_start() );
			break;
		case(2):
			ESP_ERROR_CHECK( esp_wifi_set_mode(WIFI_MODE_AP) );                      // softAP+station模式
			ESP_ERROR_CHECK( esp_wifi_set_config(ESP_IF_WIFI_AP, &wifi_ap_config) );    // AP配置
			ESP_ERROR_CHECK( esp_wifi_start() );
			break;
		case(3):
			ESP_ERROR_CHECK( esp_wifi_set_mode(WIFI_MODE_APSTA) );                      // softAP+station模式
			ESP_ERROR_CHECK( esp_wifi_set_config(ESP_IF_WIFI_STA, &wifi_sta_config) );  // STA配置
			ESP_ERROR_CHECK( esp_wifi_set_config(ESP_IF_WIFI_AP, &wifi_ap_config) );    // AP配置
			ESP_ERROR_CHECK( esp_wifi_start() );
			break;
	}

	return 0;
}

SemaphoreHandle_t MyWifiMutexSemaphore = NULL;
int MyWifi_Init(my_wifi_conf_t* conf)
{
	if(!my_wifi_info.init_ok)
    {
        if(conf!=NULL)
        {
            memset(&my_wifi_info.conf, 0, sizeof(my_wifi_info.conf));
            memcpy(&my_wifi_info.conf, conf, sizeof(my_wifi_conf_t));
        }
        else
        {
            memset(&my_wifi_info.conf, 0, sizeof(my_wifi_info.conf));
            my_wifi_info.conf.enable = MYWIFI_ENABLE_DEFAULT;
			my_wifi_info.conf.mode = MYWIFI_MODE_DEFAULT;
            
            memcpy(my_wifi_info.conf.sta_ssid, MYWIFI_STA_SSID_DEFAULT, strlen(MYWIFI_STA_SSID_DEFAULT));
            memcpy(my_wifi_info.conf.sta_ssid, MYWIFI_STA_PASS_DEFAULT, strlen(MYWIFI_STA_PASS_DEFAULT));

			memcpy(my_wifi_info.conf.sta_ssid, MYWIFI_AP_SSID_DEFAULT, strlen(MYWIFI_AP_SSID_DEFAULT));
            memcpy(my_wifi_info.conf.sta_ssid, MYWIFI_AP_PASS_DEFAULT, strlen(MYWIFI_AP_PASS_DEFAULT));
        }

        if(my_wifi_info.conf.enable)
        {
            if(!my_wifi_info.first_init)
            {
                MyWifiMutexSemaphore = xSemaphoreCreateMutex();
                my_wifi_info.first_init = 1;
            }

			_MyWifi_Init(conf);

            my_wifi_info.init_ok = 1;
        }
    }
    return 0;
}

// int My_Wifi_Get_State(void)
// {
// 	return my_wifi_info.wifi_state;
// }

#endif