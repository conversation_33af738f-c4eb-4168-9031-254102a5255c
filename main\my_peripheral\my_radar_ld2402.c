#include "my_radar_ld2402.h"

#if MY_RADAR_LD2402_FUNC

#include "my_radar_uart.h"
#include <esp_log.h>
#include "esp_system.h"

const char my_radar_ld2402_cmd_enable_config_mode[] = {0xFD, 0xFC, 0xFB, 0xFA, 0x04, 0x00, 0xFF, 0x00, 0x01, 0x00, 0x04, 0x03, 0x02, 0x01};
const char my_radar_ld2402_cmd_disable_config_mode[] = {0xFD, 0xFC, 0xFB, 0xFA, 0x02, 0x00, 0xFE, 0x00, 0x04, 0x03, 0x02, 0x01};
const char my_radar_ld2402_cmd_energy_output_mode[] = {0xFD, 0xFC, 0xFB, 0xFA, 0x08, 0x00, 0x12, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x04, 0x03, 0x02, 0x01};

int _My_Radar_LD2402_EnableConfig(void)
{
    vTaskDelay(100 / portTICK_PERIOD_MS);
    AsendData("My_Radar_LD2402_EnableConfig", my_radar_ld2402_cmd_enable_config_mode, sizeof(my_radar_ld2402_cmd_enable_config_mode));
    vTaskDelay(100 / portTICK_PERIOD_MS);

    return 0;
}

int _My_Radar_LD2402_DisableConfig(void)
{
    vTaskDelay(100 / portTICK_PERIOD_MS);
    AsendData("My_Radar_LD2402_DisableConfig", my_radar_ld2402_cmd_disable_config_mode, sizeof(my_radar_ld2402_cmd_disable_config_mode));
    vTaskDelay(100 / portTICK_PERIOD_MS);


    return 0;
}

int _My_Radar_LD2402_Config_Energy_Output(void)
{
    _My_Radar_LD2402_EnableConfig();
    AsendData("My_Radar_LD2402_Config_Trace_Single", my_radar_ld2402_cmd_energy_output_mode, sizeof(my_radar_ld2402_cmd_energy_output_mode));
    _My_Radar_LD2402_DisableConfig();

    return 0;
}

int LD2402_start_detect()
{
    return _My_Radar_LD2402_DisableConfig();
}

int LD2402_stop_detect()
{
    return _My_Radar_LD2402_EnableConfig();
}

int LD2402_energy_output()
{
    return _My_Radar_LD2402_Config_Energy_Output();
}

#endif