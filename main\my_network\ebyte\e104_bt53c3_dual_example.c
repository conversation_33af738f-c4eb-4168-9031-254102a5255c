#include "e104_bt53c3.h"
#include "e104_bt53c3_sw.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char* TAG = "E104_DUAL_EXAMPLE";

/**
 * @brief 双E104模组使用示例
 * 
 * 本示例展示如何同时使用两个E104-BT53C3模组：
 * - 第一个模组使用硬件UART (UART_NUM_2)
 * - 第二个模组使用软串口 (SW_UART_NUM_0)
 */

// 第一个模组的引脚定义 (使用硬件UART)
#define E104_HW_TXD_PIN     (GPIO_NUM_7)
#define E104_HW_RXD_PIN     (GPIO_NUM_6)
#define E104_HW_RST_PIN     (GPIO_NUM_15)
#define E104_HW_MOD_PIN     (GPIO_NUM_16)

// 第二个模组的引脚定义 (使用软串口)
#define E104_SW_TXD_PIN     (GPIO_NUM_8)
#define E104_SW_RXD_PIN     (GPIO_NUM_19)
#define E104_SW_RST_PIN     (GPIO_NUM_17)
#define E104_SW_MOD_PIN     (GPIO_NUM_18)

/**
 * @brief 初始化双E104模组
 */
void e104_dual_init(void) {
    ESP_LOGI(TAG, "Initializing dual E104-BT53C3 modules...");
    
    // 初始化第一个模组 (硬件UART)
    ESP_LOGI(TAG, "Initializing E104 module #1 (Hardware UART)...");
    int ret1 = e104_bt53c3_init();
    if (ret1 == 0) {
        ESP_LOGI(TAG, "E104 module #1 initialized successfully");
    } else {
        ESP_LOGE(TAG, "Failed to initialize E104 module #1: %d", ret1);
    }
    
    // 等待一段时间，避免两个模组初始化冲突
    vTaskDelay(2000 / portTICK_PERIOD_MS);
    
    // 初始化第二个模组 (软串口)
    ESP_LOGI(TAG, "Initializing E104 module #2 (Software UART)...");
    int ret2 = e104_bt53c3_sw_init(E104_SW_TXD_PIN, E104_SW_RXD_PIN, 
                                   E104_SW_RST_PIN, E104_SW_MOD_PIN);
    if (ret2 == 0) {
        ESP_LOGI(TAG, "E104 module #2 initialized successfully");
    } else {
        ESP_LOGE(TAG, "Failed to initialize E104 module #2: %d", ret2);
    }
    
    ESP_LOGI(TAG, "Dual E104-BT53C3 modules initialization completed");
}

/**
 * @brief 配置双E104模组
 */
// void e104_dual_config(void) {
//     ESP_LOGI(TAG, "Configuring dual E104-BT53C3 modules...");
    
//     // 配置第一个模组
//     ESP_LOGI(TAG, "Configuring E104 module #1...");
//     e104_bt53c3_set_dev_role(0);  // 设置为从机
//     vTaskDelay(500 / portTICK_PERIOD_MS);
//     e104_bt53c3_set_adv_interval(100);  // 设置广播间隔
//     vTaskDelay(500 / portTICK_PERIOD_MS);
//     e104_bt53c3_set_power(4);  // 设置发射功率
//     vTaskDelay(500 / portTICK_PERIOD_MS);
    
//     // 配置第二个模组
//     ESP_LOGI(TAG, "Configuring E104 module #2...");
//     e104_bt53c3_sw_set_dev_role(0);  // 设置为从机
//     vTaskDelay(500 / portTICK_PERIOD_MS);
//     e104_bt53c3_sw_set_adv_interval(100);  // 设置广播间隔
//     vTaskDelay(500 / portTICK_PERIOD_MS);
//     e104_bt53c3_sw_set_power(4);  // 设置发射功率
//     vTaskDelay(500 / portTICK_PERIOD_MS);
    
//     ESP_LOGI(TAG, "Dual E104-BT53C3 modules configuration completed");
// }

/**
 * @brief 发送测试数据到两个模组
 */
void e104_dual_send_test_data(void) {
    const char* test_data1 = "Hello from E104 module #1 (HW UART)";
    const char* test_data2 = "Hello from E104 module #2 (SW UART)";
    
    ESP_LOGI(TAG, "Sending test data to both modules...");
    
    // 向第一个模组发送数据
    int ret1 = e104_bt53c3_send_data_to_master((char*)test_data1, strlen(test_data1));
    if (ret1 == 0) {
        ESP_LOGI(TAG, "Data sent to E104 module #1 successfully");
    } else {
        ESP_LOGE(TAG, "Failed to send data to E104 module #1");
    }
    
    vTaskDelay(1000 / portTICK_PERIOD_MS);
    
    // 向第二个模组发送数据
    int ret2 = e104_bt53c3_sw_send_data_to_master((char*)test_data2, strlen(test_data2));
    if (ret2 == 0) {
        ESP_LOGI(TAG, "Data sent to E104 module #2 successfully");
    } else {
        ESP_LOGE(TAG, "Failed to send data to E104 module #2");
    }
}

/**
 * @brief 启动双E104模组测试
 */
void e104_dual_start_test(void) {
    // 初始化双模组
    e104_dual_init();
    
    // 等待初始化完成
    vTaskDelay(5000 / portTICK_PERIOD_MS);
    
    // 配置双模组
    // e104_dual_config();
    
    ESP_LOGI(TAG, "Dual E104-BT53C3 test started");
}

/**
 * @brief 在main函数中调用此函数来启动双E104模组
 */
void app_main_e104_dual_example(void) {
    ESP_LOGI(TAG, "=== E104-BT53C3 Dual Module Example ===");
    ESP_LOGI(TAG, "Module #1: Hardware UART (TX=%d, RX=%d)", E104_HW_TXD_PIN, E104_HW_RXD_PIN);
    ESP_LOGI(TAG, "Module #2: Software UART (TX=%d, RX=%d)", E104_SW_TXD_PIN, E104_SW_RXD_PIN);
    
    // 启动双模组测试
    e104_dual_start_test();
}

/*
使用说明：
1. 在main.c中包含此头文件：#include "e104_bt53c3_dual_example.h"
2. 在app_main()函数中调用：app_main_e104_dual_example();
3. 确保引脚配置不冲突
4. 确保软串口库已正确编译和链接

引脚连接：
第一个模组 (硬件UART):
- TX: GPIO7  -> E104模组RX
- RX: GPIO6  -> E104模组TX  
- RST: GPIO15 -> E104模组RST
- MOD: GPIO16 -> E104模组MOD

第二个模组 (软串口):
- TX: GPIO4  -> E104模组RX
- RX: GPIO5  -> E104模组TX
- RST: GPIO17 -> E104模组RST  
- MOD: GPIO18 -> E104模组MOD
*/
