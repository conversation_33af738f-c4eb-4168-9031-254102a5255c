#ifndef MY_WORKMODE_H
#define MY_WORKMODE_H 

#define MY_WORKMODE_FUNC 1


#if MY_WORKMODE_FUNC

#include <lwip/netdb.h>

#define MY_WORKMODE_FUNC_DEBUG 1

typedef enum
{
    workmode_standby=0x00,
    workmode_energy_saving,
    workmode1_realtime,
    workmode2_semirealtime,
    workmode3_timer,
    workmode4_semitimer,
}my_workmode_t;

typedef struct
{
    int work_start_time;
    int work_end_time;
}workplan_list_item_t;

typedef struct
{
    int sleep_start_time;
    int sleep_keep_time;
}sleepplan_list_item_t;

typedef struct
{
    int vrc_start_time;
    int vrc_end_time;
}vrc_list_item_t;

#define myworkmode_workmode_default workmode4_semitimer
#define MYWORKMODE_WORKPLAN_LIST_COUNT  5
#define MYWORKMODE_VRCPLAN_LIST_COUNT  5
typedef struct
{
    uint8_t dev_state;
    my_workmode_t workmode;
    int debug_mode;
    workplan_list_item_t workplan_list[MYWORKMODE_WORKPLAN_LIST_COUNT];
    sleepplan_list_item_t sleepplan_list[MYWORKMODE_WORKPLAN_LIST_COUNT];
    vrc_list_item_t vrc_plan_list[MYWORKMODE_VRCPLAN_LIST_COUNT];
    int fisrt_poweron_sleep_keep_time;
    int workmode3_timer_sleep_keep_time;
}my_workmode_conf_t;

typedef struct
{
    my_workmode_conf_t conf;
    void (*MyLowPower_Init_BeforeSleep)(void);
    void (*MyLowPower_Init_AfterWakeup)(int);
}my_workmode_info_t;

#define MYWORKMODE_ONCE_SLEEP_TIME_ULIMIT   (60*60)

#define WKMD_NVS_FORM_NAME	"NVS_WKMD"
#define WKMD_NVS_KEY_NAME	"NVS_WKMD_1"

/**
 * @return 
 */
int MyWorkmode_Check(void);

int MyWorkmode_Check_WhetherInWorktime(my_workmode_conf_t* conf);

int MyWorkmode_Init(my_workmode_conf_t* conf, void (*callback_func_beforesleep)(void), void (*callback_func_aftersleep)(int));

int MyWorkmode_ReLoad_Conf(my_workmode_conf_t* conf);

int MyWorkmode_WorkList_To_SleepList(workplan_list_item_t* p_worklist, int workplan_num, sleepplan_list_item_t* p_sleeplist, int sleepplan_num);

my_workmode_t MyWorkmode_GetCurrentWorkmode(void);


#endif
#endif
