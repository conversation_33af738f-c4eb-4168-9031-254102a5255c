#include "my_iot_box.h"

#if MY_IOT_BOX_FUNC

#include "my_nvs.h"
#include "my_time.h"
#include "my_whgm5.h"
#include "my_asl.h"
#include "my_tsfc.h"
#include "my_workmode.h"
#include "my_camera.h"
#include "my_power_mng.h"
#include "my_esp_chip_info_mng.h"
#include "my_ble.h"
#include "my_lowpower.h"
#include "my_config.h"
#include "my_ds.h"
#include "my_asl_dpu.h"
#include "my_esp_aes.h"
#include "my_whgm5.h"
#include "checksum.h"
#include "my_dev_paramid.h"
#include "uart_ota.h"
#include "mywifi.h"
#include "my_tcp_server.h"
#include "my_tcp_client.h"
#include "my_monitor.h"
#include "my_debug.h"
#include "my_video_record.h"
#include "my_air780eg.h"

#include "esp_log.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"
#include "freertos/semphr.h"

#include "string.h"

#include "adxl345.h"
#include "mytest_radar.h"

#include "my_relay.h"


#if defined MY_DEBUG_PRINT_FUNC
#define DEBUG_PRINT_LEVEL_my_iot_box_PRINT   MY_DEBUG_PRINT_LEVEL_0
#else
#define DEBUG_PRINT_LEVEL_my_iot_box_PRINT   DEBUG_PRINT_LEVEL_0
#endif


#define DEV_MODEL   "SWQZ-WC2D75-12A"

TaskHandle_t My_IotBox_TimerTaskHandle = NULL;

my_iotbox_info_t my_iotbox_info;

#define test_picture_data_buf_size (500*1024)

int iot_net_ok = 0;
bool iot_ota_checking = false;

SemaphoreHandle_t MyIotbox_linkbusy_MutexSemaphore = NULL;


void My_IotBox_Routine_DevActive(void)
{
    //获取所有配置
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();

    //更新一些出厂参数
    if(p_my_conf->factory.dev_state<2)
    {
        char my_dev_mac[6]={0};
        MyEspChipInfoMng_GetMacInt(my_dev_mac, sizeof(my_dev_mac));
        ESP_LOGI("My_IotBox_Routine_DevActive()", "###---synchronize 'mac");
        MyConfig_Modify_factory_mac(my_dev_mac, sizeof(my_dev_mac), 1);
    }
    
    if(p_my_conf->factory.dev_state<2)
    {
        ESP_LOGI("My_IotBox_Routine_DevActive()", "###---synchronize 'imei");
        char imei[32];
        memset(imei, 0, sizeof(imei));
        MyWHGM5_GetIMEI(imei, sizeof(imei));
        if(MyConfig_Modify_factory_IMEI(imei, sizeof(imei), 1))
        {
            ESP_LOGW("My_IotBox_Routine_DevActive()", "imei changed!!!");
            // if(p_my_conf->factory.dev_state==2)
            // {
            //     ESP_LOGI("My_IotBox_Routine_DevActive()", "rfs");
            //     MyConfig_ResetAllConf_To_Default();
            // }
        }
    }

    if(p_my_conf->factory.dev_state<2)
    {
        ESP_LOGI("My_IotBox_Routine_DevActive()", "###---synchronize 'iccid");
        char iccid[32];
        memset(iccid, 0, sizeof(iccid));
        MyWHGM5_GetICCID(iccid, sizeof(iccid));
        if(MyConfig_Modify_factory_ICCID(iccid, sizeof(iccid), 1))
        {
            ESP_LOGW("My_IotBox_Routine_DevActive()", "iccid changed!!!");
        }
    }

    if(p_my_conf->factory.dev_state<2)
    {
        ESP_LOGI("My_IotBox_Routine_DevActive()", "creat dev_id");
        char dev_id[32];
        memset(dev_id, 0, sizeof(dev_id));
        char dev_imei[16];
        memset(dev_imei, 0, sizeof(dev_imei));
        MyWHGM5_GetIMEI(dev_imei, sizeof(dev_imei));
        MyWHGM5_GetIMEI(dev_id, sizeof(dev_id));
        memset(dev_id+strlen(dev_id), '0', 20-strlen(dev_id));
        ESP_LOGI("My_IotBox_Routine_DevActive()", "dev_id(%d)=%s", strlen(dev_id), dev_id);
        MyConfig_Modify_factory_devId(dev_id, strlen(dev_id), 1);

        // #if MY_BLE_FUNC
        // MyConfig_Modify_BLE_advData(dev_imei, strlen(dev_imei), 1, 1);

        // char dev_ble_name[16];
        // memset(dev_ble_name, 0, sizeof(dev_ble_name));
        // memcpy(dev_ble_name, "NL_", strlen("NL_"));
        // memcpy(dev_ble_name+strlen("NL_"), dev_imei+10, 5);
        // MyConfig_Modify_BLE_name(dev_ble_name, strlen(dev_ble_name), 1, 1);
        // #endif
    }

    if(p_my_conf->factory.dev_state==0)
    {
        MyConfig_Modify_factory_devstate(1, 1);
        ESP_LOGI("My_IotBox_TimerTask", "###---device activated locally");
    }
    else if(p_my_conf->factory.dev_state==1)
    {
        ESP_LOGI("My_IotBox_TimerTask", "###---device activated locally");
    }
}

void My_IotBox_offline_poweroff_check(void)
{
    if(!GetPower())
    {
        vTaskDelay(1000 / portTICK_PERIOD_MS);
        if(!GetPower())
        {
            ESP_LOGW("My_IotBox_offline_poweroff_check()", "external power off, power off...");
            vTaskDelay(1000 / portTICK_PERIOD_MS);
            MyPowerMng_MyWHGM5_PowerOFF();
            MyPowerMng_MobilePower_OFF();
            BAKPWR_OFF();
            vTaskDelay(1000 / portTICK_PERIOD_MS);
            My_esp_restart();
        }
    }
}

void My_IotBox_Routine_DevRegister(void)
{
    int err_count = 0;
    while(My_ASLdpu_DevRegister())
    {
        err_count++;
        ESP_LOGI("My_IotBox_Routine_DevRegister()", "dev register err_count=%d", err_count);
        vTaskDelay(((err_count%3)+1)*5000 / portTICK_PERIOD_MS);
        My_IotBox_offline_poweroff_check();
    }
    iot_net_ok = 100;
    ESP_LOGI("My_IotBox_Routine_DevRegister()", "dev register succeed");
}

int My_IotBox_Routine_DevLogin(uint8_t force_login)
{
    int err_count = 0;
    int re_register = 0;
    if(!my_iotbox_info.login_state||force_login)
    {
        int err = 0;
        do
        {
            err = My_ASLdpu_DevLogin();
            if(err)
            {
                err_count++;
                ESP_LOGI("My_IotBox_Routine_DevLogin()", "dev login err_count=%d, err=%d", err_count, err);
                if(err==1)
                {
                    vTaskDelay(5000 / portTICK_PERIOD_MS);
                    ESP_LOGI("My_IotBox_Routine_DevLogin()", "login err==1, try register to IoTServer...");
                    My_IotBox_Routine_DevRegister();
                    re_register = 1;
                }
                vTaskDelay(((err_count%3)+1)*5000 / portTICK_PERIOD_MS);
                My_IotBox_offline_poweroff_check();
            }
            else
            {
                break;
            }
        } while (err);
        ESP_LOGI("My_IotBox_Routine_DevLogin()", "dev login succeed");
        my_iotbox_info.login_state = 1;
        iot_net_ok = 101;
        if(re_register)
        {
            return 2;
        }

        return 0;
    }
    return 1;
}

int My_IotBox_Routine_DevTimesync(void)
{
    int err_count = 0;
    while(My_ASLdpu_DevTimeSync())
    {
        err_count++;
        ESP_LOGI("My_IotBox_Routine_DevTimesync()", "dev timesync err_count=%d", err_count);
        vTaskDelay(((err_count%3)+1)*5000 / portTICK_PERIOD_MS);
        if(err_count>=3)
        {
            ESP_LOGI("My_IotBox_Routine_DevTimesync()", "dev timesync failed");
            return -1;
        }
    }
    ESP_LOGI("My_IotBox_Routine_DevTimesync()", "dev timesync succeed");
    iot_net_ok = 102;

    return 0;
}

void My_IotBoxRoutine_DevTimeSync_Check(uint32_t time_ValidTime)
{
    if(MyTime_GetTime() - MyASL_Get_LastTimeSyncTime() >= time_ValidTime)
    {
        if(My_IotBox_Routine_DevTimesync()!=0)
        {
            ESP_LOGI("My_IotBoxRoutine_DevTimeSync_Check()", "iot timesync failed, try WHGM5 timesync...");
            if(MyWHGM5_timesync()==0)
            {
                ESP_LOGI("My_IotBoxRoutine_DevTimeSync_Check()", "WHGM5 timesync succeed");
            }
            else
            {
                ESP_LOGI("My_IotBoxRoutine_DevTimeSync_Check()", "WHGM5 timesync failed");
            }
        }
    }
}

int My_IotBox_Routine_GetOtaInfo(int repeat_count)
{
    int ret = 0;
    my_d2link_list_t* my_d2link_list = My_D2Link_Creat("ota info paramid list");

    // My_D2Link_Append_Item(my_d2link_list, 1, NULL, 0, devPI_factory_ota_vercode, 2, NULL);
    // My_D2Link_Append_Item(my_d2link_list, 2, NULL, 0, devPI_factory_ota_vername, 2, NULL);
    // My_D2Link_Append_Item(my_d2link_list, 3, NULL, 0, devPI_factory_ota_filename, 2, NULL);

    My_D2Link_Append_Item(my_d2link_list, 3, NULL, 0, devPI_factory_ota_info_net, 2, NULL);
    My_D2Link_PrintList(my_d2link_list);

    int err_count = 0;
    while(My_ASLdpu_DevParamReq_Partial(my_d2link_list))
    {
        err_count++;
        ESP_LOGW("My_IotBox_Routine_GetOtaInfo()", "err_count=%d", err_count);
        vTaskDelay(((err_count%3)+1)*5000 / portTICK_PERIOD_MS);
        if(err_count>=repeat_count)
        {
            ret = 1;
            break;
        }
    }
    if(!ret)
    {
        ESP_LOGI("My_IotBox_Routine_GetOtaInfo()", "succeed");
    }
    else
    {
        ESP_LOGW("My_IotBox_Routine_GetOtaInfo()", "failed");
    }

    // //debug
    // MyConfig_Modify_factory_verCode_net(1, 0);
    // MyConfig_Modify_factory_vername_net("V1.1", strlen("V1.1"), 0);
    // MyConfig_Modify_factory_verfilename_net("next version filename", strlen("next version filename"), 0);
    // //end of debug

    My_D2Link_Delete(&my_d2link_list);

    return ret;
}

int My_IotBox_Routine_DevOTACheck(int force, int get_info_repeat_count, int download_repeat_count)
{
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    int need_check_ota = 0;
    int ota_err = 0;

    if(!force)
    {
        if(p_my_conf->factory.ota_check_type==0)
        {
            return 0;
        }
        else if(p_my_conf->factory.ota_check_type==1)
        {
            if(my_iotbox_info.ota_check_after_poweron)
            {
                return 0;
            }
            need_check_ota = 1;
        }
        else if(p_my_conf->factory.ota_check_type==2)
        {
            if(my_iotbox_info.ota_checked_upgrade_time)
            {
                return 0;
            }
            time_t cur_time_in_day = MyTime_GetTime()%(3600*24);
            if(cur_time_in_day<4*3600)
            {
                need_check_ota = 1;
            }
            else
            {
                return 0;
            }
        }
        else if(p_my_conf->factory.ota_check_type==3)
        {
            if(!my_iotbox_info.ota_check_after_poweron)
            {
                need_check_ota = 1;
            }
            else
            {
                if(my_iotbox_info.ota_checked_upgrade_time)
                {
                    return 0;
                }
                time_t cur_time_in_day = MyTime_GetTime()%(3600*24);
                if(cur_time_in_day<4*3600)
                {
                    need_check_ota = 1;
                }
                else
                {
                    return 0;
                }
            }
        }
    }
    else
    {
        need_check_ota = 1;
    }

    
    if(need_check_ota==1)
    {
        // if(!my_iotbox_info.ota_info_sync_once)
        // {
        //     #if DEBUG_PRINT_LEVEL_my_iot_box_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
        //     MyConfig_DebugPrint_my_config_nvs_t_All("my_iot_box.c: before My_IotBox_Routine_GetOtaInfo()");
        //     #endif
            
        //     my_iotbox_info.ota_info_sync_once = 1;
        // }

        xSemaphoreTake(MyIotbox_linkbusy_MutexSemaphore, portMAX_DELAY);

        iot_ota_checking = true;

        ota_err = My_IotBox_Routine_GetOtaInfo(get_info_repeat_count);

        if(!ota_err)
        {
            if(p_my_conf->factory.my_ota_info_net.vercode > MY_IOTBOX_devPI_factory_ota_vercode_default)
            {

                // #if MY_BLE_FUNC
                // MyBle_Close();
                // #endif

                // goto test_My_IotBox_Routine_DevOTACheck_goto;
                char* filedownload_saving = NULL;
                int filedownload_len = 0;
                int err = My_ASLdpu_DevFileDownload_OTA(&filedownload_saving, &filedownload_len, 60000, download_repeat_count);
                if(!err)
                {
                    if(filedownload_saving!=NULL)
                    {
                        uint32_t download_crc = crc_32((void*)(filedownload_saving), filedownload_len);
                        ESP_LOGI("\n\n\n\n\n\n\n\n\n\nMy_IotBox_Routine_DevOTACheck()", \
                        "filedownload_len = %d, file_crc=%08X\n\n\n\n\n\n\n\n\n\n", \
                        filedownload_len, download_crc);

                        ESP_LOGI("My_IotBox_Routine_DevOTACheck()", "start uart ota routine...");
                        int err = UartOTA_Routine(filedownload_saving, filedownload_len);
                        if(!err)
                        {
                            ESP_LOGI("My_IotBox_Routine_DevOTACheck()", "uart ota succeed!, modify local vercode to %d", p_my_conf->factory.my_ota_info_net.vercode);
                            MyConfig_Modify_factory_verCode_local(p_my_conf->factory.my_ota_info_net.vercode, 1);
                            ESP_LOGI("My_IotBox_Routine_DevOTACheck()", "uart ota succeed!, modify local vername to %s", p_my_conf->factory.my_ota_info_net.vername);
                            MyConfig_Modify_factory_vername_local(p_my_conf->factory.my_ota_info_net.vername, \
                                                                    strlen(p_my_conf->factory.my_ota_info_net.vername), 1);
                            ESP_LOGI("My_IotBox_Routine_DevOTACheck()", "uart ota succeed!, modify local filename to %s", p_my_conf->factory.my_ota_info_net.filename);
                            MyConfig_Modify_factory_verfilename_local(p_my_conf->factory.my_ota_info_net.filename, \
                                                                    strlen(p_my_conf->factory.my_ota_info_net.filename), 1);
                            
                            #if DEBUG_PRINT_LEVEL_my_iot_box_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
                            MyConfig_DebugPrint_my_config_nvs_t_All("my_iot_box.c: after ota succeed");
                            #endif

                            my_d2link_list_t* modify_paramid_list = My_D2Link_Creat("modify_paramid_list");
                            My_D2Link_Append_Item(modify_paramid_list, 1, NULL, 0, devPI_factory_ota_vercode, sizeof(uint16_t), NULL);
                            My_D2Link_Append_Item(modify_paramid_list, 2, NULL, 0, devPI_factory_ota_vername, sizeof(uint16_t), NULL);
                            My_D2Link_Append_Item(modify_paramid_list, 3, NULL, 0, devPI_factory_ota_filename, sizeof(uint16_t), NULL);
                            My_ASLdpu_DevParamReport_Partial(modify_paramid_list);
                            My_D2Link_Delete(&modify_paramid_list);

                            vTaskDelay(5000 / portTICK_PERIOD_MS);

                            ESP_LOGI("My_IotBox_Routine_DevOTACheck()", "uart ota succeed!, restart system now...");

                            My_esp_restart();
                        }
                        else
                        {
                            ota_err = 1;
                            ESP_LOGW("My_IotBox_Routine_DevOTACheck()", "uart ota failed!");
                        }
                        free(filedownload_saving);filedownload_saving=NULL;
                    }
                    else
                    {
                        ota_err = 2;
                        ESP_LOGW("My_IotBox_Routine_DevOTACheck()", "filedownload_saving!=NULL is false!");
                    }
                }
                else
                {
                    ota_err = 3;
                    ESP_LOGW("My_IotBox_Routine_DevOTACheck()", "err = My_ASLdpu_DevFileDownload_OTA = %d", err);
                }
                // #if MY_BLE_FUNC
                // MyBle_Open();
                // #endif

            }
        }
        iot_ota_checking = false;
        xSemaphoreGive(MyIotbox_linkbusy_MutexSemaphore);
    }

    // test_My_IotBox_Routine_DevOTACheck_goto:
    if(p_my_conf->factory.ota_check_type>0)
    {
        if(!ota_err)
        {
            if(p_my_conf->factory.ota_check_type==1)
            {
                my_iotbox_info.ota_check_after_poweron = 1;
            }
            else if(p_my_conf->factory.ota_check_type==2)
            {
                my_iotbox_info.ota_check_after_poweron = 1;
                my_iotbox_info.ota_checked_upgrade_time = 1;
            }
            else if(p_my_conf->factory.ota_check_type==3)
            {
                if(!my_iotbox_info.ota_check_after_poweron)
                {
                    my_iotbox_info.ota_check_after_poweron = 1;
                    time_t cur_time_in_day = MyTime_GetTime()%(3600*24);
                    if(cur_time_in_day<4*3600)
                    {
                        my_iotbox_info.ota_checked_upgrade_time = 1;
                    }
                }
                else
                {
                    my_iotbox_info.ota_checked_upgrade_time = 1;
                }
            }
        }
    }

    ESP_LOGW("My_IotBox_Routine_DevOTACheck()", "ota_err=%d", ota_err);

    return ota_err;
}

int My_IotBox_Routine_EventReport(uint16_t param_id, int repeat_count)
{
    ESP_LOGI("My_IotBox_Routine_EventReport()", "event_id=0x%04X", param_id);

    // // debug
    // return 0;
    // // end of debug
    my_d2link_list_t* my_d2link_list = My_D2Link_Creat("event report paramid list");

    int id_cnt = 0;
    // My_D2Link_Append_Item(my_d2link_list, id_cnt++, NULL, 0, devPI_event_external_poweron, 2, NULL);
    // My_D2Link_Append_Item(my_d2link_list, id_cnt++, NULL, 0, devPI_event_external_poweroff, 2, NULL);
    My_D2Link_Append_Item(my_d2link_list, id_cnt++, NULL, 0, param_id, 2, NULL);
    #if DEBUG_PRINT_LEVEL_my_iot_box_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
    My_D2Link_PrintList(my_d2link_list);
    #endif

    int err_count = 0;
    while(My_ASLdpu_DevEventReport_Partial(my_d2link_list))
    {
        err_count++;
        ESP_LOGI("My_IotBox_Routine_EventReport()", "err_count=%d", err_count);
        vTaskDelay(((err_count%3)+1)*5000 / portTICK_PERIOD_MS);
        if(err_count>=repeat_count)
            break;
    }
    if(err_count<repeat_count)
    {
        ESP_LOGI("My_IotBox_Routine_EventReport()", "report succeed");
        return 0;
    }
    else
    {
        ESP_LOGW("My_IotBox_Routine_EventReport()", "report failed");
        return 1;
    }
    
    My_D2Link_Delete(&my_d2link_list);

    return 0;
}

void My_IotBox_Routine_ParamReport_GNSS(void)
{
    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\n\n\n\n\n\n\n\n\n\n\nMy_ASLdpu_DevParamReport_All():My_ASLdpu_DevParamReport_All\n");
    #endif


    uint16_t report_param_list[]={
        // devPI_gnss_longitude,
        // devPI_gnss_latitude,

        devPI_factory_imei,
        devPI_factory_iccid,
        0xffff
    };

    my_d2link_list_t* my_d2link_list = My_D2Link_Creat("gnss param item list");
    int item_cnt = 1;
    for(int i=0; report_param_list[i]!=0xffff; i++)
    {
        My_D2Link_Append_Item(my_d2link_list, item_cnt++, NULL, 0, report_param_list[i], sizeof(uint16_t), NULL);
    }

    My_ASLdpu_DevParamReport_Partial(my_d2link_list);

    My_D2Link_Delete(&my_d2link_list);
}

extern int unread_data;
extern char* radar_data_report_buf;
bool My_blufi_Get_Connect_State(void);


bool bts_get_lte_info_request = false;
bool bts_check_ota_request = false;
bool bts_isp_request = false;
bool bts_rdrptrgl = false;
bool bts_debug_mode_request = false;
int bts_debug_mode = 0;
bool bts_set_meas_report_mode_req = 0;
int bts_new_meas_report_mode = 0;
void My_IotBox_Refresh_LTE_INFO(void)
{
    ESP_LOGI("\nMy_IotBox_Refresh_LTE_INFO()", ">>MyWHGM5_RefreshDynamicInfo<<\n");

    xSemaphoreTake(MyIotbox_linkbusy_MutexSemaphore, portMAX_DELAY);
    // MyWHGM5_RefreshDynamicInfo();
    MyWHGM5_CheckRTI();

    xSemaphoreGive(MyIotbox_linkbusy_MutexSemaphore);

    ESP_LOGI("\nMy_IotBox_Refresh_LTE_INFO()", ">>MyWHGM5_RefreshDynamicInfo Done<<\n");
}

extern int radar_data_regular_report_buf_written_len;
extern int radar_data_regular_report_buf_default_written_len;
extern char* radar_data_regular_report_buf;
extern char* radar_data_regular_report_buf_default;
extern SemaphoreHandle_t Myradar_regular_data_MutexSemaphore;
extern bool radar_init_ok;
extern bool radar_start;
extern bool radar_data_regular_report_buf_full;
extern bool radar_regular_statistics_switch;
void My_IotBox_Report_Radar_Data(int force_report)
{
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();

    if(radar_init_ok)
    {
        if(p_my_conf->workmode.measReportMode==1)
        {
            if(force_report)
            {
                int unread_data_timeout = 0;
                while(unread_data==0)
                {
                    vTaskDelay(1000 / portTICK_PERIOD_MS);
                    unread_data_timeout++;
                    if(unread_data_timeout>70)
                    {
                        ESP_LOGW("My_IotBox_Report_Radar_Data(imageReportInterval)", "unread_data_timeout=%d", unread_data_timeout);
                        break;
                    }
                }
            }
            if(unread_data>0)
            {
                ESP_LOGI("My_IotBox_Report_Radar_Data()", "radar_data_report_buf(%d)\n", unread_data);
                esp_log_buffer_hex("queue_rcv_item->data", radar_data_report_buf, unread_data);

                xSemaphoreTake(MyIotbox_linkbusy_MutexSemaphore, portMAX_DELAY);
                My_ASLdpu_DevMeasurementReport(devPI_report_type_radar_stat_data, devPI_reportdata_type_radar_stat_data, radar_data_report_buf, unread_data);
                xSemaphoreGive(MyIotbox_linkbusy_MutexSemaphore);

                unread_data = 0;

                vTaskDelay(1000 / portTICK_PERIOD_MS);
            }
        }
        else if(p_my_conf->workmode.measReportMode==0)
        {
            time_t now_time = MyTime_GetTime();
            if((now_time - my_iotbox_info.last_radar_regular_report_time >= p_my_conf->workmode.measReportInterval)||(bts_rdrptrgl==true)||force_report)
            // if((now_time - my_iotbox_info.last_radar_regular_report_time >= 3*60)||(bts_rdrptrgl==true))
            {
                ESP_LOGI("My_IotBox_Report_Radar_Data", "------------------------report radar regular data------------------------");
                my_iotbox_info.last_radar_regular_report_time = now_time;
                xSemaphoreTake(Myradar_regular_data_MutexSemaphore, portMAX_DELAY);
                if(radar_data_regular_report_buf_written_len>0)
                {
                    ESP_LOGI("My_IotBox_Report_Radar_Data", "radar_data_regular_report_buf(%d):", radar_data_regular_report_buf_written_len);
                    esp_log_buffer_hex("radar_data_regular_report_buf", radar_data_regular_report_buf, radar_data_regular_report_buf_written_len);
                    
                    xSemaphoreTake(MyIotbox_linkbusy_MutexSemaphore, portMAX_DELAY);
                    My_ASLdpu_DevMeasurementReport(devPI_report_type_radar_regular_data, devPI_reportdata_type_radar_regular_data, radar_data_regular_report_buf, radar_data_regular_report_buf_written_len);
                    xSemaphoreGive(MyIotbox_linkbusy_MutexSemaphore);

                    radar_data_regular_report_buf_written_len = 0;
                    memset(radar_data_regular_report_buf, 0, radar_data_regular_report_buf_size);
                }
                else
                {
                    // radar_refresh_default_regular_data();
                    if(radar_data_regular_report_buf_default_written_len>0)
                    {
                        ESP_LOGI("My_IotBox_Report_Radar_Data", "radar_data_regular_report_buf_default(%d):", radar_data_regular_report_buf_default_written_len);
                        esp_log_buffer_hex("radar_data_regular_report_buf_default", radar_data_regular_report_buf_default, radar_data_regular_report_buf_default_written_len);
                        
                        xSemaphoreTake(MyIotbox_linkbusy_MutexSemaphore, portMAX_DELAY);
                        My_ASLdpu_DevMeasurementReport(devPI_report_type_radar_regular_data, devPI_reportdata_type_radar_regular_data, radar_data_regular_report_buf_default, radar_data_regular_report_buf_default_written_len);
                        xSemaphoreGive(MyIotbox_linkbusy_MutexSemaphore);

                        radar_data_regular_report_buf_default_written_len = 0;
                        memset(radar_data_regular_report_buf_default, 0, radar_data_regular_report_buf_default_size);
                    }
                }
                xSemaphoreGive(Myradar_regular_data_MutexSemaphore);
            }
            else if(radar_data_regular_report_buf_full==true)
            {
                ESP_LOGI("My_IotBox_Report_Radar_Data", "------------------------report radar regular data------------------------");
                my_iotbox_info.last_radar_regular_report_time = now_time;
                xSemaphoreTake(Myradar_regular_data_MutexSemaphore, portMAX_DELAY);

                if(radar_data_regular_report_buf_written_len>0)
                {
                    ESP_LOGI("My_IotBox_Report_Radar_Data", "radar_data_regular_report_buf(%d):", radar_data_regular_report_buf_written_len);
                    esp_log_buffer_hex("radar_data_regular_report_buf", radar_data_regular_report_buf, radar_data_regular_report_buf_written_len);
                    
                    xSemaphoreTake(MyIotbox_linkbusy_MutexSemaphore, portMAX_DELAY);
                    My_ASLdpu_DevMeasurementReport(devPI_report_type_radar_regular_data, devPI_reportdata_type_radar_regular_data, radar_data_regular_report_buf, radar_data_regular_report_buf_written_len);
                    xSemaphoreGive(MyIotbox_linkbusy_MutexSemaphore);

                    radar_data_regular_report_buf_written_len = 0;
                    memset(radar_data_regular_report_buf, 0, radar_data_regular_report_buf_size);
                }
                radar_data_regular_report_buf_full = false;
                xSemaphoreGive(Myradar_regular_data_MutexSemaphore);
            }
            if(bts_rdrptrgl==true)
            {
                bts_rdrptrgl=false;
            }
        }
    }
}


// bool channeldoor_mode0_changed_update = false;
// void iotbox_channeldoormode_update(void)
// {
//     channeldoor_mode0_changed_update = true;
// }

void My_IotBox_TimerTask(void* param)
{
    uint32_t runtimes = 0;

    int err_count = 0;

    vTaskDelay(5000 / portTICK_PERIOD_MS);

    my_iotbox_info.last_time_sync_time = 0;

    //同步供电状态
    my_iotbox_info.check_poweroff_lock = 1;
    my_iotbox_info.check_poweron_lock = 0;
    MyConfig_Modify_ExternalPowerstate(GetPower());
    if(!GetPower())
    {
        #if DEBUG_PRINT_LEVEL_my_iot_box_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
        printf("My_IotBox_TimerTask(): OK1");
        #endif
        my_iotbox_info.check_poweroff_lock = 0;
        my_iotbox_info.check_poweron_lock = 1;
    }
    

    //设备激活
    My_IotBox_Routine_DevActive();


    //同步开机次数
    ESP_LOGI("My_IotBox_TimerTask", "###---synchronize 'poweron count");
    MyConfig_Modify_factory_poweron_count(MyTime_GetPoweronCount(), 1);
    //同步开机时间
    ESP_LOGI("My_IotBox_TimerTask", "###---synchronize 'runtime_since_poweron");
    MyConfig_Modify_factory_runtime_since_poweron(MyTime_GetRuntimeSincePoweron());
    //同步开机原因
    ESP_LOGI("My_IotBox_TimerTask", "###---synchronize 'poweron_reason");
    MyConfig_Modify_factory_poweron_reason(MyEspChipInfoMng_GetResetReason());
    

    vTaskDelay(5000 / portTICK_PERIOD_MS);


    //获取所有配置
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();

    int timesync_err_count = 0;
    do
    {
        ESP_LOGI("My_IotBox_TimerTask", "###---MyWHGM5_timesync...");
        if(MyWHGM5_timesync()==0)
        {
            break;
        }
        timesync_err_count++;
        vTaskDelay(((timesync_err_count%3)+1)*5000 / portTICK_PERIOD_MS);
    }while(timesync_err_count<3);


    //注册
    uint8_t first_register_succeed = 0;
    /***未成功注册将无法到达这里***/


    //登录
    
    /***未成功登录将无法到达这里***/


    //对时
    
    /***未成功对时将无法到达这里***/


    if(first_register_succeed)
    {
        
    }


    //检查OTA
    my_iotbox_info.ota_check_after_poweron = 0;
    my_iotbox_info.ota_checked_upgrade_time = 0;
    my_iotbox_info.ota_check_err_count = 0;
    my_iotbox_info.next_check_ota_time = 0;
    if(My_IotBox_Routine_DevOTACheck(0, 1, 10)==0)
    {
        my_iotbox_info.next_check_ota_time = 0;
    }
    else
    {
        my_iotbox_info.ota_check_err_count++;
        my_iotbox_info.next_check_ota_time = MyTime_GetTime() + ((1*60)*my_iotbox_info.ota_check_err_count);
    }

    //最后一次采集照片的时间设置为0，以便稍后立即采集一张图片
    my_iotbox_info.last_takepicture_time = 0;

    #if DEBUG_PRINT_LEVEL_my_iot_box_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\n\n\n\n\n\n\n");
    ESP_LOGI("My_IotBox_TimerTask()", " == ^_^ ^_^ == enter work loop == ^_^ ^_^ == ");
    printf("\n\n\n\n\n\n\n\n");
    #endif

    my_iotbox_info.last_workmode = p_my_conf->workmode.conf.workmode;
    int channeldoor_last_workmode = -1;
    int runtimes_1 = 0;
    bool first_cclk_sync_time = true;
    bool channeldoor_mode2_work_exe_lock = false;
    bool channeldoor_mode2_holiday_exe_lock = false;
    for(;;)
    {
        runtimes++;
        runtimes_1++;
        vTaskDelay(1000 / portTICK_PERIOD_MS);

        //
        if(runtimes%60==0)
        {
            #if DEBUG_PRINT_LEVEL_my_iot_box_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
            My_ASLdpu_IotBoxInfo_DebugPrint();
            #endif
        }

        //---------------------------------------------------------------------------------------------------

        //检查供电，任何模式都需要做的工作
        if(runtimes%5==0)
        {
            if(p_my_conf->power.poweroff_alarm_enable_state)
            {
                if(!GetPower())
                {
                    ESP_LOGW("My_IotBox_TimerTask()", "external power off");
                    if(my_iotbox_info.check_poweroff_lock)
                    {
                        my_iotbox_info.check_poweroff_lock = 0;
                        my_iotbox_info.check_poweron_lock = 1;

                        MyConfig_Modify_ExternalPowerstate(0);

                        if(p_my_conf->power.shutdown_after_poweroffAlarm)
                        {
                            

                            ESP_LOGW("My_IotBox_TimerTask()", "shutdown");
                            if(my_iotbox_info.login_state)
                            {
                                vTaskDelay(5000 / portTICK_PERIOD_MS);

                                
                            }
                            
                            vTaskDelay(1000 / portTICK_PERIOD_MS);
                            
                            vTaskDelay(5000 / portTICK_PERIOD_MS);
                            MyPowerMng_MyWHGM5_PowerOFF();
                            MyPowerMng_MobilePower_OFF();
                            BAKPWR_OFF();
                            My_esp_restart();
                            if(!GetPower())
                            {
                                ESP_LOGW("My_IotBox_TimerTask()", "shutdown, switch to workmode_energy_saving");
                                my_iotbox_info.last_workmode = MyWorkmode_GetCurrentWorkmode();
                                MyConfig_Modify_Workmode_mode(workmode_energy_saving, 0);
                                // MyWorkmode_ReLoad_Conf(&p_my_conf->workmode.conf);
                                my_iotbox_info.last_enter_energy_mode_time = MyTime_GetTime();
                            }
                        }
                    }
                }
                else
                {
                    if(my_iotbox_info.check_poweron_lock)//正常外部上电导致的开机后，不会进入这里，外部掉电，系统没重启过，外部再次上电，才会进入这里
                    {
                        my_iotbox_info.check_poweroff_lock = 1;
                        my_iotbox_info.check_poweron_lock = 0;
                        MyConfig_Modify_ExternalPowerstate(1);
                        ESP_LOGW("My_IotBox_TimerTask()", "external power on");
                        if(my_iotbox_info.login_state)
                        {
                            
                        }

                        MyConfig_Modify_Workmode_mode(my_iotbox_info.last_workmode, 0);
                        // MyWorkmode_ReLoad_Conf(&p_my_conf->workmode.conf);
                    }
                    else
                    {
                        my_iotbox_info.last_workmode = p_my_conf->workmode.conf.workmode;
                    }
                }
            }
            else
            {
                ESP_LOGW("My_IotBox_TimerTask()", "shutdown, about to poweroff");
                vTaskDelay(5000 / portTICK_PERIOD_MS);
                MyPowerMng_MyWHGM5_PowerOFF();
                MyPowerMng_MobilePower_OFF();
                BAKPWR_OFF();
                My_esp_restart();
                if(!GetPower())
                {
                    ESP_LOGW("My_IotBox_TimerTask()", "shutdown, switch to workmode_energy_saving");
                    my_iotbox_info.last_workmode = MyWorkmode_GetCurrentWorkmode();
                    MyConfig_Modify_Workmode_mode(workmode_energy_saving, 0);
                    // MyWorkmode_ReLoad_Conf(&p_my_conf->workmode.conf);
                    my_iotbox_info.last_enter_energy_mode_time = MyTime_GetTime();
                }
            }
        }

        //除节能模式外，肯定要做的工作
        if((p_my_conf->workmode.conf.workmode!=workmode_energy_saving))
        {
            //更新开机时间到配置表
            if(runtimes%5==0)
            {
                MyConfig_Modify_factory_runtime_since_poweron(MyTime_GetRuntimeSincePoweron());
            }

            //更新蓝牙状态到配置表
            if(runtimes%5==0)
            {
                // #if MY_BLE_FUNC
                // MyConfig_Modify_BLE_CurState(MyBle_GetInitState());
                // #endif
            }

            if(GetPower())
            {
                if(my_iotbox_info.login_state)
                {
                    //对时检查
                    if(runtimes%5==0)
                    {
                    My_IotBoxRoutine_DevTimeSync_Check(MY_IOTBOX_TIMESYNC_VALIDTIME);
                }
                    if(runtimes%20==0)
                    {
                        //外部上电才会检查OTA
                        if(GetPower())
                        {
                            //检查OTA
                            if(MyTime_GetTime() > my_iotbox_info.next_check_ota_time)
                            {
                                // int err = My_IotBox_Routine_DevOTACheck(0, 1, 10);
                                // if(err)
                                // {
                                //     my_iotbox_info.ota_check_err_count++;
                                //     my_iotbox_info.next_check_ota_time = MyTime_GetTime() + ((1*60)*my_iotbox_info.ota_check_err_count);
                                // }
                                // else
                                // {
                                //     my_iotbox_info.ota_check_err_count = 0;
                                //     my_iotbox_info.next_check_ota_time = 0;
                                // }
                            }
                        } 
                    }
                }
            }

            if(runtimes%5==0)
            {
                
            }
        }

        if(My_blufi_Get_Connect_State()==true)
        {
            if(bts_get_lte_info_request==true)
            {
                bts_get_lte_info_request = false;
                My_IotBox_Refresh_LTE_INFO();
                runtimes_1 = 1;
            }
            if(runtimes_1%30==0)
            {
                My_IotBox_Refresh_LTE_INFO();
            }
            if(bts_isp_request==true)
            {
                bts_isp_request = false;
            }
            if(bts_check_ota_request==true)
            {
                bts_check_ota_request = false;
            }
            if(bts_debug_mode_request==true)
            {
                bts_debug_mode_request = false;
                // MyConfig_Modify_Debug_mode(bts_debug_mode, 1);
            }
        }
        
        //工作模式，除待机模式以及节能模式外，肯定要做的工作
        if((p_my_conf->workmode.conf.workmode!=workmode_standby)&&(p_my_conf->workmode.conf.workmode!=workmode_energy_saving))
        {
            //工作模式，检查是否在工作时段
            if(MyWorkmode_Check_WhetherInWorktime(&p_my_conf->workmode.conf))
            {
                //采集照片
                if(runtimes%5==0)
                {
                    ESP_LOGI("\nMy_IotBox_TimerTask()", "^_^ ^_^ working ^_^ ^_^\n");
                    {
                        if(p_my_conf->workmode.channeldoor_mode==2)
                        {
                            if(channeldoor_mode2_work_exe_lock==false)
                            {
                                channeldoor_mode2_work_exe_lock = true;
                                channeldoor_mode2_holiday_exe_lock = false;
                                MyConfig_Modify_Workmode_Relay1_mode(2, 0);
                                My_Relay_Off_Supper();
                                My_Relay_Unlock();
                            }
                        }
                    }
                }
            }
            else//工作模式，但不在工作时段
            {
                if(runtimes%5==0)
                {
                    ESP_LOGI("\nMy_IotBox_TimerTask()", " ^_^ ^_^ It's a holiday, ha-ha ^_^ ^_^ \n");
                    if(p_my_conf->workmode.channeldoor_mode==2)
                    {
                        if(channeldoor_mode2_holiday_exe_lock==false)
                        {
                            channeldoor_mode2_holiday_exe_lock = true;
                            channeldoor_mode2_work_exe_lock = false;
                            MyConfig_Modify_Workmode_Relay1_mode(3, 0);
                            My_Relay_On_Supper();
                            My_Relay_Lock();
                        }
                    }
                }
            }
            //
        }
        else
        {
            if(runtimes%5==0)
            {
                ESP_LOGI("\nMy_IotBox_TimerTask()", " ^_^ ^_^ ... Thinking about life ... ^_^ ^_^ \n");
            }

            if((p_my_conf->workmode.conf.workmode==workmode_standby))
            {

            }

            if((p_my_conf->workmode.conf.workmode==workmode_energy_saving))
            {
                if(MyTime_GetTime() - my_iotbox_info.last_enter_energy_mode_time >= 60*60)
                {
                    if(!GetPower())
                    {
                        ESP_LOGI("My_IotBox_TimerTask()", "poweroff");

                        ESP_LOGI("My_IotBox_TimerTask()", "BAKPWR_OFF()");
                        BAKPWR_OFF();
                        vTaskDelay(1000 / portTICK_PERIOD_MS);

                        //防止关机时候恰好外部上电，不应该执行到这里
                        BAKPWR_ON();
                        ESP_LOGI("My_IotBox_TimerTask(external poweron while BAKPWR_OFF())", "My_esp_restart()");
                        My_esp_restart();
                    }
                }
            }
        }

        if(runtimes%10==0)
        {
            int wakeup_reason = MYLOWPOWER_WAKEUP_TYPE_NO_SLEEP;
            ESP_LOGI("My_IotBox_TimerTask()", " - _ -  - _ -  I want to sleep - _ -  - _ - \n");
            //工作模式检查，可能进入light_sleep模式
            wakeup_reason = MyWorkmode_Check();
            //来到这里说明：
            // 1. 没有进入睡眠
            // 2. 已经从睡眠模式醒来
            // 此时蓝牙、LTE等的状态已经恢复，登录状态也已经恢复，且已经
            if(wakeup_reason!=MYLOWPOWER_WAKEUP_TYPE_PAUSE_MOMENT)
            {
                ESP_LOGI("My_IotBox_TimerTask()", " ^_^ ^_^ I came back ^_^ ^_^ \n");
            }
            else
            {
                ESP_LOGI("My_IotBox_TimerTask()", " - _ -  ^ _ -  I'm sleepwalking  - _ ^  - _ - \n");
            }
        }
    }
}

uint8_t My_IotBox_DataAnalyse(char* data)
{
    return 0;
}

TaskHandle_t My_IotBox_EventRcvTaskHandle = NULL;
void My_IotBox_EventRcvTask(void* param)
{
    my_asl_dpu_ctrlevent_queue_t my_asl_dpu_ctrlevent;

    for(;;)
    {
        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }
}

void MyLowPower_Init_BeforeSleep(void)
{
    ESP_LOGI("MyLowPower_Init_BeforeSleep()", "callback MyLowPower_Init_BeforeSleep");

    my_iotbox_info.login_state = 0;
}

void MyLowPower_Init_AfterWakeup(int wakeup_reason)
{
    ESP_LOGI("MyLowPower_Init_BeforeSleep()", "callback MyLowPower_Init_AfterWakeup");

    uint8_t external_pwr_supply = 0;
    
    //尝试从外部获取电力
    external_pwr_supply = MyPowerMng_SwitchToExternalPWR();

    if(external_pwr_supply)
    {
        
    }
    else
    {
        if(MyWorkmode_GetCurrentWorkmode()==workmode_energy_saving)
        {

        }
    }
    
    //-----------------------------------------
    
}

typedef struct
{
    uint8_t msg_type;
    uint16_t asl_retcode;
}myiotbox_daemon_rcv_queue_item_t;
TaskHandle_t My_IotBox_IotdaemonTaskHandle = NULL;

int My_IotBox_DaemonHandler(myiotbox_daemon_rcv_queue_item_t* data)
{
    ESP_LOGI("My_IotBox_DaemonHandler()", "----");

    
    return 0;
}

QueueHandle_t my_iotbox_iotdaemon_rcv_queue = NULL;
void My_IotBox_IotdaemonTask(void* param)
{
    int queue_rcv_ret = pdTRUE;
    myiotbox_daemon_rcv_queue_item_t myiotbox_iotdaemon_rcv_queue_item;

    for(;;)
    {
        memset(&myiotbox_iotdaemon_rcv_queue_item, 0, sizeof(myiotbox_iotdaemon_rcv_queue_item));
        queue_rcv_ret = xQueueReceive(my_iotbox_iotdaemon_rcv_queue, &myiotbox_iotdaemon_rcv_queue_item, (10*60*1000) / portTICK_PERIOD_MS);
        if(queue_rcv_ret==pdTRUE)
        {
            My_IotBox_DaemonHandler(&myiotbox_iotdaemon_rcv_queue_item);
        }
        else
        {
            ESP_LOGW("My_IotBox_IotdaemonTask()", "queue_rcv_ret==pdTRUE is false!!!");
        }
    }
}

static mbedtls_aes_context aes_ctx;
int My_IotBox_Init(void)
{
    //加载所有配置数据
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();

    ESP_LOGI("My_IotBox_Init()", "frame vercode=%d", MY_IOTBOX_devPI_factory_ota_vercode_default);
    ESP_LOGI("My_IotBox_Init()", "frame vername=%s", MY_IOTBOX_devPI_factory_ota_vername_default);
    ESP_LOGI("My_IotBox_Init()", "frame filename=%s", MY_IOTBOX_devPI_factory_ota_filename_default);

    ESP_LOGI("My_IotBox_Init()", "nvs vercode=%d", p_my_conf->factory.my_ota_info_local.vercode);
    ESP_LOGI("My_IotBox_Init()", "nvs vername=%s", p_my_conf->factory.my_ota_info_local.vername);
    ESP_LOGI("My_IotBox_Init()", "nvs filename=%s", p_my_conf->factory.my_ota_info_local.filename);

    ESP_LOGI("My_IotBox_Init()", "check vercode frame=%d:nvs=%d", MY_IOTBOX_devPI_factory_ota_vercode_default, p_my_conf->factory.my_ota_info_local.vercode);

    if(p_my_conf->factory.my_ota_info_local.vercode!=MY_IOTBOX_devPI_factory_ota_vercode_default)
    {
        ESP_LOGI("My_IotBox_Init()", "sync vercode info...");
        ESP_LOGI("My_IotBox_Init()", "modify local vercode to %d", MY_IOTBOX_devPI_factory_ota_vercode_default);
        MyConfig_Modify_factory_verCode_local(MY_IOTBOX_devPI_factory_ota_vercode_default, 1);
        ESP_LOGI("My_IotBox_Init()", "modify local vername to %s", MY_IOTBOX_devPI_factory_ota_vername_default);
        MyConfig_Modify_factory_vername_local(MY_IOTBOX_devPI_factory_ota_vername_default, \
                                                strlen(MY_IOTBOX_devPI_factory_ota_vername_default), 1);
        ESP_LOGI("My_IotBox_Init()", "modify local filename to %s", MY_IOTBOX_devPI_factory_ota_filename_default);
        MyConfig_Modify_factory_verfilename_local(MY_IOTBOX_devPI_factory_ota_filename_default, \
                                                strlen(MY_IOTBOX_devPI_factory_ota_filename_default), 1);
        MyConfig_ResetAllConf_To_Default();

        p_my_conf = MyConfig_LoadFromNVS();
        if(p_my_conf->factory.my_ota_info_local.vercode!=MY_IOTBOX_devPI_factory_ota_vercode_default)
        {
            ESP_LOGW("My_IotBox_Init()", "sync vercode info failed, ignore!!!");
        }
        else
        {
            ESP_LOGI("My_IotBox_Init()", "sync vercode info succeed");
            My_esp_restart();
        }
    }

    MyIotbox_linkbusy_MutexSemaphore = xSemaphoreCreateMutex();
    if(MyIotbox_linkbusy_MutexSemaphore==NULL)
    {
        ESP_LOGE("My_IotBox_Init()", "MyIotbox_linkbusy_MutexSemaphore is NULL!!!");
        vTaskDelay(3000 / portTICK_PERIOD_MS);
        My_esp_restart();
    }

    //--------------------------------------------------------
    BaseType_t ret = pdPASS;
{
    #if My_IotBox_TimerTask_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    p_task_stack = calloc(1, My_IotBox_TimerTask_task_stack_size*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
			if(xTaskCreateStaticPinnedToCore(My_IotBox_TimerTask, "My_IotBox_TimerTask", My_IotBox_TimerTask_task_stack_size, NULL, My_IotBox_TimerTask_priority, p_task_stack, p_task_data, My_IotBox_TimerTask_COREID)!=p_task_data)
			{
				ESP_LOGE("MyEspChipInfoMng_Init()", "creat My_IotBox_TimerTask use xTaskCreateStaticPinnedToCore() error\n");
                return 1;
			}
        }else return 1;
    }else return 1;
	#elif My_IotBox_TimerTask_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
	ret = xTaskCreatePinnedToCore(My_IotBox_TimerTask, "My_IotBox_TimerTask", My_IotBox_TimerTask_task_stack_size, NULL, My_IotBox_TimerTask_priority, &My_IotBox_TimerTaskHandle, My_IotBox_TimerTask_COREID);
	if(ret!=pdPASS)
	{
		ESP_LOGE("MyEspChipInfoMng_Init()", "creat My_IotBox_TimerTask use xTaskCreatePinnedToCore() error=%d\n", ret);
        return 1;
	}
	#endif
}

{
    #if My_IotBox_EventRcvTask_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    p_task_stack = calloc(1, My_IotBox_EventRcvTask_task_stack_size*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
			if(xTaskCreateStaticPinnedToCore(My_IotBox_EventRcvTask, "My_IotBox_EventRcvTask", My_IotBox_EventRcvTask_task_stack_size, NULL, My_IotBox_EventRcvTask_priority, p_task_stack, p_task_data, My_IotBox_EventRcvTask_COREID)!=p_task_data)
			{
				ESP_LOGE("MyEspChipInfoMng_Init()", "creat My_IotBox_EventRcvTask use xTaskCreateStaticPinnedToCore() error\n");
                return 1;
			}
        }else return 1;
    }else return 1;
	#elif My_IotBox_EventRcvTask_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
	ret = xTaskCreatePinnedToCore(My_IotBox_EventRcvTask, "My_IotBox_EventRcvTask", My_IotBox_EventRcvTask_task_stack_size, NULL, My_IotBox_EventRcvTask_priority, &My_IotBox_EventRcvTaskHandle, My_IotBox_EventRcvTask_COREID);
	if(ret!=pdPASS)
	{
		ESP_LOGE("MyEspChipInfoMng_Init()", "creat My_IotBox_EventRcvTask use xTaskCreatePinnedToCore() error=%d\n", ret);
        return 1;
	}
	#endif
}

    my_iotbox_iotdaemon_rcv_queue = xQueueGenericCreate(4, sizeof(myiotbox_daemon_rcv_queue_item_t), 1);
    if(my_iotbox_iotdaemon_rcv_queue==NULL)ESP_LOGE("My_IotBox_Init()", "my_iotbox_iotdaemon_rcv_queue=NULL\n");

{
    #if My_IotBox_IotdaemonTask_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    p_task_stack = calloc(1, My_IotBox_IotdaemonTask_task_stack_size*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
			if(xTaskCreateStaticPinnedToCore(My_IotBox_IotdaemonTask, "My_IotBox_IotdaemonTask", My_IotBox_IotdaemonTask_task_stack_size, NULL, My_IotBox_IotdaemonTask_priority, p_task_stack, p_task_data, My_IotBox_IotdaemonTask_COREID)!=p_task_data)
			{
				ESP_LOGE("MyEspChipInfoMng_Init()", "creat My_IotBox_IotdaemonTask use xTaskCreateStaticPinnedToCore() error\n");
                return 1;
			}
        }else return 1;
    }else return 1;
	#elif My_IotBox_IotdaemonTask_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
	ret = xTaskCreatePinnedToCore(My_IotBox_IotdaemonTask, "My_IotBox_IotdaemonTask", My_IotBox_IotdaemonTask_task_stack_size, NULL, My_IotBox_IotdaemonTask_priority, &My_IotBox_IotdaemonTaskHandle, My_IotBox_IotdaemonTask_COREID);
	if(ret!=pdPASS)
	{
		ESP_LOGE("MyEspChipInfoMng_Init()", "creat My_IotBox_IotdaemonTask use xTaskCreatePinnedToCore() error=%d\n", ret);
        return 1;
	}
	#endif
}

    return 0;
}

#endif