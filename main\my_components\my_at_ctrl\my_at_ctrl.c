#include "my_at_ctrl.h"


#if AT_CTRL_FUNC
#include "my_uart.h"
#include "my_gpio.h"
#include "my_debug.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"
#include "freertos/semphr.h"
#include "esp_system.h"
#include "esp_log.h"
#include "driver/uart.h"
#include "driver/gpio.h"


#if defined MY_DEBUG_PRINT_FUNC
#define DEBUG_PRINT_LEVEL_my_at_ctrl_PRINT   MY_DEBUG_PRINT_LEVEL_4_BUGFIX
#else
#define DEBUG_PRINT_LEVEL_my_at_ctrl_PRINT   DEBUG_PRINT_LEVEL_0
#endif


int AT_Rcv(char* saving, int recv_timeout)
{
    int rxbytes = 0;

    if(recv_timeout<portTICK_PERIOD_MS)
    {
        recv_timeout = portTICK_PERIOD_MS;

    }
    rxbytes = uart_read_bytes(LTE_UART_NUM, (void*)saving, LTE_CMD_LENGTH_LIMIT, recv_timeout / portTICK_PERIOD_MS);

    if(rxbytes>0)
    {
        #if DEBUG_PRINT_LEVEL_my_at_ctrl_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
        printf("AT_Rcv:%s\n", saving);
        #endif
        uart_flush(LTE_UART_NUM);
        return 1;
    }
    return 0;
}

int AT_SendSingleCmd_And_Rcv(char *cmd, char* saving, int recv_timeout)
{
    uart_flush(LTE_UART_NUM);
    if(UartSendData(cmd, strlen(cmd)))
    {
        if(AT_Rcv(saving, recv_timeout))
        {
            return 0;
        }
        return 2;
    }
    return 1;
}

int AT_SendSingleCmd_And_Wait_Endsymbol(char *cmd, char *endsymbol, int recv_timeout)
{
    char* rcv_buf = (char*)calloc(1, LTE_CMD_LENGTH_LIMIT);
    if(rcv_buf==NULL)
    {
        return 3;
    }

    if(!AT_SendSingleCmd_And_Rcv(cmd, rcv_buf, recv_timeout))
    {
        if(strstr(rcv_buf, endsymbol)!=NULL)
        {
            free(rcv_buf);
            return 0;
        }
        free(rcv_buf);
        return 2;
    }
    free(rcv_buf);
    return 1;
}

static void AT_Config_Routine_Error(void)
{
    ESP_LOGE("AT_Config_Routine_Error", "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\n");
    #if LTE_TYPE==LTE_WH_GM5
    // vTaskDelay(2000/portTICK_PERIOD_MS);
    // MyGPIO_SET(LTE_RESET_GPIO_PIN, 0);
    // vTaskDelay(3000/portTICK_PERIOD_MS);
    // MyGPIO_SET(LTE_RESET_GPIO_PIN, 1);
    // vTaskDelay(3000/portTICK_PERIOD_MS);
    //Lte_PowerOff();
    //vTaskDelay(3000/portTICK_PERIOD_MS);
    esp_restart();
    #elif LTE_TYPE==LTE_724UG
    vTaskDelay(2000/portTICK_PERIOD_MS);
    MyGPIO_SET(LTE_RESET_GPIO_PIN, 0);
    vTaskDelay(3000/portTICK_PERIOD_MS);
    MyGPIO_SET(LTE_RESET_GPIO_PIN, 1);
    vTaskDelay(3000/portTICK_PERIOD_MS);
    //Lte_PowerOff();
    //vTaskDelay(3000/portTICK_PERIOD_MS);
    esp_restart();
    #endif
}

int AT_Config_Routine_error_count = 0;
int AT_Routine(AT_Config_Routine_t p[])
{
    uint8_t index_progress = 0;
    int ret = 0;
    int config_result = 0;

    while(1)
    {
        if(index_progress==0xff)
        {
            break;
        }
        if(p[index_progress].pridelay>0)
        {
            vTaskDelay(p[index_progress].pridelay/portTICK_PERIOD_MS);
        }
        if(p[index_progress].keystrexist)
        {
            #if DEBUG_PRINT_LEVEL_my_at_ctrl_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
            printf("SNED------>:%s\n", p[index_progress].cmdstr);
            #endif
            ret = AT_SendSingleCmd_And_Wait_Endsymbol(p[index_progress].cmdstr, p[index_progress].keystr, p[index_progress].timeout_count);
            if(ret==0)
            {
                p[index_progress].failedcount=0;
                //ESP_LOGI("AT_Config_Routine()", "index_progress=%d, succeed, client ack correct\n", index_progress);
                if(p[index_progress].lagdelay>0)
                {
                    //ESP_LOGI("AT_Config_Routine()", "lagdelay=%dms\n", p[index_progress].lagdelay);
                    if(p[index_progress].lagdelay>portTICK_PERIOD_MS)
                    {
                        vTaskDelay(p[index_progress].lagdelay/portTICK_PERIOD_MS);
                        
                    }
                    else
                    {
                        vTaskDelay(1);
                    }
                }
                index_progress = p[index_progress].sucnext;
            }
            else
            {
                if(++p[index_progress].failedcount>p[index_progress].failedlimit)
                {
                    if(++AT_Config_Routine_error_count>3)
                    {
                        AT_Config_Routine_Error();
                    }
                    return -1;
                }
                //ESP_LOGI("AT_Config_Routine()", "index_progress=%d, failed, ret=%d\n", index_progress, ret);
                if(p[index_progress].faillagdelay>0)
                {
                    //ESP_LOGI("AT_Config_Routine()", "faillagdelay=%dms\n", p[index_progress].faillagdelay);
                    if(p[index_progress].faillagdelay>portTICK_PERIOD_MS)
                    {
                        vTaskDelay(p[index_progress].faillagdelay/portTICK_PERIOD_MS);
                        
                    }
                    else
                    {
                        vTaskDelay(1);
                    }
                }
                index_progress = p[index_progress].failnext;
                config_result++;
            }
        }
        else
        {
            #if DEBUG_PRINT_LEVEL_my_at_ctrl_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
            printf("SNED------>:%s\n", p[index_progress].cmdstr);
            #endif
            UartSendData(p[index_progress].cmdstr, strlen(p[index_progress].cmdstr));

            p[index_progress].failedcount=0;
            //ESP_LOGI("AT_Config_Routine()", "index_progress=%d, succeed, no answer required\n", index_progress);
            if(p[index_progress].lagdelay>0)
            {
                //ESP_LOGI("AT_Config_Routine()", "lagdelay=%dms\n", p[index_progress].lagdelay);
                vTaskDelay(p[index_progress].lagdelay/portTICK_PERIOD_MS);
            }
            index_progress = p[index_progress].sucnext;
        }
    }
    //ESP_LOGI("AT_Config_Routine()", "finished\n");

    index_progress = 0;

    return config_result;
}

#endif