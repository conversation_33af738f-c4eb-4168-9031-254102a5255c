#ifndef MYTEST_RADAR_H
#define MYTEST_RADAR_H

#include <lwip/netdb.h>

#include "my_radar.h"

#if MY_RADAR_FUNC


#define MY_TEST_RADAR_QUEUE_LEN 8
#define MY_TEST_RADAR_PACK_SIZE 1024
#define radar_report_internal (60)
typedef struct
{
    uint8_t id;
	uint8_t valid;
	uint64_t time_stamp;
    uint32_t len;
	char* data;                 //应用服务层数据
}my_test_radar_queue_t;

my_test_radar_queue_t* my_test_radar_rcv_queue_array[MY_TEST_RADAR_QUEUE_LEN];

typedef struct
{
    uint16_t object_number;
    int16_t object_x;
    int16_t object_y;
    int16_t object_speed;
    uint16_t object_distancce_resolution;
}my_test_radar_object_info_t;


typedef struct
{
    int16_t x;
    int16_t y;
    int16_t speed;
    int16_t distance_resolution;
}ld2450_object_t;

typedef struct
{
    char head[4];
    ld2450_object_t objects[3];
    char tail[2];
}ld2450_data_frame_t;

typedef struct
{
    int16_t number;
    int16_t x;
    int16_t y;
    int16_t speed;
}single_object_t;

#define radar_data_regular_report_buf_default_size (64)
#define radar_data_regular_report_buf_size (3200)
#define radar_data_regular_report_buf_explore_size (36)

int MyTest_Radar_Init(void);

uint8_t My_Test_Radar_Send(char* data, uint32_t len, uint32_t timeout);

void radar_refresh_default_regular_data(void);

#endif


#endif