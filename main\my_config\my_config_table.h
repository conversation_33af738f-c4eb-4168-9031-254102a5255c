#ifndef MY_CONFIG_TABLE_H
#define MY_CONFIG_TABLE_H

#include <lwip/netdb.h>
#include "my_ble.h"




#define save_to_nvs   1
#define not_save_to_nvs   (!save_to_nvs)


typedef struct
{
    uint32_t item_id;
    char* item_name;

    int saving_to_nvs;
    char* form_name;
    char* key_name;

    uint8_t data_type;
    uint32_t data_len;
    uint64_t data_uint64;
    uint8_t p_data_type;
    void* p_data;
}my_config_table_t;

#define DATA_TYPE_BASIC     0
#define DATA_TYPE_POINTER   1
#define DATA_POINTER_TYPE_QUOTE     0
#define DATA_POINTER_TYPE_ENTITY    1

typedef enum
{
    myconftable_type_asl=0,
    myconftable_type_tsfc,
    myconftable_type_net,
    myconftable_type_workmode,
    // #if MY_BLE_FUNC
    myconftable_type_ble,
    // #endif
    myconftable_type_power,
    myconftable_type_factory,
    myconftable_type_wifi,
    myconftable_type_lte,
    myconftable_type_gnss,

    myconftable_type_max
}myconftable_conftable_type_t;

extern my_config_table_t my_conf_table_asl_default[6];
extern my_config_table_t my_conf_table_tsfc_default[4];
extern my_config_table_t my_conf_table_net_default[5];
extern my_config_table_t my_conf_table_workmode_default[29];
// #if MY_BLE_FUNC
extern my_config_table_t my_conf_table_ble_default[11];
// #endif
extern my_config_table_t my_conf_table_power_default[3];
extern my_config_table_t my_conf_table_factory_default[16];
extern my_config_table_t my_conf_table_wifi_default[8];
extern my_config_table_t my_conf_table_lte_default[1];
extern my_config_table_t my_conf_table_gnss_default[4];



#endif