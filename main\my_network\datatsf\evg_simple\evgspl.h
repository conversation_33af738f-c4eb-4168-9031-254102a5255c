#ifndef MY_EVGSPL_H
#define MY_EVGSPL_H

#include <stdbool.h>
#include <stdint.h>

/**
 * @brief Initialize the EVGSPL protocol
 *
 * This function initializes the protocol and creates the necessary tasks
 * for sending and receiving data.
 */
void evgspl_init(void);

/**
 * @brief Send registration message to the server
 *
 * @return true if successful, false otherwise
 */
bool evgspl_send_register(void);

/**
 * @brief Send heartbeat message to the server
 *
 * @return true if successful, false otherwise
 */
bool evgspl_send_heartbeat(void);

/**
 * @brief Send an event report to the server
 *
 * @param event_type Type of event (e.g., "alarm", "status_change", etc.)
 * @param event_data Additional event data as a JSON string
 * @return true if successful, false otherwise
 */
bool evgspl_send_event_report(const char* event_type, const char* event_data);

/**
 * @brief Send parameter report to the server
 *
 * @param param_json JSON string containing parameters to report
 * @return true if successful, false otherwise
 */
bool evgspl_send_param_report(const char* param_json);

#endif /* MY_EVGSPL_H */