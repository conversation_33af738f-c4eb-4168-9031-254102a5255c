#include "my_monitor.h"

#if MY_MONITOR_FUNC
#include "my_config.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "freertos/queue.h"
#include "esp_system.h"
#include "esp_log.h"
#include "driver/uart.h"
#include "string.h"
#include "driver/gpio.h"




static TaskHandle_t MonitorTask_Handle = NULL;



#if MONITOR_USART_FUNC
#define MONITOR_USART_SEND_LOG_NAME "MONITOR_USART_SEND: "
static int sendData(const char* logName, const char* data, uint16_t len);
static TaskHandle_t monitor_rx_task_Handle = NULL;
#define TXD_PIN (GPIO_NUM_19)
#define RXD_PIN (GPIO_NUM_21)
static const int RX_BUF_SIZE = 200;

static void usart_init(void) {
    const uart_config_t uart_config = {
        .baud_rate = 115200,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE
    };
    uart_param_config(UART_NUM_0, &uart_config);
    uart_set_pin(UART_NUM_0, TXD_PIN, RXD_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    // We won't use a buffer for sending data.
    uart_driver_install(UART_NUM_0, RX_BUF_SIZE * 2, 0, 0, NULL, 0);
}

static void monitor_rx_task(void* arg)
{
    static const char *RX_TASK_TAG = "RX_TASK";
    esp_log_level_set(RX_TASK_TAG, ESP_LOG_INFO);
    uint8_t* data = (uint8_t*) malloc(RX_BUF_SIZE+1);

    while (1)
    {
        const int rxBytes = uart_read_bytes(UART_NUM_0, data, RX_BUF_SIZE, 300 / portTICK_RATE_MS);
        if (rxBytes > 0)
        {
            data[rxBytes] = 0;
            
            if(MatchString((char*)data, "est"))
            {
                LedFastFlash(1000, 4);
                sendData(MONITOR_USART_SEND_LOG_NAME, "hello\r\n", strlen("hello\r\n"));
            }
        }
    }
    free(data);
}

static int sendData(const char* logName, const char* data, uint16_t len)
{
    const int txBytes = uart_write_bytes(UART_NUM_0, data, len);
    #if MONITOR_USART_PROMPT
    ESP_LOGI(logName, "Wrote %d bytes", txBytes);
    #endif
    return txBytes;
}
#endif

void MonitorTask( void *pvParameters )
{
    char *pxTaskStatusArray;
    unsigned int notifyValue;
    unsigned int freemem;
    
    for(;;)
    {
        xTaskNotifyWait(0x0, 0xffffffff, &notifyValue, portMAX_DELAY);
        if(notifyValue==MONITOR_TYPE_TASK_LIST_DETAIL)
        {
            pxTaskStatusArray = pvPortMalloc( uxTaskGetNumberOfTasks() * sizeof( TaskStatus_t ) );
            vTaskList(pxTaskStatusArray);
            printf("MonitorTask(): prompt------------------------------------------------------");
            printf("MonitorTask(): \n\n%s\n", pxTaskStatusArray);
            vPortFree(pxTaskStatusArray);
            printf("MonitorTask(): MonitorTask stack high water mark: remain %d word\n", uxTaskGetStackHighWaterMark(NULL));
            freemem=xPortGetFreeHeapSize();
            printf("MonitorTask(): freemem:%d\n",freemem);
            printf("\nMonitorTask(): prompt end--------------------------------------------------\n\n");
        }
    }
}

void IndvMonitorService(uint8_t monitor_type)
{
    if(MonitorTask_Handle!=NULL)
    {
        xTaskNotify(MonitorTask_Handle, monitor_type, eSetValueWithOverwrite);
    }
    
}

void My_Monitor_Init(void)
{
    #if MONITOR_USART_FUNC
    usart_init();
    xTaskCreate(monitor_rx_task, "monitor_rx_task", 8192, NULL, 5, &monitor_rx_task_Handle);
    #endif

    xTaskCreate(MonitorTask,            "MonitorTask",          3072,     NULL, 3, &MonitorTask_Handle);
}
#endif
