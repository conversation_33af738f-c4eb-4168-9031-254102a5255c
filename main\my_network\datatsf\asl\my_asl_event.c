#include "my_asl_event.h"
#include "my_debug.h"

#include <esp_log.h>

#include <stdio.h>
#include <string.h>
#include <stdlib.h>


#if defined MY_DEBUG_PRINT_FUNC
#define DEBUG_PRINT_LEVEL_my_asl_event_PRINT   MY_DEBUG_PRINT_LEVEL_0
#else
#define DEBUG_PRINT_LEVEL_my_asl_event_PRINT   DEBUG_PRINT_LEVEL_0
#endif


int MyAslEvent_Register(my_asl_event_list_t* p_list, int id, void* data, char* service_name)
{
    if(p_list==NULL)return -1;
    my_asl_event_list_item_t* newitem = NULL;
    newitem = (my_asl_event_list_item_t*)calloc(1, sizeof(my_asl_event_list_item_t));
    int service_name_len = 0;
    my_asl_event_list_item_t* item_index = p_list->first_item;

    if(service_name!=NULL)
    {
        // ESP_LOGI("MyAslEvent_Register()", "add item: id=%d, service_name=%s", id, service_name);
    }
    else
    {
        // ESP_LOGI("MyAslEvent_Register()", "add item: id=%d, service_name=NULL", id);
    }
    
    if(p_list->list_item_num>0)
    {
        do
        {
            if(item_index->id==id)
            {
                break;
            }
            if(item_index->next_item!=NULL)
            {
                item_index = item_index->next_item;
            }
        } while (item_index!=p_list->end_item);
        if(item_index->id==id)
        {
            if(newitem!=NULL)free(newitem);
            // ESP_LOGI("MyAslEvent_Register()", "add item failed,1(already exists)\n");
            return 1;
        }
    }

    if(newitem!=NULL)
    {
        if(service_name!=NULL)
        {
            service_name_len = strlen(service_name);
            if(service_name_len>31)
            {
                service_name_len = 31;
            }
            memcpy(newitem->item_name, service_name, service_name_len);
        }

        newitem->id = id;
        //------------------------------------
        newitem->data = data;
        //------------------------------------

        newitem->next_item = NULL;
        if(p_list->list_item_num==0)
        {
            p_list->first_item = newitem;
            newitem->pri_item = NULL;
            newitem->next_item = NULL;
        }
        else
        {
            p_list->end_item->next_item = newitem;
            newitem->pri_item = p_list->end_item;
            newitem->next_item = NULL;
        }
        p_list->end_item = newitem;
        p_list->list_item_num++;
        // ESP_LOGI("MyAslEvent_Register()", "add item succeed, p_list->list_item_num=%d\n", p_list->list_item_num);
        return 0;
    }
    else
    {
        // ESP_LOGI("MyAslEvent_Register()", "add item failed,3\n");
        return 1;
    }
    return 0;
}

int MyAslEvent_Delete(my_asl_event_list_t* p_list, int id)
{
    if(p_list==NULL)return -1;
    // ESP_LOGI("MyAslEvent_Delete()", "delete item from p_list(%p)", p_list);
    if(p_list->list_item_num==0)
    {
        // ESP_LOGI("MyAslEvent_Delete()", "error, p_list already empty\n");
        return 1;
    }

    my_asl_event_list_item_t* item_index = p_list->first_item;
    do
    {
        if(item_index->id==id)
        {
            break;
        }
        if(item_index->next_item!=NULL)
        {
            item_index = item_index->next_item;
        }
    } while (item_index!=p_list->end_item);
    if(item_index->id==id)
    {
        if(item_index->item_name!=NULL)
        {
            // ESP_LOGI("MyAslEvent_Delete()", "delete id=%d, service_name=%s", item_index->id, item_index->item_name);
        }
        else
        {
            // ESP_LOGI("MyAslEvent_Delete()", "delete id=%d, service_name=NULL", item_index->id);
        }
        if((item_index!=p_list->first_item)&&(item_index!=p_list->end_item))
        {
            item_index->pri_item->next_item = item_index->next_item;
            item_index->next_item->pri_item = item_index->pri_item;
        }
        else
        {
            if(item_index==p_list->first_item)
            {
                p_list->first_item = item_index->next_item;
            }
            if(item_index==p_list->end_item)
            {
                p_list->end_item = item_index->pri_item;
                if(p_list->end_item!=NULL)
                {
                    p_list->end_item->next_item = NULL;
                }
            }
        }
        
        free(item_index);
        if(p_list->list_item_num>0)
        {
            p_list->list_item_num--;
        }
        return 0;
    }
    else
    {
        // ESP_LOGI("MyAslEvent_Delete()", "the item to delete was not found\n");
        return 1;
    }
    return 0;
}

void* MyAslEvent_GetItem_Storage_ById(my_asl_event_list_t* p_list, int id)
{
    if(p_list==NULL)return NULL;
    // ESP_LOGI("MyAslEvent_GetItem_Storage_ById()", "find item(%d) from p_list(%p)", id, p_list);
    if(p_list->list_item_num==0)
    {
        // ESP_LOGI("MyAslEvent_GetItem_Storage_ById()", "error, p_list already empty\n");
        return NULL;
    }

    my_asl_event_list_item_t* item_index = p_list->first_item;
    do
    {
        if(item_index->id==id)
        {
            break;
        }
        if(item_index->next_item!=NULL)
        {
            item_index = item_index->next_item;
        }
    } while (item_index!=p_list->end_item);
    if(item_index->id==id)
    {
        if(item_index->item_name!=NULL)
        {
            // ESP_LOGI("MyAslEvent_GetItem_Storage_ById()", "find id=%d, service_name=%s", item_index->id, item_index->item_name);
        }
        else
        {
            // ESP_LOGI("MyAslEvent_GetItem_Storage_ById()", "find id=%d, service_name=NULL", item_index->id);
        }
        
        return item_index->data;
    }
    else
    {
        // ESP_LOGI("MyAslEvent_GetItem_Storage_ById()", "the item to find was not found\n");
        return NULL;
    }
    return NULL;
}

int MyAslEvent_PrintList(my_asl_event_list_t* p_list)
{
    if(p_list==NULL)return -1;
    if(p_list->list_item_num==0)
    {
        printf("\nMyAslEvent_PrintList():list already empty\n");
        return 1;
    }

    printf("\n\nMyAslEvent_PrintList---------------------------\n");
    printf("list_item_num=%d\n", p_list->list_item_num);

    my_asl_event_list_item_t* p = p_list->first_item;
    for(int i=0; i<p_list->list_item_num; i++)
    {
        printf("my_asl_event_list[%d].id=%d\n", i, p->id);
        printf("my_asl_event_list[%d].item_name=%s\n", i, p->item_name);
        p = p->next_item;
    }
    printf("\nMyAslEvent_PrintList---------------------------\n\n");

    return 0;
}

int MyAslEvent_Init(my_asl_event_list_t* p_list)
{
    if(p_list==NULL)return -1;
    memset(p_list, 0, sizeof(my_asl_event_list_t));

    p_list->first_item = NULL;
    p_list->end_item = NULL;

    return 0;
}