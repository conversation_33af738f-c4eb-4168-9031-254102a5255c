#include "e104_bt53c3_sw.h"

#if E104_BT53C3_SW_ENABLE

#include "myesp_sw_uart.h"
#include "my_gpio.h"
#include "my_relay.h"
#include "my_esp_chip_info_mng.h"
#include "my_led_mng.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"
#include "freertos/semphr.h"
#include "freertos/timers.h"

#include "esp_system.h"
#include "esp_log.h"
#include "driver/gpio.h"

#include "string.h"
#include "stdlib.h"
#include <stdio.h>
#include <ctype.h>

static const char* TAG = "E104_BT53C3_SW";

// 第二个E104模组的状态变量
int e104_sw_role = -1;
int e104_sw_adv_interval = -1;
int e104_sw_adv_power = -1;
int e104_sw_long_range = -1;
char e104_sw_mac[32];
bool e104_sw_mac_got = false;
bool e104_sw_bt53c3_init_ok = false;
bool e104_sw_bt53c3_chip1_get_mac_ok = false;
char* e104_sw_chip1_mac = NULL;
char* e104_sw_chip1_mac_no_colons = NULL;

// 第二个E104模组的引脚定义
static gpio_num_t bt53c3_sw_rst_pin = GPIO_NUM_17;
static gpio_num_t bt53c3_sw_mod_pin = GPIO_NUM_18;

// 软串口配置
#define BT53C3_SW_RX_BUF_SIZE   1024
#define BT53C3_SW_BAUDRATE      115200UL
#define BT53C3_SW_UART_PORT     SW_UART_NUM_0

// 任务句柄和定时器
static TaskHandle_t UsartBT53C3SwRcvTask_Handle = NULL;
static TimerHandle_t Bt53c3SwTimeoutTimer_Handle = NULL;

// GPIO初始化函数
static void BT53C3_SW_RSTGpioInit(void) {
    if (bt53c3_sw_rst_pin == GPIO_NUM_NC) return;
    
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_OUTPUT,
        .pin_bit_mask = (1ULL << bt53c3_sw_rst_pin),
        .pull_down_en = 0,
        .pull_up_en = 0,
    };
    gpio_config(&io_conf);
    MyGPIO_SET(bt53c3_sw_rst_pin, 0);
}

static void BT53C3_SW_ModGpioInit(void) {
    if (bt53c3_sw_mod_pin == GPIO_NUM_NC) return;
    
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_OUTPUT,
        .pin_bit_mask = (1ULL << bt53c3_sw_mod_pin),
        .pull_down_en = 0,
        .pull_up_en = 0,
    };
    gpio_config(&io_conf);
    MyGPIO_SET(bt53c3_sw_mod_pin, 0);
}

// 复位函数
void BT53C3_SW_RST(void) {
    if (bt53c3_sw_rst_pin == GPIO_NUM_NC) return;
    
    MyGPIO_SET(bt53c3_sw_rst_pin, 0);
    vTaskDelay(2000 / portTICK_PERIOD_MS);
    MyGPIO_SET(bt53c3_sw_rst_pin, 1);
}

// 模式切换函数
void BT53C3_SW_Mod_cmd(void) {
    if (bt53c3_sw_mod_pin == GPIO_NUM_NC) return;
    MyGPIO_SET(bt53c3_sw_mod_pin, 0);
}

void BT53C3_SW_Mod_data(void) {
    if (bt53c3_sw_mod_pin == GPIO_NUM_NC) return;
    MyGPIO_SET(bt53c3_sw_mod_pin, 1);
}

// 软串口初始化
static int Uart_BT53C3_SW_Init(gpio_num_t tx_pin, gpio_num_t rx_pin) {
    // 配置软串口参数
    sw_uart_config_t sw_uart_config = {
        .baud_rate = BT53C3_SW_BAUDRATE,
        .data_bits = SW_UART_DATA_8_BITS,
        .parity = SW_UART_PARITY_DISABLE,
        .stop_bits = SW_UART_STOP_BITS_1,
        .flow_ctrl_enable = false
    };
    
    // 应用配置
    int ret = sw_uart_param_config(BT53C3_SW_UART_PORT, &sw_uart_config);
    if (ret != SW_UART_OK) {
        ESP_LOGE(TAG, "Failed to configure SW_UART: %d", ret);
        return -1;
    }
    
    // 设置引脚
    ret = sw_uart_set_pin(BT53C3_SW_UART_PORT, tx_pin, rx_pin, 
                         SW_UART_PIN_NO_CHANGE, SW_UART_PIN_NO_CHANGE);
    if (ret != SW_UART_OK) {
        ESP_LOGE(TAG, "Failed to set SW_UART pins: %d", ret);
        return -1;
    }
    
    // 安装驱动
    ret = sw_uart_driver_install(BT53C3_SW_UART_PORT, BT53C3_SW_RX_BUF_SIZE, 0, 0, NULL, 0);
    if (ret != SW_UART_OK) {
        ESP_LOGE(TAG, "Failed to install SW_UART driver: %d", ret);
        return -1;
    }
    
    ESP_LOGI(TAG, "SW_UART initialized successfully for E104-BT53C3");
    return 0;
}

// 发送数据函数
static int BT53C3_SW_sendData(const char* logName, const char* data, uint16_t len) {
    const int txBytes = sw_uart_write_bytes(BT53C3_SW_UART_PORT, data, len);
    ESP_LOGI(logName, "SW_UART wrote %d bytes", txBytes);
    return txBytes;
}

// 从字符串末尾提取数字
static int get_last_number_sw(const char *str) {
    int length = strlen(str);
    int i = length - 1;
    int number = 0;
    int multiplier = 1;
    int found_digit = 0;
 
    while (i >= 0) {
        if (isdigit(str[i])) {
            number = (str[i] - '0') * multiplier + number;
            multiplier *= 10;
            found_digit = 1;
        } else if (found_digit) {
            break;
        }
        i--;
    }
 
    return number;
}

// 提取MAC地址
static char* extractMacAddress_sw(const char* input, const char* ref_str) {
    static char mac[18];
    
    const char* pos = strstr(input, ref_str);
    if (pos == NULL) {
        printf("MAC address not found in input string.\n");
        return NULL;
    }
    
    // 计算MAC地址的起始位置 (向前17个字符)
    const char* mac_start = pos - 17;
    if (mac_start < input) {
        printf("MAC address position is invalid.\n");
        return NULL;
    }
    
    // 复制MAC地址
    strncpy(mac, mac_start, 17);
    mac[17] = '\0';
    
    return mac;
}

// 移除MAC地址中的冒号
static char* remove_colons_sw(const char* mac) {
    size_t len = strlen(mac);
    char* result = malloc(len + 1);
    if (!result) return NULL;

    size_t j = 0;
    for (size_t i = 0; i < len; i++) {
        if (mac[i] != ':') {
            result[j++] = mac[i];
        }
    }
    result[j] = '\0';
    return result;
}

// 超时定时器回调
static void Bt53c3SwTimeoutTimer(TimerHandle_t xTimer) {
    ESP_LOGI(TAG, "----------------------Bt53c3SwTimeoutTimer");
    My_Relay_On();
    e104_bt53c3_sw_discon_all_dev();
}

// 模式切换函数实现
int e104_bt53c3_sw_mode_cmd(void) {
    vTaskDelay(210 / portTICK_PERIOD_MS);
    BT53C3_SW_Mod_cmd();
    vTaskDelay(210 / portTICK_PERIOD_MS);
    return 0;
}

int e104_bt53c3_sw_mode_data(void) {
    vTaskDelay(210 / portTICK_PERIOD_MS);
    BT53C3_SW_Mod_data();
    vTaskDelay(210 / portTICK_PERIOD_MS);
    return 0;
}

int e104_bt53c3_sw_rst(void) {
    BT53C3_SW_RST();
    return 0;
}

// AT命令函数
int e104_bt53c3_sw_set_dev_role(int role) {
    e104_bt53c3_sw_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+ROLE=%d\r\n", role);
    BT53C3_SW_sendData("e104_bt53c3_sw_set_dev_role", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_sw_query_dev_role(void) {
    e104_bt53c3_sw_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+ROLE?\r\n");
    BT53C3_SW_sendData("e104_bt53c3_sw_query_dev_role", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_sw_query_dev_mac(void) {
    e104_bt53c3_sw_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+MAC?\r\n");
    BT53C3_SW_sendData("e104_bt53c3_sw_query_dev_mac", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_sw_set_adv_name(char *name, int len) {
    if (!name || len <= 0 || len > 31) {
        return -1;
    }
    
    e104_bt53c3_sw_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+NAME=%s\r\n", name);
    ESP_LOGI("e104_bt53c3_sw_set_adv_name", "cmd:%s", cmd);
    BT53C3_SW_sendData("e104_bt53c3_sw_set_adv_name", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_sw_set_adv_interval(int interval) {
    e104_bt53c3_sw_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+ADVI=%d\r\n", interval);
    BT53C3_SW_sendData("e104_bt53c3_sw_set_adv_interval", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_sw_query_adv_interval(void) {
    e104_bt53c3_sw_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+ADVI?\r\n");
    BT53C3_SW_sendData("e104_bt53c3_sw_query_adv_interval", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_sw_set_power(int power) {
    e104_bt53c3_sw_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+PWR=%d\r\n", power);
    BT53C3_SW_sendData("e104_bt53c3_sw_set_power", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_sw_query_power(void) {
    e104_bt53c3_sw_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+PWR?\r\n");
    BT53C3_SW_sendData("e104_bt53c3_sw_query_power", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_sw_set_adv_data(char *data, int len) {
    if (!data || len <= 0 || len > 31) {
        return -1;
    }

    if (strlen(data) > len) {
        data[len] = '\0';
    }

    e104_bt53c3_sw_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+ADVDATA=0,%s\r\n", data);
    ESP_LOGI("e104_bt53c3_sw_set_adv_data", "cmd:%s", cmd);
    BT53C3_SW_sendData("e104_bt53c3_sw_set_adv_data", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_sw_set_long_range(bool long_range) {
    e104_bt53c3_sw_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+LE_CODED=%d\r\n", long_range ? 1 : 0);
    ESP_LOGI("e104_bt53c3_sw_set_long_range", "cmd:%s", cmd);
    BT53C3_SW_sendData("e104_bt53c3_sw_set_long_range", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_sw_query_connect_list(void) {
    e104_bt53c3_sw_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+CONNL?\r\n");
    BT53C3_SW_sendData("e104_bt53c3_sw_query_connect_list", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_sw_discon_all_dev(void) {
    e104_bt53c3_sw_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+DISCON=ALL\r\n");
    BT53C3_SW_sendData("e104_bt53c3_sw_discon_all_dev", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_sw_send_data_to_master(char *data, int len) {
    if (!data || len <= 0) {
        return -1;
    }

    e104_bt53c3_sw_mode_data();
    BT53C3_SW_sendData("e104_bt53c3_sw_send_data_to_master", data, len);
    e104_bt53c3_sw_mode_cmd();
    return 0;
}

// 软串口接收任务
static void UsartBT53C3SwRcvTask(void *pvParameters) {
    bool bt53c3_sw_timer_created = false;
    static const char *RX_TASK_TAG = "UsartBT53C3SwRcvTask";
    char* data = (char*)calloc(BT53C3_SW_RX_BUF_SIZE + 1, sizeof(char));
    uint32_t rxbytes = 0;

    if (!bt53c3_sw_timer_created) {
        bt53c3_sw_timer_created = true;
        Bt53c3SwTimeoutTimer_Handle = xTimerCreate("Bt53c3SwTimeoutTimer",
                                                   2000/portTICK_PERIOD_MS,
                                                   pdTRUE, 0, Bt53c3SwTimeoutTimer);
    }

    vTaskDelay(1000 / portTICK_PERIOD_MS);
    e104_bt53c3_sw_discon_all_dev();
    vTaskDelay(1000 / portTICK_PERIOD_MS);

    e104_sw_chip1_mac = calloc(1, strlen("00:11:22:33:44:55"));
    e104_sw_chip1_mac_no_colons = calloc(1, strlen("001122334455"));

    while (1) {
        while (!e104_sw_bt53c3_init_ok) {
            vTaskDelay(1000 / portTICK_PERIOD_MS);
        }

        // 使用软串口接收数据
        rxbytes = sw_uart_read_bytes(BT53C3_SW_UART_PORT, (void*)data,
                                    BT53C3_SW_RX_BUF_SIZE, 100 / portTICK_PERIOD_MS);

        if (rxbytes) {
            ESP_LOGI(RX_TASK_TAG, "SW_UART received %d bytes: '%s'", rxbytes, data);

            // 处理连接事件
            if ((strstr(data, " CONNECTD P") != NULL) && (strstr(data, " DISCONNECTD P") == NULL)) {
                char* mac = extractMacAddress_sw(data, " CONNECTD P");
                if (mac) {
                    xTimerStart(Bt53c3SwTimeoutTimer_Handle, 0);

                    size_t len = strlen(mac) + strlen("BT_sw_master_con:") + 1;
                    char* data_pack = malloc(len);
                    if (data_pack != NULL) {
                        snprintf(data_pack, len, "BT_sw_master_con:%s", mac);
                        ESP_LOGI(RX_TASK_TAG, "SW data_pack=%s", data_pack);
                        free(data_pack);
                    }
                }
            }

            // 处理断开连接事件
            if (strstr(data, " DISCONNECTD P") != NULL) {
                char* mac = extractMacAddress_sw(data, " DISCONNECTD P");
                if (mac) {
                    xTimerStop(Bt53c3SwTimeoutTimer_Handle, 0);

                    size_t len = strlen(mac) + strlen("BT_sw_master_discon:") + 1;
                    char* data_pack = malloc(len);
                    if (data_pack != NULL) {
                        snprintf(data_pack, len, "BT_sw_master_discon:%s", mac);
                        ESP_LOGI(RX_TASK_TAG, "SW data_pack=%s", data_pack);
                        free(data_pack);
                    }
                }
            }

            // 处理接收到的数据
            if (strstr(data, "+RECEIVED:") != NULL) {
                const char *input = data;
                const char *received_prefix = "+RECEIVED:";
                const char *ble_data_prefix = "BLE DATA\r\n";

                char *received_pos = strstr(input, received_prefix);
                char *ble_data_pos = strstr(input, ble_data_prefix);

                if (received_pos != NULL && ble_data_pos != NULL) {
                    received_pos += strlen(received_prefix);

                    int device_id, data_length;
                    if (sscanf(received_pos, "%d,%d", &device_id, &data_length) == 2) {
                        ble_data_pos += strlen(ble_data_prefix);

                        char* _data = malloc(data_length + 1);
                        if (_data != NULL) {
                            strncpy(_data, ble_data_pos, data_length);
                            _data[data_length] = '\0';

                            ESP_LOGI(RX_TASK_TAG, "SW Device %d sent %d bytes: %s",
                                   device_id, data_length, _data);

                            // 处理继电器控制命令
                            if (strstr(_data, "evg relay1 on") != NULL) {
                                xTimerStop(Bt53c3SwTimeoutTimer_Handle, 0);
                                ESP_LOGI(RX_TASK_TAG, "SW recv cmd: evg relay1 on");
                                My_Relay_On();
                                MyLedMng_Find_Dev();
                                e104_bt53c3_sw_send_data_to_master("success: evg relay1 on",
                                                                  strlen("success: evg relay1 on"));
                                e104_bt53c3_sw_query_connect_list();
                                e104_bt53c3_sw_discon_all_dev();
                            }

                            if (strstr(_data, "evg relay1 off") != NULL) {
                                ESP_LOGI(RX_TASK_TAG, "SW recv cmd: evg relay1 off");
                                My_Relay_Off();
                                e104_bt53c3_sw_send_data_to_master("success: evg relay1 off",
                                                                  strlen("success: evg relay1 off"));
                            }

                            free(_data);
                        }
                    }
                }
            }

            // 处理MAC地址查询响应
            if (strstr(data, "+MAC:") != NULL) {
                char* mac_pos = strstr(data, "+MAC:");
                if (mac_pos != NULL) {
                    mac_pos += 5; // 跳过"+MAC:"

                    if (e104_sw_chip1_mac && e104_sw_chip1_mac_no_colons) {
                        strncpy(e104_sw_chip1_mac, mac_pos, 17);
                        e104_sw_chip1_mac[17] = '\0';

                        char* no_colons = remove_colons_sw(e104_sw_chip1_mac);
                        if (no_colons) {
                            strcpy(e104_sw_chip1_mac_no_colons, no_colons);
                            free(no_colons);
                            e104_sw_bt53c3_chip1_get_mac_ok = true;
                            ESP_LOGI(RX_TASK_TAG, "SW MAC: %s", e104_sw_chip1_mac);
                        }
                    }
                }
            }

            // 处理其他AT命令响应
            if (strstr(data, "+ROLE") != NULL) {
                e104_sw_role = get_last_number_sw(data);
                ESP_LOGI(RX_TASK_TAG, "SW e104_role: %d", e104_sw_role);
            }

            if (strstr(data, "+ADVI") != NULL) {
                e104_sw_adv_interval = get_last_number_sw(data);
                ESP_LOGI(RX_TASK_TAG, "SW e104_adv_interval: %d", e104_sw_adv_interval);
            }

            if (strstr(data, "+PWR") != NULL) {
                e104_sw_adv_power = get_last_number_sw(data);
                ESP_LOGI(RX_TASK_TAG, "SW e104_adv_power: %d", e104_sw_adv_power);
            }

            memset(data, 0, BT53C3_SW_RX_BUF_SIZE);
        }
    }

    free(data);
}

// 重新初始化函数
int e104_bt53c3_sw_reinit(void) {
    e104_sw_bt53c3_init_ok = false;
    vTaskDelay(1000 / portTICK_PERIOD_MS);
    BT53C3_SW_RST();
    BT53C3_SW_Mod_cmd();
    vTaskDelay(5000 / portTICK_PERIOD_MS);

    char esp_mac[16];
    memset(esp_mac, 0, sizeof(esp_mac));
    MyEspChipInfoMng_GetMacStr(esp_mac);

    char c104_adv_name[32];
    memset(c104_adv_name, 0, sizeof(c104_adv_name));
    sprintf(c104_adv_name, "EVG-SW-%s", esp_mac);

    ESP_LOGI(TAG, "SW c104_adv_name: %s", c104_adv_name);

    vTaskDelay(1000 / portTICK_PERIOD_MS);

    e104_bt53c3_sw_set_adv_name(c104_adv_name, strlen(c104_adv_name));
    vTaskDelay(200 / portTICK_PERIOD_MS);
    e104_bt53c3_sw_set_adv_interval(100);

    // 创建接收任务
    if (UsartBT53C3SwRcvTask_Handle == NULL) {
        xTaskCreate(UsartBT53C3SwRcvTask, "UsartBT53C3SwRcvTask",
                   3072, NULL, 5, &UsartBT53C3SwRcvTask_Handle);
    }

    e104_sw_bt53c3_init_ok = true;

    vTaskDelay(3000 / portTICK_PERIOD_MS);
    e104_bt53c3_sw_query_dev_role();
    vTaskDelay(500 / portTICK_PERIOD_MS);
    e104_bt53c3_sw_query_dev_mac();
    vTaskDelay(500 / portTICK_PERIOD_MS);
    e104_bt53c3_sw_query_adv_interval();
    vTaskDelay(500 / portTICK_PERIOD_MS);
    e104_bt53c3_sw_query_power();

    return 0;
}

// 主初始化函数
int e104_bt53c3_sw_init(gpio_num_t tx_pin, gpio_num_t rx_pin, gpio_num_t rst_pin, gpio_num_t mod_pin) {
    ESP_LOGI(TAG, "Initializing E104-BT53C3 SW module with TX=%d, RX=%d, RST=%d, MOD=%d",
             tx_pin, rx_pin, rst_pin, mod_pin);

    // 保存引脚配置
    bt53c3_sw_rst_pin = rst_pin;
    bt53c3_sw_mod_pin = mod_pin;

    e104_sw_bt53c3_init_ok = false;

    // 初始化GPIO
    BT53C3_SW_ModGpioInit();
    BT53C3_SW_RSTGpioInit();
    vTaskDelay(1000 / portTICK_PERIOD_MS);

    // 复位模组
    BT53C3_SW_RST();
    BT53C3_SW_Mod_cmd();
    vTaskDelay(5000 / portTICK_PERIOD_MS);

    // 初始化软串口
    if (Uart_BT53C3_SW_Init(tx_pin, rx_pin) != 0) {
        ESP_LOGE(TAG, "Failed to initialize SW_UART for E104-BT53C3");
        return -1;
    }

    // 创建接收任务
    xTaskCreate(UsartBT53C3SwRcvTask, "UsartBT53C3SwRcvTask",
               3072, NULL, 5, &UsartBT53C3SwRcvTask_Handle);

    e104_sw_bt53c3_init_ok = true;

    vTaskDelay(3000 / portTICK_PERIOD_MS);
    e104_bt53c3_sw_query_dev_mac();

    // 等待获取MAC地址
    while (!e104_sw_bt53c3_chip1_get_mac_ok) {
        vTaskDelay(100 / portTICK_PERIOD_MS);
    }

    // 设置广播名称
    char c104_adv_name[32];
    memset(c104_adv_name, 0, sizeof(c104_adv_name));
    sprintf(c104_adv_name, "EVG-SW-%s", e104_sw_chip1_mac_no_colons + 4);

    ESP_LOGI(TAG, "SW c104_adv_name: %s", c104_adv_name);

    vTaskDelay(1000 / portTICK_PERIOD_MS);

    e104_bt53c3_sw_set_adv_name(c104_adv_name, strlen(c104_adv_name));
    vTaskDelay(1000 / portTICK_PERIOD_MS);
    e104_bt53c3_sw_set_adv_interval(100);
    vTaskDelay(500 / portTICK_PERIOD_MS);

    e104_bt53c3_sw_query_dev_role();
    vTaskDelay(500 / portTICK_PERIOD_MS);
    e104_bt53c3_sw_query_adv_interval();
    vTaskDelay(500 / portTICK_PERIOD_MS);
    e104_bt53c3_sw_query_power();

    ESP_LOGI(TAG, "E104-BT53C3 SW module initialized successfully");
    return 0;
}

#endif // E104_BT53C3_SW_ENABLE
