#include "my_endian.h"


int16_t my_endian_conversion_16(int16_t host_uint16)
{
    // return  ((host_uint16 & (uint16_t)0x00ffU) << 8) | ((host_uint16 & (uint16_t)0xff00U) >> 8);
    return htons(host_uint16);
}

int32_t my_endian_conversion_32(int32_t host_uint32)
{
    // return  ((host_uint32 & (uint32_t)0x000000ffUL) << 24) | ((host_uint32 & (uint32_t)0x0000ff00UL) <<  8) |
    // 		((host_uint32 & (uint32_t)0x00ff0000UL) >>  8) | ((host_uint32 & (uint32_t)0xff000000UL) >> 24);
    return htonl(host_uint32);
}

int64_t my_endian_conversion_64(int64_t host_uint64)
{
    return  (int64_t)((host_uint64 & (uint64_t)0x00000000000000ffULL) << 56) |
    		(int64_t)((host_uint64 & (uint64_t)0x000000000000ff00ULL) << 40) |
    		(int64_t)((host_uint64 & (uint64_t)0x0000000000ff0000ULL) << 24) |
    		(int64_t)((host_uint64 & (uint64_t)0x00000000ff000000ULL) <<  8) |
    		(int64_t)((host_uint64 & (uint64_t)0x000000ff00000000ULL) >>  8) |
    		(int64_t)((host_uint64 & (uint64_t)0x0000ff0000000000ULL) >> 24) |
    		(int64_t)((host_uint64 & (uint64_t)0x00ff000000000000ULL) >> 40) |
    		(int64_t)((host_uint64 & (uint64_t)0xff00000000000000ULL) >> 56);
}

int my_endian_conversion_custom(void* data, int data_len)
{
    if(data==NULL)
	{
		return -1;
	}
	if(data_len<=1)
	{
		return 1;
	}

	char* p_in_byte = (char*)data;
	uint8_t temp = 0;

	for(int i=0; i<data_len/2; i++)
	{
		temp = *(p_in_byte+i);
		*(p_in_byte+i) = *(p_in_byte+data_len-1-i);
		*(p_in_byte+data_len-1-i) = temp;
	}

	return 0;
}

int my_endian_conversion_custom_not_OverWrite(void* saving, void* source, int data_len)
{
    if(saving==NULL)
	{
		return -1;
	}
	if(source==NULL)
	{
		return -1;
	}
	if(data_len<1)
	{
		return 1;
	}

	char* p_source_in_byte = (char*)source;
	char* p_saving_in_byte = (char*)saving;
	uint8_t temp = 0;

	int i=0;
	for(; i<data_len/2; i++)
	{
		temp = *(p_source_in_byte+i);
		*(p_saving_in_byte+i) = *(p_source_in_byte+data_len-1-i);
		*(p_saving_in_byte+data_len-1-i) = temp;
	}
	if(data_len%2)
	{
		*(p_saving_in_byte+i) = *(p_source_in_byte+i);
	}

	return 0;
}