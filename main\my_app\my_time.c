#include "my_time.h"

#if MY_TIME_FUNC

#include "my_nvs.h"

#include "esp_attr.h"
#include "esp_log.h"

mytime_info_t mytime_info;
mytime_info_nvs_t mytime_info_nvs;

RTC_DATA_ATTR mytime_timemark_t mytime_timemark;


/**
  * @brief 软件复位后加载开机时间,多次调用不影响开机时间值
  * @param 1:从nvs加载时间点，0:不加载
  * @return 0:time since poweron in second
  */
static time_t MyTime_LoadPoweronTime(uint8_t reload_mark);

/**
  * @brief 标记最后一次复位时间点，软件复位后调用，需要在MyTime_LoadPoweronTime()之后调用，调用后复位以来运行时间将以当前开机以来运行时间为基点计算
  * @param second  Time in Second
  * @return mytime_timemark.mark_last_reset
  */
static time_t MyTime_MarkLastResetTime(void);

/**
  * @brief 设置RTC时钟，立即生效，不标记时间同步点
  * @param second  Time in Second
  * @return 0:success, -1:Function not implemented
  */
static int MyTime_SetTime(time_t second);

/**
  * @brief 立即结算开机时间，软件复位前调用
  * @return mytime_info.runtime_since_poweron;:开机以来的时间
  */
static time_t MyTime_SettlementRuntimeSincePoweron(void);


void MyTime_Refresh(void)
{
    //mytime_info.runtime_since_reset = xTaskGetTickCount()/configTICK_RATE_HZ;
    
    mytime_info.runtime_since_poweron = mytime_timemark.mark_prehistoric_since_zero + MyTime_GetTime()-mytime_timemark.mark_last_sync;
    mytime_info.runtime_since_reset = mytime_info.runtime_since_poweron - mytime_timemark.mark_last_reset;

    #if MY_TIME_FUNC_DEBUG
    ESP_LOGI("MyTime_Task()", "runtime_since_poweron=%ld\n", mytime_info.runtime_since_poweron);
    ESP_LOGI("MyTime_Task()", "runtime_since_reset=%ld\n", mytime_info.runtime_since_reset);
    #endif
}

static void MyTimeInfo_Init(void)
{
    memset(&mytime_info_nvs, 0, sizeof(mytime_info_nvs_t));

	int err = 0;

	err = LoadDataFromNvs(MTM_NVS_FORM_NAME, MTM_NVS_KEY_NAME, (void*)&mytime_info_nvs, sizeof(mytime_info_nvs_t));

	if(err==2)
	{
		ESP_LOGI("MyTimeInfo_Init()", "first run since factory, erase flash once...");
		My_nvs_flash_erase();
		LoadDataFromNvs(MTM_NVS_FORM_NAME, MTM_NVS_KEY_NAME, (void*)&mytime_info_nvs, sizeof(mytime_info_nvs_t));
	}

	#if MY_TIME_FUNC_DEBUG
    printf("MyTimeInfo_Init():\n");
    printf("mytime_info_nvs.poweron_count=%d\n", mytime_info_nvs.poweron_count);
	printf("mytime_info_nvs.mark_prehistoric_since_zero=%ld\n", mytime_info_nvs.mark_prehistoric_since_zero);
    printf("mytime_info_nvs.mark_last_sync=%ld\n", mytime_info_nvs.mark_last_sync);
	#endif
}

static int SaveMTMConfig(int reboot)
{
	#if MY_TIME_FUNC_DEBUG
    printf("SaveMTMConfig():saving MTM configuration\n");
	#endif

    return SaveDataToNvs(MTM_NVS_FORM_NAME, MTM_NVS_KEY_NAME, (void*)&mytime_info_nvs, sizeof(mytime_info_nvs_t), reboot);
}

void MyTime_Init(uint8_t ispoweron, uint8_t reload_mark)
{
	MyTimeInfo_Init();

	//-----------------
	if(ispoweron)
	{
		mytime_info_nvs.poweron_count++;
		mytime_info_nvs.mark_prehistoric_since_zero = 0;
		mytime_info_nvs.mark_last_sync = 0;
		SaveMTMConfig(0);

		mytime_info.runtime_since_poweron = 0;
	}
	mytime_info.poweron_count = mytime_info_nvs.poweron_count;

    #if MY_TIME_FUNC_DEBUG
    printf("MyTime_Init():\n");
    printf("mytime_timemark.mark_last_reset=%ld\n", mytime_timemark.mark_last_reset);
	printf("mytime_timemark.mark_prehistoric_since_zero=%ld\n", mytime_timemark.mark_prehistoric_since_zero);
    printf("mytime_timemark.mark_last_sync=%ld\n", mytime_timemark.mark_last_sync);
	#endif

	MyTime_LoadPoweronTime(reload_mark);
	MyTime_MarkLastResetTime();

	/* printf("mytime_info.runtimes_since_reset=%ds\n", mytime_info.runtime_since_reset);
	printf("mytime_info.runtime_since_poweron=%ds\n", mytime_info.runtime_since_poweron); */
	ESP_LOGI("MyTime_Init()", "poweron_count=%d\n", mytime_info.poweron_count);

    #if MY_TIME_FUNC_DEBUG
    printf("MyTime_Init():\n");
    printf("mytime_timemark.mark_last_reset=%ld\n", mytime_timemark.mark_last_reset);
	printf("mytime_timemark.mark_prehistoric_since_zero=%ld\n", mytime_timemark.mark_prehistoric_since_zero);
    printf("mytime_timemark.mark_last_sync=%ld\n", mytime_timemark.mark_last_sync);
	#endif
}


static time_t MyTime_LoadPoweronTime(uint8_t reload_mark)
{
    if(reload_mark)
    {
        mytime_timemark.mark_prehistoric_since_zero = mytime_info_nvs.mark_prehistoric_since_zero;
	    mytime_timemark.mark_last_sync = mytime_info_nvs.mark_last_sync;
    }

	mytime_info.runtime_since_poweron = mytime_timemark.mark_prehistoric_since_zero + MyTime_GetTime()-mytime_timemark.mark_last_sync;

	return mytime_info.runtime_since_poweron;
}

static time_t MyTime_MarkLastResetTime(void)
{
	return mytime_timemark.mark_last_reset = MyTime_GetRuntimeSincePoweron();
}

static int MyTime_SetTime(time_t second)
{
	struct timeval tv;
	tv.tv_sec = second;
	tv.tv_usec = 0;
	return settimeofday(&tv, NULL);
}

static time_t MyTime_SettlementRuntimeSincePoweron(void)
{
	return mytime_info.runtime_since_poweron = mytime_timemark.mark_prehistoric_since_zero + MyTime_GetTime()-mytime_timemark.mark_last_sync;
}

time_t MyTime_GetTime(void)
{
	return time(NULL);
}

int MyTime_SetTimeAndMark(time_t second)
{
	mytime_timemark.mark_prehistoric_since_zero += time(NULL)-mytime_timemark.mark_last_sync;
	mytime_timemark.mark_last_sync = second;

    #if MY_TIME_FUNC_DEBUG
    printf("MyTime_SetTimeAndMark():\n");
    printf("mytime_timemark.mark_last_reset=%ld\n", mytime_timemark.mark_last_reset);
	printf("mytime_timemark.mark_prehistoric_since_zero=%ld\n", mytime_timemark.mark_prehistoric_since_zero);
    printf("mytime_timemark.mark_last_sync=%ld\n", mytime_timemark.mark_last_sync);
	#endif

	return MyTime_SetTime(second);
}

time_t MyTime_SetTime_Format(int year, int month, int day, int hour, int minute, int second)
{
	time_t ret;
	struct tm info;
	char buffer[80];

	info.tm_year = year-1900;
	if(month>0&&month<=12)info.tm_mon = month - 1;
	else info.tm_mon = 0;
	if(day>0&&day<=31)info.tm_mday = day;
	else info.tm_mday = 1;
	if(hour>=0&&hour<24)info.tm_hour = hour;
	else info.tm_hour = 0;
	if(minute>=0&&minute<60)info.tm_min = minute;
	else info.tm_min = 0;
	if(second>=0&&second<60)info.tm_sec = second;
	else info.tm_sec = 0;
	info.tm_isdst = -1;

	ret = mktime(&info);
	#if MY_TIME_FUNC_DEBUG
	ESP_LOGI("MyTime_SetTime_Format()", "now time=%ld\n", ret);
	#endif
	if( ret == -1 )
	{
		#if MY_TIME_FUNC_DEBUG
		ESP_LOGE("MyTime_SetTime_Format()", "error, cannot convert time use mktime()\n");
		#endif
	}
	else
	{
		strftime(buffer, sizeof(buffer), "%c", &info );
		#if MY_TIME_FUNC_DEBUG
		ESP_LOGI("MyTime_SetTime_Format()", "%s\n", buffer);
		#endif

		MyTime_SetTimeAndMark(ret);
	}

	return ret;
}

time_t MyTime_GetRuntimeSincePoweron(void)
{
	return mytime_info.runtime_since_poweron;
}

time_t MyTime_GetRuntimeSinceReset(void)
{
	return mytime_info.runtime_since_reset;
}

uint32_t MyTime_GetPoweronCount(void)
{
	return mytime_info.poweron_count;
}

void MyTime_PrintTime(void)
{
	char buffer[80];
	struct tm* tminfo1;

	time_t nowtime = MyTime_GetTime();
	tminfo1 = localtime( &nowtime );

	strftime(buffer,80,"%Y-%m-%e %H:%M:%S", tminfo1);//以年月日_时分秒的形式表示当前时间
	ESP_LOGI("MyTime_PrintTime()", "nowtime=%s, powerontime=%ld, resettime=%ld\n\n", buffer, MyTime_GetRuntimeSincePoweron(), MyTime_GetRuntimeSinceReset());
}

void MyTime_SavingForReboot(void)
{
	//保存状态
    #if MY_TIME_FUNC_DEBUG
    printf("MyTime_SavingForReboot():\n");
    printf("mytime_timemark.mark_last_reset=%ld\n", mytime_timemark.mark_last_reset);
	printf("mytime_timemark.mark_prehistoric_since_zero=%ld\n", mytime_timemark.mark_prehistoric_since_zero);
    printf("mytime_timemark.mark_last_sync=%ld\n", mytime_timemark.mark_last_sync);
	#endif
	mytime_info_nvs.mark_prehistoric_since_zero = MyTime_SettlementRuntimeSincePoweron();
	mytime_info_nvs.mark_last_sync = MyTime_GetTime();
	SaveMTMConfig(0);
    #if MY_TIME_FUNC_DEBUG
    printf("MyTime_SavingForReboot():\n");
    printf("mytime_timemark.mark_last_reset=%ld\n", mytime_timemark.mark_last_reset);
	printf("mytime_timemark.mark_prehistoric_since_zero=%ld\n", mytime_timemark.mark_prehistoric_since_zero);
    printf("mytime_timemark.mark_last_sync=%ld\n", mytime_timemark.mark_last_sync);
	#endif
}

#if MY_TIME_FUNC_DEBUG
void MyDebug_PrintTimeInfo(char* name)
{
	ESP_LOGI("\n\nMyDebug_PrintTimeInfo()", "---------------------------------\n%s\n\n", name);

	ESP_LOGI("MyDebug_PrintTimeInfo()", "runtime_since_poweron=%ld\n", mytime_info.runtime_since_poweron);
    ESP_LOGI("MyDebug_PrintTimeInfo()", "runtime_since_reset=%ld\n", mytime_info.runtime_since_reset);

	ESP_LOGI("\n\nMyDebug_PrintTimeInfo()", "---------------------------------\n\n");
}
#endif

#endif