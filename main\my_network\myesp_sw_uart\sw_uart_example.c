#include "myesp_sw_uart.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "string.h"

static const char* TAG = "SW_UART_EXAMPLE";

// 软串口使用示例
void sw_uart_example_init(void) {
    // 1. 配置软串口参数 (类似于ESP32的uart_config_t)
    sw_uart_config_t sw_uart_config = {
        .baud_rate = 9600,                    // 波特率9600
        .data_bits = SW_UART_DATA_8_BITS,     // 8数据位
        .parity = SW_UART_PARITY_DISABLE,     // 无校验
        .stop_bits = SW_UART_STOP_BITS_1,     // 1停止位
        .flow_ctrl_enable = false             // 无流控制
    };
    
    // 2. 应用配置 (类似于uart_param_config)
    int ret = sw_uart_param_config(SW_UART_NUM_0, &sw_uart_config);
    if (ret != SW_UART_OK) {
        ESP_LOGE(TAG, "Failed to configure SW_UART: %d", ret);
        return;
    }
    
    // 3. 设置引脚 (类似于uart_set_pin)
    ret = sw_uart_set_pin(SW_UART_NUM_0, 
                         GPIO_NUM_14,  // TX引脚
                         GPIO_NUM_13,  // RX引脚
                         SW_UART_PIN_NO_CHANGE,  // RTS引脚(不使用)
                         SW_UART_PIN_NO_CHANGE); // CTS引脚(不使用)
    if (ret != SW_UART_OK) {
        ESP_LOGE(TAG, "Failed to set SW_UART pins: %d", ret);
        return;
    }
    
    // 4. 安装驱动 (类似于uart_driver_install)
    ret = sw_uart_driver_install(SW_UART_NUM_0, 
                                1024,  // RX缓冲区大小
                                0,     // TX缓冲区大小(不使用)
                                0,     // 事件队列大小(不使用)
                                NULL,  // 事件队列句柄(不使用)
                                0);    // 中断分配标志(不使用)
    if (ret != SW_UART_OK) {
        ESP_LOGE(TAG, "Failed to install SW_UART driver: %d", ret);
        return;
    }
    
    ESP_LOGI(TAG, "Software UART initialized successfully");
}

// 软串口发送数据示例 (类似于uart_write_bytes)
int sw_uart_example_send_data(const char* data, uint16_t len) {
    int bytes_written = sw_uart_write_bytes(SW_UART_NUM_0, data, len);
    if (bytes_written > 0) {
        ESP_LOGI(TAG, "SW_UART sent %d bytes", bytes_written);
    } else {
        ESP_LOGE(TAG, "SW_UART send failed: %d", bytes_written);
    }
    return bytes_written;
}

// 软串口接收任务示例 (类似于使用uart_read_bytes的接收任务)
void sw_uart_example_rx_task(void *pvParameters) {
    static const char *RX_TASK_TAG = "SW_UART_RX_TASK";
    char* data = (char*)malloc(1024 + 1);
    
    if (data == NULL) {
        ESP_LOGE(RX_TASK_TAG, "Failed to allocate memory");
        vTaskDelete(NULL);
        return;
    }
    
    while (1) {
        // 接收数据 (类似于uart_read_bytes)
        int rxBytes = sw_uart_read_bytes(SW_UART_NUM_0, data, 1024, 100 / portTICK_PERIOD_MS);
        
        if (rxBytes > 0) {
            data[rxBytes] = 0; // 添加字符串结束符
            ESP_LOGI(RX_TASK_TAG, "SW_UART received %d bytes: '%s'", rxBytes, data);
            
            // 回显接收到的数据
            sw_uart_example_send_data(data, rxBytes);
            
            // 清空缓冲区
            memset(data, 0, 1024);
        }
    }
    
    free(data);
}

// 启动软串口示例
void sw_uart_example_start(void) {
    // 初始化软串口
    sw_uart_example_init();
    
    // 创建接收任务
    xTaskCreate(sw_uart_example_rx_task, "sw_uart_rx_task", 4096, NULL, 5, NULL);
    
    // 发送测试数据
    vTaskDelay(1000 / portTICK_PERIOD_MS);
    sw_uart_example_send_data("Hello from Software UART!\r\n", 
                             strlen("Hello from Software UART!\r\n"));
}

// 与ESP32原生UART接口对比说明:
/*
ESP32原生UART使用方式:
1. uart_config_t uart_config = {...};
2. uart_param_config(UART_NUM_1, &uart_config);
3. uart_set_pin(UART_NUM_1, tx_pin, rx_pin, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
4. uart_driver_install(UART_NUM_1, rx_buf_size, 0, 0, NULL, 0);
5. uart_write_bytes(UART_NUM_1, data, len);
6. uart_read_bytes(UART_NUM_1, buf, len, timeout);

软串口使用方式:
1. sw_uart_config_t sw_uart_config = {...};
2. sw_uart_param_config(SW_UART_NUM_0, &sw_uart_config);
3. sw_uart_set_pin(SW_UART_NUM_0, tx_pin, rx_pin, SW_UART_PIN_NO_CHANGE, SW_UART_PIN_NO_CHANGE);
4. sw_uart_driver_install(SW_UART_NUM_0, rx_buf_size, 0, 0, NULL, 0);
5. sw_uart_write_bytes(SW_UART_NUM_0, data, len);
6. sw_uart_read_bytes(SW_UART_NUM_0, buf, len, timeout);

接口完全一致，只需要将函数名前缀从"uart_"改为"sw_uart_"，
将端口号从"UART_NUM_x"改为"SW_UART_NUM_x"即可。
*/
