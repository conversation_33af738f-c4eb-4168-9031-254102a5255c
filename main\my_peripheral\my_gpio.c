#include "my_gpio.h"
#include "my_debug.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"

#include "driver/gpio.h"


#if defined MY_DEBUG_PRINT_FUNC
#define DEBUG_PRINT_LEVEL_my_gpio_PRINT   MY_DEBUG_PRINT_LEVEL_0
#else
#define DEBUG_PRINT_LEVEL_my_gpio_PRINT   DEBUG_PRINT_LEVEL_0
#endif


volatile uint8_t gpiolevel_array[CHIP_GPIONUM_MAX];

#define IN_EN_DISABLE   0
#define IN_EN_ENABLE    1
typedef struct
{
    uint8_t in_en;
    uint8_t currentstate;
    uint8_t last_state;
    uint8_t level_changed;

}gpioinput_t;

static gpioinput_t gpioinputpin_array[CHIP_GPIONUM_MAX];


void MyGPIO_SET(gpio_num_t gpio_num, uint32_t level)
{
    if(gpio_num>=0)
    {
        if(gpio_num<=CHIP_GPIOOUT_MAX)
        {
            gpio_set_level(gpio_num, level);
            gpiolevel_array[gpio_num] = level;
            #if DEBUG_PRINT_LEVEL_my_gpio_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
            printf("gpio%d = %d\r\n", gpio_num, gpiolevel_array[gpio_num]);
            #endif
        }
        else if(gpioinputpin_array[gpio_num].in_en)
        {
            gpiolevel_array[gpio_num] = 1;
        }
        else if(gpio_num<CHIP_GPIONUM_MAX)
        {
            #if DEBUG_PRINT_LEVEL_my_gpio_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
            printf("GPIO SET ERROR: GPIO34 - GPIO39 only support input mode!!!\r\n");
            #endif
        }
        else
        {
            #if DEBUG_PRINT_LEVEL_my_gpio_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
            printf("GPIO SET ERROR: GPIO PIN NOT EXIST!!!");
            #endif
        }
    }
    else
    {
        #if DEBUG_PRINT_LEVEL_my_gpio_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
        printf("GPIO SET ERROR: GPIO PIN NOT EXIST!!!");
        #endif
    }
}

void MyGpioInputPinRegister(uint8_t pin)
{
    gpioinputpin_array[pin].in_en = IN_EN_ENABLE;
}

static void gpio_task_example(void* arg)
{
    uint8_t pin = 0;
    for(;;)
    {
        vTaskDelay(100/portTICK_PERIOD_MS);

        for(pin=0; pin<CHIP_GPIONUM_MAX; pin++)
        {
            if(gpioinputpin_array[pin].in_en)
            {
                gpioinputpin_array[pin].currentstate = gpio_get_level(pin);
                gpioinputpin_array[pin].level_changed = 0;
            }
        }
        for(pin=0; pin<CHIP_GPIONUM_MAX; pin++)
        {
            if(gpioinputpin_array[pin].in_en)
            {
                if(gpioinputpin_array[pin].currentstate!=gpioinputpin_array[pin].last_state)
                {
                    gpioinputpin_array[pin].level_changed = 1;
                }
            }
            
        }
        vTaskDelay(50/portTICK_PERIOD_MS);
        for(pin=0; pin<CHIP_GPIONUM_MAX; pin++)
        {
            if(gpioinputpin_array[pin].level_changed)
            {
                gpioinputpin_array[pin].currentstate = gpio_get_level(pin);
            }
        }
        for(pin=0; pin<CHIP_GPIONUM_MAX; pin++)
        {
            if(gpioinputpin_array[pin].level_changed)
            {
                if(gpioinputpin_array[pin].currentstate!=gpioinputpin_array[pin].last_state)
                {
                    gpioinputpin_array[pin].last_state = gpioinputpin_array[pin].currentstate;
                    gpiolevel_array[pin] = gpioinputpin_array[pin].currentstate;
                    #if DEBUG_PRINT_LEVEL_my_gpio_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
                    printf("gpio%d = %d\r\n", pin, gpiolevel_array[pin]);
                    #endif
                    gpioinputpin_array[pin].level_changed = 0;
                }
                gpioinputpin_array[pin].level_changed = 0;
            }
        }
    }
}

void MyGpioInit(void)
{
    uint8_t i = 0;

    for(i=0; i<CHIP_GPIONUM_MAX; i++)
    {
        gpioinputpin_array[i].in_en = IN_EN_DISABLE;
        gpioinputpin_array[i].currentstate = 0;
        gpioinputpin_array[i].last_state = 0;
        gpioinputpin_array[i].level_changed = 0;
    }
    //xTaskCreate(gpio_task_example, "gpio_task_example", 4096, NULL, 7, NULL);
    StackType_t* p_task_stack = calloc(1, 3072*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        StaticTask_t* p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
            xTaskCreateStatic(&gpio_task_example, "gpio_task_example", 2048, NULL, 7, p_task_stack, p_task_data);
        }
    }
}
