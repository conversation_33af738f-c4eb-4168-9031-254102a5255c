#include "my_uart.h"

#if MYUART_FUNC

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"
#include "freertos/semphr.h"
#include "esp_system.h"
#include "esp_log.h"
#include "driver/uart.h"
#include "driver/gpio.h"

//串口参数

#define A_TXD_PIN (GPIO_NUM_3)
#define A_RXD_PIN (GPIO_NUM_9)



SemaphoreHandle_t UartASendMutexSemaphore = NULL;

typedef struct
{
    int uart_installed;
    int baudrate;
}myuart_t;

myuart_t myuart_info;


int UartSendData(char* data, int len)
{
    int ret = 0;
    xSemaphoreTake(UartASendMutexSemaphore, portMAX_DELAY);
    ret = uart_write_bytes(LTE_UART_NUM, data, len);
    xSemaphoreGive(UartASendMutexSemaphore);

    return ret;
}

void MyUartFlush(void)
{
    uart_flush(LTE_UART_NUM);
}

void MyUartSetBaudrate(int newbuadrate)
{
    if(myuart_info.uart_installed)
    {
        if(newbuadrate!=myuart_info.baudrate)
        {
            myuart_info.baudrate = newbuadrate;
            uart_set_baudrate(LTE_UART_NUM, newbuadrate);
        }
    }
}

void MyUartInit(int baudrate)
{
    if(!myuart_info.uart_installed)
    {
        const uart_config_t uart_config = {
        .baud_rate = baudrate,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE
        };
        uart_param_config(LTE_UART_NUM, &uart_config);
        uart_set_pin(LTE_UART_NUM, A_TXD_PIN, A_RXD_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
        // We won't use a buffer for sending data.
        uart_driver_install(LTE_UART_NUM, UART_A_RCV_BUF_SIZE*16, 0, 0, NULL, 0);

        UartASendMutexSemaphore = xSemaphoreCreateMutex();

        myuart_info.baudrate = baudrate;
        myuart_info.uart_installed = 1;
    }
}

#endif