#include "my_config.h"
#include "my_debug.h"

#include "my_nvs.h"
#include "my_nvs_ds.h"
#include "my_dev_paramid.h"
#include "my_config_table.h"
#include "my_esp_chip_info_mng.h"

#include <esp_log.h>


#if defined MY_DEBUG_PRINT_FUNC
#define DEBUG_PRINT_LEVEL_my_config_PRINT   MY_DEBUG_PRINT_LEVEL_0
#else
#define DEBUG_PRINT_LEVEL_my_config_PRINT   DEBUG_PRINT_LEVEL_0
#endif



static uint8_t conf_list_already = 0;
my_config_nvs_t my_config_nvs;

my_nvs_d2link_list_t* myconf_table_list_array[myconftable_type_max];

my_nvs_d2link_list_item_t* MyConfig_Get_ConfigItem_FromId(int id)
{
    int myconftable_type = 0;
    my_nvs_d2link_list_item_t* config_item = NULL;
    for(; myconftable_type<myconftable_type_max; myconftable_type++)
    {
        config_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type], id);
        if(config_item!=NULL)
        {
            break;
        }
    }
    if(config_item!=NULL)
    {
        return config_item;
    }

    ESP_LOGE("MyConfig_Get_ConfigItem_FromId()", "not found!");
    
    return NULL;
}

int ipv4_int_to_str(char* saving, uint8_t* ipv4_int)
{
    if(saving==NULL)
    {
        return -1;
    }
    if(ipv4_int==NULL)
    {
        return -2;
    }
	sprintf(saving, "%d.%d.%d.%d", *(ipv4_int+0), *(ipv4_int+1), *(ipv4_int+2), *(ipv4_int+3));
    #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
    printf("*******ok11-%s\n",saving);
    #endif
    return 0;
}

int MyConfig_GetConfTable_Format(myconftable_conftable_type_t type)
{
    if(type>=myconftable_type_max)
    {
        return -1;
    }
    my_nvs_d2link_list_item_t* p_item = NULL;
    switch(type)
    {
        case(myconftable_type_asl):
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_asl_appId);
            my_config_nvs.asl.appid = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_asl_loginFlag);
            my_config_nvs.asl.loginflag = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_asl_dic);
            my_config_nvs.asl.dic = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_asl_token);
            my_config_nvs.asl.token = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_asl_last_sync_param_timestamp);
            my_config_nvs.asl.last_sync_param_timestamp = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_asl_sync_param_effect_time);
            my_config_nvs.asl.sync_param_effect_time = p_item->data_uint64;
            break;
        case(myconftable_type_tsfc):
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_tsfc_frame_max_size_send);
            my_config_nvs.tsfc.frame_max_size_send = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_tsfc_frame_max_size_rcv);
            my_config_nvs.tsfc.frame_max_size_rcv = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_tsfc_sendFail_max_repeat);
            my_config_nvs.tsfc.sendFail_max_repeat = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_tsfc_aesKey);
            if(p_item->p_data!=NULL)memcpy(my_config_nvs.tsfc.aesKey, p_item->p_data, sizeof(my_config_nvs.tsfc.aesKey));
            break;
        case(myconftable_type_net):
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_net_server_ipv4_address_type);
            if(p_item!=NULL)
            {
                my_config_nvs.net.conf_tcp1.address_type = p_item->data_uint64;
                #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
                printf("*******ok1\n");
                #endif
                memset(my_config_nvs.net.conf_tcp1.address, 0, sizeof(my_config_nvs.net.conf_tcp1.address));
                if(p_item->data_uint64==0)
                {
                    p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_net_server_ipv4_address);
                    if(p_item->p_data!=NULL)memcpy(my_config_nvs.net.conf_tcp1.address, p_item->p_data, strlen(p_item->p_data));
                    #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
                    printf("*******ok2\n");
                    #endif
                }
                else
                {
                    p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_net_server_ipv4_address);
                    if(p_item->p_data!=NULL)ipv4_int_to_str(my_config_nvs.net.conf_tcp1.address, p_item->p_data);
                    #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
                    printf("*******ok3\n");
                    #endif
                }
            }
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_net_server_port);
            my_config_nvs.net.conf_tcp1.port = p_item->data_uint64;
            
            // p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_net_server_ipv4_address);
            // if(p_item->p_data!=NULL)ipv4_int_to_str(my_config_nvs.net.conf_tcp2.address, p_item->p_data);
            // p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_net_server_port);
            // my_config_nvs.net.conf_tcp2.port = p_item->data_uint64;

            // p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_net_server_ipv4_address);
            // if(p_item->p_data!=NULL)ipv4_int_to_str(my_config_nvs.net.conf_tcp3.address, p_item->p_data);
            // p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_net_server_port);
            // my_config_nvs.net.conf_tcp3.port = p_item->data_uint64;

            // p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_net_server_ipv4_address);
            // if(p_item->p_data!=NULL)ipv4_int_to_str(my_config_nvs.net.conf_tcp4.address, p_item->p_data);
            // p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_net_server_port);
            // my_config_nvs.net.conf_tcp4.port = p_item->data_uint64;

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_net_server_ota_ipv4_address);
            if(p_item->p_data!=NULL)ipv4_int_to_str(my_config_nvs.net.conf_ota.address, p_item->p_data);
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_net_server_ota_port);
            my_config_nvs.net.conf_ota.port = p_item->data_uint64;
            break;
        case(myconftable_type_workmode):
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_curMode);
            my_config_nvs.workmode.conf.workmode = p_item->data_uint64;

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_debugmode);
            my_config_nvs.workmode.conf.debug_mode = p_item->data_uint64;

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_worktimeList_1_start_time);
            my_config_nvs.workmode.conf.workplan_list[0].work_start_time = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_worktimeList_1_keep_time);
            my_config_nvs.workmode.conf.workplan_list[0].work_end_time = p_item->data_uint64;

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_worktimeList_2_start_time);
            my_config_nvs.workmode.conf.workplan_list[1].work_start_time = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_worktimeList_2_keep_time);
            my_config_nvs.workmode.conf.workplan_list[1].work_end_time = p_item->data_uint64;

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_worktimeList_3_start_time);
            my_config_nvs.workmode.conf.workplan_list[2].work_start_time = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_worktimeList_3_keep_time);
            my_config_nvs.workmode.conf.workplan_list[2].work_end_time = p_item->data_uint64;

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_worktimeList_4_start_time);
            my_config_nvs.workmode.conf.workplan_list[3].work_start_time = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_worktimeList_4_keep_time);
            my_config_nvs.workmode.conf.workplan_list[3].work_end_time = p_item->data_uint64;

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_worktimeList_5_start_time);
            my_config_nvs.workmode.conf.workplan_list[4].work_start_time = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_worktimeList_5_keep_time);
            my_config_nvs.workmode.conf.workplan_list[4].work_end_time = p_item->data_uint64;

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_timermodeSleepTime);
            my_config_nvs.workmode.conf.workmode3_timer_sleep_keep_time = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_imageReportInterval);
            my_config_nvs.workmode.imageReportInterval = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_measReportInterval);
            my_config_nvs.workmode.measReportInterval = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_measReportmode);
            my_config_nvs.workmode.measReportMode = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_relay1_mode);
            my_config_nvs.workmode.relay1_mode = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_relay1_Xmode_keep_time);
            my_config_nvs.workmode.relay1_Xmode_keep_time = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_channeldoor_mode);
            my_config_nvs.workmode.channeldoor_mode = p_item->data_uint64;

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_vrc1_start_time);
            my_config_nvs.workmode.conf.vrc_plan_list[0].vrc_start_time = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_vrc1_keep_time);
            my_config_nvs.workmode.conf.vrc_plan_list[0].vrc_end_time = p_item->data_uint64;

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_vrc2_start_time);
            my_config_nvs.workmode.conf.vrc_plan_list[1].vrc_start_time = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_vrc2_keep_time);
            my_config_nvs.workmode.conf.vrc_plan_list[1].vrc_end_time = p_item->data_uint64;

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_vrc3_start_time);
            my_config_nvs.workmode.conf.vrc_plan_list[2].vrc_start_time = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_vrc3_keep_time);
            my_config_nvs.workmode.conf.vrc_plan_list[2].vrc_end_time = p_item->data_uint64;

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_vrc4_start_time);
            my_config_nvs.workmode.conf.vrc_plan_list[3].vrc_start_time = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_vrc4_keep_time);
            my_config_nvs.workmode.conf.vrc_plan_list[3].vrc_end_time = p_item->data_uint64;

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_vrc5_start_time);
            my_config_nvs.workmode.conf.vrc_plan_list[4].vrc_start_time = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_workmode_vrc5_keep_time);
            my_config_nvs.workmode.conf.vrc_plan_list[4].vrc_end_time = p_item->data_uint64;
            MyWorkmode_ReLoad_Conf(&my_config_nvs.workmode.conf);
            break;
        // #if MY_BLE_FUNC
        case(myconftable_type_ble):
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_ble_enable_state);
            my_config_nvs.ble.conf.enable = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_ble_advData_len);
            my_config_nvs.ble.conf.adv_custom_data_len = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_ble_name);
            if(p_item->p_data!=NULL)
            {
                memset(my_config_nvs.ble.conf.gatts_device_name, 0, sizeof(my_config_nvs.ble.conf.gatts_device_name));
                memcpy(my_config_nvs.ble.conf.gatts_device_name, p_item->p_data, sizeof(my_config_nvs.ble.conf.gatts_device_name)-1);
            }
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_ble_mac);
            if(p_item->p_data!=NULL)
            {
                memset(my_config_nvs.ble.conf.mac_str, 0, sizeof(my_config_nvs.ble.conf.mac_str));
                memcpy(my_config_nvs.ble.conf.mac_str, p_item->p_data, sizeof(my_config_nvs.ble.conf.mac_str)-1);
            }
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_ble_advType);
            my_config_nvs.ble.adv_type = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_ble_advData);
            if(p_item->p_data!=NULL)memcpy(my_config_nvs.ble.conf.adv_custom_data, p_item->p_data, sizeof(my_config_nvs.ble.conf.adv_custom_data));

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_ble_service_uuid_a);
            my_config_nvs.ble.conf.service_uuid_a = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_ble_char_uuid_a);
            my_config_nvs.ble.conf.char_uuid_a = p_item->data_uint64;

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_ble_service_uuid_b);
            my_config_nvs.ble.conf.service_uuid_b = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_ble_char_uuid_b);
            my_config_nvs.ble.conf.char_uuid_b = p_item->data_uint64;

            break;
        // #endif

        case(myconftable_type_power):
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_power_poweroff_alarm_enable_state);
            my_config_nvs.power.poweroff_alarm_enable_state = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_power_shutdown_after_poweroffAlarm);
            my_config_nvs.power.shutdown_after_poweroffAlarm = p_item->data_uint64;
            break;
        case(myconftable_type_factory):
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_factory_devstate);
            my_config_nvs.factory.dev_state = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_factory_factoryDate);
            if(p_item->p_data!=NULL)
            {
                memset(my_config_nvs.factory.factory_factoryDate, 0, sizeof(my_config_nvs.factory.factory_factoryDate));
                memcpy(my_config_nvs.factory.factory_factoryDate, p_item->p_data, sizeof(my_config_nvs.factory.factory_factoryDate)-1);
            }
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_factory_poweronCount);
            my_config_nvs.factory.poweron_count = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_factory_runtime_since_poweron);
            my_config_nvs.factory.runtime_since_poweron = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_factory_mac);
            if(p_item->p_data!=NULL)
            {
                memset(my_config_nvs.factory.mac, 0, sizeof(my_config_nvs.factory.mac));
                memcpy(my_config_nvs.factory.mac, p_item->p_data, sizeof(my_config_nvs.factory.mac));
            }
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_factory_imei);
            if(p_item->p_data!=NULL)
            {
                memset(my_config_nvs.factory.imei, 0, sizeof(my_config_nvs.factory.imei));
                memcpy(my_config_nvs.factory.imei, p_item->p_data, sizeof(my_config_nvs.factory.imei));
            }
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_factory_iccid);
            if(p_item->p_data!=NULL)
            {
                memset(my_config_nvs.factory.iccid, 0, sizeof(my_config_nvs.factory.iccid));
                memcpy(my_config_nvs.factory.iccid, p_item->p_data, sizeof(my_config_nvs.factory.iccid));
            }
            // p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_factory_verCode);
            // if(p_item->p_data!=NULL)
            // {
            //     memset(my_config_nvs.factory.verCode, 0, sizeof(my_config_nvs.factory.verCode));
            //     memcpy(my_config_nvs.factory.verCode, p_item->p_data, sizeof(my_config_nvs.factory.verCode));
            // }
            // p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_factory_verCode_Latest);
            // if(p_item->p_data!=NULL)
            // {
            //     memset(my_config_nvs.factory.verCode_latest, 0, sizeof(my_config_nvs.factory.verCode_latest));
            //     memcpy(my_config_nvs.factory.verCode_latest, p_item->p_data, sizeof(my_config_nvs.factory.verCode_latest));
            // }

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_factory_ota_vercode);
            my_config_nvs.factory.my_ota_info_local.vercode = p_item->data_uint64;

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_factory_ota_vername);
            if(p_item->p_data!=NULL)
            {
                memset(my_config_nvs.factory.my_ota_info_local.vername, 0, sizeof(my_config_nvs.factory.my_ota_info_local.vername));
                memcpy(my_config_nvs.factory.my_ota_info_local.vername, p_item->p_data, sizeof(my_config_nvs.factory.my_ota_info_local.vername));
            }
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_factory_ota_filename);
            if(p_item->p_data!=NULL)
            {
                memset(my_config_nvs.factory.my_ota_info_local.filename, 0, sizeof(my_config_nvs.factory.my_ota_info_local.filename));
                memcpy(my_config_nvs.factory.my_ota_info_local.filename, p_item->p_data, sizeof(my_config_nvs.factory.my_ota_info_local.filename));
            }

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_factory_ota_check_type);
            my_config_nvs.factory.ota_check_type = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_factory_devType);
            my_config_nvs.factory.devType = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_factory_devModel);
            if(p_item->p_data!=NULL)
            {
                memset(my_config_nvs.factory.devModel, 0, sizeof(my_config_nvs.factory.devModel));
                memcpy(my_config_nvs.factory.devModel, p_item->p_data, sizeof(my_config_nvs.factory.devModel));
            }
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_factory_devId);
            if(p_item->p_data!=NULL)memcpy(my_config_nvs.factory.devId, p_item->p_data, sizeof(my_config_nvs.factory.devId));

            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_factory_httpota_url);
            if(p_item->p_data!=NULL)
            {
                memset(my_config_nvs.factory.http_ota_url, 0, sizeof(my_config_nvs.factory.http_ota_url));
                memcpy(my_config_nvs.factory.http_ota_url, p_item->p_data, sizeof(my_config_nvs.factory.http_ota_url));
            }

            break;
        case(myconftable_type_wifi):
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_wifi_enable_state);
            my_config_nvs.wifi.conf.enable = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_wifi_mode);
            my_config_nvs.wifi.conf.mode = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_wifi_station_ssid);
            if(p_item->p_data!=NULL)
            {
                memset(my_config_nvs.wifi.conf.sta_ssid, 0, sizeof(my_config_nvs.wifi.conf.sta_ssid));
                memcpy(my_config_nvs.wifi.conf.sta_ssid, p_item->p_data, sizeof(my_config_nvs.wifi.conf.sta_ssid)-1);
            }
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_wifi_station_password);
            if(p_item->p_data!=NULL)
            {
                memset(my_config_nvs.wifi.conf.sta_password, 0, sizeof(my_config_nvs.wifi.conf.sta_password));
                memcpy(my_config_nvs.wifi.conf.sta_password, p_item->p_data, sizeof(my_config_nvs.wifi.conf.sta_password)-1);
            }
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_wifi_ap_ssid);
            if(p_item->p_data!=NULL)
            {
                memset(my_config_nvs.wifi.conf.ap_ssid, 0, sizeof(my_config_nvs.wifi.conf.ap_ssid));
                memcpy(my_config_nvs.wifi.conf.ap_ssid, p_item->p_data, sizeof(my_config_nvs.wifi.conf.ap_ssid)-1);
            }
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_wifi_ap_password);
            if(p_item->p_data!=NULL)
            {
                memset(my_config_nvs.wifi.conf.ap_password, 0, sizeof(my_config_nvs.wifi.conf.ap_password));
                memcpy(my_config_nvs.wifi.conf.ap_password, p_item->p_data, sizeof(my_config_nvs.wifi.conf.ap_password)-1);
            }
            break;

            case(myconftable_type_lte):
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_lte_baudrate);
            my_config_nvs.lte.baudrate = p_item->data_uint64;
            break;

            case(myconftable_type_gnss):
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_gnss_auto_report_switch);
            my_config_nvs.gnss.auto_report_switch = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_gnss_auto_report_interval);
            my_config_nvs.gnss.auto_report_interval = p_item->data_uint64;
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_gnss_longitude);
            if(p_item->p_data!=NULL)
            {
                memset(my_config_nvs.gnss.longitude, 0, sizeof(my_config_nvs.gnss.longitude));
                memcpy(my_config_nvs.gnss.longitude, p_item->p_data, sizeof(my_config_nvs.gnss.longitude)-1);
            }
            p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[type], devPI_gnss_latitude);
            if(p_item->p_data!=NULL)
            {
                memset(my_config_nvs.gnss.latitude, 0, sizeof(my_config_nvs.gnss.latitude));
                memcpy(my_config_nvs.gnss.latitude, p_item->p_data, sizeof(my_config_nvs.gnss.latitude)-1);
            }
            break;

        default:break;
    }

    return 0;
}

#define MyConfig_DebugPrint_my_config_nvs_t_filter  (2)
int MyConfig_DebugPrint_my_config_nvs_t(my_config_nvs_t* p, int filter, char* tag)
{
    if(p==NULL)
    {
        return -1;
    }

    if(filter>=MyConfig_DebugPrint_my_config_nvs_t_filter)
    {
        if(tag!=NULL)
        {
            printf("\n\nSoPL------------------------------------------------------------\ntag = %s\n", tag);
        }
        else
        {
            printf("\n\nSoPL------------------------------------------------------------\nunknow tag\n");
        }
        printf("MyConfig_DebugPrint_my_config_nvs_t():sizeof(my_config_nvs_t)=%d\n", sizeof(my_config_nvs_t));

        //asl
        printf("\n--asl:\n");
        printf("p->asl.appid=%#04x\n", p->asl.appid);
        printf("p->asl.loginflag=%#x\n", p->asl.loginflag);
        printf("p->asl.dic=%#08x\n", p->asl.dic);
        printf("p->asl.token=%#08x\n", p->asl.token);

        struct tm* tminfo1;
        time_t nowtime = p->asl.last_sync_param_timestamp;
        tminfo1 = localtime( &nowtime );
        char buffer[80];
        strftime(buffer,80,"%Y-%m-%e %H:%M:%S", tminfo1);//以年月日_时分秒的形式表示当前时间
        printf("p->asl.last_sync_param_timestamp=%#08x(%d, %s)\n", p->asl.last_sync_param_timestamp, p->asl.last_sync_param_timestamp, buffer);
        printf("p->asl.sync_param_effect_time=%#08x(%d)\n", p->asl.sync_param_effect_time, p->asl.sync_param_effect_time);
        printf("\n");

        //tsfc
        printf("\n--tsfc:\n");
        printf("p->tsfc.frame_max_size_send=%#x(%d)\n", p->tsfc.frame_max_size_send, p->tsfc.frame_max_size_send);
        printf("p->tsfc.frame_max_size_rcv=%#x(%d)\n", p->tsfc.frame_max_size_rcv, p->tsfc.frame_max_size_rcv);
        printf("p->tsfc.sendFail_max_repeat=%#x(%d)\n", p->tsfc.sendFail_max_repeat, p->tsfc.sendFail_max_repeat);
        printf("p->tsfc.aesKey=");
        for(int i=0; i<sizeof(p->tsfc.aesKey); i++)
        {
            printf("%02X ", p->tsfc.aesKey[i]);
        }
        printf("\n");

        //net
        printf("\n\n--net:\n");
        printf("p->net.conf_tcp1.address=");
        for(int i=0; i<sizeof(p->net.conf_tcp1.address); i++)
        {
            printf("%02X ", p->net.conf_tcp1.address[i]);
        }
        printf("(%s)", p->net.conf_tcp1.address);
        printf("\n");
        printf("p->net.conf_tcp1.address_type=%#x(%d)\n", p->net.conf_tcp1.address_type, p->net.conf_tcp1.address_type);
        printf("p->net.conf_tcp1.port=%#x(%d)\n", p->net.conf_tcp1.port, p->net.conf_tcp1.port);

        printf("p->net.conf_tcp2.address=");
        for(int i=0; i<sizeof(p->net.conf_tcp2.address); i++)
        {
            printf("%02X ", p->net.conf_tcp2.address[i]);
        }
        printf("(%s)", p->net.conf_tcp2.address);
        printf("\n");
        printf("p->net.conf_tcp2.address_type=%#x(%d)\n", p->net.conf_tcp2.address_type, p->net.conf_tcp2.address_type);
        printf("p->net.conf_tcp2.port=%#x(%d)\n", p->net.conf_tcp2.port, p->net.conf_tcp2.port);

        printf("p->net.conf_tcp3.address=");
        for(int i=0; i<sizeof(p->net.conf_tcp3.address); i++)
        {
            printf("%02X ", p->net.conf_tcp3.address[i]);
        }
        printf("(%s)", p->net.conf_tcp3.address);
        printf("\n");
        printf("p->net.conf_tcp3.address_type=%#x(%d)\n", p->net.conf_tcp3.address_type, p->net.conf_tcp3.address_type);
        printf("p->net.conf_tcp3.port=%#x(%d)\n", p->net.conf_tcp3.port, p->net.conf_tcp3.port);

        printf("p->net.conf_tcp4.address=");
        for(int i=0; i<sizeof(p->net.conf_tcp4.address); i++)
        {
            printf("%02X ", p->net.conf_tcp4.address[i]);
        }
        printf("(%s)", p->net.conf_tcp4.address);
        printf("\n");
        printf("p->net.conf_tcp4.address_type=%#x(%d)\n", p->net.conf_tcp4.address_type, p->net.conf_tcp4.address_type);
        printf("p->net.conf_tcp4.port=%#x(%d)\n", p->net.conf_tcp4.port, p->net.conf_tcp4.port);

        printf("p->net.conf_ota.address=");
        for(int i=0; i<sizeof(p->net.conf_ota.address); i++)
        {
            printf("%02X ", p->net.conf_ota.address[i]);
        }
        printf("(%s)", p->net.conf_ota.address);
        printf("\n");
        printf("p->net.conf_ota.address_type=%#x(%d)\n", p->net.conf_ota.address_type, p->net.conf_ota.address_type);
        printf("p->net.conf_ota.port=%#x(%d)\n", p->net.conf_ota.port, p->net.conf_ota.port);
        printf("\n");

        //workmode
        printf("\n--workmode:\n");
        printf("p->workmode.conf.workmode=%d\n", p->workmode.conf.workmode);
        printf("p->workmode.conf.debug_mode=%d\n", p->workmode.conf.debug_mode);
        printf("p->workmode.conf.workplan_list[0].work_start_time=%d\n", p->workmode.conf.workplan_list[0].work_start_time);
        printf("p->workmode.conf.workplan_list[0].work_end_time=%d\n", p->workmode.conf.workplan_list[0].work_end_time);
        printf("p->workmode.conf.workplan_list[1].work_start_time=%d\n", p->workmode.conf.workplan_list[1].work_start_time);
        printf("p->workmode.conf.workplan_list[1].work_end_time=%d\n", p->workmode.conf.workplan_list[1].work_end_time);
        printf("p->workmode.conf.workplan_list[2].work_start_time=%d\n", p->workmode.conf.workplan_list[2].work_start_time);
        printf("p->workmode.conf.workplan_list[2].work_end_time=%d\n", p->workmode.conf.workplan_list[2].work_end_time);
        printf("p->workmode.conf.workplan_list[3].work_start_time=%d\n", p->workmode.conf.workplan_list[3].work_start_time);
        printf("p->workmode.conf.workplan_list[3].work_end_time=%d\n", p->workmode.conf.workplan_list[3].work_end_time);
        printf("p->workmode.conf.workplan_list[4].work_start_time=%d\n", p->workmode.conf.workplan_list[4].work_start_time);
        printf("p->workmode.conf.workplan_list[4].work_end_time=%d\n", p->workmode.conf.workplan_list[4].work_end_time);
        printf("p->workmode.conf.workmode3_timer_sleep_keep_time=%d\n", p->workmode.conf.workmode3_timer_sleep_keep_time);
        printf("p->workmode.imageReportInterval=%d\n", p->workmode.imageReportInterval);
        printf("p->workmode.measReportInterval=%d\n", p->workmode.measReportInterval);
        printf("p->workmode.measReportMode=%d\n", p->workmode.measReportMode);
        printf("p->workmode.relay1_mode=%d\n", p->workmode.relay1_mode);
        printf("p->workmode.relay1_Xmode_keep_time=%d\n", p->workmode.relay1_Xmode_keep_time);
        printf("p->workmode.channeldoor_mode=%d\n", p->workmode.channeldoor_mode);
        printf("p->workmode.conf.vrc_plan_list[0].vrc_start_time=%d\n", p->workmode.conf.vrc_plan_list[0].vrc_start_time);
        printf("p->workmode.conf.vrc_plan_list[0].vrc_end_time=%d\n", p->workmode.conf.vrc_plan_list[0].vrc_end_time);
        printf("p->workmode.conf.vrc_plan_list[1].vrc_start_time=%d\n", p->workmode.conf.vrc_plan_list[1].vrc_start_time);
        printf("p->workmode.conf.vrc_plan_list[1].vrc_end_time=%d\n", p->workmode.conf.vrc_plan_list[1].vrc_end_time);
        printf("p->workmode.conf.vrc_plan_list[2].vrc_start_time=%d\n", p->workmode.conf.vrc_plan_list[2].vrc_start_time);
        printf("p->workmode.conf.vrc_plan_list[2].vrc_end_time=%d\n", p->workmode.conf.vrc_plan_list[2].vrc_end_time);
        printf("p->workmode.conf.vrc_plan_list[3].vrc_start_time=%d\n", p->workmode.conf.vrc_plan_list[3].vrc_start_time);
        printf("p->workmode.conf.vrc_plan_list[3].vrc_end_time=%d\n", p->workmode.conf.vrc_plan_list[3].vrc_end_time);
        printf("p->workmode.conf.vrc_plan_list[4].vrc_start_time=%d\n", p->workmode.conf.vrc_plan_list[4].vrc_start_time);
        printf("p->workmode.conf.vrc_plan_list[4].vrc_end_time=%d\n", p->workmode.conf.vrc_plan_list[4].vrc_end_time);
        printf("\n");

        // #if MY_BLE_FUNC
        //ble
        printf("\n--ble:\n");
        printf("p->ble.conf.enable=%d\n", p->ble.conf.enable);
        printf("p->ble.adv_type=%d\n", p->ble.adv_type);
        printf("p->ble.conf.adv_custom_data_len=%d\n", p->ble.conf.adv_custom_data_len);
        printf("p->ble.conf.adv_custom_data=");
        for(int i=0; i<sizeof(p->ble.conf.adv_custom_data); i++)
        {
            printf("%02X ", p->ble.conf.adv_custom_data[i]);
        }
        printf("\n");

        printf("p->ble.conf.service_uuid_a=0x%04X\n", p->ble.conf.service_uuid_a);
        printf("p->ble.conf.char_uuid_a=0x%04X\n", p->ble.conf.char_uuid_a);
        printf("p->ble.conf.service_uuid_b=0x%04X\n", p->ble.conf.service_uuid_b);
        printf("p->ble.conf.char_uuid_b=0x%04X\n", p->ble.conf.char_uuid_b);

        printf("p->ble.conf.gatts_device_name=%s\n", p->ble.conf.gatts_device_name);
        printf("p->ble.conf.mac_str=%s\n", p->ble.conf.mac_str);
        printf("\n");
        // #endif

        //power
        printf("\n--power:\n");
        printf("p->power.poweroff_alarm_enable_state=%d\n", p->power.poweroff_alarm_enable_state);
        printf("p->power.shutdown_after_poweroffAlarm=%d\n", p->power.shutdown_after_poweroffAlarm);
        printf("\n");

        //factory
        printf("\n--factory:\n");
        printf("p->factory.dev_state=%d\n", p->factory.dev_state);
        printf("p->factory.factory_factoryDate=");
        for(int i=0; i<sizeof(p->factory.factory_factoryDate); i++)
        {
            printf("%02X ", p->factory.factory_factoryDate[i]);
        }
        printf("(%s)\n", p->factory.factory_factoryDate);
        printf("p->factory.poweron_count=%#x(%d)\n", p->factory.poweron_count, p->factory.poweron_count);
        printf("p->factory.runtime_since_poweron=%#x(%d)\n", p->factory.runtime_since_poweron, p->factory.runtime_since_poweron);
        printf("p->factory.mac=");
        for(int i=0; i<sizeof(p->factory.mac); i++)
        {
            printf("%02X", p->factory.mac[i]);
            if(i!=sizeof(p->factory.mac)-1)
            {
                printf("-");
            }
        }
        printf("\n");
        printf("p->factory.imei=");
        for(int i=0; i<sizeof(p->factory.imei); i++)
        {
            printf("%02X ", p->factory.imei[i]);
        }
        printf("(%s)\n", p->factory.imei);
        printf("p->factory.iccid=");
        for(int i=0; i<sizeof(p->factory.iccid); i++)
        {
            printf("%02X ", p->factory.iccid[i]);
        }
        printf("(%s)\n", p->factory.iccid);
        printf("p->factory.my_ota_info_local.vercode=%#x(%d)\n", p->factory.my_ota_info_local.vercode, p->factory.my_ota_info_local.vercode);
        printf("p->factory.my_ota_info_local.vername=");
        for(int i=0; i<sizeof(p->factory.my_ota_info_local.vername); i++)
        {
            printf("%02X ", p->factory.my_ota_info_local.vername[i]);
        }
        printf("(%s)\n", p->factory.my_ota_info_local.vername);
        printf("p->factory.my_ota_info_local.filename=");
        for(int i=0; i<sizeof(p->factory.my_ota_info_local.filename); i++)
        {
            printf("%02X ", p->factory.my_ota_info_local.filename[i]);
        }
        printf("(%s)\n", p->factory.my_ota_info_local.filename);

        printf("p->factory.my_ota_info_net.vercode=%#x(%d)\n", p->factory.my_ota_info_net.vercode, p->factory.my_ota_info_net.vercode);
        printf("p->factory.my_ota_info_net.vername=");
        for(int i=0; i<sizeof(p->factory.my_ota_info_net.vername); i++)
        {
            printf("%02X ", p->factory.my_ota_info_net.vername[i]);
        }
        printf("(%s)\n", p->factory.my_ota_info_net.vername);
        printf("p->factory.my_ota_info_net.filename=");
        for(int i=0; i<sizeof(p->factory.my_ota_info_net.filename); i++)
        {
            printf("%02X ", p->factory.my_ota_info_net.filename[i]);
        }
        printf("(%s)\n", p->factory.my_ota_info_net.filename);

        printf("p->factory.ota_check_type=%#x(%d)\n", p->factory.ota_check_type, p->factory.ota_check_type);
        printf("p->factory.devType=%#x(%d)\n", p->factory.devType, p->factory.devType);
        printf("p->factory.devModel=");
        for(int i=0; i<sizeof(p->factory.devModel); i++)
        {
            printf("%02X ", p->factory.devModel[i]);
        }
        printf("(%s)\n", p->factory.devModel);
        printf("p->factory.devId=");
        for(int i=0; i<sizeof(p->factory.devId); i++)
        {
            printf("%02X ", p->factory.devId[i]);
        }
        printf("(%s)\n", p->factory.devId);
        printf("p->factory.http_ota_url=");
        for(int i=0; i<sizeof(p->factory.http_ota_url); i++)
        {
            printf("%02X ", p->factory.http_ota_url[i]);
        }
        printf("(%s)\n", p->factory.http_ota_url);
        printf("\n");

        //wifi
        printf("--wifi:\n");
        printf("p->wifi.conf.enable=%d\n", p->wifi.conf.enable);
        printf("p->wifi.conf.mode=%d\n", p->wifi.conf.mode);
        printf("p->wifi.conf.sta_ssid=");
        for(int i=0; i<sizeof(p->wifi.conf.sta_ssid); i++)
        {
            printf("%02X ", p->wifi.conf.sta_ssid[i]);
        }
        printf("(%s)\n", p->wifi.conf.sta_ssid);
        printf("p->wifi.conf.sta_password=");
        for(int i=0; i<sizeof(p->wifi.conf.sta_password); i++)
        {
            printf("%02X ", p->wifi.conf.sta_password[i]);
        }
        printf("(%s)\n", p->wifi.conf.sta_password);
        printf("p->wifi.conf.ap_ssid=");
        for(int i=0; i<sizeof(p->wifi.conf.ap_ssid); i++)
        {
            printf("%02X ", p->wifi.conf.ap_ssid[i]);
        }
        printf("(%s)\n", p->wifi.conf.ap_ssid);
        printf("p->wifi.conf.ap_password=");
        for(int i=0; i<sizeof(p->wifi.conf.ap_password); i++)
        {
            printf("%02X ", p->wifi.conf.ap_password[i]);
        }
        printf("(%s)\n", p->wifi.conf.ap_password);
        printf("\n");

        //lte
        printf("\n--lte:\n");
        printf("p->lte.baudrate=%d\n", p->lte.baudrate);
        printf("\n");

        //gnss
        printf("\n--gnss:\n");
        printf("p->gnss.auto_report_switch=%d\n", p->gnss.auto_report_switch);
        printf("p->gnss.auto_report_interval=%d\n", p->gnss.auto_report_interval);
        printf("\n");
        printf("p->gnss.longitude=");
        for(int i=0; i<sizeof(p->gnss.longitude); i++)
        {
            printf("%02X ", p->gnss.longitude[i]);
        }
        printf("(%s)", p->gnss.longitude);
        printf("\n");
        printf("p->gnss.latitude=");
        for(int i=0; i<sizeof(p->gnss.latitude); i++)
        {
            printf("%02X ", p->gnss.latitude[i]);
        }
        printf("(%s)", p->gnss.latitude);
        printf("\n");

        if(tag!=NULL)
        {
            printf("\ntag = %s\nEoPL------------------------------------------------------------\n\n", tag);
        }
        else
        {
            printf("\nunknow tag\nEoPL------------------------------------------------------------\n\n");
        }
    }
    
    return 0;
}

int MyConfig_DebugPrint_my_config_nvs_t_All(char* tag)
{
    return MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 2, tag);
}

int MyConfig_AddTableToList(my_nvs_d2link_list_t* p_list, my_config_table_t* table, int table_item_num)
{
    if(p_list==NULL)
    {
        return -1;
    }
    if(table==NULL)
    {
        return -2;
    }
    
    int i=0;
    int err = 0;
    int err_count = 0;
    for(i=0; i<table_item_num; i++, table++)
    {
        err = MyNvs_D2Link_Append_Item(p_list, table->item_id, table->saving_to_nvs, table->form_name, table->key_name, table->p_data, table->p_data_type, table->data_uint64, table->data_len, table->item_name);
        if(err)
        {
            err_count++;
            ESP_LOGE("MyConfig_AddTableToList()", "err=%d", err);
        }
    }
    return err_count;
}

#define my_conf_table_item_num(_my_config_table) (sizeof(_my_config_table)/sizeof(my_config_table_t))

my_config_nvs_t* MyConfig_LoadFromNVS(void)
{
    if(!conf_list_already)
    {
        conf_list_already = 1;
        #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
        printf("\n\n\n\n\n\n\n\n\n\n\n\n\n\nMyConfig_LoadFromNVS():MyConfig_AddTableToList...\n\n\n\n\n\n");
        #endif
        myconf_table_list_array[myconftable_type_asl] = MyNvs_D2Link_Creat(myconftable_type_asl, "myconftable_type_asl");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_asl], my_conf_table_asl_default, my_conf_table_item_num(my_conf_table_asl_default));
        myconf_table_list_array[myconftable_type_tsfc] = MyNvs_D2Link_Creat(myconftable_type_tsfc, "myconftable_type_tsfc");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_tsfc], my_conf_table_tsfc_default, my_conf_table_item_num(my_conf_table_tsfc_default));
        myconf_table_list_array[myconftable_type_net] = MyNvs_D2Link_Creat(myconftable_type_net, "myconftable_type_net");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_net], my_conf_table_net_default, my_conf_table_item_num(my_conf_table_net_default));
        myconf_table_list_array[myconftable_type_workmode] = MyNvs_D2Link_Creat(myconftable_type_workmode, "myconftable_type_workmode");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_workmode], my_conf_table_workmode_default, my_conf_table_item_num(my_conf_table_workmode_default));
        // #if MY_BLE_FUNC
        myconf_table_list_array[myconftable_type_ble] = MyNvs_D2Link_Creat(myconftable_type_ble, "myconftable_type_ble");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_ble], my_conf_table_ble_default, my_conf_table_item_num(my_conf_table_ble_default));
        // #endif
        myconf_table_list_array[myconftable_type_power] = MyNvs_D2Link_Creat(myconftable_type_power, "myconftable_type_power");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_power], my_conf_table_power_default, my_conf_table_item_num(my_conf_table_power_default));
        myconf_table_list_array[myconftable_type_factory] = MyNvs_D2Link_Creat(myconftable_type_factory, "myconftable_type_factory");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_factory], my_conf_table_factory_default, my_conf_table_item_num(my_conf_table_factory_default));
        myconf_table_list_array[myconftable_type_wifi] = MyNvs_D2Link_Creat(myconftable_type_wifi, "myconftable_type_wifi");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_wifi], my_conf_table_wifi_default, my_conf_table_item_num(my_conf_table_wifi_default));
        myconf_table_list_array[myconftable_type_lte] = MyNvs_D2Link_Creat(myconftable_type_lte, "myconftable_type_lte");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_lte], my_conf_table_lte_default, my_conf_table_item_num(my_conf_table_lte_default));
        myconf_table_list_array[myconftable_type_gnss] = MyNvs_D2Link_Creat(myconftable_type_gnss, "myconftable_type_gnss");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_gnss], my_conf_table_gnss_default, my_conf_table_item_num(my_conf_table_gnss_default));
    }
    
    #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
    printf("\n\n\n\n\n\n\n\n\n\n\n\n\n\nMyConfig_LoadFromNVS():MyNvs_D2Link_PrintList...\n\n\n\n\n\n");
    for(int i=0; i<myconftable_type_max; i++)
    {
        MyNvs_D2Link_PrintList(myconf_table_list_array[i]);
    }
    #endif

    #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
    printf("\n\n\n\n\n\n\n\n\n\n\n\n\n\nMyConfig_LoadFromNVS():Load_KVP_List_FromNvs...\n\n\n\n\n\n");
    #endif
    for(int i=0; i<myconftable_type_max; i++)
    {
        Load_KVP_List_FromNvs(myconf_table_list_array[i]);
    }

    #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
    printf("\n\n\n\n\n\n\n\n\n\n\n\n\n\nMyConfig_LoadFromNVS():MyNvs_D2Link_PrintList...\n\n\n\n\n\n");
    for(int i=0; i<myconftable_type_max; i++)
    {
        MyNvs_D2Link_PrintList(myconf_table_list_array[i]);
    }
    #endif

    #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
    printf("\n\n\n\n\n\n\n\n\n\n\n\n\n\nMyConfig_LoadFromNVS():MyConfig_GetConfTable_Format...\n\n\n\n\n\n");
    #endif
    for(int i=0; i<myconftable_type_max; i++)
    {
        MyConfig_GetConfTable_Format(i);
    }

    #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
    printf("\n\n\n\n\n\n\n\n\n\n\n\n\n\nMyConfig_LoadFromNVS():MyConfig_DebugPrint_my_config_nvs_t...\n\n\n\n\n\n");
    #endif

    #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 2, "my_config.c: MyConfig_LoadFromNVS()");
    #endif

    return MyConfig_GetGCT_Cur_p();
}

my_config_nvs_t* MyConfig_ResetAllConf_To_Default(void)
{
    if(conf_list_already)
    {
        conf_list_already = 0;
        #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
        printf("\n\n\n\n\n\n\n\n\n\n\n\n\n\nMyConfig_ResetAllConf_To_Default():MyNvs_D2Link_Delete...\n\n\n\n\n\n");
        #endif
        for(int i=0; i<myconftable_type_max; i++)
        {
            MyNvs_D2Link_Delete(&myconf_table_list_array[i]);
        }
    }

    if(!conf_list_already)
    {
        conf_list_already = 1;
        #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
        printf("\n\n\n\n\n\n\n\n\n\n\n\n\n\nMyConfig_ResetAllConf_To_Default():MyConfig_AddTableToList...\n\n\n\n\n\n");
        #endif
        myconf_table_list_array[myconftable_type_asl] = MyNvs_D2Link_Creat(myconftable_type_asl, "myconftable_type_asl");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_asl], my_conf_table_asl_default, my_conf_table_item_num(my_conf_table_asl_default));
        myconf_table_list_array[myconftable_type_tsfc] = MyNvs_D2Link_Creat(myconftable_type_tsfc, "myconftable_type_tsfc");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_tsfc], my_conf_table_tsfc_default, my_conf_table_item_num(my_conf_table_tsfc_default));
        myconf_table_list_array[myconftable_type_net] = MyNvs_D2Link_Creat(myconftable_type_net, "myconftable_type_net");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_net], my_conf_table_net_default, my_conf_table_item_num(my_conf_table_net_default));
        myconf_table_list_array[myconftable_type_workmode] = MyNvs_D2Link_Creat(myconftable_type_workmode, "myconftable_type_workmode");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_workmode], my_conf_table_workmode_default, my_conf_table_item_num(my_conf_table_workmode_default));
        // #if MY_BLE_FUNC
        myconf_table_list_array[myconftable_type_ble] = MyNvs_D2Link_Creat(myconftable_type_ble, "myconftable_type_ble");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_ble], my_conf_table_ble_default, my_conf_table_item_num(my_conf_table_ble_default));
        // #endif
        myconf_table_list_array[myconftable_type_power] = MyNvs_D2Link_Creat(myconftable_type_power, "myconftable_type_power");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_power], my_conf_table_power_default, my_conf_table_item_num(my_conf_table_power_default));
        myconf_table_list_array[myconftable_type_factory] = MyNvs_D2Link_Creat(myconftable_type_factory, "myconftable_type_factory");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_factory], my_conf_table_factory_default, my_conf_table_item_num(my_conf_table_factory_default));
        myconf_table_list_array[myconftable_type_wifi] = MyNvs_D2Link_Creat(myconftable_type_wifi, "myconftable_type_wifi");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_wifi], my_conf_table_wifi_default, my_conf_table_item_num(my_conf_table_wifi_default));
        myconf_table_list_array[myconftable_type_lte] = MyNvs_D2Link_Creat(myconftable_type_lte, "myconftable_type_lte");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_lte], my_conf_table_lte_default, my_conf_table_item_num(my_conf_table_lte_default));
        myconf_table_list_array[myconftable_type_gnss] = MyNvs_D2Link_Creat(myconftable_type_gnss, "myconftable_type_gnss");
        MyConfig_AddTableToList(myconf_table_list_array[myconftable_type_gnss], my_conf_table_gnss_default, my_conf_table_item_num(my_conf_table_gnss_default));
    }
    
    #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
    printf("\n\n\n\n\n\n\n\n\n\n\n\n\n\nMyConfig_ResetAllConf_To_Default():MyNvs_D2Link_PrintList...\n\n\n\n\n\n");
    for(int i=0; i<myconftable_type_max; i++)
    {
        MyNvs_D2Link_PrintList(myconf_table_list_array[i]);
    }
    #endif

    #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
    printf("\n\n\n\n\n\n\n\n\n\n\n\n\n\nMyConfig_ResetAllConf_To_Default():Load_KVP_List_FromNvs...\n\n\n\n\n\n");
    #endif

    My_nvs_flash_erase();

    for(int i=0; i<myconftable_type_max; i++)
    {
        Save_KVP_List_To_Nvs(myconf_table_list_array[i]);
    }

    #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
    printf("\n\n\n\n\n\n\n\n\n\n\n\n\n\nMyConfig_ResetAllConf_To_Default():MyNvs_D2Link_PrintList...\n\n\n\n\n\n");
    for(int i=0; i<myconftable_type_max; i++)
    {
        MyNvs_D2Link_PrintList(myconf_table_list_array[i]);
    }
    #endif

    #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
    printf("\n\n\n\n\n\n\n\n\n\n\n\n\n\nMyConfig_ResetAllConf_To_Default():MyConfig_GetConfTable_Format...\n\n\n\n\n\n");
    #endif
    my_config_nvs_t* p_my_config_nvs_temp = calloc(1, sizeof(my_config_nvs_t));
    memcpy(p_my_config_nvs_temp, &my_config_nvs, sizeof(my_config_nvs_t));
    for(int i=0; i<myconftable_type_max; i++)
    {
        MyConfig_GetConfTable_Format(i);
    }
    if(p_my_config_nvs_temp!=NULL)
    {
        MyConfig_Modify_factory_verCode_local(p_my_config_nvs_temp->factory.my_ota_info_local.vercode, 1);
        MyConfig_Modify_factory_vername_local(p_my_config_nvs_temp->factory.my_ota_info_local.vername, strlen(p_my_config_nvs_temp->factory.my_ota_info_local.vername), 1);
        MyConfig_Modify_factory_verfilename_local(p_my_config_nvs_temp->factory.my_ota_info_local.filename, strlen(p_my_config_nvs_temp->factory.my_ota_info_local.filename), 1);
        MyConfig_Modify_factory_factoryDate(p_my_config_nvs_temp->factory.factory_factoryDate, strlen(p_my_config_nvs_temp->factory.factory_factoryDate), 1);
        MyConfig_Modify_factory_poweron_count(p_my_config_nvs_temp->factory.poweron_count, 1);
        
        free(p_my_config_nvs_temp);
    }

    #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
    printf("\n\n\n\n\n\n\n\n\n\n\n\n\n\nMyConfig_ResetAllConf_To_Default():MyConfig_DebugPrint_my_config_nvs_t...\n\n\n\n\n\n");
    #endif
    #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 2, "my_config.c: MyConfig_ResetAllConf_To_Default()");
    #endif

    #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    ESP_LOGI("MyConfig_ResetAllConf_To_Default()", "reboot");
    #endif
    // My_esp_restart();
    
    return MyConfig_GetGCT_Cur_p();
}

my_config_nvs_t* MyConfig_GetGCT_Cur_p(void)
{
    return &my_config_nvs;
}

int MyConfig_GetGCT(my_config_nvs_t* saving)
{
    if(saving!=NULL)
    {
        memcpy(saving, &my_config_nvs, sizeof(my_config_nvs_t));
        return 0;
    }
    return -1;
}

int MyConfig_GetConfItem_Modifiable(int param_id)
{
    int i = 0;
    for(i=0; i<myconftable_type_max; i++)
    {
        if(MyNvs_D2Link_CheckItemExistInList_Id(myconf_table_list_array[i], param_id))
        {
            break;
        }
    }
    if(i>=myconftable_type_max)
    {
        ESP_LOGE("MyConfig_GetConfItem_Modifiable()", "param_id not find in all param lists");
        return 0;
    }
    
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[i], param_id);

    if(p_item!=NULL)
    {
        if(p_item->saving_to_nvs==save_to_nvs)
        {
            return 1;
        }
    }
    else
    {
        ESP_LOGE("MyConfig_GetConfItem_Modifiable()", "p_item!=NULL is false");
    }
    return 0;
}

int MyConfig_SavingAllConf_To_NVS(void)
{
    #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
    // printf("\n\n\n\n\n\n\n\n\n\n--MyConfig_SavingAllConf_To_NVS():saving all conf to nvs\n");
    #endif

    for(int i=0; i<myconftable_type_max; i++)
    {
        Save_KVP_List_To_Nvs(myconf_table_list_array[i]);
    }
    #if DEBUG_PRINT_LEVEL_my_config_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
    // printf("\n\n--END OF MyConfig_SavingAllConf_To_NVS():saving all conf to nvs\n\n\n\n\n\n\n\n\n\n\n");
    #endif

    return 0;
}

int MyConfig_Modify_Asl_appId(uint16_t new_value, uint8_t saving_to_nvs)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_asl], devPI_asl_appId);

    p_item->data_uint64 = new_value;

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_asl], devPI_asl_appId);
    }
    MyConfig_GetConfTable_Format(myconftable_type_asl);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Asl_loginFlag(uint8_t new_value, uint8_t saving_to_nvs)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_asl], devPI_asl_loginFlag);

    p_item->data_uint64 = new_value;

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_asl], devPI_asl_loginFlag);
    }
    MyConfig_GetConfTable_Format(myconftable_type_asl);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Asl_dic(uint32_t new_value, uint8_t saving_to_nvs)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_asl], devPI_asl_dic);

    p_item->data_uint64 = new_value;

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_asl], devPI_asl_dic);
    }
    MyConfig_GetConfTable_Format(myconftable_type_asl);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Asl_token(uint32_t new_value, uint8_t saving_to_nvs)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_asl], devPI_asl_token);

    p_item->data_uint64 = new_value;

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_asl], devPI_asl_token);
    }
    MyConfig_GetConfTable_Format(myconftable_type_asl);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Asl_last_sync_param_timestamp(uint32_t new_value, uint8_t saving_to_nvs)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_asl], devPI_asl_last_sync_param_timestamp);

    p_item->data_uint64 = new_value;

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_asl], devPI_asl_last_sync_param_timestamp);
    }
    MyConfig_GetConfTable_Format(myconftable_type_asl);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Asl_sync_param_effect_time(uint32_t new_value, uint8_t saving_to_nvs)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_asl], devPI_asl_sync_param_effect_time);

    p_item->data_uint64 = new_value;

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_asl], devPI_asl_sync_param_effect_time);
    }
    MyConfig_GetConfTable_Format(myconftable_type_asl);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Tsfc_aesKey(uint8_t* new_data, uint16_t new_data_len, uint8_t saving_to_nvs)
{
    if(new_data==NULL)
    {
        return -1;
    }

    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_tsfc], devPI_tsfc_aesKey);

    memset(p_item->p_data, 0, p_item->data_len);
    memcpy(p_item->p_data, new_data, p_item->data_len);

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_tsfc], devPI_tsfc_aesKey);
    }

    MyConfig_GetConfTable_Format(myconftable_type_tsfc);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Net_ipv4Address(uint8_t ip_address_type, uint8_t* new_value, int new_value_len, uint8_t saving_to_nvs)
{
    if(new_value==NULL)
    {
        return -1;
    }

    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_net], devPI_net_server_ipv4_address);

    memset(p_item->p_data, 0, p_item->data_len);
    if(new_value_len>p_item->data_len)new_value_len = p_item->data_len;
    memcpy(p_item->p_data, new_value, new_value_len);

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_net], devPI_net_server_ipv4_address);
    }

    MyConfig_GetConfTable_Format(myconftable_type_net);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    MyConfig_Modify_Net_ipv4AddressType(ip_address_type, saving_to_nvs);

    return 0;
}

int MyConfig_Modify_Net_ipv4AddressType(uint8_t newvalue, uint8_t saving_to_nvs)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_net], devPI_net_server_ipv4_address_type);
    p_item->data_uint64 = newvalue;
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_net], devPI_net_server_ipv4_address_type);
    }


    MyConfig_GetConfTable_Format(myconftable_type_net);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Net_serverPort(uint32_t new_value, uint8_t saving_to_nvs)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_net], devPI_net_server_port);

    p_item->data_uint64 = new_value;

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_net], devPI_net_server_port);
    }
    MyConfig_GetConfTable_Format(myconftable_type_net);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Net_otaIpv4Address(uint8_t* new_value, uint8_t saving_to_nvs)
{
    if(new_value==NULL)
    {
        return -1;
    }

    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_net], devPI_net_server_ota_ipv4_address);

    memset(p_item->p_data, 0, p_item->data_len);
    memcpy(p_item->p_data, new_value, p_item->data_len);

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_net], devPI_net_server_ota_ipv4_address);
    }

    MyConfig_GetConfTable_Format(myconftable_type_net);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Net_otaServerPort(uint32_t new_value, uint8_t saving_to_nvs)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_net], devPI_net_server_ota_port);

    p_item->data_uint64 = new_value;

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_net], devPI_net_server_ota_port);
    }
    MyConfig_GetConfTable_Format(myconftable_type_net);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Workmode_mode(uint8_t new_value, uint8_t saving_to_nvs)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_curMode);

    p_item->data_uint64 = new_value;

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_curMode);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Debug_mode(uint8_t new_value, uint8_t saving_to_nvs)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_debugmode);

    p_item->data_uint64 = new_value;

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_debugmode);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Workmode_timePointer1(int start_time, int keep_time, uint8_t saving_to_nvs)
{
    if(start_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_1_start_time);

        p_item->data_uint64 = start_time;
    }
    if(keep_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_1_keep_time);

        p_item->data_uint64 = keep_time;
    }
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_1_start_time);
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_1_keep_time);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Workmode_timePointer2(int start_time, int keep_time, uint8_t saving_to_nvs)
{
    if(start_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_2_start_time);

        p_item->data_uint64 = start_time;
    }
    if(keep_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_2_keep_time);

        p_item->data_uint64 = keep_time;
    }
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_2_start_time);
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_2_keep_time);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Workmode_timePointer3(int start_time, int keep_time, uint8_t saving_to_nvs)
{
    if(start_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_3_start_time);

        p_item->data_uint64 = start_time;
    }
    if(keep_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_3_keep_time);

        p_item->data_uint64 = keep_time;
    }
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_3_start_time);
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_3_keep_time);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Workmode_timePointer4(int start_time, int keep_time, uint8_t saving_to_nvs)
{
    if(start_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_4_start_time);

        p_item->data_uint64 = start_time;
    }
    if(keep_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_4_keep_time);

        p_item->data_uint64 = keep_time;
    }
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_4_start_time);
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_4_keep_time);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Workmode_timePointer5(int start_time, int keep_time, uint8_t saving_to_nvs)
{
    if(start_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_5_start_time);

        p_item->data_uint64 = start_time;
    }
    if(keep_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_5_keep_time);

        p_item->data_uint64 = keep_time;
    }
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_5_start_time);
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_worktimeList_5_keep_time);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Workmode_timermodeSleepTime(int newvalue, uint8_t saving_to_nvs)
{
    if(newvalue>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_timermodeSleepTime);

        p_item->data_uint64 = newvalue;
    }
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_timermodeSleepTime);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Workmode_imageReportInterval(int newvalue, uint8_t saving_to_nvs)
{
    if(newvalue>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_imageReportInterval);

        p_item->data_uint64 = newvalue;
    }
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_imageReportInterval);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Workmode_measReportInterval(int newvalue, uint8_t saving_to_nvs)
{
    if(newvalue>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_measReportInterval);

        p_item->data_uint64 = newvalue;
    }
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_measReportInterval);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Workmode_measReportMode(int newvalue, uint8_t saving_to_nvs)
{
    if(newvalue>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_measReportmode);

        p_item->data_uint64 = newvalue;
    }
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_measReportmode);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Workmode_Relay1_mode(int newvalue, uint8_t saving_to_nvs)
{
    if(newvalue>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_relay1_mode);

        p_item->data_uint64 = newvalue;
    }
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_relay1_mode);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Workmode_Relay1_Xmode_keeptime(int newvalue, uint8_t saving_to_nvs)
{
    if(newvalue>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_relay1_Xmode_keep_time);

        p_item->data_uint64 = newvalue;
    }
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_relay1_Xmode_keep_time);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Workmode_channeldoor_mode(int newvalue, uint8_t saving_to_nvs)
{
    if(newvalue>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_channeldoor_mode);

        p_item->data_uint64 = newvalue;
    }
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_channeldoor_mode);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Workmode_vrctimePointer1(int start_time, int keep_time, uint8_t saving_to_nvs)
{
    if(start_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc1_start_time);

        p_item->data_uint64 = start_time;
    }
    if(keep_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc1_keep_time);

        p_item->data_uint64 = keep_time;
    }
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc1_start_time);
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc1_keep_time);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Workmode_vrctimePointer2(int start_time, int keep_time, uint8_t saving_to_nvs)
{
    if(start_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc2_start_time);

        p_item->data_uint64 = start_time;
    }
    if(keep_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc2_keep_time);

        p_item->data_uint64 = keep_time;
    }
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc2_start_time);
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc2_keep_time);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Workmode_vrctimePointer3(int start_time, int keep_time, uint8_t saving_to_nvs)
{
    if(start_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc3_start_time);

        p_item->data_uint64 = start_time;
    }
    if(keep_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc3_keep_time);

        p_item->data_uint64 = keep_time;
    }
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc3_start_time);
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc3_keep_time);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Workmode_vrctimePointer4(int start_time, int keep_time, uint8_t saving_to_nvs)
{
    if(start_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc4_start_time);

        p_item->data_uint64 = start_time;
    }
    if(keep_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc4_keep_time);

        p_item->data_uint64 = keep_time;
    }
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc4_start_time);
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc4_keep_time);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_Workmode_vrctimePointer5(int start_time, int keep_time, uint8_t saving_to_nvs)
{
    if(start_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc5_start_time);

        p_item->data_uint64 = start_time;
    }
    if(keep_time>=0)
    {
        my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc5_keep_time);

        p_item->data_uint64 = keep_time;
    }
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc5_start_time);
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_workmode], devPI_workmode_vrc5_keep_time);
    }
    MyConfig_GetConfTable_Format(myconftable_type_workmode);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

// #if MY_BLE_FUNC
int MyConfig_Modify_BLE_enableState(uint8_t newvalue, uint8_t saving_to_nvs, uint8_t eff_im)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_ble], devPI_ble_enable_state);

    p_item->data_uint64 = newvalue;
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_ble], devPI_ble_enable_state);
    }

    if(eff_im)
    {
        MyConfig_GetConfTable_Format(myconftable_type_ble);
        // MyBleReInit_TimerSafe(&my_config_nvs.ble.conf);
    }

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_BLE_CurState(uint32_t newvalue)
{
   
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_ble], devPI_ble_cur_state);

    p_item->data_uint64 = newvalue;

    return 0;
}

int MyConfig_Modify_BLE_name(char const* newstr, uint8_t newstr_len, uint8_t saving_to_nvs, uint8_t eff_im)
{
    if(newstr==NULL)
    {
        return -1;
    }

    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_ble], devPI_ble_name);
    if(newstr_len>=p_item->data_len)newstr_len = p_item->data_len;

    memset(p_item->p_data, 0, p_item->data_len);
    memcpy(p_item->p_data, newstr, newstr_len);

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_ble], devPI_ble_name);
    }
    if(eff_im)
    {
        MyConfig_GetConfTable_Format(myconftable_type_ble);
        // MyBleReInit_TimerSafe(&my_config_nvs.ble.conf);
    }

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_BLE_mac(char const* newstr, uint8_t newstr_len, uint8_t saving_to_nvs, uint8_t eff_im)
{
    if(newstr==NULL)
    {
        return -1;
    }

    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_ble], devPI_ble_mac);
    if(newstr_len>=p_item->data_len)newstr_len = p_item->data_len;

    memset(p_item->p_data, 0, p_item->data_len);
    memcpy(p_item->p_data, newstr, newstr_len);

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_ble], devPI_ble_mac);
    }
    if(eff_im)
    {
        MyConfig_GetConfTable_Format(myconftable_type_ble);
        // MyBleReInit_TimerSafe(&my_config_nvs.ble.conf);
    }

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_BLE_advType(uint8_t newvalue, uint8_t saving_to_nvs, uint8_t eff_im)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_ble], devPI_ble_advType);

    p_item->data_uint64 = newvalue;
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_ble], devPI_ble_advType);
    }

    if(eff_im)
    {
        MyConfig_GetConfTable_Format(myconftable_type_ble);
        /* ...code... */
    }
    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_BLE_advDateLen(uint8_t newvalue, uint8_t saving_to_nvs, uint8_t eff_im)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_ble], devPI_ble_advData_len);
    if(newvalue>16)newvalue=16;
    p_item->data_uint64 = newvalue;
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_ble], devPI_ble_advData_len);
    }

    if(eff_im)
    {
        MyConfig_GetConfTable_Format(myconftable_type_ble);
        /* ...code... */
    }
    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_BLE_advData(char const* newdata, uint8_t newdata_len, uint8_t saving_to_nvs, uint8_t eff_im)
{
    if(newdata==NULL)
    {
        return -1;
    }

    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_ble], devPI_ble_advType);

    // if(p_item->data_uint64!=my_ble_adv_type_custom)
    // {
    //     return 1;
    // }

    p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_ble], devPI_ble_advData);

    if(newdata_len>=p_item->data_len)newdata_len = p_item->data_len;

    memset(p_item->p_data, 0, p_item->data_len);
    memcpy(p_item->p_data, newdata, newdata_len);

    MyConfig_Modify_BLE_advDateLen(newdata_len, saving_to_nvs, 0);

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_ble], devPI_ble_advData);
    }
    if(eff_im)
    {
        MyConfig_GetConfTable_Format(myconftable_type_ble);
        // MyBleReInit_TimerSafe(&my_config_nvs.ble.conf);
    }

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_BLE_service_uuid_a(uint16_t newvalue, uint8_t saving_to_nvs, uint8_t eff_im)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_ble], devPI_ble_service_uuid_a);
    p_item->data_uint64 = newvalue;
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_ble], devPI_ble_service_uuid_a);
    }

    if(eff_im)
    {
        MyConfig_GetConfTable_Format(myconftable_type_ble);
        // MyBleReInit_TimerSafe(&my_config_nvs.ble.conf);
    }
    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_BLE_char_uuid_a(uint16_t newvalue, uint8_t saving_to_nvs, uint8_t eff_im)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_ble], devPI_ble_char_uuid_a);
    p_item->data_uint64 = newvalue;
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_ble], devPI_ble_char_uuid_a);
    }

    if(eff_im)
    {
        MyConfig_GetConfTable_Format(myconftable_type_ble);
        // MyBleReInit_TimerSafe(&my_config_nvs.ble.conf);
    }
    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_BLE_service_uuid_b(uint16_t newvalue, uint8_t saving_to_nvs, uint8_t eff_im)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_ble], devPI_ble_service_uuid_b);
    p_item->data_uint64 = newvalue;
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_ble], devPI_ble_service_uuid_b);
    }

    if(eff_im)
    {
        MyConfig_GetConfTable_Format(myconftable_type_ble);
        // MyBleReInit_TimerSafe(&my_config_nvs.ble.conf);
    }
    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_BLE_char_uuid_b(uint16_t newvalue, uint8_t saving_to_nvs, uint8_t eff_im)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_ble], devPI_ble_char_uuid_b);
    p_item->data_uint64 = newvalue;
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_ble], devPI_ble_char_uuid_b);
    }

    if(eff_im)
    {
        MyConfig_GetConfTable_Format(myconftable_type_ble);
        // MyBleReInit_TimerSafe(&my_config_nvs.ble.conf);
    }
    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}
// #endif

int MyConfig_Modify_power_poweroff_alarm_enable_state(uint8_t newvalue, uint8_t saving_to_nvs)
{
   
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_power], devPI_power_poweroff_alarm_enable_state);

    p_item->data_uint64 = newvalue;
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_power], devPI_power_poweroff_alarm_enable_state);
    }
    MyConfig_GetConfTable_Format(myconftable_type_power);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_ExternalPowerstate(uint8_t newvalue)
{
   
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_power], devPI_power_state);

    p_item->data_uint64 = newvalue;

    return 0;
}

int MyConfig_Modify_power_shutdown_after_poweroffAlarm(uint8_t newvalue, uint8_t saving_to_nvs)
{
   
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_power], devPI_power_shutdown_after_poweroffAlarm);

    p_item->data_uint64 = newvalue;
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_power], devPI_power_shutdown_after_poweroffAlarm);
    }
    MyConfig_GetConfTable_Format(myconftable_type_power);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_factory_devstate(uint8_t new_value, uint8_t saving_to_nvs)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_factory], devPI_factory_devstate);

    p_item->data_uint64 = new_value;

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_factory], devPI_factory_devstate);
    }
    MyConfig_GetConfTable_Format(myconftable_type_factory);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_factory_factoryDate(char const* newstr, uint8_t newstr_len, uint8_t saving_to_nvs)
{
    if(newstr==NULL)
    {
        return -1;
    }

    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_factory], devPI_factory_factoryDate);
    if(newstr_len>p_item->data_len)newstr_len = p_item->data_len;

    memset(p_item->p_data, 0, p_item->data_len);
    memcpy(p_item->p_data, newstr, newstr_len);

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_factory], devPI_factory_factoryDate);
    }
    MyConfig_GetConfTable_Format(myconftable_type_factory);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_factory_poweron_reason(uint8_t newvalue)
{
   
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_factory], devPI_factory_poweron_reason);

    p_item->data_uint64 = newvalue;

    return 0;
}

int MyConfig_Modify_factory_poweron_count(uint32_t newvalue, uint8_t saving_to_nvs)
{
   
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_factory], devPI_factory_poweronCount);

    p_item->data_uint64 = newvalue;

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_factory], devPI_factory_poweronCount);
    }
    MyConfig_GetConfTable_Format(myconftable_type_factory);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_factory_runtime_since_poweron(uint32_t newvalue)
{
   
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_factory], devPI_factory_runtime_since_poweron);

    p_item->data_uint64 = newvalue;

    return 0;
}

int MyConfig_Modify_factory_mac(char const* newdata, uint8_t newdata_len, uint8_t saving_to_nvs)
{
    if(newdata==NULL)
    {
        return -1;
    }

    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_factory], devPI_factory_mac);

    p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_factory], devPI_factory_mac);

    if(newdata_len>=p_item->data_len)newdata_len = p_item->data_len;

    memset(p_item->p_data, 0, p_item->data_len);
    memcpy(p_item->p_data, newdata, newdata_len);

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_factory], devPI_factory_mac);
    }

    MyConfig_GetConfTable_Format(myconftable_type_factory);
    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_factory_IMEI(char const* newstr, uint8_t newstr_len, uint8_t saving_to_nvs)
{
    if(newstr==NULL)
    {
        return -1;
    }

    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_factory], devPI_factory_imei);
    if(newstr_len>=p_item->data_len)newstr_len = p_item->data_len;

    if(strcmp(p_item->p_data, newstr)==0)
    {
        return 0;
    }

    memset(p_item->p_data, 0, p_item->data_len);
    memcpy(p_item->p_data, newstr, newstr_len);

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_factory], devPI_factory_imei);
    }

    MyConfig_GetConfTable_Format(myconftable_type_factory);
    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 1;
}

int MyConfig_Modify_factory_ICCID(char const* newstr, uint8_t newstr_len, uint8_t saving_to_nvs)
{
    if(newstr==NULL)
    {
        return -1;
    }

    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_factory], devPI_factory_iccid);
    if(newstr_len>=p_item->data_len)newstr_len = p_item->data_len;

    if(strcmp(p_item->p_data, newstr)==0)
    {
        return 0;
    }

    memset(p_item->p_data, 0, p_item->data_len);
    memcpy(p_item->p_data, newstr, newstr_len);

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_factory], devPI_factory_iccid);
    }

    MyConfig_GetConfTable_Format(myconftable_type_factory);
    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 1;
}

int MyConfig_Modify_factory_verCode_local(uint32_t newvalue, uint8_t saving_to_nvs)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_factory], devPI_factory_ota_vercode);

    p_item->data_uint64 = newvalue;

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_factory], devPI_factory_ota_vercode);
    }
    MyConfig_GetConfTable_Format(myconftable_type_factory);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_factory_vername_local(char const* newstr, uint8_t newdata_len, uint8_t saving_to_nvs)
{
    if(newstr==NULL)
    {
        return -1;
    }

    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_factory], devPI_factory_ota_vername);
    if(newdata_len>=p_item->data_len)newdata_len = p_item->data_len;

    memset(p_item->p_data, 0, p_item->data_len);
    memcpy(p_item->p_data, newstr, newdata_len);

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_factory], devPI_factory_ota_vername);
        MyConfig_GetConfTable_Format(myconftable_type_factory);
    }

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_factory_verfilename_local(char const* newstr, uint8_t newdata_len, uint8_t saving_to_nvs)
{
    if(newstr==NULL)
    {
        return -1;
    }

    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_factory], devPI_factory_ota_filename);
    if(newdata_len>=p_item->data_len)newdata_len = p_item->data_len;

    memset(p_item->p_data, 0, p_item->data_len);
    memcpy(p_item->p_data, newstr, newdata_len);

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_factory], devPI_factory_ota_filename);
        MyConfig_GetConfTable_Format(myconftable_type_factory);
    }

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_factory_verCode_net(uint32_t newvalue, uint8_t saving_to_nvs)
{
    my_config_nvs.factory.my_ota_info_net.vercode = newvalue;

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_factory_vername_net(char const* newstr, uint8_t newdata_len, uint8_t saving_to_nvs)
{
    if(newstr==NULL)
    {
        return -1;
    }

    if(newdata_len>sizeof(my_config_nvs.factory.my_ota_info_net.vername))
    {
        newdata_len = sizeof(my_config_nvs.factory.my_ota_info_net.vername) - 1;
    }

    memset(my_config_nvs.factory.my_ota_info_net.vername, 0, sizeof(my_config_nvs.factory.my_ota_info_net.vername));
    memcpy(my_config_nvs.factory.my_ota_info_net.vername, newstr, newdata_len);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_factory_verfilename_net(char const* newstr, uint8_t newdata_len, uint8_t saving_to_nvs)
{
    if(newstr==NULL)
    {
        return -1;
    }

    if(newdata_len>sizeof(my_config_nvs.factory.my_ota_info_net.filename))
    {
        newdata_len = sizeof(my_config_nvs.factory.my_ota_info_net.filename) - 1;
    }

    memset(my_config_nvs.factory.my_ota_info_net.filename, 0, sizeof(my_config_nvs.factory.my_ota_info_net.filename));
    memcpy(my_config_nvs.factory.my_ota_info_net.filename, newstr, newdata_len);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_factory_ota_check_type(uint8_t new_value, uint8_t saving_to_nvs)
{
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_factory], devPI_factory_ota_check_type);

    p_item->data_uint64 = new_value;

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_factory], devPI_factory_ota_check_type);
    }
    MyConfig_GetConfTable_Format(myconftable_type_factory);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_factory_devType(uint16_t newvalue, uint8_t saving_to_nvs)
{
   
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_factory], devPI_factory_devType);

    p_item->data_uint64 = newvalue;
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_factory], devPI_factory_devType);
    }
    MyConfig_GetConfTable_Format(myconftable_type_factory);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_factory_devModel(char const* newstr, uint8_t newstr_len, uint8_t saving_to_nvs)
{
    if(newstr==NULL)
    {
        return -1;
    }

    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_factory], devPI_factory_devModel);
    if(newstr_len>=p_item->data_len)newstr_len = p_item->data_len;

    memset(p_item->p_data, 0, p_item->data_len);
    memcpy(p_item->p_data, newstr, newstr_len);

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_factory], devPI_factory_devModel);
    }
    MyConfig_GetConfTable_Format(myconftable_type_factory);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_factory_devId(char const* newstr, uint8_t newdata_len, uint8_t saving_to_nvs)
{
    if(newstr==NULL)
    {
        return -1;
    }

    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_factory], devPI_factory_devId);

    if(newdata_len>=p_item->data_len)newdata_len = p_item->data_len;

    memset(p_item->p_data, 0, p_item->data_len);
    memcpy(p_item->p_data, newstr, newdata_len);

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_factory], devPI_factory_devId);
    }
    MyConfig_GetConfTable_Format(myconftable_type_factory);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_factory_otaurl(char const* newstr, uint8_t newdata_len, uint8_t saving_to_nvs)
{
    if(newstr==NULL)
    {
        return -1;
    }

    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_factory], devPI_factory_httpota_url);

    if(newdata_len>=p_item->data_len)newdata_len = p_item->data_len;

    memset(p_item->p_data, 0, p_item->data_len);
    memcpy(p_item->p_data, newstr, newdata_len);

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_factory], devPI_factory_httpota_url);
    }
    MyConfig_GetConfTable_Format(myconftable_type_factory);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_lte_baudrate(int newvalue, uint8_t saving_to_nvs)
{
   
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_lte], devPI_lte_baudrate);

    p_item->data_uint64 = newvalue;
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_lte], devPI_lte_baudrate);
    }
    MyConfig_GetConfTable_Format(myconftable_type_lte);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_gnss_auto_report_switch(uint8_t newvalue, uint8_t saving_to_nvs)
{
   
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_gnss], devPI_gnss_auto_report_switch);

    p_item->data_uint64 = newvalue;
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_gnss], devPI_gnss_auto_report_switch);
    }
    MyConfig_GetConfTable_Format(myconftable_type_gnss);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_gnss_auto_report_interval(uint8_t newvalue, uint8_t saving_to_nvs)
{
   
    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_gnss], devPI_gnss_auto_report_interval);

    p_item->data_uint64 = newvalue;
    
    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_gnss], devPI_gnss_auto_report_interval);
    }
    MyConfig_GetConfTable_Format(myconftable_type_gnss);

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_gnss_longitude(char const* newstr, uint8_t newstr_len, uint8_t saving_to_nvs)
{
    if(newstr==NULL)
    {
        return -1;
    }

    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_gnss], devPI_gnss_longitude);
    if(newstr_len>=p_item->data_len)newstr_len = p_item->data_len;

    memset(p_item->p_data, 0, p_item->data_len);
    memcpy(p_item->p_data, newstr, newstr_len);

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_gnss], devPI_gnss_longitude);
    }

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}

int MyConfig_Modify_gnss_latitude(char const* newstr, uint8_t newstr_len, uint8_t saving_to_nvs)
{
    if(newstr==NULL)
    {
        return -1;
    }

    my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(myconf_table_list_array[myconftable_type_gnss], devPI_gnss_latitude);
    if(newstr_len>=p_item->data_len)newstr_len = p_item->data_len;

    memset(p_item->p_data, 0, p_item->data_len);
    memcpy(p_item->p_data, newstr, newstr_len);

    if(saving_to_nvs)
    {
        Save_KVP_To_Nvs_FromListItem(myconf_table_list_array[myconftable_type_gnss], devPI_gnss_latitude);
    }

    MyConfig_DebugPrint_my_config_nvs_t(&my_config_nvs, 1, "my_config.c");

    return 0;
}