#include "my_led_mng.h"

#include <esp_log.h>
#include "esp_system.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"
#include "freertos/semphr.h"
#include "freertos/timers.h"

#include <stdint.h>
#include <stdbool.h>
#include <stdio.h>
#include <string.h>

#include "my_ledc.h"

#include "adxl345.h"
#include "mytest_radar.h"

extern my_adxl345_info_t my_adxl345_info;
extern int rb24_ob_count;
extern int iot_net_ok;
extern bool iot_ota_checking;


TimerHandle_t   LedMngfinddevTimer_Handle;

static led_mode led_mode_bak = ledmode_on;
static bool led_mng_db_flag = false;

void LedMngfinddevTimer(xTimerHandle xTimer)
{
	MyLed_SetMode_Super(led_mode_bak);
    led_mng_db_flag = false;
    xTimerStop(LedMngfinddevTimer_Handle,portMAX_DELAY);
}

TaskHandle_t MyLedMng_TaskHandle = NULL;
void MyLedMng_Task(void* param)
{
    bool led_timer_created = false;
    uint32_t notifyValue = 0;
    MyLed_Mng_Enable();
    for(;;)
    {
        notifyValue = 0;
        xTaskNotifyWait(0x0, 0xffffffff, &notifyValue, 200/portTICK_PERIOD_MS);
        if(notifyValue==1)
        {
            led_mode_bak = MyLed_GetMode();
            if(led_timer_created==false)
            {
                led_timer_created = true;
                LedMngfinddevTimer_Handle = xTimerCreate("LedMngfinddevTimer", 2000/portTICK_PERIOD_MS, pdTRUE, (void*)1, LedMngfinddevTimer);
                
            }
            MyLed_SetMode_Super(ledmode_flash_find_dev);
            led_mng_db_flag = true;
            xTimerStart(LedMngfinddevTimer_Handle,portMAX_DELAY);
            continue;
        }
        if(led_mng_db_flag==true)
        {
            continue;
        }
        if(my_adxl345_info.dev_attitude_err)
        {
            continue;
        }

        if(iot_net_ok<100)
        {
            switch(iot_net_ok)
            {
                case 0:
                    MyLed_SetMode_Super(ledmode_fade);
                break;
            }
            continue;
        }
        else
        {
            MyLed_SetMode_Super(ledmode_on);
        }

        if(iot_ota_checking==true)
        {
            MyLed_SetMode_Super(ledmode_fade_flash);
            continue;
        }
    }
}

void MyLedMng_Find_Dev(void)
{
    if(MyLedMng_TaskHandle!=NULL)
    {
        xTaskNotify(MyLedMng_TaskHandle, 1, eSetValueWithOverwrite);
    }
}

void MyLedMng_Init(void)
{
    xTaskCreatePinnedToCore(MyLedMng_Task, "MyLedMng_Task", 2048, NULL, 8, &MyLedMng_TaskHandle, 1);
}