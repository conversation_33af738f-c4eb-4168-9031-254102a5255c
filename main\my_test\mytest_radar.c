#include "mytest_radar.h"
#include "my_radar.h"

#if MY_RADAR_FUNC

#include "my_radar_ld2450.h"
#include "my_radar_ld2402.h"

#include <esp_log.h>
#include "esp_system.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"
#include "freertos/semphr.h"
#include "freertos/timers.h"

#include <stdint.h>
#include <stdbool.h>
#include <stdio.h>
#include <string.h>

#include "my_tcp_client.h"

#include "my_time.h"
#include "my_ledc.h"

#include "my_ds.h"
#include "my_esp_chip_info_mng.h"
#include "my_endian.h"

#include "cJSON.h"




int test_radar_tcp_chanel_id = -1;

int unread_data = 0;
char* radar_data_report_buf = NULL;
char* radar_data_regular_report_buf_default = NULL;

int radar_data_regular_report_buf_default_written_len = 0;
// #define radar_data_regular_report_buf_size ((60/10)*60*24*18)
char* radar_data_regular_report_buf = NULL;

int radar_data_regular_report_buf_written_len = 0;
int last_object_count = 0;
bool radar_data_regular_report_buf_full = false;

QueueHandle_t my_test_radar_rcv_queue = NULL;

SemaphoreHandle_t Myradar_regular_data_MutexSemaphore = NULL;


single_object_t rd24_objects[3];
int rb24_ob_count = 0;

bool radar_start = false;

bool radar_regular_statistics_switch = false;



bool radar_init_ok = false;

#define test_radar_task1_rcvbuf_field_size (10)

char radar_ld2450_demo_frame[30] = {
0xAA, 0xFF, 0x03, 0x00, 
0xA0, 0x03, 0xA0, 0x86, 0x10, 0x00, 0x40, 0x01, 
0xB0, 0x03, 0xB0, 0x86, 0x10, 0x00, 0x40, 0x01, 
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x55, 0xCC};

void radar_refresh_default_regular_data(void)
{
    my_d2link_list_t* radar_change_data_default_list = My_D2Link_Creat("radar_change_data");

    if(radar_change_data_default_list!=NULL)
    {
        time_t now_time = MyTime_GetTime();
        My_D2Link_Append_Item(radar_change_data_default_list, 1, NULL, 1, now_time, sizeof(time_t), "time");
        My_D2Link_Append_Item(radar_change_data_default_list, 2, NULL, 1, 0, 2, "ob_num");

        for(int i=0; i<3; i++)
        {
            My_D2Link_Append_Item(radar_change_data_default_list, 2*i+3, NULL, 1, 0, sizeof(int16_t), "x");
            My_D2Link_Append_Item(radar_change_data_default_list, 2*i+4, NULL, 1, 0, sizeof(int16_t), "y");
        }

        memset(radar_data_regular_report_buf_default, 0, radar_data_regular_report_buf_default_size);

        My_D2Link_Data_Packer(radar_change_data_default_list, radar_data_regular_report_buf_default, radar_change_data_default_list->list_data_len);
        radar_data_regular_report_buf_default_written_len = radar_change_data_default_list->list_data_len;

        My_D2Link_Delete(&radar_change_data_default_list);
        radar_change_data_default_list = NULL;
    }
}

int radar_get_rgl_data(char** saving)
{
    xSemaphoreTake(Myradar_regular_data_MutexSemaphore, portMAX_DELAY);
    int ret = 0;
    if(radar_data_regular_report_buf_written_len>0)
    {
        *saving = calloc(1, radar_data_regular_report_buf_written_len);
        if(saving!=NULL)
        {
            memcpy(*saving, radar_data_regular_report_buf, radar_data_regular_report_buf_written_len);
            ret =  radar_data_regular_report_buf_written_len;
        }
        else
        {
            ret = 0;
        }
    }
    else
    {
        *saving = calloc(1, radar_data_regular_report_buf_default_written_len);
        if(saving!=NULL)
        {
            memcpy(*saving, radar_data_regular_report_buf_default, radar_data_regular_report_buf_default_written_len);
            ret = radar_data_regular_report_buf_default_written_len;
        }
        else
        {
            ret = 0;
        }
    }
    xSemaphoreGive(Myradar_regular_data_MutexSemaphore);
    return ret;
}

// 函数来提取距离信息
int extractDistance(const char *data) {
    const char *distancePrefix = "distance:";
    char *distanceStr = strstr(data, distancePrefix); // 查找"distance:"的位置
 
    if (distanceStr != NULL) {
        distanceStr += strlen(distancePrefix); // 跳过"distance:"
 
        // 查找下一个非数字字符的位置（假设距离数值后直接是非数字字符）
        char *endPtr;
        int distance = (int)strtol(distanceStr, &endPtr, 10);
 
        // 如果endPtr指向的不是数字后的字符（例如字母等），说明转换成功且合理
        if (distanceStr != endPtr) {
            return distance;
        }
    }
 
    // 如果没有找到"distance:"，或者转换失败，检查是否包含"OFF"
    if (strstr(data, "OFF") != NULL) {
        return 0; // 包含"OFF"时，返回0
    }
 
    // 如果以上条件都不满足，可以根据需求返回一个默认值或错误值
    return -1; // 这里返回-1表示未能成功提取有效的距离值
}

void radar_task1(void* param)
{
	int runtimes = 0;

	#define test_recv_buf_len 8192
	char* test_recv_buf = calloc(1, test_recv_buf_len);
	int recv_len = 0;

    int queue_rcv_ret = pdFALSE;
    my_test_radar_queue_t* queue_rcv_item = NULL;
    uint32_t data_packager_count = 0;

    time_t last_send_time = 0;
    #define send_internal (1)

	if(test_recv_buf==NULL)
	{
		ESP_LOGE("test_radar_task1()", "test_recv_buf==NULL!");
		while(1)
		{
			vTaskDelay(1000 / portTICK_PERIOD_MS);
		}
	}

    vTaskDelay(2000 / portTICK_PERIOD_MS);
    MyDebug_PrintMemInfo("radar_task1");
    vTaskDelay(100 / portTICK_PERIOD_MS);

    my_d2link_list_t* radar_period_data_list = My_D2Link_Creat("radar_period_data");
    int radar_period_data_list_id = 0;

    time_t last_there_object_time = MyTime_GetTime();
    time_t last_report_time = MyTime_GetTime();
    
    
    printf("\n\n\n\n-------------------------------start of period-------------------------------\n\n\n\n");

    int period_count = 0;
	for(;;)
	{
        // printf("\n\n\n\n-------------------------------runtimes=%d-------------------------------\n\n\n\n", runtimes);
        queue_rcv_ret = xQueueReceive(my_test_radar_rcv_queue, &queue_rcv_item, 1000/portTICK_PERIOD_MS);
        if(queue_rcv_ret==pdTRUE)
        {
            if(queue_rcv_item!=NULL)
            {
                if(queue_rcv_item->valid)
                {
                    if(radar_start==true)
                    {
                        if(queue_rcv_item->data!=NULL)
                        {
                            ld2450_data_frame_t ld2450_data_frame;
                            memcpy(&ld2450_data_frame, queue_rcv_item->data, sizeof(ld2450_data_frame_t));
                            bool frame_ok = true;

                            // printf("queue_rcv_item->data = %s\n", queue_rcv_item->data);
                            int ob_distance = extractDistance(queue_rcv_item->data);

                            if(ob_distance<0)
                            {
                                frame_ok = false;
                            }
                            
                            if(frame_ok==true)
                            {
                                int object_count = 0;
                                memset(&ld2450_data_frame, 0, sizeof(ld2450_data_frame));
                                ld2450_data_frame.objects[0].x = 0;
                                ld2450_data_frame.objects[0].y = ob_distance;
                                if(ob_distance!=0)
                                {
                                    object_count = 1;
                                }
                                ld2450_data_frame.objects[0].speed = 0;

                                time_t cur_time = MyTime_GetTime();
                                if(cur_time - last_send_time >= send_internal)
                                {
                                    rb24_ob_count = object_count;
                                    // ESP_LOGI("radar_task1(1)", "ld2450_data_frame=");
                                    // char *p_print = &ld2450_data_frame;
                                    // for(int i=0; i<sizeof(ld2450_data_frame_t); i++)
                                    // {
                                    //     printf("%02X ", *(p_print+i));
                                    // }
                                    // printf("queue_rcv_item->data(1) = %s\n", queue_rcv_item->data);
                                    // printf("\n####\n");
                                    // for(int i=0; i<3; i++)
                                    // {
                                    //     printf("\n\n");
                                    //     printf("objects[%d].x=%dmm, ", i, ld2450_data_frame.objects[i].x);
                                    //     printf("objects[%d].y=%dmm, ", i, ld2450_data_frame.objects[i].y);
                                    //     printf("objects[%d].speed=%dcm/s, ", i, ld2450_data_frame.objects[i].speed);
                                    //     printf("objects[%d].distance_resolution=%dmm", i, ld2450_data_frame.objects[i].distance_resolution);
                                    //     printf("\n\n");
                                    // }
                                    // if(object_count>0)
                                    {
                                        #define max_object_count 3
                                        single_object_t *single_objects = (single_object_t*)calloc(max_object_count, sizeof(single_object_t));
                                        if(single_objects!=NULL)
                                        {
                                            int write_count = 0;
                                            // printf("\n------------------------------------------------------------------\n");
                                            for(int i=0; i<max_object_count; i++)
                                            {
                                                // if((ld2450_data_frame.objects[i].x+ld2450_data_frame.objects[i].y+ld2450_data_frame.objects[i].speed+ld2450_data_frame.objects[i].distance_resolution)!=0)
                                                {
                                                    (single_objects+write_count)->number = i;
                                                    (single_objects+write_count)->x = ld2450_data_frame.objects[i].x;
                                                    (single_objects+write_count)->y = ld2450_data_frame.objects[i].y;
                                                    (single_objects+write_count)->speed =ld2450_data_frame.objects[i].speed;

                                                    rd24_objects[i].number = i;
                                                    rd24_objects[i].x = ld2450_data_frame.objects[i].x;
                                                    rd24_objects[i].y = ld2450_data_frame.objects[i].y;
                                                    rd24_objects[i].speed = ld2450_data_frame.objects[i].speed;
                                                    // printf("rd24_objects.number=%d\n", rd24_objects->number);
                                                    // printf("rd24_objects.x=%dmm\n", rd24_objects->x);
                                                    // printf("rd24_objects.y=%dmm\n", rd24_objects->y);
                                                    // printf("rd24_objects.speed=%dcm/s\n", rd24_objects->speed);

                                                    
                                                    // printf("\n");
                                                    // printf("(single_objects+%d)->number=%d\n", write_count, (single_objects+write_count)->number);
                                                    // printf("(single_objects+%d)->x=%dmm\n", write_count, (single_objects+write_count)->x);
                                                    // printf("(single_objects+%d)->y=%dmm\n", write_count, (single_objects+write_count)->y);
                                                    // printf("(single_objects+%d)->speed=%dcm/s\n", write_count, (single_objects+write_count)->speed);
                                                    // printf("\n");

                                                    // printf("\n");
                                                    // ESP_LOGI("", "-------------------------------");
                                                    // ESP_LOGI("", "(single_objects+%d)->number=%d\n", write_count, (single_objects+write_count)->number);
                                                    // ESP_LOGI("", "(single_objects+%d)->x=%dmm\n", write_count, (single_objects+write_count)->x);
                                                    // ESP_LOGI("", "(single_objects+%d)->y=%dmm\n", write_count, (single_objects+write_count)->y);
                                                    // ESP_LOGI("", "(single_objects+%d)->speed=%dcm/s\n", write_count, (single_objects+write_count)->speed);
                                                    // printf("\n");

                                                    // (single_objects+write_count)->number = my_endian_conversion_16(i);
                                                    // (single_objects+write_count)->x = my_endian_conversion_16(ld2450_data_frame.objects[i].x);
                                                    // (single_objects+write_count)->y = my_endian_conversion_16(ld2450_data_frame.objects[i].y);
                                                    // (single_objects+write_count)->speed = my_endian_conversion_16(ld2450_data_frame.objects[i].speed);

                                                    // ESP_LOGI("radar_task1(2)", "single_objects=");
                                                    // char *p_print = single_objects;
                                                    // for(int i=0; i<sizeof(single_object_t)*max_object_count; i++)
                                                    // {
                                                    //     printf("%02X ", *(p_print+i));
                                                    // }
                                                    // printf("\n####\n");
                                                    if(write_count<max_object_count)
                                                    {
                                                        write_count++;
                                                    }
                                                }
                                            }
                                            // printf("\n------------------------------------------------------------------\n");
                                            last_there_object_time = MyTime_GetTime();
                                            time_t now_time = MyTime_GetTime();
                                            my_d2link_list_t* radar_instant_data_list = My_D2Link_Creat("radar_instant_data");
                                            if(radar_instant_data_list!=NULL)
                                            {
                                                for(int i=0; i<max_object_count; i++)
                                                {
                                                    My_D2Link_Append_Item(radar_instant_data_list, 2*i, NULL, 1, (single_objects+i)->x, sizeof(int16_t), "x");
                                                    My_D2Link_Append_Item(radar_instant_data_list, 2*i+1, NULL, 1, (single_objects+i)->y, sizeof(int16_t), "y");
                                                }
                                                char* radar_instant_data = calloc(1, radar_instant_data_list->list_data_len);
                                                if(radar_instant_data!=NULL)
                                                {
                                                    My_D2Link_Data_Packer(radar_instant_data_list, radar_instant_data, radar_instant_data_list->list_data_len);
                                                    // My_D2Link_PrintList(radar_instant_data_list);
                                                    My_D2Link_Append_Item(radar_period_data_list, radar_period_data_list_id++, radar_instant_data, 1, 0, radar_instant_data_list->list_data_len, "ld2450_data_frame.objects");
                                                    // My_D2Link_PrintList(radar_period_data_list);

                                                    free(radar_instant_data);
                                                    radar_instant_data = NULL;
                                                }
                                                My_D2Link_Delete(&radar_instant_data_list);
                                                radar_instant_data_list = NULL;
                                            }
                                            free(single_objects);
                                            single_objects = NULL;
                                        }
                                    }

                                    // if(object_count != last_object_count)
                                    // {
                                    //     ESP_LOGI("radar_task1", "--------------- new object_count [ %d --> %d ] ---------------", last_object_count, object_count);
                                    //     last_object_count = object_count;
                                    if(radar_regular_statistics_switch==true)
                                    {
                                        xSemaphoreTake(Myradar_regular_data_MutexSemaphore, portMAX_DELAY);
                                        single_object_t *single_objects = NULL;
                                        #define max_object_count 3
                                        // if(object_count>0)
                                        {
                                            single_objects = (single_object_t*)calloc(max_object_count, sizeof(single_object_t));
                                            if(single_objects!=NULL)
                                            {
                                                int write_count = 0;
                                                // printf("\n------------------------------------------------------------------\n");
                                                for(int i=0; i<max_object_count; i++)
                                                {
                                                    // if((ld2450_data_frame.objects[i].x+ld2450_data_frame.objects[i].y+ld2450_data_frame.objects[i].speed+ld2450_data_frame.objects[i].distance_resolution)!=0)
                                                    {
                                                        (single_objects+write_count)->number = i;
                                                        (single_objects+write_count)->x = ld2450_data_frame.objects[i].x;
                                                        (single_objects+write_count)->y = ld2450_data_frame.objects[i].y;
                                                        (single_objects+write_count)->speed =ld2450_data_frame.objects[i].speed;
                                                        
                                                        // printf("\n");
                                                        // printf("(single_objects+%d)->number=%d\n", write_count, (single_objects+write_count)->number);
                                                        // printf("(single_objects+%d)->x=%dmm\n", write_count, (single_objects+write_count)->x);
                                                        // printf("(single_objects+%d)->y=%dmm\n", write_count, (single_objects+write_count)->y);
                                                        // printf("(single_objects+%d)->speed=%dcm/s\n", write_count, (single_objects+write_count)->speed);
                                                        // printf("\n");

                                                        // printf("\n");
                                                        // ESP_LOGI("", "-------------------------------");
                                                        // ESP_LOGI("", "(single_objects+%d)->number=%d\n", write_count, (single_objects+write_count)->number);
                                                        // ESP_LOGI("", "(single_objects+%d)->x=%dmm\n", write_count, (single_objects+write_count)->x);
                                                        // ESP_LOGI("", "(single_objects+%d)->y=%dmm\n", write_count, (single_objects+write_count)->y);
                                                        // ESP_LOGI("", "(single_objects+%d)->speed=%dcm/s\n", write_count, (single_objects+write_count)->speed);
                                                        // printf("\n");

                                                        // ESP_LOGI("radar_task1(2)", "single_objects=");
                                                        // char *p_print = single_objects;
                                                        // for(int i=0; i<sizeof(single_object_t)*max_object_count; i++)
                                                        // {
                                                        //     printf("%02X ", *(p_print+i));
                                                        // }
                                                        // printf("\n####\n");
                                                        if(write_count<max_object_count)
                                                        {
                                                            write_count++;
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        time_t now_time = MyTime_GetTime();
                                        my_d2link_list_t* radar_change_data_default_list = My_D2Link_Creat("radar_change_data_default");
                                        if(radar_change_data_default_list!=NULL)
                                        {
                                            My_D2Link_Append_Item(radar_change_data_default_list, 1, NULL, 1, now_time, sizeof(time_t), "time");
                                            My_D2Link_Append_Item(radar_change_data_default_list, 2, NULL, 1, max_object_count, 2, "ob_num");
                                            // if(object_count>0)
                                            {
                                                for(int i=0; i<max_object_count; i++)
                                                {
                                                    My_D2Link_Append_Item(radar_change_data_default_list, 2*i+3, NULL, 1, (single_objects+i)->x, sizeof(int16_t), "x");
                                                    My_D2Link_Append_Item(radar_change_data_default_list, 2*i+4, NULL, 1, (single_objects+i)->y, sizeof(int16_t), "y");
                                                }
                                            }
                                            My_D2Link_Data_Packer(radar_change_data_default_list, radar_data_regular_report_buf_default, radar_change_data_default_list->list_data_len);
                                            radar_data_regular_report_buf_default_written_len = radar_change_data_default_list->list_data_len;
                                            // My_D2Link_PrintList(radar_change_data_default_list);

                                            My_D2Link_Delete(&radar_change_data_default_list);
                                            radar_change_data_default_list = NULL;
                                        }

                                        if((object_count != last_object_count) || (radar_data_regular_report_buf_written_len==0))
                                        {
                                            ESP_LOGI("radar_task1", "--------------- new object_count [ %d --> %d ] ---------------", last_object_count, object_count);

                                            for(int i=0; i<max_object_count; i++)
                                            {
                                                printf("\n");
                                                ESP_LOGI("", "(single_objects+%d)->number=%d\n", i, (single_objects+i)->number);
                                                ESP_LOGI("", "(single_objects+%d)->x=%dmm\n", i, (single_objects+i)->x);
                                                ESP_LOGI("", "(single_objects+%d)->y=%dmm\n", i, (single_objects+i)->y);
                                                ESP_LOGI("", "(single_objects+%d)->speed=%dcm/s\n", i, (single_objects+i)->speed);
                                                printf("\n");
                                            }
                                            last_object_count = object_count;
                                            // printf("\n------------------------------------------------------------------\n");
                                            time_t now_time = MyTime_GetTime();
                                            my_d2link_list_t* radar_change_data_list = My_D2Link_Creat("radar_change_data");
                                            if(radar_change_data_list!=NULL)
                                            {
                                                My_D2Link_Append_Item(radar_change_data_list, 1, NULL, 1, now_time, sizeof(time_t), "time");
                                                My_D2Link_Append_Item(radar_change_data_list, 2, NULL, 1, max_object_count, 2, "ob_num");
                                                // if(object_count>0)
                                                {
                                                    for(int i=0; i<max_object_count; i++)
                                                    {
                                                        My_D2Link_Append_Item(radar_change_data_list, 2*i+3, NULL, 1, (single_objects+i)->x, sizeof(int16_t), "x");
                                                        My_D2Link_Append_Item(radar_change_data_list, 2*i+4, NULL, 1, (single_objects+i)->y, sizeof(int16_t), "y");
                                                    }
                                                }
                                                char* radar_change_data = calloc(1, radar_change_data_list->list_data_len);
                                                if(radar_change_data!=NULL)
                                                {
                                                    My_D2Link_Data_Packer(radar_change_data_list, radar_change_data, radar_change_data_list->list_data_len);
                                                    My_D2Link_PrintList(radar_change_data_list);

                                                    // memcpy(radar_data_regular_report_buf_default, radar_change_data, radar_change_data_list->list_data_len);
                                                    // radar_data_regular_report_buf_default_written_len=radar_change_data_list->list_data_len;

                                                    ESP_LOGI("radar_task1", "radar_change_data(%d):", radar_change_data_list->list_data_len);
                                                    esp_log_buffer_hex("radar_change_data", radar_change_data, radar_change_data_list->list_data_len);

                                                    if(radar_data_regular_report_buf_full==false)
                                                    {
                                                        if(radar_data_regular_report_buf_written_len+radar_change_data_list->list_data_len < radar_data_regular_report_buf_size)
                                                        {
                                                            // xSemaphoreTake(Myradar_regular_data_MutexSemaphore, portMAX_DELAY);
                                                            memcpy(radar_data_regular_report_buf+radar_data_regular_report_buf_written_len, radar_change_data, radar_change_data_list->list_data_len);
                                                            radar_data_regular_report_buf_written_len+=radar_change_data_list->list_data_len;
                                                            // xSemaphoreGive(Myradar_regular_data_MutexSemaphore);
                                                            ESP_LOGI("radar_task1", "radar_data_regular_report_buf(%d):", radar_data_regular_report_buf_written_len);
                                                            esp_log_buffer_hex("radar_data_regular_report_buf", radar_data_regular_report_buf, radar_data_regular_report_buf_written_len);
                                                        }
                                                        if(radar_data_regular_report_buf_written_len >= radar_data_regular_report_buf_size-radar_data_regular_report_buf_explore_size)
                                                        {
                                                            radar_data_regular_report_buf_full = true;
                                                        }
                                                    }
                                                    else
                                                    {
                                                        ESP_LOGW("radar_task1", "radar_data_regular_report_buf is full, can't write more data!");
                                                    }
                                                    free(radar_change_data);
                                                    radar_change_data = NULL;
                                                }
                                                My_D2Link_Delete(&radar_change_data_list);
                                                radar_change_data_list = NULL;
                                            }
                                        }
                                        if(single_objects!=NULL)
                                        {
                                            free(single_objects);
                                            single_objects = NULL;
                                        }
                                        xSemaphoreGive(Myradar_regular_data_MutexSemaphore);
                                    };

                                    last_send_time = cur_time;
                                    // if(My_TcpClient_Get_state(test_radar_tcp_chanel_id))
                                    // {
                                    //     ESP_LOGI("test_radar_task1()", "send radar data %d bytes to tcp server...", queue_rcv_item->len);
                                    //     My_TcpClient_SendDataToServer(test_radar_tcp_chanel_id, &ld2450_data_frame, 30);
                                    // }
                                }
                                
                                switch(object_count)
                                {
                                    case 1:
                                        MyLed_SetMode(ledmode_flash);
                                    break;
                                    case 2:
                                        MyLed_SetMode(ledmode_fade);
                                    break;
                                    case 3:
                                        MyLed_SetMode(ledmode_fade_flash);
                                    break;
                                    default:
                                        MyLed_SetMode(ledmode_on);
                                    break;
                                }
                            }
                        }
                    }
                    queue_rcv_item->valid = 0;
                }
            }
        }
        else
        {

        }

        time_t cur_time = MyTime_GetTime();
        if(cur_time - last_report_time > radar_report_internal)
        {
            // printf("\n\n\n\n-------------------------------end of period-------------------------------\n\n\n\n");
            // My_D2Link_PrintList(radar_period_data_list);
            char* radar_period_data = calloc(1, radar_period_data_list->list_data_len);
            My_D2Link_Data_Packer(radar_period_data_list, radar_period_data, radar_period_data_list->list_data_len);
            // ESP_LOGI("test_radar_task1()", "radar_period_data_list data len: %d", radar_period_data_list->list_data_len);
            // esp_log_buffer_hex("radar_period_data", radar_period_data, radar_period_data_list->list_data_len);

            memset(radar_data_report_buf, 0, 4096);
            memcpy(radar_data_report_buf, radar_period_data, radar_period_data_list->list_data_len);
            unread_data = radar_period_data_list->list_data_len;
            


            // free(radar_period_data);

            /*----------------------------------------------------*/
            // if(My_TcpClient_Get_state(test_radar_tcp_chanel_id))
            // {
            //     ESP_LOGI("test_radar_task1()", "send radar data %d bytes to tcp server...", radar_period_data_list->list_data_len);
            //     My_TcpClient_SendDataToServer(test_radar_tcp_chanel_id, radar_period_data, radar_period_data_list->list_data_len);
            // }
            
            /*----------------------------------------------------*/

            if(radar_period_data_list!=NULL)
            {
                My_D2Link_Delete(&radar_period_data_list);
            }
            free(radar_period_data);
            radar_period_data = NULL;
            // vTaskDelay(2000 / portTICK_PERIOD_MS);
            MyDebug_PrintMemInfo("radar_task1");
            // vTaskDelay(100 / portTICK_PERIOD_MS);

            // period_count++;
            // if(period_count >= 3)
            // {
            //     while(true)
            //     {
            //         vTaskDelay(1000 / portTICK_PERIOD_MS);
            //     }
            // }
            radar_period_data_list = My_D2Link_Creat("radar_period_data");
            time_t cur_time = MyTime_GetTime();
            last_report_time = cur_time;
            // printf("\n\n\n\n-------------------------------start of period-------------------------------\n\n\n\n");
        }
		// if(test_radar_tcp_chanel_id>=0)
		// {
		// 	memset(test_recv_buf, 0, test_recv_buf_len);
		// 	recv_len = My_TcpClient_RecvDataFromServer(test_radar_tcp_chanel_id, test_recv_buf, test_recv_buf_len, 0, 3000);
		// 	ESP_LOGI("test_radar_task1()", "test_recv_buf(%d)=%s", recv_len, test_recv_buf);
		// }
	}
}

uint8_t My_Test_Radar_Send(char* data, uint32_t len, uint32_t timeout)
{
    int queue_send_ret = pdTRUE;
    int i = 0;
    int retry_count = 0;

    //获取信号量
    //...
    MyTestRadar_Send_retry:
    for(i=0; i<MY_TEST_RADAR_QUEUE_LEN; i++)
    {
        if(!my_test_radar_rcv_queue_array[i]->valid)
        {
            my_test_radar_rcv_queue_array[i]->len = len;
            my_test_radar_rcv_queue_array[i]->valid = 1;
            //my_test_radar_rcv_queue_array[i]->data = data;
            if(my_test_radar_rcv_queue_array[i]->data!=NULL)
            {
                memcpy(my_test_radar_rcv_queue_array[i]->data, data, len);
                my_test_radar_rcv_queue_array[i]->data[len]=0;
            }
            break;
        }
    }
    //释放信号量
    //...
    if(i>=MY_TEST_RADAR_QUEUE_LEN&&retry_count<40)
    {
        vTaskDelay(timeout / portTICK_PERIOD_MS);
        retry_count++;
        ESP_LOGW("My_Test_Radar_Send()", "retry_count=%d\n", retry_count);
        goto MyTestRadar_Send_retry;
    }
    else if(i<MY_TEST_RADAR_QUEUE_LEN)
    {
        if(my_test_radar_rcv_queue!=NULL)
            queue_send_ret = xQueueSend(my_test_radar_rcv_queue, &my_test_radar_rcv_queue_array[i], timeout / portTICK_PERIOD_MS);
    }
    else
    {
        ESP_LOGE("My_Test_Radar_Send()", "error, i=%d\n", i);
        return 1;
    }
    return 0;
}



void radar_task2(void* param)
{
	int runtimes = 0;

	#define test_send_buf_len 1024
	char* test_send_buf = calloc(1, test_send_buf_len);

	char* test_recv_buf = calloc(1, test_recv_buf_len);
	int recv_len = 0;

	if(test_send_buf==NULL)
	{
		ESP_LOGE("test_radar_task2()", "test_send_buf==NULL!");
		while(1)
		{
			vTaskDelay(1000 / portTICK_PERIOD_MS);
		}
	}

    while(radar_init_ok==0)
    {
        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }

    // radar_rfs();
    // vTaskDelay(1000 / portTICK_PERIOD_MS);
    // radar_trace_multi();
    // vTaskDelay(1000 / portTICK_PERIOD_MS);
    // radar_open_bluetooth();
    // vTaskDelay(1000 / portTICK_PERIOD_MS);

    // vTaskDelay(5000 / portTICK_PERIOD_MS);
    // LD2402_energy_output();

	for(;;)
	{
        while(true)
        {
            vTaskDelay(1000 / portTICK_PERIOD_MS);
        }
		runtimes++;
		if(test_radar_tcp_chanel_id>=0)
		{
			memset(test_recv_buf, 0, test_recv_buf_len);
			recv_len = My_TcpClient_RecvDataFromServer(test_radar_tcp_chanel_id, test_recv_buf, test_recv_buf_len, 0, 3000);
            if(recv_len>0)
            {
                ESP_LOGI("test_radar_task2()", "test_recv_buf(%d)=%s", recv_len, test_recv_buf);

                if ((strstr( (char *)test_recv_buf, "start detect"))!=NULL)
                {
                    ESP_LOGI("test_radar_task2()", "recv cmd: start detect");
                    My_Radar_LD2450_StartDetect();
                }
                if ((strstr( (char *)test_recv_buf, "stop detect"))!=NULL)
                {
                    ESP_LOGI("test_radar_task2()", "recv cmd: stop detect");
                    My_Radar_LD2450_StopDetect();
                }
                if ((strstr( (char *)test_recv_buf, "trace single"))!=NULL)
                {
                    ESP_LOGI("test_radar_task2()", "recv cmd: trace single");
                    My_Radar_LD2450_Config_Trace_Single();
                }
                if ((strstr( (char *)test_recv_buf, "trace multi"))!=NULL)
                {
                    ESP_LOGI("test_radar_task2()", "recv cmd: trace multi");
                    My_Radar_LD2450_Config_Trace_Multi();
                }
                if ((strstr( (char *)test_recv_buf, "reboot"))!=NULL)
                {
                    ESP_LOGI("test_radar_task2()", "recv cmd: reboot");
                    My_Radar_LD2450_Config_Reboot();
                }
                if ((strstr( (char *)test_recv_buf, "open bluetooth"))!=NULL)
                {
                    ESP_LOGI("test_radar_task2()", "recv cmd: open bluetooth");
                    My_Radar_LD2450_Config_Open_Bluetooth();
                }
                if ((strstr( (char *)test_recv_buf, "close bluetooth"))!=NULL)
                {
                    ESP_LOGI("test_radar_task2()", "recv cmd: close bluetooth");
                    My_Radar_LD2450_Config_Close_Bluetooth();
                }
                if ((strstr( (char *)test_recv_buf, "rfs"))!=NULL)
                {
                    ESP_LOGI("test_radar_task2()", "recv cmd: rfs");
                    My_Radar_LD2450_Config_RFS();
                }
                if ((strstr( (char *)test_recv_buf, "query frame version"))!=NULL)
                {
                    ESP_LOGI("test_radar_task2()", "recv cmd: query frame version");
                    My_Radar_LD2450_Config_Query_FrameVersion();
                }
                if ((strstr( (char *)test_recv_buf, "query mac"))!=NULL)
                {
                    ESP_LOGI("test_radar_task2()", "recv cmd: query mac");
                    My_Radar_LD2450_Config_Query_Mac();
                }
                if ((strstr( (char *)test_recv_buf, "query filter"))!=NULL)
                {
                    ESP_LOGI("test_radar_task2()", "recv cmd: query filter");
                    My_Radar_LD2450_Config_Query_CurAreaFilterConfig();
                }
                char* setfilter_start = NULL;
                if ((setfilter_start=(strstr( (char *)test_recv_buf, "set filter:")))!=NULL)
                {
                    ESP_LOGI("test_radar_task2()", "recv cmd: set filter");

                    char* setfilter_end = NULL;
                    if ((setfilter_end=(strstr( (char *)setfilter_start, "end")))!=NULL)
                    {
                        char* setfilter_data_start = setfilter_start + strlen("set filter:");
                        printf("setfilter_end-setfilter_data_start=%d\n", setfilter_end-setfilter_data_start);
                        char* area_number_str = calloc(1, setfilter_end-setfilter_data_start+1);
                        memcpy(area_number_str, setfilter_data_start, setfilter_end-setfilter_data_start);

                        printf("area_number_str=%s\n", area_number_str);

                        int16_t filter_data_array[12] = {0};
                        uint8_t filter_type = 0;
                        memset(filter_data_array, 0, sizeof(filter_data_array));

                        char* pt = NULL;
                        pt = strtok (area_number_str,",");
                        if(pt!=NULL)
                        {
                            filter_type = atoi(pt);
                        }
                        
                        pt = strtok (NULL,",");
                        for(int i=0; pt!=NULL&&i<12; i++)
                        if(pt!=NULL)
                        {
                            filter_data_array[i] = atoi(pt);
                            pt = strtok (NULL,",");
                        }

                        for(int i=0; i<12; i++)
                        {
                            printf("area1_e_y=%d(%#04X)\n", filter_data_array[i], filter_data_array[i]);
                        }

                        // for(int i=0; i<12; i++)
                        // {
                        //     if(filter_data_array[i]&0x8000)
                        //     {
                        //         filter_data_array[i] = abs(filter_data_array[i]);
                        //         filter_data_array[i]|=0x8000;
                        //     }
                        // }

                        My_Radar_LD2450_Config_Set_CurAreaFilterConfig(filter_type, filter_data_array);

                        for(int i=0; i<12; i++)
                        {
                            printf("filter_data_array[%d]=%d(%#04X)\n", i, filter_data_array[i], filter_data_array[i]);
                        } 
                        free(area_number_str);
                    }
                }
            }
			else
            {
                vTaskDelay(1000 / portTICK_PERIOD_MS);
            }
		}
        else
        {
            vTaskDelay(1000 / portTICK_PERIOD_MS);
        }
	}
}

static int MyTestRadar_Queue_Init(my_test_radar_queue_t** p, uint8_t data_area)
{
    if(data_area)
    {
        char* p_temp = (char*)calloc(MY_TEST_RADAR_QUEUE_LEN, MY_TEST_RADAR_PACK_SIZE);
        if(p_temp!=NULL)
        { 
            for(int i=0; i<MY_TEST_RADAR_QUEUE_LEN; i++)
            {
                p[i] = (my_test_radar_queue_t*)calloc(1, sizeof(my_test_radar_queue_t));
                if(p[i]!=NULL)
                {
                    p[i]->data = p_temp+(i*(MY_TEST_RADAR_PACK_SIZE-8));
                }
                else
                {
                    ESP_LOGE("MyTestRadar_Queue_Init()", "error1\n");
                    return 1;
                }
            }
        }
        else
        {
            ESP_LOGE("MyTestRadar_Queue_Init()", "error2\n");
            return 2;
        }
    }
    else
    {
        for(int i=0; i<MY_TEST_RADAR_QUEUE_LEN; i++)
        {
            p[i] = (my_test_radar_queue_t*)calloc(1, sizeof(my_test_radar_queue_t));
            if(p[i]==NULL)
            {
                ESP_LOGE("MyTSFC_Queue_Init()", "error3, i=%d\n", i);
                return i;
            }
        }
    }
    return MY_TEST_RADAR_QUEUE_LEN;
}

uint8_t chip_mac_int[6];
char chip_mac_str[12+1];
char dev_id[64];
int MyTest_Radar_Init(void)
{
    radar_data_report_buf = calloc(1, 16384);
    if(radar_data_report_buf==NULL)
    {
        ESP_LOGE("MyTest_Radar_Init()", "radar_data_report_buf=NULL\n");
        return -1;
    }

    radar_data_regular_report_buf = calloc(1, radar_data_regular_report_buf_size);
    ESP_LOGI("MyTest_Radar_Init()", "radar_data_regular_report_buf_size=%d\n", radar_data_regular_report_buf_size);
    if(radar_data_regular_report_buf==NULL)
    {
        ESP_LOGE("MyTest_Radar_Init()", "radar_data_regular_report_buf=NULL\n");
        return -1;
    }

    radar_data_regular_report_buf_default = calloc(1, radar_data_regular_report_buf_default_size);
    ESP_LOGI("MyTest_Radar_Init()", "radar_data_regular_report_buf_default_size=%d\n", radar_data_regular_report_buf_default_size);
    if(radar_data_regular_report_buf_default==NULL)
    {
        ESP_LOGE("MyTest_Radar_Init()", "radar_data_regular_report_buf_default=NULL\n");
        return -1;
    }

    memset(chip_mac_str, 0, sizeof(chip_mac_str));
    memset(chip_mac_int, 0, sizeof(chip_mac_int));
    esp_read_mac(chip_mac_int, 0);
	for(int i=0; i<6; i++)
	{
		sprintf(&chip_mac_str[i*2], "%02X", chip_mac_int[i]);
	}
	chip_mac_str[sizeof(chip_mac_str)] = 0;
	printf("chip_mac:%s\n", chip_mac_str);
    
    
    memset(dev_id, 0, sizeof(dev_id));
    
    sprintf(dev_id, "dev_id=%s", chip_mac_str);
    
    printf("ok21\n");
    MyTestRadar_Queue_Init(my_test_radar_rcv_queue_array, 1);

    Myradar_regular_data_MutexSemaphore = xSemaphoreCreateMutex();
    if(Myradar_regular_data_MutexSemaphore==NULL)
    {
        ESP_LOGE("My_IotBox_Init()", "Myradar_regular_data_MutexSemaphore is NULL!!!");
        vTaskDelay(3000 / portTICK_PERIOD_MS);
        My_esp_restart();
    }

    my_test_radar_rcv_queue = xQueueGenericCreate(MY_TEST_RADAR_QUEUE_LEN, sizeof(my_test_radar_queue_t*), 1);
    if(my_test_radar_rcv_queue==NULL)ESP_LOGE("MyTest_Radar_Init()", "my_test_radar_rcv_queue=NULL\n");

    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    // ESP_LOGI("MyTest_Radar_Init()", "My_TcpClient_CreateSocket(\"***********\", 5000);");
	// test_radar_tcp_chanel_id = -1;
    // vTaskDelay(1000 / portTICK_PERIOD_MS);
    // do
    // {
    //     test_radar_tcp_chanel_id = My_TcpClient_CreateSocket("***********", 5000);
    //     vTaskDelay(1000 / portTICK_PERIOD_MS);
    // }while(test_radar_tcp_chanel_id<0);

	// vTaskDelay(5000 / portTICK_PERIOD_MS);

    //xTaskCreate(CheckPowerTask,  "CheckPowerTask",    4096,    NULL, 14, NULL);
    p_task_stack = calloc(1, 4096*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
            //xTaskCreateStatic(&test_radar_task1, "test_radar_task1", 4096, NULL, 8, p_task_stack, p_task_data);
			xTaskCreateStaticPinnedToCore(&radar_task1, "radar_task1", 4096, NULL, 8, p_task_stack, p_task_data, 0);
        }
    }

	p_task_stack = calloc(1, 4096*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
            //xTaskCreateStatic(&test_radar_task1, "test_radar_task1", 4096, NULL, 8, p_task_stack, p_task_data);
			xTaskCreateStaticPinnedToCore(&radar_task2, "radar_task2", 4096, NULL, 8, p_task_stack, p_task_data, 1);
        }
    }

    MyRadar_Init();

    radar_init_ok = true;
    
    return 0;
}

#endif