#ifndef MY_LEDC_H
#define MY_LEDC_H

#include "my_config.h"

#define MY_LEDC_FUNC    1

#if MY_LEDC_FUNC

#include <lwip/netdb.h>

typedef enum
{
    ledmode_fade_flash=1,
    ledmode_on,
    ledmode_flash,
    ledmode_flash1,
    ledmode_flash2,
    ledmode_flash3,
    ledmode_flash_find_dev,
    ledmode_fade,
    ledmode_off
}led_mode;
typedef struct
{
    bool led_mng_enable;
    led_mode mode;
}my_ledc_info_t;

#define MyLedTask_CREATE_TYPE   TASK_CREATE_TYPE_DYNAMIC

#if MyLedTask_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
#define MyLedTask_COREID    1
#define MyLedTask_priority  5
#define MyLedTask_task_stack_size   2048
#elif MyLedTask_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
#define MyLedTask_COREID    1
#define MyLedTask_priority  5
#define MyLedTask_task_stack_size   2048
#endif

void MyLedInit(void);
void MyLed_SetMode(uint8_t new_mode);
uint8_t MyLed_GetMode(void);
void MyLed_Mng_Enable(void);
void MyLed_Mng_Disable(void);
void MyLed_SetMode_Super(uint8_t new_mode);

#endif
#endif