#ifndef E104_BT53C3_H
#define E104_BT53C3_H


#include <lwip/netdb.h>


typedef struct
{
    int number;
    int dev_role;
    int con_status;
    char mac[32];
}e104_bt53c3_connection_info_t;

typedef struct
{
    int dev_role;
    int at_mode;
    char mac[32];
    char adv_name[32];
    char adv_data[32];
}e104_bt53c3_info_t;


int e104_bt53c3_mode_cmd(void);
int e104_bt53c3_mode_data(void);
int e104_bt53c3_rst(void);
int e104_bt53c3_set_dev_role(int role);
int e104_bt53c3_set_adv_name(char *name, int len);
int e104_bt53c3_set_adv_data(char *data, int len);
int e104_bt53c3_send_data_to_master(char *data, int len);
int e104_bt53c3_init(void);
int e104_bt53c3_set_adv_interval(int adv_interval);



#endif