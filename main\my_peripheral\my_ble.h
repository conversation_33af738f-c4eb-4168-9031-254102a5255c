#ifndef MY_BLE_H
#define MY_BLE_H

// #define MY_BLE_FUNC  0

// #if MY_BLE_FUNC

#include <lwip/netdb.h>


#define GATTS_DEVICE_ENABLE_DEFAULT   1                     //开机后默认打开蓝牙
#define GATTS_DEVICE_NAME_DEFAULT   "SWQZ_BLE"              //默认的蓝牙名称，当 my_ble_conf_t 为空时有效
#define custom_adv_data_default     "0123456789abcdef"      //默认的蓝牙广播数据，当 my_ble_conf_t 为空时有效

#define GATTS_SERVICE_UUID_TEST_A_DEFAULT   0x1122
#define GATTS_CHAR_UUID_TEST_A_DEFAULT      0xAABB
#define GATTS_SERVICE_UUID_TEST_B_DEFAULT   0x3344
#define GATTS_CHAR_UUID_TEST_B_DEFAULT      0xCCDD

/* typedef struct
{
    uint8_t enable;                 //开机后是否开启蓝牙
    uint8_t adv_custom_data_len;    //自定义广播包长度
    char adv_custom_data[24];       //自定义广播包内容
    char gatts_device_name[32];     //蓝牙设备名称
}my_ble_conf_t; */
#define MY_BLE_ADV_CUSTOM_DATA_LEN_MAX  16
#define MY_BLE_NAME_LEN_MAX  16
typedef struct
{
    uint8_t enable;                 //开机后是否开启蓝牙
    uint8_t adv_custom_data_len;    //自定义广播包长度
    char adv_custom_data[MY_BLE_ADV_CUSTOM_DATA_LEN_MAX];       //自定义广播包内容
    char gatts_device_name[MY_BLE_NAME_LEN_MAX];     //蓝牙设备名称
    uint16_t service_uuid_a;
    uint16_t char_uuid_a;
    uint16_t service_uuid_b;
    uint16_t char_uuid_b;
    char mac_str[32];
}my_ble_conf_t;

/* typedef struct//my_ble.c内部使用
{
    my_ble_conf_t conf;             //my_ble_conf_t
    uint8_t init_ok;                //蓝牙是否已经开启
    uint8_t first_init;             //开机后第一次配置
}my_ble_info_t; */
typedef struct
{
    my_ble_conf_t conf;             //my_ble_conf_t
    uint8_t init_ok;                //蓝牙是否已经开启
    uint8_t first_init;             //开机后第一次配置
    my_ble_conf_t* conf_timer_safe;
    int (*MyBleECCB_Callback)(char*, int);
}my_ble_info_t;


/**
 * @brief 根据conf配置蓝牙并立即生效
 * @param conf 为空时将使用默认参数 GATTS_DEVICE_NAME_DEFAULT, custom_adv_data_default
 * @note 
 */
int Gatts_Init(my_ble_conf_t* conf);

/**
 * @brief 根据conf重新配置蓝牙并立即生效
 * @param conf 为空时将使用默认参数 GATTS_DEVICE_NAME_DEFAULT, custom_adv_data_default
 * @note 蓝牙必须已经开启，此函数才生效。如果蓝牙已经打开，则会关闭蓝牙，然后再重新调用Gatts_Init()
 */
int MyBleReInit(my_ble_conf_t* conf);

/**
 * @brief 重启蓝牙
 * @note 配置不变
 */
int MyBleReStart(void);

/**
 * @brief 根据conf重新配置蓝牙并延迟生效
 * @param conf 为空时将使用默认参数 GATTS_DEVICE_NAME_DEFAULT, custom_adv_data_default
 * @note 蓝牙必须已经开启，此函数才生效。如果蓝牙已经打开，则会关闭蓝牙，然后再重新调用Gatts_Init()
 */
int MyBleReInit_TimerSafe(my_ble_conf_t* conf);

/**
 * @brief 开启蓝牙
 * @note 可重复调用，重复调用无效
 */
int MyBle_Open(void);

/**
 * @brief 关闭蓝牙
 * @note 可重复调用，重复调用无效
 */
int MyBle_Close(void);

/**
 * @brief 蓝牙低功耗处理, 仅调用 MyBle_Close()
 */
int MyBleConfig_Before_LightSleep(void);

/**
 * @brief 蓝牙低功耗处理, 仅调用 MyBle_Open()
 */
int MyBleConfig_After_LightSleep(void);

void MyBle_ECCB_Register(int (*callback_func)(char*, int));

/**
 * @brief 
 * @return 1：蓝牙已打开，0：蓝牙未打开
 */
uint8_t MyBle_GetInitState(void);

/**
 * @brief 获取蓝牙名（临时配置名）
 * @return 1：失败，可能是saving为空，0：成功
 */
uint8_t MyBle_GetName_Cur(char* saving, uint8_t len);

/**
 * @brief 获取蓝牙名（NVS配置名）
 * @return 1：失败，可能是saving为空，0：成功
 */
uint8_t MyBle_GetName_NVS(char* saving, uint8_t len);

// #endif

#endif