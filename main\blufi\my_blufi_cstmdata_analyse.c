#include "my_blufi_cstmdata_analyse.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"
#include "freertos/semphr.h"
#include "esp_mac.h"
#include "esp_log.h"

#include "esp_blufi_api.h"

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "mytest_radar.h"
#include "my_esp_chip_info_mng.h"
#include "my_time.h"

#include "my_whgm5.h"
#include "my_ledc.h"
#include "my_led_mng.h"
#include "my_config.h"
#include "my_workmode.h"
#include "my_iot_box.h"
#include "my_endian.h"

#include "my_relay.h"

#include "my_monitor.h"


QueueHandle_t my_blufi_cda_rcv_queue = NULL;
#define MY_BLUFI_CDA_RCV_QUEUE_LEN 8
#define MY_BLUFI_CDA_RCV_QUEUE_PACK_SIZE 8192
typedef struct
{
    uint8_t id;
	uint8_t valid;
	uint64_t time_stamp;
    uint32_t len;
	char* data;                 //应用服务层数据
}my_blufi_cda_rcv_queue_t;

my_blufi_cda_rcv_queue_t* my_blufi_cda_rcv_queue_array[MY_BLUFI_CDA_RCV_QUEUE_LEN];


int MyBlufi_Send_Data_to_Master(void* data, int len)
{
    return esp_blufi_send_custom_data(data, len);
}

extern single_object_t rd24_objects[3];
extern my_esp_chip_info_mng_t my_esp_chip_info_mng_info;
extern TaskHandle_t MyLedMng_TaskHandle;

#define MyBlufi_CDA_sendbuf_size (8192)
#define MyBlufi_CDA_itempack_size (4096)

int MyBlufi_CDA_new_head(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    char buffer[64];
    memset(buffer, 0, 64);
    struct tm* tminfo1;
    time_t now_time = MyTime_GetTime();
    tminfo1 = localtime( &now_time );
    strftime(buffer,64,"%Y-%m-%e %H:%M:%S", tminfo1);//以年月日_时分秒的形式表示当前时间
    sprintf(saving, "\r\n\r\n>>-- bts %s --<<\r\n", buffer);
    BLUFICDA_INFO("MyBlufi_CDA_new_response, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_new_tail(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    char buffer[64];
    memset(buffer, 0, 64);
    struct tm* tminfo1;
    time_t now_time = MyTime_GetTime();
    tminfo1 = localtime( &now_time );
    strftime(buffer,64,"%Y-%m-%e %H:%M:%S", tminfo1);//以年月日_时分秒的形式表示当前时间
    sprintf(saving, "\r\n>>-- bts end %s --<<\r\n", buffer);
    BLUFICDA_INFO("MyBlufi_CDA_new_response, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_rd_ob(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    int write_posi = strlen(saving);
    sprintf(saving+write_posi, "\r\n雷达目标:\r\n");
    for(int i=0; i<3; i++)
    {
        write_posi = strlen(saving);
        sprintf(saving+write_posi, "\t目标%d: %dmm, %dmm, %dcm/s\r\n", 
                            rd24_objects[i].number+1, 
                            rd24_objects[i].x, 
                            rd24_objects[i].y, 
                            rd24_objects[i].speed);
    }
    BLUFICDA_INFO("MyBlufi_CDA_get_rd_ob, saving=%s\n", saving);

    return strlen(saving);
}

extern int radar_get_rgl_data(char** saving);

typedef struct
{
    int16_t x;
    int16_t y;
}x_single_ob_t;

typedef struct
{
    x_single_ob_t obs[3];
}x_obs_t;
int MyBlufi_CDA_get_rd_rgl_ob(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    char* p_data = NULL;
    int data_len = radar_get_rgl_data(&p_data);
    ESP_LOGI("MyBlufi_CDA_get_rd_rgl_ob", "data_len=%d\n", data_len);
    if(p_data != NULL)
    {
        int write_posi = strlen(saving);
        sprintf(saving+write_posi, "雷达定时统计数据:\r\n");

        int valid_ob_num = data_len/18;

        write_posi = strlen(saving);
        sprintf(saving+write_posi, "\t变化次数: %d\r\n\r\n", valid_ob_num);

        char* p_read_posi = p_data;

        for(int i=0; i<valid_ob_num; i++)
        {
            p_read_posi=p_data+(18*i);
            write_posi = strlen(saving);

            if(write_posi<MyBlufi_CDA_itempack_size-(strlen("\t2025-01-01 00:00:00\r\n\t(9999, 9999) (9999, 9999) (9999, 9999)\r\n")+strlen("...\r\n\t...\r\n")+strlen("\r\n>>-- bts end 2025-01-01 00:00:00 --<<\r\n")))
            {
                char buffer[64];
                memset(buffer, 0, 64);
                struct tm* tminfo1;
                time_t obs_time_stamp = (*((time_t*) p_read_posi));
                obs_time_stamp = my_endian_conversion_32(obs_time_stamp);
                tminfo1 = localtime( &obs_time_stamp );
                strftime(buffer,64,"%Y-%m-%e %H:%M:%S", tminfo1);//以年月日_时分秒的形式表示当前时间

                x_obs_t* p_obs_data = p_read_posi+6;
                sprintf(saving+write_posi, "\t[%d] %s\r\n\t(%d, %d) (%d, %d) (%d, %d)\r\n\r\n",
                                    i+1,
                                    buffer, 
                                    my_endian_conversion_16(p_obs_data->obs[0].x), 
                                    my_endian_conversion_16(p_obs_data->obs[0].y), 
                                    my_endian_conversion_16(p_obs_data->obs[1].x), 
                                    my_endian_conversion_16(p_obs_data->obs[1].y),
                                    my_endian_conversion_16(p_obs_data->obs[2].x), 
                                    my_endian_conversion_16(p_obs_data->obs[2].y) );
            }
            else
            {
                write_posi = strlen(saving);
                sprintf(saving+write_posi, "\t...\r\n\t...\r\n");
                ESP_LOGE("MyBlufi_CDA_get_rd_rgl_ob", "MyBlufi_CDA_get_rd_rgl_ob, saving FULL\n");
            }
        }
        free(p_data);
        p_data = NULL;
    }
    BLUFICDA_INFO("MyBlufi_CDA_get_rd_ob, saving(%d)=\n%s\n", strlen(saving), saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_meminfo(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    int write_posi = strlen(saving);
    write_posi = strlen(saving);
    sprintf(saving+write_posi, "内存信息:\r\n");
    write_posi = strlen(saving);
    sprintf(saving+write_posi, "\ttotal_free_heap_size: %d.%dKB\r\n", my_esp_chip_info_mng_info.total_free_heap_size/1024, my_esp_chip_info_mng_info.total_free_heap_size%1024); 
    write_posi = strlen(saving);
    sprintf(saving+write_posi, "\tinternal_free_heap_size: %d.%dKB\r\n", my_esp_chip_info_mng_info.internal_free_heap_size/1024, my_esp_chip_info_mng_info.internal_free_heap_size%1024); 
    write_posi = strlen(saving);
    sprintf(saving+write_posi, "\thistory_min_iram_size: %d.%dKB\r\n", my_esp_chip_info_mng_info.history_min_iram_size/1024, my_esp_chip_info_mng_info.history_min_iram_size%1024); 
    write_posi = strlen(saving);
    sprintf(saving+write_posi, "\texternal_free_heap_size: %d.%dKB\r\n", my_esp_chip_info_mng_info.external_free_heap_size/1024, my_esp_chip_info_mng_info.external_free_heap_size%1024); 
    write_posi = strlen(saving);
    sprintf(saving+write_posi, "\thistory_min_eram_size: %d.%dKB\r\n", my_esp_chip_info_mng_info.history_min_eram_size/1024, my_esp_chip_info_mng_info.history_min_eram_size%1024); 
    BLUFICDA_INFO("MyBlufi_CDA_get_meminfo, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_rst_reason(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    int write_posi = strlen(saving);
    write_posi = strlen(saving);
    sprintf(saving+write_posi, "rst_reason: %s\r\n", my_esp_chip_info_mng_info.system_reset_reason_str);
    BLUFICDA_INFO("MyBlufi_CDA_get_rst_reason, saving=%s\n", saving);

    return strlen(saving);
}

extern int e104_role;
extern int e104_adv_interval;
extern int e104_adv_power;
extern int e104_long_range;
extern char e104_mac[32];
int MyBlufi_CDA_get_bt_role(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    int write_posi = strlen(saving);
    write_posi = strlen(saving);
    if(e104_role == 0)
    {
        sprintf(saving+write_posi, "角色：%s\r\n", "从机");
    }
    else if(e104_role == 1)
    {
        sprintf(saving+write_posi, "角色：%s\r\n", "主机");
    }
    else if(e104_role == 2)
    {
        sprintf(saving+write_posi, "角色：%s\r\n", "从机+主机");
    }
    else if(e104_role == 3)
    {
        sprintf(saving+write_posi, "角色：%s\r\n", "Beacon");
    }
    else
    {
        sprintf(saving+write_posi, "角色：%s\r\n", "未知");
    }
    BLUFICDA_INFO("MyBlufi_CDA_get_bt_role, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_bt_adv_interval(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    int write_posi = strlen(saving);
    write_posi = strlen(saving);
    sprintf(saving+write_posi, "广播间隔：%dms\r\n", e104_adv_interval);
    BLUFICDA_INFO("MyBlufi_CDA_get_bt_adv_interval, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_bt_adv_power(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    int write_posi = strlen(saving);
    write_posi = strlen(saving);
    sprintf(saving+write_posi, "广播功率：%ddBm\r\n", e104_adv_power);
    BLUFICDA_INFO("MyBlufi_CDA_get_bt_adv_power, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_bt_long_range(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    int write_posi = strlen(saving);
    write_posi = strlen(saving);
    if(e104_long_range == 0)
    {
        sprintf(saving+write_posi, "长距离模式：%s\r\n", "关闭");
    }
    else if(e104_long_range == 1)
    {
        sprintf(saving+write_posi, "长距离模式：%s\r\n", "开启");
    }
    else
    {
        sprintf(saving+write_posi, "长距离模式：%s\r\n", "未知");
    }
    BLUFICDA_INFO("MyBlufi_CDA_get_bt_long_range, saving=%s\n", saving);

    return strlen(saving);
}



int MyBlufi_CDA_get_debug_mode(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    int write_posi = strlen(saving);

    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    switch(p_my_conf->workmode.conf.debug_mode)
    {
        case 0:
            sprintf(saving, "调试模式: %s\r\n", "关闭");
        break;
        case 1:
            sprintf(saving, "调试模式: %s\r\n", "模式1");
        break;
    }

    BLUFICDA_INFO("MyBlufi_CDA_get_debug_mode, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_systime(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    char buffer[80];
    memset(buffer, 0, 80);
    struct tm* tminfo1;

    time_t nowtime = MyTime_GetTime();
    tminfo1 = localtime( &nowtime );

    strftime(buffer,80,"%Y-%m-%e %H:%M:%S", tminfo1);//以年月日_时分秒的形式表示当前时间
    int write_posi = strlen(saving);
    sprintf(saving+write_posi, "系统时间:\r\n");
    write_posi = strlen(saving);

    time_t runtimesincepoweron = MyTime_GetRuntimeSincePoweron();
    time_t runtimesincereset = MyTime_GetRuntimeSinceReset();
    sprintf(saving+write_posi, "\t当前系统时间: %s\r\n\t开机以来的时间: %ld天%ld时%ld分%ld秒\r\n\t复位以来的时间: %ld天%ld时%ld分%ld秒\r\n", 
            buffer, 
            runtimesincepoweron/(24*60*60), runtimesincepoweron%(24*60*60)/(60*60), runtimesincepoweron%(24*60*60)%(60*60)/60, runtimesincepoweron%60,
            runtimesincereset/(24*60*60), runtimesincereset%(24*60*60)/(60*60), runtimesincereset%(24*60*60)%(60*60)/60, runtimesincereset%60);
    BLUFICDA_INFO("MyBlufi_CDA_get_systime, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_systime_btc(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    char buffer[80];
    memset(buffer, 0, 80);
    struct tm* tminfo1;

    time_t nowtime = MyTime_GetTime();
    tminfo1 = localtime( &nowtime );

    strftime(buffer,80,"%Y-%m-%e %H:%M:%S", tminfo1);//以年月日_时分秒的形式表示当前时间
    int write_posi = strlen(saving);
    sprintf(saving+write_posi, "系统时间:\r\n");
    write_posi = strlen(saving);

    time_t runtimesincepoweron = MyTime_GetRuntimeSincePoweron();
    time_t runtimesincereset = MyTime_GetRuntimeSinceReset();
    sprintf(saving+write_posi, "\t当前系统时间: %s\r\n\t开机总时长: %ld天%ld时%ld分%ld秒\r\n", 
            buffer, 
            runtimesincepoweron/(24*60*60), runtimesincepoweron%(24*60*60)/(60*60), runtimesincepoweron%(24*60*60)%(60*60)/60, runtimesincepoweron%60);
    BLUFICDA_INFO("MyBlufi_CDA_get_systime_btc, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_iccid(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    char iccid[32];
    memset(iccid, 0, sizeof(iccid));
    MyWHGM5_GetICCID(iccid, sizeof(iccid));
    sprintf(saving, "ICCID: %s\r\n", iccid);
    BLUFICDA_INFO("MyBlufi_CDA_get_iccid, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_imei(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    char imei[32];
    memset(imei, 0, sizeof(imei));
    MyWHGM5_GetIMEI(imei, sizeof(imei));
    sprintf(saving, "IMEI: %s\r\n", imei);
    BLUFICDA_INFO("MyBlufi_CDA_get_imei, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_dev_number(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    char imei[32];
    memset(imei, 0, sizeof(imei));
    MyWHGM5_GetIMEI(imei, sizeof(imei));
    sprintf(saving, "设备编号: %s\r\n", imei);
    BLUFICDA_INFO("MyBlufi_CDA_get_dev_number, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_csq(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    int csq = MyWHGM5_GetCSQ();
    if(csq==99)
    {
        sprintf(saving, " 信号强度rssi: ...(无信号)\r\n");
    }
    else
    {
        int rssi = csq*2-113;
        if(rssi>=-51)
        {
            sprintf(saving, " 信号强度rssi: %ddBm(非常好)\r\n", rssi);
        }
        if(rssi>=-60&&rssi<-51)
        {
            sprintf(saving, " 信号强度rssi: %ddBm(很好)\r\n", rssi);
        }
        if(rssi>=-70&&rssi<-60)
        {
            sprintf(saving, " 信号强度rssi: %ddBm(较好)\r\n", rssi);
        }
        if(rssi>=-80&&rssi<-70)
        {
            sprintf(saving, " 信号强度rssi: %ddBm(一般)\r\n", rssi);
        }
        if(rssi>=-85&&rssi<-80)
        {
            sprintf(saving, " 信号强度rssi: %ddBm(较弱)\r\n", rssi);
        }
        if(rssi>=-90&&rssi<-85)
        {
            sprintf(saving, " 信号强度rssi: %ddBm(很弱)\r\n", rssi);
        }
        if(rssi<-90)
        {
            sprintf(saving, " 信号强度rssi: %ddBm(非常弱)\r\n", rssi);
        }
    }
    
    BLUFICDA_INFO("MyBlufi_CDA_get_rssi, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_iot_ip(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    if(p_my_conf->net.conf_tcp1.address_type==0)
    {
        sprintf(saving, "IP: %s, %d\r\n", p_my_conf->net.conf_tcp1.address, p_my_conf->net.conf_tcp1.port);
    }
    else if(p_my_conf->net.conf_tcp1.address_type==1)
    {
        sprintf(saving, "IP: %d.%d.%d.%d, %d\r\n", 
                p_my_conf->net.conf_tcp1.address[0], 
                p_my_conf->net.conf_tcp1.address[1], 
                p_my_conf->net.conf_tcp1.address[2], 
                p_my_conf->net.conf_tcp1.address[3], 
                p_my_conf->net.conf_tcp1.port);
    }
    BLUFICDA_INFO("MyBlufi_CDA_get_iot_ip, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_iot_dev_status(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    switch(p_my_conf->factory.dev_state)
    {
        case 0:
            sprintf(saving, "IoT设备注册状态: %s\r\n", "设备未激活");
        break;
        case 1:
            sprintf(saving, "IoT设备注册状态: %s\r\n", "设备已本地激活, 但未注册到IoT Server");
        break;
        case 2:
            sprintf(saving, "IoT设备注册状态: %s\r\n", "设备已注册到IoT Server");
        break;
    }
    
    BLUFICDA_INFO("MyBlufi_CDA_iot_dev_status, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_iot_nvs_appid(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    sprintf(saving, "AppID: %d(0x%04x)\r\n", p_my_conf->asl.appid, p_my_conf->asl.appid);
    BLUFICDA_INFO("MyBlufi_CDA_get_iot_nvs_appid, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_last_isp_time(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    char buffer[80];
    memset(buffer, 0, 80);
    struct tm* tminfo1;

    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();

    time_t last_isp_time = p_my_conf->asl.last_sync_param_timestamp;
    tminfo1 = localtime( &last_isp_time );

    strftime(buffer,80,"%Y-%m-%e %H:%M:%S", tminfo1);//以年月日_时分秒的形式表示当前时间
    int write_posi = strlen(saving);
    write_posi = strlen(saving);

    sprintf(saving+write_posi, "上次IoT参数同步时间: %s\r\n", buffer);
    BLUFICDA_INFO("MyBlufi_CDA_get_last_isp_time, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_iot_nvs_poweroff_alarm_enable_status(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();

    if(p_my_conf->power.poweroff_alarm_enable_state)
    {
        sprintf(saving, "断电告警开关: 已打开\r\n");
    }
    else
    {
        sprintf(saving, "断电告警开关: 已关闭\r\n");
    }

    int write_posi = strlen(saving);
    write_posi = strlen(saving);
    if(p_my_conf->power.shutdown_after_poweroffAlarm)
    {
        sprintf(saving+write_posi, "断电告警后是否关机: 关机\r\n");
    }
    else
    {
        sprintf(saving+write_posi, "断电告警后是否关机: 不关机\r\n");
    }
    
    BLUFICDA_INFO("MyBlufi_CDA_get_iot_nvs_poweroff_alarm_enable_status, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_esp_mac(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    char mac[32];
    memset(mac, 0, sizeof(mac));
    MyEspChipInfoMng_GetMacStr(mac);
    sprintf(saving, "ESP-MAC: %s\r\n", mac);
    BLUFICDA_INFO("MyBlufi_CDA_get_esp_mac, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_version(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    
    sprintf(saving, "软件版本: %s\r\n版本代号: %d\r\n文件名称: %s\r\n", 
        p_my_conf->factory.my_ota_info_local.vername,
        p_my_conf->factory.my_ota_info_local.vercode,
        p_my_conf->factory.my_ota_info_local.filename
    );
    BLUFICDA_INFO("MyBlufi_CDA_get_version, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_version_btc(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    
    sprintf(saving, "软件版本: %s\r\n", 
        p_my_conf->factory.my_ota_info_local.vername
    );
    BLUFICDA_INFO("MyBlufi_CDA_get_version_btc, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_ota_mode(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    switch(p_my_conf->factory.ota_check_type)
    {
        case 0:
            sprintf(saving, "OTA模式: %s\r\n", "已关闭自动升级");
        break;
        case 1:
            sprintf(saving, "OTA模式: %s\r\n", "每次开机后");
        break;
        case 2:
            sprintf(saving, "OTA模式: %s\r\n", "每天00:00~4:00");
        break;
        case 3:
            sprintf(saving, "OTA模式: %s\r\n", "每次开机后 + 每天00:00~4:00");
        break;
    }
    
    BLUFICDA_INFO("MyBlufi_CDA_iot_dev_status, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_nvs_workmode(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    switch(p_my_conf->workmode.conf.workmode)
    {
        case workmode_standby:
            sprintf(saving, "已设定模式: %s\r\n", "待机模式(standby)");
        break;
        case workmode_energy_saving:
            sprintf(saving, "已设定模式: %s\r\n", "节能模式(energy_saving)");
        break;
        case workmode1_realtime:
            sprintf(saving, "已设定模式: %s\r\n", "实时模式(realtime)");
        break;
        case workmode2_semirealtime:
            sprintf(saving, "已设定模式: %s\r\n", "半实时模式(semirealtime)");
        break;
        case workmode3_timer:
            sprintf(saving, "已设定模式: %s\r\n", "定时模式(timer)");
        break;
        case workmode4_semitimer:
            sprintf(saving, "已设定模式: %s\r\n", "半定时模式(semitimer)");
        break;
    }
    BLUFICDA_INFO("MyBlufi_CDA_get_nvs_workmode, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_cur_workmode(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_workmode_t cur_workmode = MyWorkmode_GetCurrentWorkmode();
    switch(cur_workmode)
    {
        case workmode_standby:
            sprintf(saving, "当前模式: %s\r\n", "待机模式(standby)");
        break;
        case workmode_energy_saving:
            sprintf(saving, "当前模式: %s\r\n", "节能模式(energy_saving)");
        break;
        case workmode1_realtime:
            sprintf(saving, "当前模式: %s\r\n", "实时模式(realtime)");
        break;
        case workmode2_semirealtime:
            sprintf(saving, "当前模式: %s\r\n", "半实时模式(semirealtime)");
        break;
        case workmode3_timer:
            sprintf(saving, "当前模式: %s\r\n", "定时模式(timer)");
        break;
        case workmode4_semitimer:
            sprintf(saving, "当前模式: %s\r\n", "半定时模式(semitimer)");
        break;
    }
    
    BLUFICDA_INFO("MyBlufi_CDA_get_cur_workmode, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_cur_measreportinfo(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    switch(p_my_conf->workmode.measReportMode)
    {
        case 0:
            sprintf(saving, "雷达测量报告上报模式: %s\r\n定时上报间隔: %d:%d:%d\r\n", "定时上报", p_my_conf->workmode.measReportInterval/3600, p_my_conf->workmode.measReportInterval%3600/60, p_my_conf->workmode.measReportInterval%60);
        break;
        case 1:
            sprintf(saving, "雷达测量报告上报模式: %s\r\n实时上报间隔: %d:%d:%d\r\n", "实时上报", 60/3600, 60/60, 60%60);
        break;
    }
    
    BLUFICDA_INFO("MyBlufi_CDA_get_cur_workmode, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_cur_worklist(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    int write_posi = strlen(saving);
    sprintf(saving+write_posi, "工作计划表:\r\n");
    for(int i=0; i<MYWORKMODE_WORKPLAN_LIST_COUNT; i++)
    {
        write_posi = strlen(saving);
        sprintf(saving+write_posi, "\t时段%d: %d:%d ~ %d:%d (%ds, %ds)\r\n", 
                    i+1,
                    p_my_conf->workmode.conf.workplan_list[i].work_start_time/3600,
                    p_my_conf->workmode.conf.workplan_list[i].work_start_time%3600/60,
                    p_my_conf->workmode.conf.workplan_list[i].work_end_time/3600,
                    p_my_conf->workmode.conf.workplan_list[i].work_end_time%3600/60,
                    p_my_conf->workmode.conf.workplan_list[i].work_start_time,
                    p_my_conf->workmode.conf.workplan_list[i].work_end_time
        );
    }
    BLUFICDA_INFO("MyBlufi_CDA_get_worklist, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_cur_sleeplist(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    int write_posi = strlen(saving);
    sprintf(saving+write_posi, "睡眠计划表:\r\n");
    for(int i=0; i<MYWORKMODE_WORKPLAN_LIST_COUNT; i++)
    {
        write_posi = strlen(saving);
        if((p_my_conf->workmode.conf.sleepplan_list[i].sleep_start_time+p_my_conf->workmode.conf.sleepplan_list[i].sleep_keep_time)>(24*60*60))
        {
            sprintf(saving+write_posi, "\t时段%d: %d:%d ~ %d:%d (+1天, 睡眠总时长: %d:%d)\r\n", 
                    i+1,
                    p_my_conf->workmode.conf.sleepplan_list[i].sleep_start_time/3600,
                    p_my_conf->workmode.conf.sleepplan_list[i].sleep_start_time%3600/60,
                    ((p_my_conf->workmode.conf.sleepplan_list[i].sleep_start_time+p_my_conf->workmode.conf.sleepplan_list[i].sleep_keep_time-(24*60*60)))/3600,
                    ((p_my_conf->workmode.conf.sleepplan_list[i].sleep_start_time+p_my_conf->workmode.conf.sleepplan_list[i].sleep_keep_time-(24*60*60)))%3600/60,
                    (p_my_conf->workmode.conf.sleepplan_list[i].sleep_keep_time)/3600,
                    (p_my_conf->workmode.conf.sleepplan_list[i].sleep_keep_time)%3600/60
            );
        }
        else
        {
            sprintf(saving+write_posi, "\t时段%d: %d:%d ~ %d:%d (睡眠总时长: %d:%d)\r\n", 
                    i+1,
                    p_my_conf->workmode.conf.sleepplan_list[i].sleep_start_time/3600,
                    p_my_conf->workmode.conf.sleepplan_list[i].sleep_start_time%3600/60,
                    ((p_my_conf->workmode.conf.sleepplan_list[i].sleep_start_time+p_my_conf->workmode.conf.sleepplan_list[i].sleep_keep_time))/3600,
                    ((p_my_conf->workmode.conf.sleepplan_list[i].sleep_start_time+p_my_conf->workmode.conf.sleepplan_list[i].sleep_keep_time))%3600/60,
                    (p_my_conf->workmode.conf.sleepplan_list[i].sleep_keep_time)/3600,
                    (p_my_conf->workmode.conf.sleepplan_list[i].sleep_keep_time)%3600/60
            );
        }
        
    }
    BLUFICDA_INFO("MyBlufi_CDA_get_cur_sleeplist, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_model(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    sprintf(saving, "设备型号: %s\r\n", p_my_conf->factory.devModel);
    BLUFICDA_INFO("MyBlufi_CDA_get_model, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_httpotaurl(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    sprintf(saving, "http ota url: %s\r\n", p_my_conf->factory.http_ota_url);
    BLUFICDA_INFO("MyBlufi_CDA_get_httpotaurl, saving=%s\n", saving);

    return strlen(saving);
}

extern bool My_Wifi_Get_State(void);
int MyBlufi_CDA_get_wifi_status(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    if(My_Wifi_Get_State()==true)
    {
        sprintf(saving, "WIFI: 已连接\r\n");
    }
    else
    {
        sprintf(saving, "WIFI: 未连接\r\n");
    }
    BLUFICDA_INFO("MyBlufi_CDA_get_wifi_status, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_relay1_mode(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    if(p_my_conf->workmode.relay1_mode==0)
    {
        sprintf(saving, "继电器1模式: 手动常态, 默认常开\r\n");
    }
    else if(p_my_conf->workmode.relay1_mode==1)
    {
        sprintf(saving, "继电器1模式: 手动常态, 默认常闭\r\n");
    }
    else if(p_my_conf->workmode.relay1_mode==2)
    {
        sprintf(saving, "继电器1模式: 点动常开\r\n");
    }
    else if(p_my_conf->workmode.relay1_mode==3)
    {
        sprintf(saving, "继电器1模式: 点动常闭\r\n");
    }
    BLUFICDA_INFO("MyBlufi_CDA_get_relay1_mode, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_relay1_Xmode_keep_time(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    sprintf(saving, "点动暂态保持时间: %ds\r\n", p_my_conf->workmode.relay1_Xmode_keep_time);
    BLUFICDA_INFO("MyBlufi_CDA_get_relay1_mode, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_channeldoor_mode(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    if(p_my_conf->workmode.channeldoor_mode==0)
    {
        sprintf(saving, "闸机模式: mode0 (全天禁用)\r\n");
    }
    else if(p_my_conf->workmode.channeldoor_mode==1)
    {
        sprintf(saving, "闸机模式: mode1 (全天启用)\r\n");
    }
    else if(p_my_conf->workmode.channeldoor_mode==2)
    {
        sprintf(saving, "闸机模式: mode2 (定时启用)\r\n");
    }
    BLUFICDA_INFO("MyBlufi_CDA_get_relay1_mode, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_relay1_status(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    
    if(My_Relay_GetState(1)==true)
    {
        sprintf(saving, "继电器1当前状态: 吸合\r\n");
    }
    else
    {
        sprintf(saving, "继电器1当前状态: 释放\r\n");
    }
    BLUFICDA_INFO("MyBlufi_CDA_get_relay1_status, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_get_poweroncount(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    sprintf(saving, "开机次数: %d\r\n", p_my_conf->factory.poweron_count);
    BLUFICDA_INFO("MyBlufi_CDA_get_poweroncount, saving=%s\n", saving);

    return strlen(saving);
}

extern my_iotbox_info_t my_iotbox_info;
int MyBlufi_CDA_get_iot_login_status(char* saving)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    switch(my_iotbox_info.login_state)
    {
        case 0:
            sprintf(saving, "是否登录到了IoT Server: %s\r\n", "未登录");
        break;
        case 1:
            sprintf(saving, "是否登录到了IoT Server: %s\r\n", "已登录");
        break;
    }
    BLUFICDA_INFO("MyBlufi_CDA_get_iot_login_status, saving=%s\n", saving);

    return strlen(saving);
}

int MyBlufi_CDA_add_page(char* saving, char* name)
{
    memset(saving, 0, MyBlufi_CDA_itempack_size);
    sprintf(saving, "\r\n[%s]:\r\n", name);
    BLUFICDA_INFO("MyBlufi_CDA_add_page, saving=%s\n", saving);

    return strlen(saving);
}

char *bf_cda_in_sendbuf = NULL;
char *bf_cda_in_itembuf = NULL;
int bf_cda_sendbuf_written_len = 0;
int bf_cda_itembuf_written_len = 0;
void MyBlufi_CDA_in(char* data, int len)
{
    memcpy(bf_cda_in_sendbuf+bf_cda_sendbuf_written_len, data, len);
    bf_cda_sendbuf_written_len += len;
}

extern int My_IotBox_Routine_DevOTACheck(int force, int get_info_repeat_count, int download_repeat_count);
extern bool bts_rfs_request;
extern bool bts_check_ota_request;
extern bool bts_isp_request;
extern bool bts_rdrptrgl;
extern bool bts_reboot_request;

extern bool bts_debug_mode_request;
extern int bts_debug_mode;
extern bool bts_set_meas_report_mode_req;
extern int bts_new_meas_report_mode;
extern bool bts_modify_httpotaurl_request;
extern char* http_ota_url;
extern bool bts_httpota_request;

extern bool bts_set_relay1_mode_request;
extern int bts_relay1_mode;
extern bool bts_set_relay1_Xmode_keeptime_request;
extern int bts_relay1_Xmode_keeptime;
extern bool bts_set_channeldoor_mode_request;
extern int bts_channeldoor_mode;

extern bool bts_modify_iotip_request;
extern char* iot_ip;
extern bool bts_modify_iotport_request;
extern int iot_port;

int pickurl(char* input, char** output, const char *prefix) {
    
    // 固定前缀
    // char prefix[] = "bts set http ota url: ";
    int prefix_len = strlen(prefix);
    
    // 找到前缀在输入字符串中的位置
    char *start = strstr(input, prefix);
    if (start != NULL) {
        // 移动指针到URL的起始位置
        start += prefix_len;
        
        // 找到URL结束的位置，即空格的位置
        char *end_url = strchr(start, ' ');
        if (end_url != NULL && strncmp(end_url + 1, "end", 3) == 0) {
            // 计算URL的长度
            int url_len = end_url - start;
            
            // 为URL分配内存并复制
            *output = (char *)calloc(1, (url_len + 1) * sizeof(char));
            if (*output != NULL) {
                strncpy(*output, start, url_len);
                (*output)[url_len] = '\0';  // 确保字符串以null结尾
                
                // 输出提取的URL
                printf("Extracted URL: %s\n", *output);
                return url_len;
            } else {
                printf("Memory allocation failed.\n");
                return -1;
            }
        } else {
            printf("Invalid format after URL or 'end' not found.\n");
            return -2;
        }
    } else {
        printf("Prefix not found in input string.\n");
        return -3;
    }
    
    return 0;
}

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <ctype.h>

int extract_number(const char *str, const char *prefix) {
    // 确定前缀和后缀的长度
    // const char *prefix = "bts set relay1 Xmode kt ";
    const char *suffix = " end";
    size_t prefix_len = strlen(prefix);
    size_t suffix_len = strlen(suffix);
    size_t str_len = strlen(str);

    // 检查字符串长度是否符合预期（包括前缀和后缀）
    if (str_len < prefix_len + suffix_len + 1) {
        // 如果字符串太短，无法包含数字和前后缀，则返回错误值
        fprintf(stderr, "Error: The input string is too short.\n");
        return -1; // 或者你可以选择其他错误值
    }

    // 提取出中间部分（去掉前后缀）
    const char *number_str = str + prefix_len;
    const char *end_number_str = strstr(number_str, suffix);
    if (end_number_str == NULL) {
        // 如果没有找到后缀，返回错误值
        fprintf(stderr, "Error: The input string does not contain the expected suffix.\n");
        return -1; // 或者你可以选择其他错误值
    }

    size_t number_len = end_number_str - number_str;
    char number_buf[number_len + 1]; // 临时缓冲区来存储数字字符串
    strncpy(number_buf, number_str, number_len);
    number_buf[number_len] = '\0'; // 确保字符串以空字符结尾

    // 转换提取出的字符串为整数
    char *endptr;
    int number = strtol(number_buf, &endptr, 10);

    // 检查转换是否成功
    if (*endptr != '\0') {
        // 如果endptr不是指向字符串末尾，说明转换失败
        fprintf(stderr, "Error: Conversion to integer failed.\n");
        return -1; // 或者你可以选择其他错误值
    }

    return number;
}

extern int e104_bt53c3_reinit(void);
int MyBlufi_CDA(char* data, int len)
{
    if(data==NULL||len<=0)
    {
        return -1;
    }

    bf_cda_in_sendbuf = calloc(1, MyBlufi_CDA_sendbuf_size);
    bf_cda_in_itembuf = calloc(1, MyBlufi_CDA_itempack_size);
    
    if(bf_cda_in_sendbuf==NULL)
    {
        return -2;
    }
    if(bf_cda_in_itembuf==NULL)
    {
        return -3;
    }

    

    memset(bf_cda_in_sendbuf, 0, MyBlufi_CDA_sendbuf_size);

    MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_new_head(bf_cda_in_itembuf));

    if((strstr( (char *)data, "bts get meminfo"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get meminfo");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_meminfo(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get rstreason"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get rstreason");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_rst_reason(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get debug mode"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get rstreason");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_debug_mode(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get systime"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get systime");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_systime(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get epstatus"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get epstatus");
    }
    
    if((strstr( (char *)data, "bts get espmac"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get espmac");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_esp_mac(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get version"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get version");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_version(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get ota mode"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv bts ota");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_ota_mode(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get iccid"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get iccid");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_iccid(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get imei"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get imei");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_imei(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get dev number"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get dev number");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_dev_number(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get csq"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get csq");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_csq(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get sver"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get sver");
    }
    if((strstr( (char *)data, "bts get model"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get model");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_model(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get wifi status"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get wifi status");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_wifi_status(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get http ota url"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get http ota url");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_httpotaurl(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get relay1 status"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get relay1 status");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_relay1_status(bf_cda_in_itembuf));
    }

    if((strstr( (char *)data, "bts led flash"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts led flash");
        MyLed_SetMode_Super(ledmode_flash);
    }
    if((strstr( (char *)data, "bts led flash1"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts led flash1");
        MyLed_SetMode_Super(ledmode_flash1);
    }
    if((strstr( (char *)data, "bts led flash2"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts led flash2");
        MyLed_SetMode_Super(ledmode_flash2);
    }
    if((strstr( (char *)data, "bts led flash3"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts led flash3");
        MyLed_SetMode_Super(ledmode_flash3);
    }
    if((strstr( (char *)data, "bts led on"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts led on");
        MyLed_SetMode_Super(ledmode_on);
    }
    if((strstr( (char *)data, "bts led off"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts led off");
        MyLed_SetMode_Super(ledmode_off);
    }
    if((strstr( (char *)data, "bts led fade"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts led fade");
        MyLed_SetMode_Super(ledmode_fade);
    }
    if((strstr( (char *)data, "bts led fade flash"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts led fade flash");
        MyLed_SetMode_Super(ledmode_fade_flash);
    }
    if((strstr( (char *)data, "bts reboot"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts reboot");
        MyBlufi_Send_Data_to_Master("设备即将重启", strlen("设备即将重启"));
        bts_reboot_request = true;
    }
    if((strstr( (char *)data, "bts poweroff"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts poweroff");
        My_esp_Powroff();
    }
    if((strstr( (char *)data, "bts find dev"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts find dev");
        MyLedMng_Find_Dev();
    }
    if((strstr( (char *)data, "bts rfs"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts rfs");
        MyBlufi_Send_Data_to_Master("设备即将恢复出厂设置", strlen("设备即将恢复出厂设置"));
        bts_rfs_request = true;
    }
    if((strstr( (char *)data, "bts printmonitor"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts printmonitor");
        IndvMonitorService(MONITOR_TYPE_TASK_LIST_DETAIL);
    }
    if((strstr( (char *)data, "bts pcl"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv bts print config");
        MyConfig_DebugPrint_my_config_nvs_t_All("bts");
    }
    if((strstr( (char *)data, "bts isp"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv bts isp");
        MyBlufi_Send_Data_to_Master("设备立即同步IoT参数", strlen("设备立即同步IoT参数"));
        bts_isp_request = true;
    }
    if((strstr( (char *)data, "bts ota"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv bts ota");
        MyBlufi_Send_Data_to_Master("设备即将检查ota升级", strlen("设备即将检查ota升级"));
        bts_check_ota_request = true;
    }

    if((strstr( (char *)data, "bts relay1 on"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts relay1 on");
        My_Relay_On();
    }
    if((strstr( (char *)data, "bts relay1 off"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts relay1 off");
        My_Relay_Off();
    }
    if((strstr( (char *)data, "bts set relay1 mode 0"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts set relay1 mode 0");
        bts_set_relay1_mode_request = true;
        bts_relay1_mode = 0;
        MyBlufi_Send_Data_to_Master("设置成功", strlen("设置成功"));
    }
    if((strstr( (char *)data, "bts set relay1 mode 1"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts set relay1 mode 1");
        bts_set_relay1_mode_request = true;
        bts_relay1_mode = 1;
        MyBlufi_Send_Data_to_Master("设置成功", strlen("设置成功"));
    }
    if((strstr( (char *)data, "bts set relay1 mode 2"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts set relay1 mode 2");
        bts_set_relay1_mode_request = true;
        bts_relay1_mode = 2;
        MyBlufi_Send_Data_to_Master("设置成功", strlen("设置成功"));
    }
    if((strstr( (char *)data, "bts set relay1 mode 3"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts set relay1 mode 3");
        bts_set_relay1_mode_request = true;
        bts_relay1_mode = 3;
        MyBlufi_Send_Data_to_Master("设置成功", strlen("设置成功"));
    }
    if((strstr( (char *)data, "bts set relay1 Xmode kt"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts set relay1 Xmode kt");
        int number = extract_number(data, "bts set relay1 Xmode kt ");
        if(number!=-1)
        {
            bts_set_relay1_Xmode_keeptime_request = true;
            bts_relay1_Xmode_keeptime = number;
            MyBlufi_Send_Data_to_Master("设置成功", strlen("设置成功"));
        }
        else
        {
            MyBlufi_Send_Data_to_Master("设置失败, 参数错误", strlen("设置失败, 参数错误"));
        }
    }
    if((strstr( (char *)data, "bts set channeldoor mode 0"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts set channeldoor mode 0");
        bts_set_channeldoor_mode_request = true;
        bts_channeldoor_mode = 0;
        MyBlufi_Send_Data_to_Master("设置成功", strlen("设置成功"));
    }
    if((strstr( (char *)data, "bts set channeldoor mode 1"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts set channeldoor mode 1");
        bts_set_channeldoor_mode_request = true;
        bts_channeldoor_mode = 1;
        MyBlufi_Send_Data_to_Master("设置成功", strlen("设置成功"));
    }
    if((strstr( (char *)data, "bts set channeldoor mode 2"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts set channeldoor mode 2");
        bts_set_channeldoor_mode_request = true;
        bts_channeldoor_mode = 2;
        MyBlufi_Send_Data_to_Master("设置成功", strlen("设置成功"));
    }
    if((strstr( (char *)data, "bts set bt reboot"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts set bt reboot");
        e104_bt53c3_reinit();
        MyBlufi_Send_Data_to_Master("BT即将重启", strlen("BT即将重启"));
    }
    if((strstr( (char *)data, "bts set http ota url"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv bts set http ota url");
        if(http_ota_url!=NULL)
        {
            free(http_ota_url);
            http_ota_url = NULL;
        }
        int ret = pickurl(data, &http_ota_url, "bts set http ota url: ");
        if(ret>0)
        {
            if(http_ota_url!=NULL)
            {
                bts_modify_httpotaurl_request = true;
                MyBlufi_Send_Data_to_Master("设置成功", strlen("设置成功"));
            }
        }
        else if(ret==-1)
        {
            MyBlufi_Send_Data_to_Master("提取失败, 内存不足", strlen("提取失败, 内存不足"));
        }
        else if(ret==-2)
        {
            MyBlufi_Send_Data_to_Master("提取失败, 后缀错误", strlen("提取失败, 后缀错误"));
        }
        else if(ret==-3)
        {
            MyBlufi_Send_Data_to_Master("提取失败, 前缀错误", strlen("提取失败, 前缀错误"));
        }
    }
    if((strstr( (char *)data, "bts set iot ip"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv bts set iot ip");
        if(iot_ip!=NULL)
        {
            free(iot_ip);
            iot_ip = NULL;
        }
        int ret = pickurl(data, &iot_ip, "bts set iot ip: ");
        if(ret>0)
        {
            if(iot_ip!=NULL)
            {
                bts_modify_iotip_request = true;
                MyBlufi_Send_Data_to_Master("设置成功", strlen("设置成功"));
            }
        }
        else if(ret==-1)
        {
            MyBlufi_Send_Data_to_Master("提取失败, 内存不足", strlen("提取失败, 内存不足"));
        }
        else if(ret==-2)
        {
            MyBlufi_Send_Data_to_Master("提取失败, 后缀错误", strlen("提取失败, 后缀错误"));
        }
        else if(ret==-3)
        {
            MyBlufi_Send_Data_to_Master("提取失败, 前缀错误", strlen("提取失败, 前缀错误"));
        }
    }
    if((strstr( (char *)data, "bts set iot port"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts set iot port");
        int number = extract_number(data, "bts set iot port ");
        if(number!=-1)
        {
            bts_modify_iotport_request = true;
            iot_port = number;
            MyBlufi_Send_Data_to_Master("设置成功", strlen("设置成功"));
        }
        else
        {
            MyBlufi_Send_Data_to_Master("设置失败, 参数错误", strlen("设置失败, 参数错误"));
        }
    }
    if((strstr( (char *)data, "bts http ota"))!=NULL)
    {
        bts_httpota_request = true;
        MyBlufi_Send_Data_to_Master("尝试OTA升级，成功后设备会自动重启，大约需要30s，请耐心等待...", strlen("尝试OTA升级，成功后设备会自动重启，大约需要30s，请耐心等待..."));
    }

    if((strstr( (char *)data, "bts get nvs workmode"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get nvs workmode");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_nvs_workmode(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get cur workmode"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get cur workmode");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_cur_workmode(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get cur worklist"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get cur worklist");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_cur_worklist(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get poweroncount"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get poweroncount");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_poweroncount(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get poweroffalarmset"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get poweroffalarmset");
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_iot_nvs_poweroff_alarm_enable_status(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get all info"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: bts get all info");
        
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_add_page(bf_cda_in_itembuf, "基础信息"));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_model(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_dev_number(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_esp_mac(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_version(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_ota_mode(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_systime(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_poweroncount(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_wifi_status(bf_cda_in_itembuf));


        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_add_page(bf_cda_in_itembuf, "调试信息"));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_meminfo(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_rst_reason(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_debug_mode(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_httpotaurl(bf_cda_in_itembuf));

        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_add_page(bf_cda_in_itembuf, "4G入网信息"));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_imei(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_iccid(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_csq(bf_cda_in_itembuf));

        
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_add_page(bf_cda_in_itembuf, "IoT信息"));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_iot_dev_status(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_iot_nvs_appid(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_iot_ip(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_iot_login_status(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_last_isp_time(bf_cda_in_itembuf));

        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_add_page(bf_cda_in_itembuf, "断电告警设置"));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_iot_nvs_poweroff_alarm_enable_status(bf_cda_in_itembuf));
        
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_add_page(bf_cda_in_itembuf, "工作模式"));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_cur_workmode(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_cur_worklist(bf_cda_in_itembuf));

        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_add_page(bf_cda_in_itembuf, "继电器信息"));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_channeldoor_mode(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_relay1_mode(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_relay1_Xmode_keep_time(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_relay1_status(bf_cda_in_itembuf));
    }
    if((strstr( (char *)data, "bts get bt info"))!=NULL)
    {
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_add_page(bf_cda_in_itembuf, "蓝牙信息"));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_bt_role(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_bt_adv_interval(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_bt_adv_power(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_bt_long_range(bf_cda_in_itembuf));

    }
    if((strstr( (char *)data, "btc get all info"))!=NULL)
    {
        ESP_LOGI("MyBlufi_CDA()", "recv cmd: btc get all info");
        
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_add_page(bf_cda_in_itembuf, "基础信息"));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_model(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_dev_number(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_version_btc(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_systime_btc(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_poweroncount(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_wifi_status(bf_cda_in_itembuf));

        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_add_page(bf_cda_in_itembuf, "4G入网信息"));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_imei(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_iccid(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_csq(bf_cda_in_itembuf));

        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_add_page(bf_cda_in_itembuf, "断电告警设置"));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_iot_nvs_poweroff_alarm_enable_status(bf_cda_in_itembuf));

        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_add_page(bf_cda_in_itembuf, "继电器信息"));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_channeldoor_mode(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_relay1_mode(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_relay1_Xmode_keep_time(bf_cda_in_itembuf));
        MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_get_relay1_status(bf_cda_in_itembuf));
    }
    MyBlufi_CDA_in(bf_cda_in_itembuf, MyBlufi_CDA_new_tail(bf_cda_in_itembuf));
    BLUFICDA_INFO("MyBlufi_CDA, bf_cda_in_sendbuf(%d)=\n%s\n", bf_cda_sendbuf_written_len, bf_cda_in_sendbuf);
    MyBlufi_Send_Data_to_Master(bf_cda_in_sendbuf, bf_cda_sendbuf_written_len);

    free(bf_cda_in_sendbuf);
    free(bf_cda_in_itembuf);

    bf_cda_sendbuf_written_len = 0;
    bf_cda_itembuf_written_len = 0;

    return 0;
}

void MyBlufi_CDA_task(void* param)
{
	int runtimes = 0;

	#define test_recv_buf_len 8192
	char* test_recv_buf = calloc(1, test_recv_buf_len);
	int recv_len = 0;

	if(test_recv_buf==NULL)
	{
		ESP_LOGE("testtask1()", "test_recv_buf==NULL!");
		while(1)
		{
			vTaskDelay(1000 / portTICK_PERIOD_MS);
		}
	}

    int queue_rcv_ret = pdFALSE;
    my_blufi_cda_rcv_queue_t* queue_rcv_item = NULL;

	for(;;)
	{
		queue_rcv_ret = xQueueReceive(my_blufi_cda_rcv_queue, &queue_rcv_item, 1000/portTICK_PERIOD_MS);
        if(queue_rcv_ret==pdTRUE)
        {
            if(queue_rcv_item!=NULL)
            {
                if(queue_rcv_item->valid)
                {
                    if(queue_rcv_item->data!=NULL)
                    {
                        BLUFICDA_INFO("queue_rcv_item->len %d\n", queue_rcv_item->len);
                        esp_log_buffer_hex("queue_rcv_item->data", queue_rcv_item->data, queue_rcv_item->len);

                        MyBlufi_CDA(queue_rcv_item->data, queue_rcv_item->len);

                        // MyBlufi_Send_Data_to_Master("rcv ok", strlen("rcv ok"));
                    }
                    queue_rcv_item->valid = 0;
                }
            }
        }
	}
}

uint8_t My_Blufi_CDA_rcv_queue_Send(char* data, uint32_t len, uint32_t timeout)
{
    int queue_send_ret = pdTRUE;
    int i = 0;
    int retry_count = 0;

    //获取信号量
    //...
    MyBlufi_CDA_rcv_queue_Send_retry:
    for(i=0; i<MY_BLUFI_CDA_RCV_QUEUE_LEN; i++)
    {
        if(!my_blufi_cda_rcv_queue_array[i]->valid)
        {
            my_blufi_cda_rcv_queue_array[i]->len = len;
            my_blufi_cda_rcv_queue_array[i]->valid = 1;
            //my_blufi_cda_rcv_queue_array[i]->data = data;
            if(my_blufi_cda_rcv_queue_array[i]->data!=NULL)
            {
                memcpy(my_blufi_cda_rcv_queue_array[i]->data, data, len);
                my_blufi_cda_rcv_queue_array[i]->data[len]=0;
            }
            break;
        }
    }
    //释放信号量
    //...
    if(i>=MY_BLUFI_CDA_RCV_QUEUE_LEN&&retry_count<40)
    {
        vTaskDelay(timeout / portTICK_PERIOD_MS);
        retry_count++;
        ESP_LOGW("My_Blufi_CDA_rcv_queue_Send()", "retry_count=%d\n", retry_count);
        goto MyBlufi_CDA_rcv_queue_Send_retry;
    }
    else if(i<MY_BLUFI_CDA_RCV_QUEUE_LEN)
    {
        if(my_blufi_cda_rcv_queue!=NULL)
            queue_send_ret = xQueueSend(my_blufi_cda_rcv_queue, &my_blufi_cda_rcv_queue_array[i], timeout / portTICK_PERIOD_MS);
    }
    else
    {
        ESP_LOGE("My_Blufi_CDA_rcv_queue_Send()", "error, i=%d\n", i);
        return 1;
    }
    return 0;
}

static int MyBlufi_CDA_Rcv_Queue_Init(my_blufi_cda_rcv_queue_t** p, uint8_t data_area)
{
    if(data_area)
    {
        char* p_temp = (char*)calloc(MY_BLUFI_CDA_RCV_QUEUE_LEN, MY_BLUFI_CDA_RCV_QUEUE_PACK_SIZE);
        if(p_temp!=NULL)
        { 
            for(int i=0; i<MY_BLUFI_CDA_RCV_QUEUE_LEN; i++)
            {
                p[i] = (my_blufi_cda_rcv_queue_t*)calloc(1, sizeof(my_blufi_cda_rcv_queue_t));
                if(p[i]!=NULL)
                {
                    p[i]->data = p_temp+(i*(MY_BLUFI_CDA_RCV_QUEUE_PACK_SIZE-8));
                }
                else
                {
                    ESP_LOGE("MyBlufi_CDA_Rcv_Queue_Init()", "error1\n");
                    return 1;
                }
            }
        }
        else
        {
            ESP_LOGE("MyBlufi_CDA_Rcv_Queue_Init()", "error2\n");
            return 2;
        }
    }
    else
    {
        for(int i=0; i<MY_BLUFI_CDA_RCV_QUEUE_LEN; i++)
        {
            p[i] = (my_blufi_cda_rcv_queue_t*)calloc(1, sizeof(my_blufi_cda_rcv_queue_t));
            if(p[i]==NULL)
            {
                ESP_LOGE("MyBlufi_CDA_Rcv_Queue_Init()", "error3, i=%d\n", i);
                return i;
            }
        }
    }
    return MY_BLUFI_CDA_RCV_QUEUE_LEN;
}

int MyBlufi_CDA_Init(void)
{
    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    MyBlufi_CDA_Rcv_Queue_Init(my_blufi_cda_rcv_queue_array, 1);

    my_blufi_cda_rcv_queue = xQueueGenericCreate(MY_BLUFI_CDA_RCV_QUEUE_LEN, sizeof(my_blufi_cda_rcv_queue_t*), 1);
    if(my_blufi_cda_rcv_queue==NULL)ESP_LOGE("MyBlufi_CDA_Init()", "my_blufi_cda_rcv_queue=NULL\n");

    p_task_stack = calloc(1, 4096*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
            //xTaskCreateStatic(&testtask1, "testtask1", 4096, NULL, 8, p_task_stack, p_task_data);
			xTaskCreateStaticPinnedToCore(&MyBlufi_CDA_task, "MyBlufi_CDA_task", 4096, NULL, 8, p_task_stack, p_task_data, 0);
        }
    }

    return 0;
}