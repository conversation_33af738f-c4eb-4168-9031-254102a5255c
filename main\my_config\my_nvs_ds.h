#ifndef MY_NVS_DS_H
#define MY_NVS_DS_H

#include <lwip/netdb.h>



#define MY_NVS_D2LINK_LIST_ITEM_ITEM_NAME_USED  0
//服务器命令解析-解析函数表-76byte
struct my_nvs_d2link_list_item
{
    int id;
    #if MY_NVS_D2LINK_LIST_ITEM_ITEM_NAME_USED
    char item_name[32];
    #endif
    int saving_to_nvs;
    char form_name[16];
    char key_name[16];

    uint8_t data_type;
    int data_len;
    uint64_t data_uint64;
    uint8_t p_data_type;
    void* p_data;
    struct my_nvs_d2link_list_item* next_item;
    struct my_nvs_d2link_list_item* pri_item;
};
typedef struct my_nvs_d2link_list_item my_nvs_d2link_list_item_t;

#define MY_NVS_D2LINK_LIST_LIST_NAME_USED  1
struct my_nvs_d2link_head
{
    int id;
    int list_item_num;
    uint32_t list_data_len;
    #if MY_NVS_D2LINK_LIST_LIST_NAME_USED
    char list_name[32];
    #endif
    my_nvs_d2link_list_item_t* first_item;
    my_nvs_d2link_list_item_t* end_item;

    // struct my_nvs_d2link_head* next_item;
    // struct my_nvs_d2link_head* pri_item;
};
typedef struct my_nvs_d2link_head my_nvs_d2link_list_t;

int MyNvs_D2Link_Append_Item(my_nvs_d2link_list_t* p_list, int id, int saving_to_nvs, char* form_name, char* key_name, void* p_data, uint8_t p_data_type, uint64_t data_uint64, uint32_t data_len, char* service_name);
int MyNvs_D2Link_Delete_Item(my_nvs_d2link_list_t* p_list, int id);
int MyNvs_D2Link_Insert_Item(my_nvs_d2link_list_t* p_list, int insert_id, int insert_direct, int id, int saving_to_nvs, char* form_name, char* key_name, void* p_data, uint8_t p_data_type, uint64_t data_uint64, uint32_t data_len, char* service_name);
int MyNvs_D2Link_Delete(my_nvs_d2link_list_t** p_list);
int MyNvs_D2Link_match_id_count_statistic(my_nvs_d2link_list_t* p_list, int id_begin, int id_end);
int MyNvs_D2Link_Data_Packer(my_nvs_d2link_list_t* p_list, void* saving, uint16_t saving_len);
my_nvs_d2link_list_t* MyNvs_D2Link_Creat(int id, char* list_name);
int MyNvs_D2Link_PrintList(my_nvs_d2link_list_t* p_list);
void* MyNvs_D2Link_GetItem_ById(my_nvs_d2link_list_t* p_list, int id);
int MyNvs_D2Link_CheckItemExistInList_Id(my_nvs_d2link_list_t* p_list, int id);


//-----------------------------

// my_nvs_d2link_list_t* MyNvs_D2LinkList_Creat(int id, char* list_name);
// int MyNvs_D2Link_Append_Item(my_nvs_d2link_list_t* p_list, int id, int saving_to_nvs, char* form_name, char* key_name, void* p_data, uint8_t p_data_type, uint64_t data_uint64, uint32_t data_len, char* service_name);



#endif