#include "my_asl.h"

#if MY_ASL_FUNC

#include "my_tsfc.h"
#include "my_asl_event.h"
#include "my_asl_dpu.h"
#include "my_debug.h"
#include "my_endian.h"

#include "esp_log.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"
#include "freertos/semphr.h"


#if defined MY_DEBUG_PRINT_FUNC
#define DEBUG_PRINT_LEVEL_my_asl_PRINT   MY_DEBUG_PRINT_LEVEL_0
#else
#define DEBUG_PRINT_LEVEL_my_asl_PRINT   DEBUG_PRINT_LEVEL_0
#endif


typedef struct
{
    int (*MyAslECCB_Callback)(int);
}my_asl_info_t;

my_asl_info_t my_asl_info;


QueueHandle_t my_asl_send_queue = NULL;
QueueHandle_t my_asl_rcv_queue = NULL;
my_asl_queue_t* my_asl_send_queue_array[MY_ASL_QUEUE_LEN];
my_asl_queue_t* my_asl_rcv_queue_array[MY_ASL_QUEUE_LEN];

my_asl_event_list_t my_asl_event_list;

/**
 * @brief 将一个文件分块打包
 * @param pack 打包好后的存储区
 * @param pack_size pack区大小
 * @param origin_data 原始数据
 * @param o_data_len origin_data大小
 * @param start_offset 从一个文件的多少个字节开始打包，0开始
 * @param step_len 每次打包多少字节数据
 * @return 已经打包的长度
 * @note 多次调用顺序打包一个文件
 */
uint32_t MyASL_DataBlockTsf_Packager(char* pack, uint16_t pack_size, char* origin_data, uint32_t o_data_len, uint32_t start_offset, uint32_t step_len, uint32_t* packed_len)
{
    memset(pack, 0, pack_size);
    if(pack_size>=step_len)
    {
        int remain_len = o_data_len-start_offset;
        if(remain_len>=step_len)
        {
            memcpy(pack, origin_data+start_offset, step_len);
            *packed_len = step_len;
            return start_offset+step_len;
        }
        else if(remain_len>0)
        {
            memcpy(pack, origin_data+start_offset, remain_len);
            *packed_len = remain_len;
            return start_offset+remain_len;
        }
    }
    return start_offset;
}

/**
 * @brief 将一个文件分块打包
 * @param pack 打包好后的存储区
 * @param pack_size pack区大小
 * @param origin_data 原始数据
 * @param o_data_len origin_data大小
 * @param start_offset 从一个文件的多少个字节开始打包，0开始
 * @param step_len 每次打包多少字节数据
 * @return 已经打包的长度
 * @note 多次调用顺序打包一个文件
 */
uint32_t MyASL_Packager(my_asl_data_type_t data_type, char* pack, uint16_t pack_size, char* origin_data, uint32_t o_data_len, uint32_t start_offset, uint32_t step_len)
{
    return 0;
}

TaskHandle_t MyASL_Send_TaskHandle = NULL;
void MyASL_Send_Task(void* param)
{
    int queue_rcv_ret = pdFALSE;
    char* asl_send_buf = (char*)calloc(1, 5120+MY_ASL_PACK_SIZE_MAX+1);
    my_asl_queue_t* queue_rcv_item = NULL;
    uint32_t data_packager_count = 0;
    uint32_t packed_len;

    if(asl_send_buf==NULL)
    {
        ESP_LOGE("MyASL_Send_Task()", "asl_send_buf==NULL\n");
    }

    for(;;)
    {
        //队列接收
        queue_rcv_ret = xQueueReceive(my_asl_send_queue, &queue_rcv_item, portMAX_DELAY);
        if(queue_rcv_ret==pdTRUE)
        {
            if(queue_rcv_item!=NULL)
            {
                if(queue_rcv_item->valid)
                {
                    data_packager_count = 0;
                    // if((queue_rcv_item->my_asl_data_type==datablock_tsf)&&(queue_rcv_item->data!=NULL))
                    // {
                    //     do
                    //     {
                    //         data_packager_count = MyASL_DataBlockTsf_Packager(asl_send_buf, MY_ASL_PACK_SIZE_MAX, queue_rcv_item->data, queue_rcv_item->len, data_packager_count, MY_ASL_PACK_SIZE_MAX, &packed_len);

                    //         //发送到传输控制层
                    //         MyTSFC_Send(data_frame, queue_rcv_item->sn, asl_send_buf, packed_len, 1000, 1);
                    //         //发送失败将阻塞

                    //     } while (data_packager_count<queue_rcv_item->len);
                    // }
                    // else//暂不考虑数据长度超过MY_ASL_PACK_SIZE_MAX的情况
                    // {
                    //     if((queue_rcv_item->len>0)&&(queue_rcv_item->data!=NULL)&&(queue_rcv_item->len<MY_ASL_PACK_SIZE_MAX))
                    //     {
                    //         //打包数据
                    //         MyASL_Packager(queue_rcv_item->my_asl_data_type, asl_send_buf, MY_ASL_PACK_SIZE_MAX, queue_rcv_item->data, queue_rcv_item->len, data_packager_count, MY_ASL_PACK_SIZE_MAX);
                            
                    //         //发送到传输控制层
                    //         MyTSFC_Send(msg_frame, queue_rcv_item->sn, asl_send_buf, packed_len, 1000, 1);
                    //         //发送失败将阻塞
                    //     }
                    //     else
                    //     {
                    //         MyASL_Packager(queue_rcv_item->my_asl_data_type, asl_send_buf, MY_ASL_PACK_SIZE_MAX, NULL, 0, 0, MY_ASL_PACK_SIZE_MAX);

                    //         //发送到传输控制层
                    //         MyTSFC_Send(msg_frame, queue_rcv_item->sn, asl_send_buf, packed_len, 1000, 1);
                    //         //发送失败将阻塞
                    //     }
                    // }
                    if((queue_rcv_item->data!=NULL))
                    {
                        do
                        {
                            data_packager_count = MyASL_DataBlockTsf_Packager(asl_send_buf, MY_ASL_PACK_SIZE_MAX, queue_rcv_item->data, queue_rcv_item->len, data_packager_count, MY_ASL_PACK_SIZE_MAX, &packed_len);

                            //发送到传输控制层
                            if(queue_rcv_item->my_asl_data_type==datablock_tsf)
                            {
                                MyTSFC_Send(data_frame, queue_rcv_item->sn, asl_send_buf, packed_len, 1000, 1);
                            }
                            else
                            {
                                MyTSFC_Send(msg_frame, queue_rcv_item->sn, asl_send_buf, packed_len, 1000, 1);
                            }
                            //发送失败将阻塞

                        } while (data_packager_count<queue_rcv_item->len);
                    }
                    //清空队列缓冲区
                    queue_rcv_item->valid = 0;

                    //发送完成事件

                }
            }
            else
            {
                ESP_LOGE("MyASL_Send_Task()", "queue_rcv_item!=NULL is false\n");
            }
        }
    }
}

uint8_t MyASL_Send(my_asl_data_type_t type, uint8_t sn, char* data, uint32_t len, uint32_t timeout, uint8_t id, uint8_t datablock)
{
    int queue_send_ret = pdTRUE;
    int i = 0;
    int retry_count = 0;

    //获取信号量
    //...
    MyASL_Send_retry:
    for(i=0; i<MY_ASL_QUEUE_LEN; i++)
    {
        if(!my_asl_send_queue_array[i]->valid)
        {
            my_asl_send_queue_array[i]->id = id;
            my_asl_send_queue_array[i]->len = len;
            my_asl_send_queue_array[i]->valid = 1;
            my_asl_send_queue_array[i]->data = data;
            my_asl_send_queue_array[i]->my_asl_data_type = type;
            my_asl_send_queue_array[i]->sn = sn;
            break;
        }
    }
    //释放信号量
    //...
    if(i>=MY_ASL_QUEUE_LEN&&retry_count<40)
    {
        vTaskDelay(timeout / portTICK_PERIOD_MS);
        retry_count++;
        ESP_LOGE("MyASL_Send()", "retry_count=%d\n", retry_count);
        goto MyASL_Send_retry;
    }
    else if(i<MY_ASL_QUEUE_LEN)
    {
        if(my_asl_send_queue!=NULL)
        {
            queue_send_ret = xQueueSend(my_asl_send_queue, &my_asl_send_queue_array[i], timeout / portTICK_PERIOD_MS);
            if(datablock)
            {
                uint32_t datablock_timeout = 0;
                while((my_asl_send_queue_array[i]->valid)&&(datablock_timeout<timeout))
                {
                    vTaskDelay(20 / portTICK_PERIOD_MS);
                    datablock_timeout+=20;
                }
            }
        }
    }
    else
    {
        ESP_LOGE("MyASL_Send()", "error, i=%d\n", i);
        return 1;
    }

    return 0;
}



TaskHandle_t MyASL_Rcv_TaskHandle = NULL;

int MyAsl_EventList_Matching(uint8_t* list, uint16_t list_len, uint8_t id)
{
    for(int i=0; i<list_len; i++)
	{
		for(int j=0; j<2; j++)
		{
			if(*(list+i*2+j)==id)
			{
				if(j==0)
				{
					return *(list+i*2+j+1);
				}
				else if(j==1)
				{
					return *(list+i*2+j-1);
				}
			}
		}
	}
    return -1;
}

void MyASL_Rcv_Task(void* param)
{
    my_tsfc_queue_t* p_rcv_buf=NULL;
    char* rcv_buf = (char*)calloc(2, 4096);

    uint8_t my_asl_eventlist_id_table[20][2];
    int i=0;
    uint16_t table_len = 0;
    my_asl_eventlist_id_table[i][0] = dev_reg_req, my_asl_eventlist_id_table[i][1] = dev_reg_res, i++;
    my_asl_eventlist_id_table[i][0] = dev_login_req, my_asl_eventlist_id_table[i][1] = dev_login_res, i++;
    my_asl_eventlist_id_table[i][0] = net_time_sync_req, my_asl_eventlist_id_table[i][1] = net_time_sync_res, i++;
    my_asl_eventlist_id_table[i][0] = dev_param_req, my_asl_eventlist_id_table[i][1] = dev_param_res, i++;
    my_asl_eventlist_id_table[i][0] = dev_param_update_indication, my_asl_eventlist_id_table[i][1] = dev_param_update_res, i++;
    my_asl_eventlist_id_table[i][0] = dev_param_query_indication, my_asl_eventlist_id_table[i][1] = dev_param_query_res, i++;
    my_asl_eventlist_id_table[i][0] = dev_param_report, my_asl_eventlist_id_table[i][1] = dev_param_report_res, i++;
    my_asl_eventlist_id_table[i][0] = dev_ctrl_param_indication, my_asl_eventlist_id_table[i][1] = dev_ctrl_param_res, i++;
    my_asl_eventlist_id_table[i][0] = dev_measure_report, my_asl_eventlist_id_table[i][1] = 0, i++;
    my_asl_eventlist_id_table[i][0] = dev_event_report, my_asl_eventlist_id_table[i][1] = dev_event_res, i++;
    my_asl_eventlist_id_table[i][0] = file_upload_req, my_asl_eventlist_id_table[i][1] = file_upload_res, i++;
    my_asl_eventlist_id_table[i][0] = file_down_req, my_asl_eventlist_id_table[i][1] = file_down_res, i++;
    my_asl_eventlist_id_table[i][0] = data_direct_tsf_req, my_asl_eventlist_id_table[i][1] = data_direct_tsf_res, i++;
    my_asl_eventlist_id_table[i][0] = file_tsf_finish, my_asl_eventlist_id_table[i][1] = 0, i++;
    my_asl_eventlist_id_table[i][0] = datablock_tsf, my_asl_eventlist_id_table[i][1] = 0, i++;
    table_len = i;

    for(;;)
    {
        MyTSFC_RcvData(&p_rcv_buf, portMAX_DELAY);
        memcpy(rcv_buf, p_rcv_buf->data, p_rcv_buf->len);
        
        #if DEBUG_PRINT_LEVEL_my_asl_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
        printf("\nMyASL_Rcv_Task(), rcv_pack(%d)=\n", p_rcv_buf->len);
        #endif
        #if DEBUG_PRINT_LEVEL_my_asl_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
        char* in_data_byte = (char*)p_rcv_buf->data;
        if(p_rcv_buf->len<=512)
        {
            for(int i=0; i<p_rcv_buf->len; i++)
            {
                printf("%02X ", *(in_data_byte+i));
            }
            printf("\n$$$$\n");
        }
        else
        {
            printf("p_rcv_buf->len > 512, omit...\n");
        }
        #endif
        
        my_asl_head_t* my_asl_head = (my_asl_head_t*)p_rcv_buf->data;
        if(my_asl_head->vercode==0x01)
        {
            #if DEBUG_PRINT_LEVEL_my_asl_PRINT >= MY_DEBUG_PRINT_LEVEL_1_SIMPLE
            ESP_LOGI("MyASL_Rcv_Task()", "my_asl_head->type=%x\n", my_asl_head->type);
            #endif
            int event_id = MyAsl_EventList_Matching(my_asl_eventlist_id_table, table_len, my_asl_head->type);
            if(event_id>=0)
            {
                uint16_t retcode = my_endian_conversion_16(*((uint16_t*)(p_rcv_buf->data+sizeof(my_asl_head_t))));
                if(retcode==aslrc_TokenWrong)
                {
                    if(my_asl_info.MyAslECCB_Callback!=NULL)
                    {
                        my_asl_info.MyAslECCB_Callback(retcode);
                    }
                }
                my_asl_rcvdata_cache_t* my_asl_rcvdata_cache = (my_asl_rcvdata_cache_t*)MyAslEvent_GetItem_Storage_ById(&my_asl_event_list, event_id);
                if(my_asl_rcvdata_cache!=NULL)
                {
                    if(!my_asl_rcvdata_cache->valid)
                    {
                        memcpy(my_asl_rcvdata_cache->data, p_rcv_buf->data, p_rcv_buf->len);
                        my_asl_rcvdata_cache->type = my_asl_head->type;
                        my_asl_rcvdata_cache->len = p_rcv_buf->len;
                    }
                    else
                    {
                        ESP_LOGE("MyASL_Rcv_Task()", "my_asl_rcvdata_cache->valid is True");
                    }
                    MyAslEvent_Delete(&my_asl_event_list, event_id);
                    if(my_asl_rcvdata_cache->taskhandle!=NULL)
                    {
                        xTaskNotify(my_asl_rcvdata_cache->taskhandle, 1, eSetValueWithOverwrite);
                    }
                    else
                    {
                        ESP_LOGE("MyASL_Rcv_Task()", "my_asl_rcvdata_cache->taskhandle==NULL");
                    }
                }
                else
                {
                    #if DEBUG_PRINT_LEVEL_my_asl_PRINT >= MY_DEBUG_PRINT_LEVEL_1_SIMPLE
                    ESP_LOGW("MyASL_Rcv_Task()", "my_asl_rcvdata_cache==NULL, forward it to MyASL_DPU_Send()");
                    #endif
                    MyASL_DPU_Send(my_asl_head->type, p_rcv_buf->data, p_rcv_buf->len, 5000, 1);
                }
            }
            else
            {
                ESP_LOGE("MyASL_Rcv_Task()", "not found id=0x%x in my_asl_eventlist_id_table", my_asl_head->type);
            }
        }
        else
        {
            ESP_LOGE("MyASL_Rcv_Task()", "error:asl protocol version not support=%d, required=%d\n", my_asl_head->vercode, 1);
        }

        //解析，生成事件
        

        p_rcv_buf->valid = 0;
    }
}

int MyASL_ServicePort(my_asl_data_type_t service_type, void* data, int data_len, void** saving, int* saving_len, int timeout, int wait_res)
{
    char* in_data_byte = (char*)data;

    if(MyTSFC_Get_Workstate()==TSFC_WORK_STATE_OK)
    {
        if(wait_res)
        {
            uint32_t notifyValue = 0;
            my_asl_rcvdata_cache_t my_asl_rcvdata_cache;
            memset(&my_asl_rcvdata_cache, 0, sizeof(my_asl_rcvdata_cache));
            my_asl_rcvdata_cache.data = calloc(1, 1024);

            my_asl_rcvdata_cache.taskhandle = xTaskGetCurrentTaskHandle();
            MyAslEvent_Register(&my_asl_event_list, service_type, &my_asl_rcvdata_cache, NULL);

            //发送请求
            MyASL_Send(service_type, 0, data, data_len, timeout, 1, 1);

            //等待对时响应
            notifyValue = 0;
            xTaskNotifyWait(0x0, 0xffffffff, &notifyValue, timeout/portTICK_PERIOD_MS);

            if(notifyValue)
            {
                if(*saving==NULL)
                {
                    *saving = calloc(1, my_asl_rcvdata_cache.len);
                    if(*saving!=NULL)
                    {
                        *saving_len = my_asl_rcvdata_cache.len;
                        memcpy(*saving, my_asl_rcvdata_cache.data, my_asl_rcvdata_cache.len);
                    }
                    else
                    {
                        ESP_LOGE("MyASL_ServicePort()", "*saving==NULL");
                    }
                }
                else
                {
                    ESP_LOGE("MyASL_ServicePort()", "saving_len not enough");
                }

                if(my_asl_rcvdata_cache.data!=NULL)free(my_asl_rcvdata_cache.data);
                return 0;
            }
            else
            {
                #if DEBUG_PRINT_LEVEL_my_asl_PRINT >= MY_DEBUG_PRINT_LEVEL_1_SIMPLE
                ESP_LOGW("MyASL_ServicePort()", "notifyValue=%d\n", notifyValue);
                #endif
                MyAslEvent_Delete(&my_asl_event_list, service_type);
                if(my_asl_rcvdata_cache.data!=NULL)free(my_asl_rcvdata_cache.data);
                return 1;
            }
        }
        else
        {
            //发送请求
            return MyASL_Send(service_type, 0, data, data_len, timeout, 1, 1);
        }
    }

    return -1;
}

static int MyASL_Queue_Init(my_asl_queue_t** p, uint8_t data_area)
{
    if(data_area)
    {
        char* p_temp = (char*)calloc(MY_ASL_QUEUE_LEN, MY_ASL_PACK_SIZE_MAX+1);
        if(p_temp!=NULL)
        { 
            for(int i=0; i<MY_ASL_QUEUE_LEN; i++)
            {
                p[i] = (my_asl_queue_t*)calloc(1, sizeof(my_asl_queue_t));
                if(p[i]!=NULL)
                {
                    p[i]->data = p_temp+(i*(MY_ASL_PACK_SIZE_MAX+1));
                }
                else
                {
                    ESP_LOGE("MyASL_Queue_Init()", "error1\n");
                    return 1;
                }
            }
        }
        else
        {
            ESP_LOGE("MyASL_Queue_Init()", "error2\n");
            return 2;
        }
    }
    else
    {
        for(int i=0; i<MY_ASL_QUEUE_LEN; i++)
        {
            p[i] = (my_asl_queue_t*)calloc(1, sizeof(my_asl_queue_t));
            if(p[i]==NULL)
            {
                ESP_LOGE("MyASL_Queue_Init()", "error3, i=%d\n", i);
                return i;
            }
        }
    }
    return MY_ASL_QUEUE_LEN;
}

void MyAsl_ECCB_Register(int (*callback_func)(int))
{
    my_asl_info.MyAslECCB_Callback = callback_func;
}

uint8_t MyASL_Init(void)
{
    MyASL_Queue_Init(my_asl_send_queue_array, 0);
    MyASL_Queue_Init(my_asl_rcv_queue_array, 0);

    my_asl_send_queue = xQueueGenericCreate(MY_ASL_QUEUE_LEN, sizeof(my_asl_queue_t*), 1);
    if(my_asl_send_queue==NULL)ESP_LOGE("MyASL_Init()", "my_asl_send_queue=NULL\n");

    my_asl_rcv_queue = xQueueGenericCreate(MY_ASL_QUEUE_LEN, sizeof(my_asl_queue_t*), 1);
    if(my_asl_rcv_queue==NULL)ESP_LOGE("MyASL_Init()", "my_asl_rcv_queue=NULL\n");

    MyASL_DPU_Init();

    MyAslEvent_Init(&my_asl_event_list);

    BaseType_t ret = pdPASS;
    #if MyASL_Send_TASK_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    p_task_stack = calloc(1, MyASL_Send_Task_task_stack_size*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
			if(xTaskCreateStaticPinnedToCore(MyASL_Send_Task, "MyASL_Send_Task", MyASL_Send_Task_task_stack_size, NULL, MyASL_Send_Task_priority, p_task_stack, p_task_data, MyASL_Send_Task_COREID)!=p_task_data)
			{
				ESP_LOGE("MyASL_Init()", "creat MyASL_Send_Task use xTaskCreateStaticPinnedToCore() error\n");
                return 1;
			}
        }else return 1;
    }else return 1;
	#elif MyASL_Send_TASK_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
	ret = xTaskCreatePinnedToCore(MyASL_Send_Task, "MyASL_Send_Task", MyASL_Send_Task_task_stack_size, NULL, MyASL_Send_Task_priority, &MyASL_Send_TaskHandle, MyASL_Send_Task_COREID);
	if(ret!=pdPASS)
	{
		ESP_LOGE("MyASL_Init()", "creat MyASL_Send_Task use xTaskCreatePinnedToCore() error=%d\n", ret);
        return 1;
	}
	#endif

    #if MyASL_Rcv_TASK_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    p_task_stack = calloc(1, MyASL_Rcv_Task_task_stack_size*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
			if(xTaskCreateStaticPinnedToCore(MyASL_Rcv_Task, "MyASL_Rcv_Task", MyASL_Rcv_Task_task_stack_size, NULL, MyASL_Rcv_Task_priority, p_task_stack, p_task_data, MyASL_Rcv_Task_COREID)!=p_task_data)
			{
				ESP_LOGE("MyASL_Init()", "creat MyASL_Rcv_Task use xTaskCreateStaticPinnedToCore() error\n");
                return 1;
			}
        }else return 1;
    }else return 1;
	#elif MyASL_Rcv_TASK_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
	ret = xTaskCreatePinnedToCore(MyASL_Rcv_Task, "MyASL_Rcv_Task", MyASL_Rcv_Task_task_stack_size, NULL, MyASL_Rcv_Task_priority, &MyASL_Rcv_TaskHandle, MyASL_Rcv_Task_COREID);
	if(ret!=pdPASS)
	{
		ESP_LOGE("MyASL_Init()", "creat MyASL_Rcv_Task use xTaskCreatePinnedToCore() error=%d\n", ret);
        return 1;
	}
	#endif

    return 0;
}

#endif