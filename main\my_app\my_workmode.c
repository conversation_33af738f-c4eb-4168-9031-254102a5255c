#include "my_workmode.h"

#if MY_WORKMODE_FUNC

#include "my_time.h"
#include "my_lowpower.h"
#include "my_debug.h"

#include <esp_log.h>


#if defined MY_DEBUG_PRINT_FUNC
#define DEBUG_PRINT_LEVEL_my_workmode_PRINT   MY_DEBUG_PRINT_LEVEL_0
#else
#define DEBUG_PRINT_LEVEL_my_workmode_PRINT   DEBUG_PRINT_LEVEL_0
#endif


my_workmode_info_t my_workmode_info;


int MyWorkmode_WorkList_To_SleepList(workplan_list_item_t* p_worklist, int workplan_num, sleepplan_list_item_t* p_sleeplist, int sleepplan_num)
{
	if(p_worklist==NULL)
	{
		ESP_LOGE("WorkList_To_SleepList()", "p_worklist==NULL");
		return -1;
	}
	if(p_sleeplist==NULL)
	{
		ESP_LOGE("WorkList_To_SleepList()", "p_sleeplist==NULL");
		return -1;
	}
	if(sleepplan_num<workplan_num)
	{
		ESP_LOGE("WorkList_To_SleepList()", "sleepplan_num<workplan_num");
		return -11;
	}
	if(workplan_num<=0)
	{
		ESP_LOGE("WorkList_To_SleepList()", "workplan_num<=0");
		return -12;
	}

	#if DEBUG_PRINT_LEVEL_my_workmode_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
	printf("origin worklist:\n");
	for(int i=0; i<workplan_num; i++)
	{
        printf("%d, %d(%d:%d~%d:%d)\n", (p_worklist+i)->work_start_time, (p_worklist+i)->work_end_time, \
            (p_worklist+i)->work_start_time/3600, (p_worklist+i)->work_start_time%3600/60, \
            (p_worklist+i)->work_end_time/3600, (p_worklist+i)->work_end_time%3600/60);
	}
	printf("\n\n");
	#endif

	//将工作表按开始时间排序
	workplan_list_item_t workplan_temp;
	for(int j=0; j<workplan_num-1; j++)
	{
		for(int i=j+1; i<workplan_num; i++)
		{
			if((p_worklist+j)->work_start_time > (p_worklist+i)->work_start_time)
			{
				workplan_temp.work_start_time = (p_worklist+i)->work_start_time;
				workplan_temp.work_end_time = (p_worklist+i)->work_end_time;

				(p_worklist+i)->work_start_time = (p_worklist+j)->work_start_time;
				(p_worklist+i)->work_end_time = (p_worklist+j)->work_end_time;

				(p_worklist+j)->work_start_time = workplan_temp.work_start_time;
				(p_worklist+j)->work_end_time = workplan_temp.work_end_time;
			}
		}
	}

	#if DEBUG_PRINT_LEVEL_my_workmode_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
	printf("order worklist:\n");
	for(int i=0; i<workplan_num; i++)
	{
		printf("%d, %d(%d:%d~%d:%d)\n", (p_worklist+i)->work_start_time, (p_worklist+i)->work_end_time, \
            (p_worklist+i)->work_start_time/3600, (p_worklist+i)->work_start_time%3600/60, \
            (p_worklist+i)->work_end_time/3600, (p_worklist+i)->work_end_time%3600/60);
	}
	printf("\n\n");
	#endif

	//计算睡眠列表
	for(int i=0; i<workplan_num; i++)
	{
		//睡眠开始时间就是工作结束时间
		(p_sleeplist+i)->sleep_start_time = (p_worklist+i)->work_end_time;

		//根据前workplan_num-1项工作计划计算出对应的睡眠计划
		if(i<workplan_num-1)
		{
			//没有跨天
			if((p_worklist+i)->work_end_time - (p_worklist+i)->work_start_time > 0)
			{
				//workplan_num+1项的开始时间大于workplan_num项的结束时间才会计算睡眠保持时间，否则将这项工作计划对应的睡眠计划置零
				if((p_worklist+i+1)->work_start_time - (p_worklist+i)->work_end_time > 0)
				{
					(p_sleeplist+i)->sleep_keep_time = (p_worklist+i+1)->work_start_time - (p_worklist+i)->work_end_time;
				}
				else
				{
					(p_sleeplist+i)->sleep_start_time = 0;
					(p_sleeplist+i)->sleep_keep_time = 0;
				}
			}
			else
			{
				//貌似不需要这个分支，因为若出现跨天的情况，经过前一步的 对工作表排序后，跨天的那一项总是在工作表的最后一项，不会到上一层if中来
			}
		}
		//工作表的第根据前workplan_num项，也就是最后一项
		else
		{
			//没有跨天
			if((p_worklist+i)->work_end_time - (p_worklist+i)->work_start_time > 0)
			{
				//检查这个工作表中有几项有效的计划
				int n = 0;
				int y = 0;
				for(n=0; n<workplan_num; n++)
				{
					if(((p_worklist+n)->work_end_time!=0)&&((p_worklist+n)->work_start_time!=0))
					{
						y++;
					}
				}
				//只有一项，其他都是0
				if(y==1)
				{
					//睡眠时间是一天的秒数减去工作表中仅有的一项工作计划的结束时间再加上这项计划自己的开始时间
					(p_sleeplist+i)->sleep_keep_time = 24*3600-(p_worklist+i)->work_end_time + (p_worklist+i)->work_start_time;
				}
				//有多项计划
				else
				{
                    //找出工作表中开始时间最小的那项计划的开始时间
					int32_t min_start_time = 86400+1;
					for(int m=0; m<workplan_num; m++)
					{
						if((p_worklist+m)->work_end_time - (p_worklist+m)->work_start_time > 0)
						{
							if(min_start_time > (p_worklist+m)->work_start_time)
							{
								min_start_time = (p_worklist+m)->work_start_time;
							}
						}
					}
					//睡眠时间是一天的秒数减去工作表最后一项工作计划的结束时间加上工作表最早开始的计划的开始时间
					(p_sleeplist+i)->sleep_keep_time = 24*3600-(p_worklist+i)->work_end_time + min_start_time;
				}
			}
			//最后一项计划跨天了
			else if((p_worklist+i)->work_end_time - (p_worklist+i)->work_start_time < 0)
			{
				//检查这个工作表中有几项有效的计划
				int n = 0;
				int y = 0;
				for(n=0; n<workplan_num; n++)
				{
					if(((p_worklist+n)->work_end_time!=0)&&((p_worklist+n)->work_start_time!=0))
					{
						y++;
					}
				}
				//只有一项，其他都是0
				if(y==1)
				{
					//睡眠时间是工作表中仅有的一项工作计划的开始时间再加上这项计划自己的结束时间，因为跨天肯定是开始时间比结束时间秒数大
					//除非跨天之后又垮了24*3600秒，那将会导致这个式子计算出的结果很大！！！
					(p_sleeplist+i)->sleep_keep_time = (p_worklist+i)->work_start_time - (p_worklist+i)->work_end_time;
				}
				//有多项计划
				else
				{
					//找出工作表中开始时间最小的那项计划的开始时间
					int32_t min_start_time = 86400+1;
					for(int m=0; m<workplan_num; m++)
					{
						if((p_worklist+m)->work_end_time - (p_worklist+m)->work_start_time > 0)
						{
							if(min_start_time > (p_worklist+m)->work_start_time)
							{
								min_start_time = (p_worklist+m)->work_start_time;
							}
						}
					}
					//和前面一样，仍需要判断最早开始时间是否晚于本项计划的结束时间
					if(min_start_time - (p_worklist+i)->work_end_time > 0)
					{
						//睡眠时间是工作表中最早开始的那项计划的开始时间减去跨天后的结束时间
						(p_sleeplist+i)->sleep_keep_time = min_start_time - (p_worklist+i)->work_end_time;
					}
					else
					{
						(p_sleeplist+i)->sleep_start_time = 0;
						(p_sleeplist+i)->sleep_keep_time = 0;
					}
				}
			}
			else if((p_worklist+i)->work_end_time - (p_worklist+i)->work_start_time == 0)
			{
				(p_sleeplist+i)->sleep_start_time = 0;
				(p_sleeplist+i)->sleep_keep_time = 0;
			}
		}
	}

	#if DEBUG_PRINT_LEVEL_my_workmode_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
	printf("order sleeplist:\n");
	for(int i=0; i<workplan_num; i++)
	{
        printf("%d, %d(%d:%d+%d:%dh)\n", (p_sleeplist+i)->sleep_start_time, (p_sleeplist+i)->sleep_keep_time, \
            (p_sleeplist+i)->sleep_start_time/3600, (p_sleeplist+i)->sleep_start_time%3600/60, \
            (p_sleeplist+i)->sleep_keep_time/3600, (p_sleeplist+i)->sleep_keep_time%3600/60);
	}
	printf("\n\n");
	#endif

	//合并睡眠表中的跨天分割，间隙为0的，相邻两项（这一步要不要无所谓）
	for(int i=0; i<workplan_num; i++)
	{
		if((p_sleeplist+i)->sleep_start_time + (p_sleeplist+i)->sleep_keep_time == 24*3600)
		{
			for(int j=0; j<workplan_num; j++)
			{
				if(((p_sleeplist+j)->sleep_start_time==0)&&((p_sleeplist+j)->sleep_keep_time>0))
				{
					(p_sleeplist+i)->sleep_keep_time += (p_sleeplist+j)->sleep_keep_time;
					(p_sleeplist+j)->sleep_keep_time = 0;
				}
			}
		}
	}
	
	#if DEBUG_PRINT_LEVEL_my_workmode_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
	printf("order sleeplist optimal:\n");
	for(int i=0; i<workplan_num; i++)
	{
		printf("%d, %d(%d:%d+%d:%d)\n", (p_sleeplist+i)->sleep_start_time, (p_sleeplist+i)->sleep_keep_time, \
            (p_sleeplist+i)->sleep_start_time/3600, (p_sleeplist+i)->sleep_start_time%3600/60, \
            (p_sleeplist+i)->sleep_keep_time/3600, (p_sleeplist+i)->sleep_keep_time%3600/60);
	}
	printf("\n\n");
	#endif


	return 0;
}

int MyWorkmode_PickSleepKeepTime_Afterfirstpoweron(my_workmode_conf_t* conf)
{
	time_t cur_time_in_day = MyTime_GetTime()%(3600*24);

	ESP_LOGI("MyWorkmode_PickSleepKeepTime_Afterfirstpoweron()", "cur_time_in_day=%ld\n", cur_time_in_day);

	//若当前时间处于工作时段，则直接将fisrt_poweron_sleep_keep_time置零
	for(int i=0; i<5; i++)
	{
		if(( (conf->workplan_list+i)->work_start_time > 0 ) && ( (conf->workplan_list+i)->work_end_time > 0 ) )
		{
			if( (conf->workplan_list+i)->work_end_time - (conf->workplan_list+i)->work_start_time > 0 )
			{
				if( (cur_time_in_day >= (conf->workplan_list+i)->work_start_time) && \
					(cur_time_in_day < (conf->workplan_list+i)->work_end_time))
				{
					conf->fisrt_poweron_sleep_keep_time = 0;
					return 0;
				}
			}
			else if( (conf->workplan_list+i)->work_start_time - (conf->workplan_list+i)->work_end_time > 0 )
			{
				if( (cur_time_in_day >= (conf->workplan_list+i)->work_start_time) || \
					(cur_time_in_day < (conf->workplan_list+i)->work_end_time) )
				{
					conf->fisrt_poweron_sleep_keep_time = 0;
					return 0;
				}
			}
		}
	}
	//来到这里说明当前时间不在工作时段

	//查找有无跨天工作计划
	int i_ftdp = 0;
	for(i_ftdp=0; i_ftdp<5; i_ftdp++)
	{
		if(( (conf->workplan_list+i_ftdp)->work_start_time > 0 ) && ( (conf->workplan_list+i_ftdp)->work_end_time > 0 ) )
		{
			if( (conf->workplan_list+i_ftdp)->work_start_time - (conf->workplan_list+i_ftdp)->work_end_time > 0 )
			{
				break;
			}
		}
	}

	//工作表中无跨天工作计划
	if(i_ftdp>=5)
	{
		//找到工作表中在一天之内最后结束的那项计划的结束时间
		int32_t max_end_time = 0;
		for(int m=0; m<5; m++)
		{
			if(( (conf->workplan_list+m)->work_start_time > 0 ) && ( (conf->workplan_list+m)->work_end_time > 0 ) )
			{
				if((conf->workplan_list+m)->work_end_time > max_end_time)
				{
					max_end_time = (conf->workplan_list+m)->work_end_time;
				}
			}
		}

		//当前时间超过了一天之内最晚结束的那项工作计划的结束时间（隐藏成立：肯定没超过凌晨，若超过凌晨，条件不可能成立）
		if(cur_time_in_day > max_end_time)
		{
			//查找工作表中一天内最早开始的那项计划的开始时间
			int32_t min_start_time = 86400+1;
			for(int m=0; m<5; m++)
			{
				if(( (conf->workplan_list+m)->work_start_time > 0 ) && ( (conf->workplan_list+m)->work_end_time > 0 ) )
				{
					if((conf->workplan_list+m)->work_start_time < min_start_time)
					{
						min_start_time = (conf->workplan_list+m)->work_start_time;
					}
				}
			}
			//fisrt_poweron_sleep_keep_time一天的秒数减去当前时间再加上一天中最早那项计划的开始时间
			conf->fisrt_poweron_sleep_keep_time = 24*3600 - cur_time_in_day + min_start_time;
			return 0;
		}
		//当前时间没有超过一天之内最晚结束的那项工作计划的结束时间
		else
		{
			//在工作表中查找下一项最早的计划，以当前时间为参考
			int32_t next_min_start_time = 86400+1;
			for(int m=0; m<5; m++)
			{
				if(( (conf->workplan_list+m)->work_start_time > 0 ) && ( (conf->workplan_list+m)->work_end_time > 0 ) )
				{
					if((conf->workplan_list+m)->work_start_time > cur_time_in_day)
					{
						if((conf->workplan_list+m)->work_start_time < next_min_start_time)
						{
							next_min_start_time = (conf->workplan_list+m)->work_start_time;
						}
					}
				}
			}
			if(cur_time_in_day<next_min_start_time)
			{
				//计算fisrt_poweron_sleep_keep_time距离下一项计划开始的时间
				conf->fisrt_poweron_sleep_keep_time = next_min_start_time - cur_time_in_day;
			}
			else
			{
				//不应该到达这里
				conf->fisrt_poweron_sleep_keep_time = 0;
				ESP_LOGE("MyWorkmode_PickSleepKeepTime_Afterfirstpoweron()", "error2");
			}
			return 0;
		}
		//不应该到达这里
		return 0;
	}
	//工作表中有跨天工作计划
	else
	{
		//找出跨天计划
		int frdp_index = 0;
		for(frdp_index=0; frdp_index<5; frdp_index++)
		{
			if((conf->workplan_list+frdp_index)->work_start_time - (conf->workplan_list+frdp_index)->work_end_time > 0)
			{
				break;
			}
		}
		//如果当前时间在跨天计划结束之后，（隐藏成立：当前时间肯定不在工作时段之内）
		if(cur_time_in_day>(conf->workplan_list+frdp_index)->work_end_time)
		{
			//在工作表中查找下一项最早的计划，以当前时间为参考
			int32_t next_min_start_time = 86400+1;
			for(int m=0; m<5; m++)
			{
				if((conf->workplan_list+m)->work_start_time > cur_time_in_day)
				{
					if((conf->workplan_list+m)->work_start_time < next_min_start_time)
					{
						next_min_start_time = (conf->workplan_list+m)->work_start_time;
					}
				}
			}
			if(cur_time_in_day<next_min_start_time)
			{
			//计算fisrt_poweron_sleep_keep_time距离下一项计划开始的时间

				conf->fisrt_poweron_sleep_keep_time = next_min_start_time - cur_time_in_day;
			}
			else
			{
				//不应该到达这里
				conf->fisrt_poweron_sleep_keep_time = 0;
				ESP_LOGE("MyWorkmode_PickSleepKeepTime_Afterfirstpoweron()", "error2");
			}
			return 0;
		}
		else if(cur_time_in_day<(conf->workplan_list+frdp_index)->work_start_time)
		{
			//不应该到达这里

			//不需要这个分支，因为跨天的结束时间肯定是整个工作表中最早的时间，跨天工作计划的开始时间肯定是整个工作表中最晚的时间
			//所以：如果当前时间既不在工作计划内，又小于跨天计划的开始时间，那么肯定会进入上一个分支。
		}
		else
		{
			//不应该到达这里
		}
	}

	ESP_LOGE("MyWorkmode_PickSleepKeepTime_Afterfirstpoweron()", "error3");
	conf->fisrt_poweron_sleep_keep_time = 0;


	return 0;
}

int MyWorkmode_Check_WhetherInWorktime(my_workmode_conf_t* conf)
{
	int ret = 0;

	time_t cur_time_in_day = MyTime_GetTime()%(3600*24);

	// ESP_LOGI("MyWorkmode_Check_WhetherInWorktime()", "cur_time_in_day=%ld\n", cur_time_in_day);

	int zero_check_index=0;
	for(zero_check_index=0; zero_check_index<5; zero_check_index++)
	{
		if(( (conf->workplan_list+zero_check_index)->work_start_time != 0 ) && ( (conf->workplan_list+zero_check_index)->work_end_time != 0 ) )
		{
			break;
		}
	}
	if(zero_check_index==5)
	{
		return 1;
	}

	//若当前时间处于工作时段，则直接将fisrt_poweron_sleep_keep_time置零
	for(int i=0; i<5; i++)
	{
		if(( (conf->workplan_list+i)->work_start_time > 0 ) && ( (conf->workplan_list+i)->work_end_time > 0 ) )
		{
			if( (conf->workplan_list+i)->work_end_time - (conf->workplan_list+i)->work_start_time > 0 )
			{
				if( (cur_time_in_day >= (conf->workplan_list+i)->work_start_time) && \
					(cur_time_in_day < (conf->workplan_list+i)->work_end_time))
				{
					conf->fisrt_poweron_sleep_keep_time = 0;
					ret = 1;
					goto MyWorkmode_Check_WhetherInWorktime_goto;
				}
			}
			else if( (conf->workplan_list+i)->work_start_time - (conf->workplan_list+i)->work_end_time > 0 )
			{
				if( (cur_time_in_day >= (conf->workplan_list+i)->work_start_time) || \
					(cur_time_in_day < (conf->workplan_list+i)->work_end_time) )
				{
					conf->fisrt_poweron_sleep_keep_time = 0;
					ret = 1;
					goto MyWorkmode_Check_WhetherInWorktime_goto;
				}
			}
		}
	}
	//来到这里说明当前时间不在工作时段
	MyWorkmode_Check_WhetherInWorktime_goto:
	if(ret)
	{
		// ESP_LOGI("MyWorkmode_Check_WhetherInWorktime()", "It's working time\n");
	}
	else
	{
		// ESP_LOGI("MyWorkmode_Check_WhetherInWorktime()", "It's not working time\n");
	}
	return ret;
}

int MyWorkmode_Check(void)
{
    time_t cur_time_in_day = MyTime_GetTime()%(3600*24);
    uint32_t sleep_keep_time = 0;
    uint8_t wakeup_reason = MYLOWPOWER_WAKEUP_TYPE_NO_SLEEP;

    switch(my_workmode_info.conf.workmode)
    {
        int i=0;
        case(workmode_standby):break;
        case(workmode1_realtime):break;
        case(workmode2_semirealtime):
			for(i=0; i<5; i++)
			{
				if((my_workmode_info.conf.sleepplan_list[i].sleep_start_time>0) && (my_workmode_info.conf.sleepplan_list[i].sleep_keep_time>0))
				{
					if(my_workmode_info.conf.sleepplan_list[i].sleep_start_time + my_workmode_info.conf.sleepplan_list[i].sleep_keep_time < 24*3600)
					{
						if( ( cur_time_in_day >= my_workmode_info.conf.sleepplan_list[i].sleep_start_time ) && \
							( cur_time_in_day < my_workmode_info.conf.sleepplan_list[i].sleep_start_time + my_workmode_info.conf.sleepplan_list[i].sleep_keep_time ) )
						{
                			sleep_keep_time = my_workmode_info.conf.sleepplan_list[i].sleep_keep_time - (cur_time_in_day - my_workmode_info.conf.sleepplan_list[i].sleep_start_time);
							break;
						}
					}
					else
					{
						if( ( ( cur_time_in_day >= my_workmode_info.conf.sleepplan_list[i].sleep_start_time ) && (cur_time_in_day<24*3600) ) )
						{
                			sleep_keep_time = my_workmode_info.conf.sleepplan_list[i].sleep_keep_time - (cur_time_in_day - my_workmode_info.conf.sleepplan_list[i].sleep_start_time);
							break;
						}
						else if( cur_time_in_day < my_workmode_info.conf.sleepplan_list[i].sleep_keep_time-(24*3600-my_workmode_info.conf.sleepplan_list[i].sleep_start_time) )
						{
                			sleep_keep_time = my_workmode_info.conf.sleepplan_list[i].sleep_keep_time - (cur_time_in_day + 24*3600 - my_workmode_info.conf.sleepplan_list[i].sleep_start_time);
							break;
						}
					}
				}
			}
			ESP_LOGI("MyWorkmode_Check(workmode2_semirealtime)", "i=%d, sleep_keep_time=%ds", i, sleep_keep_time);
        break;
		case(workmode_energy_saving):
			ESP_LOGI("MyWorkmode_Check(workmode3_timer)", "workmode_energy_saving");
        case(workmode3_timer):
			sleep_keep_time = my_workmode_info.conf.workmode3_timer_sleep_keep_time;
			ESP_LOGI("MyWorkmode_Check(workmode3_timer)", "sleep_keep_time=%ds", sleep_keep_time);
        break;
        case(workmode4_semitimer):
		for(i=0; i<5; i++)
			{
				if((my_workmode_info.conf.sleepplan_list[i].sleep_start_time>0) && (my_workmode_info.conf.sleepplan_list[i].sleep_keep_time>0))
				{
					if(my_workmode_info.conf.sleepplan_list[i].sleep_start_time + my_workmode_info.conf.sleepplan_list[i].sleep_keep_time < 24*3600)
					{
						if( ( cur_time_in_day >= my_workmode_info.conf.sleepplan_list[i].sleep_start_time ) && \
							( cur_time_in_day < my_workmode_info.conf.sleepplan_list[i].sleep_start_time + my_workmode_info.conf.sleepplan_list[i].sleep_keep_time ) )
						{
                			sleep_keep_time = my_workmode_info.conf.sleepplan_list[i].sleep_keep_time - (cur_time_in_day - my_workmode_info.conf.sleepplan_list[i].sleep_start_time);
							break;
						}
					}
					else
					{
						if( ( ( cur_time_in_day >= my_workmode_info.conf.sleepplan_list[i].sleep_start_time ) && (cur_time_in_day<24*3600) ) )
						{
                			sleep_keep_time = my_workmode_info.conf.sleepplan_list[i].sleep_keep_time - (cur_time_in_day - my_workmode_info.conf.sleepplan_list[i].sleep_start_time);
							break;
						}
						else if( cur_time_in_day < my_workmode_info.conf.sleepplan_list[i].sleep_keep_time-(24*3600-my_workmode_info.conf.sleepplan_list[i].sleep_start_time) )
						{
                			sleep_keep_time = my_workmode_info.conf.sleepplan_list[i].sleep_keep_time - (cur_time_in_day + 24*3600 - my_workmode_info.conf.sleepplan_list[i].sleep_start_time);
							break;
						}
					}
				}
			}
			if(i>=5)
			{
				if(my_workmode_info.conf.workmode3_timer_sleep_keep_time>0)
				{
					sleep_keep_time = my_workmode_info.conf.workmode3_timer_sleep_keep_time;
				}
			}
			ESP_LOGI("MyWorkmode_Check(workmode4_semitimer)", "i=%d, sleep_keep_time=%ds", i, sleep_keep_time);
        break;
    }

	ESP_LOGI("MyWorkmode_Check()", "final sleep_keep_time=%ds", sleep_keep_time);
	if(sleep_keep_time>0)
	{
		if(sleep_keep_time > MYWORKMODE_ONCE_SLEEP_TIME_ULIMIT)
		{
			sleep_keep_time = MYWORKMODE_ONCE_SLEEP_TIME_ULIMIT;
		}
		my_workmode_info.MyLowPower_Init_BeforeSleep();
		MyTime_PrintTime();
		wakeup_reason = MyLowPower_EnterLightSleepFor(sleep_keep_time);
		MyTime_PrintTime();
		my_workmode_info.MyLowPower_Init_AfterWakeup(wakeup_reason);
	}

    if(wakeup_reason!=MYLOWPOWER_WAKEUP_TYPE_NO_SLEEP)
    {
        
    }
    return wakeup_reason;
}

#if DEBUG_PRINT_LEVEL_my_workmode_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
void PrintMyworkmodedata(my_workmode_conf_t* conf)
{
	printf("\n\nPrintMyworkmodedata()-------------------------------------------------------------------------\n\n");
	printf("--workmode:\n");
    printf("conf->workmode=%d\n", conf->workmode);
    printf("conf->workplan_list[0].work_start_time=%d\n", conf->workplan_list[0].work_start_time);
    printf("conf->workplan_list[0].work_end_time=%d\n", conf->workplan_list[0].work_end_time);
    printf("conf->workplan_list[1].work_start_time=%d\n", conf->workplan_list[1].work_start_time);
    printf("conf->workplan_list[1].work_end_time=%d\n", conf->workplan_list[1].work_end_time);
    printf("conf->workplan_list[2].work_start_time=%d\n", conf->workplan_list[2].work_start_time);
    printf("conf->workplan_list[2].work_end_time=%d\n", conf->workplan_list[2].work_end_time);
    printf("conf->workplan_list[3].work_start_time=%d\n", conf->workplan_list[3].work_start_time);
    printf("conf->workplan_list[3].work_end_time=%d\n", conf->workplan_list[3].work_end_time);
    printf("conf->workplan_list[4].work_start_time=%d\n", conf->workplan_list[4].work_start_time);
    printf("conf->workplan_list[4].work_end_time=%d\n", conf->workplan_list[4].work_end_time);
    printf("conf->workmode3_timer_sleep_keep_time=%d\n", conf->workmode3_timer_sleep_keep_time);
	printf("conf->fisrt_poweron_sleep_keep_time=%d\n", conf->fisrt_poweron_sleep_keep_time);
    printf("\n\n");

	printf("origin worklist:\n");
	for(int i=0; i<5; i++)
	{
        printf("%d, %d(%d:%d~%d:%d)\n", (conf->workplan_list+i)->work_start_time, (conf->workplan_list+i)->work_end_time, \
            (conf->workplan_list+i)->work_start_time/3600, (conf->workplan_list+i)->work_start_time%3600/60, \
            (conf->workplan_list+i)->work_end_time/3600, (conf->workplan_list+i)->work_end_time%3600/60);
	}
	printf("\n\n");

	printf("order worklist:\n");
	for(int i=0; i<5; i++)
	{
		printf("%d, %d(%d:%d~%d:%d)\n", (conf->workplan_list+i)->work_start_time, (conf->workplan_list+i)->work_end_time, \
            (conf->workplan_list+i)->work_start_time/3600, (conf->workplan_list+i)->work_start_time%3600/60, \
            (conf->workplan_list+i)->work_end_time/3600, (conf->workplan_list+i)->work_end_time%3600/60);
	}
	printf("\n\n");

	printf("order sleeplist:\n");
	for(int i=0; i<5; i++)
	{
        printf("%d, %d(%d:%d+%d:%dh)\n", (conf->sleepplan_list+i)->sleep_start_time, (conf->sleepplan_list+i)->sleep_keep_time, \
            (conf->sleepplan_list+i)->sleep_start_time/3600, (conf->sleepplan_list+i)->sleep_start_time%3600/60, \
            (conf->sleepplan_list+i)->sleep_keep_time/3600, (conf->sleepplan_list+i)->sleep_keep_time%3600/60);
	}
	printf("\n\nPrintMyworkmodedata()-------------------------------------------------------------------------\n\n");
}
#endif

void MyWorkmode_CallbackRegister_MyLowPower_Init_BeforeWakeup(void (*callback_func_beforesleep)(void))
{
	my_workmode_info.MyLowPower_Init_BeforeSleep = callback_func_beforesleep;
}

void MyWorkmode_CallbackRegister_MyLowPower_Init_AfterWakeup(void (*callback_func_aftersleep)(int))
{
	my_workmode_info.MyLowPower_Init_AfterWakeup = callback_func_aftersleep;
}

int MyWorkmode_Init(my_workmode_conf_t* conf, void (*callback_func_beforesleep)(void), void (*callback_func_aftersleep)(int))
{

    MyWorkmode_WorkList_To_SleepList(conf->workplan_list, 5, conf->sleepplan_list, 5);
	MyWorkmode_PickSleepKeepTime_Afterfirstpoweron(conf);

    if(conf!=NULL)
    {
        memcpy(&my_workmode_info.conf, conf, sizeof(my_workmode_info.conf));
    }
    else
    {
        my_workmode_info.conf.workmode = myworkmode_workmode_default;
    }

	if(callback_func_beforesleep!=NULL)
	{
		MyWorkmode_CallbackRegister_MyLowPower_Init_BeforeWakeup(callback_func_beforesleep);
	}
	if(callback_func_aftersleep!=NULL)
	{
		MyWorkmode_CallbackRegister_MyLowPower_Init_AfterWakeup(callback_func_aftersleep);
	}

	#if DEBUG_PRINT_LEVEL_my_workmode_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
	PrintMyworkmodedata(conf);
	#endif

    return 0;
}

int MyWorkmode_ReLoad_Conf(my_workmode_conf_t* conf)
{
    return MyWorkmode_Init(conf, NULL, NULL);
}

my_workmode_t MyWorkmode_GetCurrentWorkmode(void)
{
    return my_workmode_info.conf.workmode;
}

#endif