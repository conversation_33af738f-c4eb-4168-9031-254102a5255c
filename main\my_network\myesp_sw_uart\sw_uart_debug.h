#ifndef SW_UART_DEBUG_H
#define SW_UART_DEBUG_H

#include "sw_uart_config.h"

#if SW_UART_ENABLE

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化软串口调试
 * 
 * @return 
 *     - SW_UART_OK: 成功
 *     - 其他: 错误代码
 */
int sw_uart_debug_init(void);

/**
 * @brief 启动软串口调试测试
 */
void sw_uart_debug_start(void);

/**
 * @brief 手动发送测试数据
 * 
 * @param data 要发送的数据
 * @return 实际发送的字节数，错误时返回负值
 */
int sw_uart_debug_send_test(const char* data);

/**
 * @brief 清空接收缓冲区
 */
void sw_uart_debug_flush(void);

/**
 * @brief 重置调试统计
 */
void sw_uart_debug_reset_stats(void);

#ifdef __cplusplus
}
#endif

#endif // SW_UART_ENABLE

#endif // SW_UART_DEBUG_H
