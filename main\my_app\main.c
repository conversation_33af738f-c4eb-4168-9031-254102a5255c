#include "my_config.h"
#include "my_camera.h"
#include "my_gpio.h"
#include "led.h"
#include "my_monitor.h"
#include "my_nvs.h"
#include "mywifi.h"
#include "my_tcp_server.h"
#include "my_tcp_client.h"
#include "my_esp_chip_info_mng.h"
#include "my_uart.h"
#include "simple_http_ota.h"
#include "my_ledc.h"
#include "my_lowpower.h"
#include "my_power_mng.h"
#include "my_time.h"
#include "my_iot_box.h"
#include "my_tsfc.h"
#include "my_asl.h"
#include "my_ble.h"
#include "my_kmp.h"
#include "my_workmode.h"
#include "my_ds.h"
#include "my_nvs_ds.h"
#include "my_soft_aes.h"
#include "mytest_uart_iot_server.h"
#include "my_dev_paramid.h"
#include "my_esp_aes.h"
#include "test_my_aes.h"
#include "mytest_radar.h"

#include <esp_log.h>
#include "esp_system.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"
#include "freertos/semphr.h"
#include "freertos/timers.h"

#include "rtc.h"
#include "time.h"

#include <stdint.h>
#include <stdbool.h>
#include <stdio.h>
#include <string.h>

#include "adxl345.h"

#include "cJSON.h"

#include "my_blufi.h"

#include "my_led_mng.h"

#include "my_relay.h"

#include "e104_bt53c3.h"
#include "my_aux_485_port.h"
#include "evgspl.h"
//-----------------------------------

char* TAG="swqz_app_main";

int test_main_id = -1;
int test_main_id_1 = -1;

bool bts_rfs_request = false;
bool bts_reboot_request = false;
bool bts_modify_httpotaurl_request = false;
char* http_ota_url = NULL;
bool bts_httpota_request = false;

bool bts_set_relay1_mode_request = false;
int bts_relay1_mode = 0;
bool bts_set_relay1_Xmode_keeptime_request = false;
int bts_relay1_Xmode_keeptime = 0;
bool bts_set_channeldoor_mode_request = false;
int bts_channeldoor_mode = 0;

bool bts_modify_iotip_request = false;
char* iot_ip = NULL;
bool bts_modify_iotport_request = false;
int iot_port = 0;

void testtask1(void* param)
{
	int runtimes = 0;

	#define test_recv_buf_len 1024
	char* test_recv_buf = calloc(1, test_recv_buf_len);
	int recv_len = 0;

	if(test_recv_buf==NULL)
	{
		ESP_LOGE("testtask1()", "test_recv_buf==NULL!");
		while(1)
		{
			vTaskDelay(1000 / portTICK_PERIOD_MS);
		}
	}

	for(;;)
	{
		if(test_main_id>=0)
		{
			memset(test_recv_buf, 0, test_recv_buf_len);
			recv_len = My_TcpClient_RecvDataFromServer(test_main_id, test_recv_buf, test_recv_buf_len, 0, 3000);
			ESP_LOGI("testtask1()", "test_recv_buf(%d)=%s", recv_len, test_recv_buf);
		}
	}
}

void testtask2(void* param)
{
	int runtimes = 0;

	#define test_send_buf_len 1024
	char* test_send_buf = calloc(1, test_send_buf_len);

	char* test_recv_buf = calloc(1, test_recv_buf_len);
	int recv_len = 0;

	if(test_send_buf==NULL)
	{
		ESP_LOGE("testtask2()", "test_send_buf==NULL!");
		while(1)
		{
			vTaskDelay(1000 / portTICK_PERIOD_MS);
		}
	}

	if(test_main_id>=0)
	{
		memset(test_send_buf, 0, test_send_buf_len);
		sprintf(test_send_buf, "testtask2() runtimes=%d\n", runtimes);
		ESP_LOGI("testtask2()", "test_send_buf(%d)=%s", strlen(test_send_buf), test_send_buf);
		My_TcpClient_SendDataToServer(test_main_id, test_send_buf, strlen(test_send_buf));
	}

	if(test_main_id_1>=0)
	{
		memset(test_send_buf, 0, test_send_buf_len);
		sprintf(test_send_buf, "testtask2() runtimes=%d\n", runtimes);
		ESP_LOGI("testtask2()", "test_send_buf(%d)=%s", strlen(test_send_buf), test_send_buf);
		My_TcpClient_SendDataToServer(test_main_id_1, test_send_buf, strlen(test_send_buf));
	}

	for(;;)
	{
		runtimes++;
		if(test_main_id_1>=0)
		{
			memset(test_recv_buf, 0, test_recv_buf_len);
			recv_len = My_TcpClient_RecvDataFromServer(test_main_id_1, test_recv_buf, test_recv_buf_len, 0, 3000);
			ESP_LOGI("testtask2()", "test_recv_buf(%d)=%s", recv_len, test_recv_buf);
		}
	}
}

// void generate_json_example() {
//     // 创建一个JSON对象
//     cJSON *root = cJSON_CreateObject();
 
//     // 向JSON对象中添加键值对
//     cJSON_AddStringToObject(root, "time", "2024-25-25 12:00:00");
//     cJSON_AddNumberToObject(root, "object_count", 3);
//     cJSON_AddNumberToObject(root, "x", 1111);
// 	cJSON_AddNumberToObject(root, "y", 2222);
// 	cJSON_AddNumberToObject(root, "speed", 16);

// 	cJSON_Print(root);
 
//     // 将JSON对象转换为字符串
//     char *json_string = cJSON_Print(root);
//     printf("Generated JSON:\n%s\n", json_string);

// 	int json_string_len = strlen(json_string);
// 	printf("json_string_len=%d\n", json_string_len);
 
//     // 释放内存
//     cJSON_Delete(root);
//     free(json_string);
// }

extern bool gl_sta_connected;

extern void MyLowPower_Init_BeforeSleep();
extern void MyLowPower_Init_AfterWakeup(int wakeup_reason);

extern char e104_mac[32];
extern bool e104_mac_got;

extern void iotbox_channeldoormode_update(void);

void app_main()
{
	My_nvs_flash_init();
	
	#if MY_ESP_CHIP_INFO_MNG_FUNC
	MyEspChipInfoMng_Init();

	my_config_nvs_t* p_my_conf = MyConfig_LoadFromNVS();
	#endif

	MyGpioInit();

	#if MY_POWER_MNG_FUNC
	MyPowerMngInit();
	PowerCheckGet_InitState();
	#endif

	#if MY_LEDC_FUNC
	MyLedInit();
	MyLedMng_Init();
	MyLed_SetMode_Super(ledmode_on);
	#endif

	#if MY_RELAY_FUNC
	my_relay_init();
	#endif

	#if MY_MONITOR_FUNC
	My_Monitor_Init();
	#endif

	#if MYTEST_CONFIG_UART_IOT_SERVER
	Mytest_Init();
	#endif

	// My_ADXL345_Init();

    // MyTest_Radar_Init();

	//工作模式
    MyWorkmode_Init(&p_my_conf->workmode.conf, MyLowPower_Init_BeforeSleep, MyLowPower_Init_AfterWakeup);

	MyWHGM5_Init_SimpleTcpMode(&p_my_conf->net.conf_tcp1, &p_my_conf->lte, 1);

	// char imei[32];
	// memset(imei, 0, sizeof(imei));
	// MyWHGM5_GetIMEI(imei, sizeof(imei));
	blufi_main(NULL, NULL);

	// my_aux_485_port_init();
	e104_bt53c3_init();

	char esp_mac[16];
    memset(esp_mac, 0, sizeof(esp_mac));
    MyEspChipInfoMng_GetMacStr(esp_mac);

	char c104_adv_name[32];
    memset(c104_adv_name, 0, sizeof(c104_adv_name));

    sprintf(c104_adv_name, "EVG-%s", esp_mac);

	MyConfig_Modify_BLE_name(c104_adv_name, strlen(c104_adv_name), 1, 0);

	if(e104_mac_got==true)
	{
		MyConfig_Modify_BLE_mac(e104_mac, strlen(e104_mac), 1, 0);
	}

	// evgspl_init();

	vTaskDelay(10000 / portTICK_PERIOD_MS);

	ESP_LOGI("app_main()", "My_IotBox_Init()...\n\n\n\n\n\n\n\n\n\n\n\n\n");
	#if MY_IOT_BOX_FUNC
	My_IotBox_Init();
	#endif

	
	

	// MyWifi_Init(&p_my_conf->wifi);

	// My_TcpClient_Init(&p_my_conf->tcp_client.conf);

	MyTime_PrintTime();
	

	StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    // xTaskCreate(CheckPowerTask,  "CheckPowerTask",    4096,    NULL, 14, NULL);
    // p_task_stack = calloc(1, 4096*sizeof(StackType_t));
    // if(p_task_stack!=NULL)
    // {
    //     p_task_data = calloc(1, sizeof(StaticTask_t));
    //     if(p_task_data!=NULL)
    //     {
    //         //xTaskCreateStatic(&testtask1, "testtask1", 4096, NULL, 8, p_task_stack, p_task_data);
	// 		xTaskCreateStaticPinnedToCore(&testtask1, "testtask1", 4096, NULL, 8, p_task_stack, p_task_data, 0);
    //     }
    // }

	// p_task_stack = calloc(1, 4096*sizeof(StackType_t));
    // if(p_task_stack!=NULL)
    // {
    //     p_task_data = calloc(1, sizeof(StaticTask_t));
    //     if(p_task_data!=NULL)
    //     {
    //         //xTaskCreateStatic(&testtask1, "testtask1", 4096, NULL, 8, p_task_stack, p_task_data);
	// 		xTaskCreateStaticPinnedToCore(&testtask2, "testtask2", 4096, NULL, 8, p_task_stack, p_task_data, 1);
    //     }
    // }


	while (1)
	{
		while(1)
		{
			// printf("app_main()_xPortGetCoreID()=%d\n", xPortGetCoreID());
			vTaskDelay(1000 / portTICK_PERIOD_MS);
			if(bts_reboot_request==true)
			{
				bts_reboot_request = false;
				ESP_LOGI("app_main()", "bts_reboot_request=true, about to reboot after 3s ...");
				vTaskDelay(3000 / portTICK_PERIOD_MS);
				My_esp_restart();
			}
			if(bts_rfs_request==true)
			{
				bts_rfs_request = false;
				ESP_LOGI("app_main()", "bts_rfs_request=true, about to rfs after 3s ...");
				vTaskDelay(3000 / portTICK_PERIOD_MS);
				MyConfig_ResetAllConf_To_Default();

				My_esp_restart();
			}
			if(bts_modify_httpotaurl_request==true)
			{
				bts_modify_httpotaurl_request = false;
				if(http_ota_url!=NULL)
				{
					MyConfig_Modify_factory_otaurl(http_ota_url, strlen(http_ota_url), 1);
					free(http_ota_url);
					http_ota_url = NULL;
				}
			}
			if(bts_httpota_request==true)
			{
				bts_httpota_request = false;
				ESP_LOGI("app_main()", "start http ota...");

				SimpleOtaInit(p_my_conf->factory.http_ota_url);
			}
			if(bts_set_relay1_mode_request)
			{
				bts_set_relay1_mode_request = false;
				MyConfig_Modify_Workmode_Relay1_mode(bts_relay1_mode, 1);
			}
			if(bts_set_relay1_Xmode_keeptime_request)
			{
				bts_set_relay1_Xmode_keeptime_request = false;
				MyConfig_Modify_Workmode_Relay1_Xmode_keeptime(bts_relay1_Xmode_keeptime, 1);
			}
			if(bts_set_channeldoor_mode_request)
			{
				bts_set_channeldoor_mode_request = false;
				MyConfig_Modify_Workmode_channeldoor_mode(bts_channeldoor_mode, 1);
				My_Channeldoor_Update();
			}
			if(bts_modify_iotip_request)
			{
				bts_modify_iotip_request = false;
				MyConfig_Modify_Net_ipv4Address(0, (void*)iot_ip, strlen(iot_ip), 1);
			}
			if(bts_modify_iotport_request)
			{
				bts_modify_iotport_request = false;
				MyConfig_Modify_Net_serverPort(iot_port, 1);
			}
			// Myappmain_PrintSimpleDebugInfo();
			//IndvMonitorService(MONITOR_TYPE_TASK_LIST_DETAIL);
		}
	}
}