#include "e104_bt53c3_sw.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char* TAG = "E104_INTEGRATION_TEST";

/**
 * @brief E104软串口模组集成测试
 * 
 * 本测试用于验证基于软串口的第二个E104模组是否正常工作
 */

// 测试用的引脚定义
#define TEST_E104_SW_TXD_PIN     (GPIO_NUM_4)
#define TEST_E104_SW_RXD_PIN     (GPIO_NUM_5)
#define TEST_E104_SW_RST_PIN     (GPIO_NUM_17)
#define TEST_E104_SW_MOD_PIN     (GPIO_NUM_18)

// 测试状态
static bool test_init_ok = false;
static bool test_mac_received = false;
static bool test_role_received = false;

/**
 * @brief 基本功能测试
 */
void e104_sw_basic_test(void) {
    ESP_LOGI(TAG, "=== E104 SW Basic Function Test ===");
    
    // 1. 初始化测试
    ESP_LOGI(TAG, "1. Testing initialization...");
    int ret = e104_bt53c3_sw_init(TEST_E104_SW_TXD_PIN, TEST_E104_SW_RXD_PIN, 
                                  TEST_E104_SW_RST_PIN, TEST_E104_SW_MOD_PIN);
    if (ret == 0) {
        ESP_LOGI(TAG, "✓ Initialization test PASSED");
        test_init_ok = true;
    } else {
        ESP_LOGE(TAG, "✗ Initialization test FAILED: %d", ret);
        return;
    }
    
    // 等待初始化完成
    vTaskDelay(5000 / portTICK_PERIOD_MS);
    
    // 2. MAC地址查询测试
    ESP_LOGI(TAG, "2. Testing MAC address query...");
    e104_bt53c3_sw_query_dev_mac();
    
    // 等待响应
    for (int i = 0; i < 50; i++) {
        if (e104_sw_bt53c3_chip1_get_mac_ok) {
            ESP_LOGI(TAG, "✓ MAC address query test PASSED: %s", 
                    e104_sw_chip1_mac ? e104_sw_chip1_mac : "NULL");
            test_mac_received = true;
            break;
        }
        vTaskDelay(100 / portTICK_PERIOD_MS);
    }
    
    if (!test_mac_received) {
        ESP_LOGE(TAG, "✗ MAC address query test FAILED");
    }
    
    // 3. 角色查询测试
    ESP_LOGI(TAG, "3. Testing role query...");
    e104_bt53c3_sw_query_dev_role();
    
    // 等待响应
    for (int i = 0; i < 30; i++) {
        if (e104_sw_role >= 0) {
            ESP_LOGI(TAG, "✓ Role query test PASSED: %d", e104_sw_role);
            test_role_received = true;
            break;
        }
        vTaskDelay(100 / portTICK_PERIOD_MS);
    }
    
    if (!test_role_received) {
        ESP_LOGE(TAG, "✗ Role query test FAILED");
    }
    
    // 4. 参数设置测试
    ESP_LOGI(TAG, "4. Testing parameter setting...");
    
    // 设置广播名称
    ret = e104_bt53c3_sw_set_adv_name("TEST_E104_SW", strlen("TEST_E104_SW"));
    if (ret == 0) {
        ESP_LOGI(TAG, "✓ Set advertising name test PASSED");
    } else {
        ESP_LOGE(TAG, "✗ Set advertising name test FAILED: %d", ret);
    }
    
    vTaskDelay(500 / portTICK_PERIOD_MS);
    
    // 设置广播间隔
    ret = e104_bt53c3_sw_set_adv_interval(200);
    if (ret == 0) {
        ESP_LOGI(TAG, "✓ Set advertising interval test PASSED");
    } else {
        ESP_LOGE(TAG, "✗ Set advertising interval test FAILED: %d", ret);
    }
    
    vTaskDelay(500 / portTICK_PERIOD_MS);
    
    // 设置发射功率
    ret = e104_bt53c3_sw_set_power(3);
    if (ret == 0) {
        ESP_LOGI(TAG, "✓ Set power test PASSED");
    } else {
        ESP_LOGE(TAG, "✗ Set power test FAILED: %d", ret);
    }
    
    ESP_LOGI(TAG, "=== Basic Function Test Completed ===");
}

/**
 * @brief 通信测试
 */
void e104_sw_communication_test(void) {
    ESP_LOGI(TAG, "=== E104 SW Communication Test ===");
    
    if (!test_init_ok) {
        ESP_LOGE(TAG, "Skipping communication test - initialization failed");
        return;
    }
    
    // 1. 数据发送测试
    ESP_LOGI(TAG, "1. Testing data transmission...");
    const char* test_data = "Hello E104 SW Test!";
    int ret = e104_bt53c3_sw_send_data_to_master((char*)test_data, strlen(test_data));
    if (ret == 0) {
        ESP_LOGI(TAG, "✓ Data transmission test PASSED");
    } else {
        ESP_LOGE(TAG, "✗ Data transmission test FAILED: %d", ret);
    }
    
    vTaskDelay(1000 / portTICK_PERIOD_MS);
    
    // 2. 连接列表查询测试
    ESP_LOGI(TAG, "2. Testing connection list query...");
    ret = e104_bt53c3_sw_query_connect_list();
    if (ret == 0) {
        ESP_LOGI(TAG, "✓ Connection list query test PASSED");
    } else {
        ESP_LOGE(TAG, "✗ Connection list query test FAILED: %d", ret);
    }
    
    vTaskDelay(1000 / portTICK_PERIOD_MS);
    
    // 3. 断开连接测试
    ESP_LOGI(TAG, "3. Testing disconnect all devices...");
    ret = e104_bt53c3_sw_discon_all_dev();
    if (ret == 0) {
        ESP_LOGI(TAG, "✓ Disconnect all devices test PASSED");
    } else {
        ESP_LOGE(TAG, "✗ Disconnect all devices test FAILED: %d", ret);
    }
    
    ESP_LOGI(TAG, "=== Communication Test Completed ===");
}

/**
 * @brief 状态监控测试
 */
void e104_sw_status_monitor_test(void) {
    ESP_LOGI(TAG, "=== E104 SW Status Monitor Test ===");
    
    if (!test_init_ok) {
        ESP_LOGE(TAG, "Skipping status monitor test - initialization failed");
        return;
    }
    
    // 查询所有状态
    ESP_LOGI(TAG, "Querying all status parameters...");
    
    e104_bt53c3_sw_query_dev_role();
    vTaskDelay(500 / portTICK_PERIOD_MS);
    
    e104_bt53c3_sw_query_adv_interval();
    vTaskDelay(500 / portTICK_PERIOD_MS);
    
    e104_bt53c3_sw_query_power();
    vTaskDelay(500 / portTICK_PERIOD_MS);
    
    // 等待一段时间让所有响应到达
    vTaskDelay(2000 / portTICK_PERIOD_MS);
    
    // 打印状态信息
    ESP_LOGI(TAG, "=== Current Status ===");
    ESP_LOGI(TAG, "Role: %d", e104_sw_role);
    ESP_LOGI(TAG, "Advertising Interval: %d", e104_sw_adv_interval);
    ESP_LOGI(TAG, "Power: %d", e104_sw_adv_power);
    ESP_LOGI(TAG, "MAC: %s", e104_sw_chip1_mac ? e104_sw_chip1_mac : "Unknown");
    ESP_LOGI(TAG, "Init OK: %s", e104_sw_bt53c3_init_ok ? "Yes" : "No");
    ESP_LOGI(TAG, "MAC Got: %s", e104_sw_bt53c3_chip1_get_mac_ok ? "Yes" : "No");
    
    ESP_LOGI(TAG, "=== Status Monitor Test Completed ===");
}

/**
 * @brief 综合测试任务
 */
void e104_sw_integration_test_task(void *pvParameters) {
    ESP_LOGI(TAG, "E104 SW Integration Test Task Started");
    
    // 等待系统稳定
    vTaskDelay(3000 / portTICK_PERIOD_MS);
    
    // 执行基本功能测试
    e104_sw_basic_test();
    
    // 等待一段时间
    vTaskDelay(3000 / portTICK_PERIOD_MS);
    
    // 执行通信测试
    e104_sw_communication_test();
    
    // 等待一段时间
    vTaskDelay(3000 / portTICK_PERIOD_MS);
    
    // 执行状态监控测试
    e104_sw_status_monitor_test();
    
    // 测试总结
    ESP_LOGI(TAG, "=== Integration Test Summary ===");
    ESP_LOGI(TAG, "Initialization: %s", test_init_ok ? "PASS" : "FAIL");
    ESP_LOGI(TAG, "MAC Query: %s", test_mac_received ? "PASS" : "FAIL");
    ESP_LOGI(TAG, "Role Query: %s", test_role_received ? "PASS" : "FAIL");
    
    int passed_tests = 0;
    if (test_init_ok) passed_tests++;
    if (test_mac_received) passed_tests++;
    if (test_role_received) passed_tests++;
    
    ESP_LOGI(TAG, "Test Results: %d/3 tests passed", passed_tests);
    
    if (passed_tests == 3) {
        ESP_LOGI(TAG, "🎉 All tests PASSED! E104 SW module is working correctly.");
    } else {
        ESP_LOGE(TAG, "❌ Some tests FAILED. Please check hardware connections and configuration.");
    }
    
    ESP_LOGI(TAG, "Integration test completed. Task will continue monitoring...");
    
    // 持续监控
    while (1) {
        vTaskDelay(30000 / portTICK_PERIOD_MS); // 每30秒查询一次状态
        e104_sw_status_monitor_test();
    }
}

/**
 * @brief 启动E104软串口模组集成测试
 */
void e104_sw_start_integration_test(void) {
    ESP_LOGI(TAG, "Starting E104 SW Integration Test...");
    ESP_LOGI(TAG, "Test Configuration:");
    ESP_LOGI(TAG, "  TX Pin: GPIO%d", TEST_E104_SW_TXD_PIN);
    ESP_LOGI(TAG, "  RX Pin: GPIO%d", TEST_E104_SW_RXD_PIN);
    ESP_LOGI(TAG, "  RST Pin: GPIO%d", TEST_E104_SW_RST_PIN);
    ESP_LOGI(TAG, "  MOD Pin: GPIO%d", TEST_E104_SW_MOD_PIN);
    
    // 创建测试任务
    xTaskCreate(e104_sw_integration_test_task, "e104_sw_test", 
               4096, NULL, 5, NULL);
}

/*
使用方法：
1. 在main.c中包含此文件
2. 在app_main()中调用 e104_sw_start_integration_test();
3. 连接第二个E104模组到指定引脚
4. 观察串口输出的测试结果

硬件连接：
- ESP32 GPIO4  -> E104模组 RX
- ESP32 GPIO5  -> E104模组 TX  
- ESP32 GPIO17 -> E104模组 RST
- ESP32 GPIO18 -> E104模组 MOD
- 3.3V -> E104模组 VCC
- GND  -> E104模组 GND
*/
