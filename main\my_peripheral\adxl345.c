#include "adxl345.h"

#if USE_ADXL345_FUNC

#include <stdio.h>
#include "esp_log.h"
#include "driver/i2c.h"

#include "string.h"
#include "math.h"

#include "driver/gpio.h"

#include <lwip/netdb.h>

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"

#include "driver/gpio.h"

#include "my_gpio.h"

#include "my_ledc.h"

#include "my_time.h"

static const char *TAG = "i2c-simple-example";

#define I2C_MASTER_SCL_IO           19      /*!< GPIO number used for I2C master clock */
#define I2C_MASTER_SDA_IO           20      /*!< GPIO number used for I2C master data  */
#define I2C_MASTER_NUM              0                          /*!< I2C master i2c port number, the number of i2c peripheral interfaces available will depend on the chip */
#define I2C_MASTER_FREQ_HZ          100000                     /*!< I2C master clock frequency */
#define I2C_MASTER_TX_BUF_DISABLE   0                          /*!< I2C master doesn't need buffer */
#define I2C_MASTER_RX_BUF_DISABLE   0                          /*!< I2C master doesn't need buffer */
#define I2C_MASTER_TIMEOUT_MS       1000

#define MPU9250_SENSOR_ADDR                 0x68        /*!< Slave address of the MPU9250 sensor */
#define MPU9250_WHO_AM_I_REG_ADDR           0x75        /*!< Register addresses of the "who am I" register */

#define MPU9250_PWR_MGMT_1_REG_ADDR         0x6B        /*!< Register addresses of the power managment register */
#define MPU9250_RESET_BIT                   7

#define ADXL345_SENSOR_ADDR                 0x53
#define ADXL345_REG_DEVID                   0x00
#define ADXL345_REG_THRESH_TAP              0X1D
#define ADXL345_REG_OFSX                    0x1E
#define ADXL345_REG_OFSY                    0x1F
#define ADXL345_REG_OFSZ                    0x20
#define ADXL345_REG_DUR                     0x21
#define ADXL345_REG_Latent                  0x22



static esp_err_t mpuadxl345_register_read(uint8_t reg_addr, uint8_t *data, size_t len)
{
    return i2c_master_write_read_device(I2C_MASTER_NUM, ADXL345_SENSOR_ADDR, &reg_addr, 1, data, len, I2C_MASTER_TIMEOUT_MS / portTICK_RATE_MS);
}

static esp_err_t mpuadxl345_register_write_byte(uint8_t reg_addr, uint8_t data)
{
    int ret;
    uint8_t write_buf[2] = {reg_addr, data};

    ret = i2c_master_write_to_device(I2C_MASTER_NUM, ADXL345_SENSOR_ADDR, write_buf, sizeof(write_buf), I2C_MASTER_TIMEOUT_MS / portTICK_RATE_MS);

    return ret;
}

/**
 * @brief i2c master initialization
 */
static esp_err_t i2c_master_init(void)
{
    int i2c_master_port = I2C_MASTER_NUM;

    i2c_config_t conf = {
        .mode = I2C_MODE_MASTER,
        .sda_io_num = I2C_MASTER_SDA_IO,
        .scl_io_num = I2C_MASTER_SCL_IO,
        .sda_pullup_en = GPIO_PULLUP_ENABLE,
        .scl_pullup_en = GPIO_PULLUP_ENABLE,
        .master.clk_speed = I2C_MASTER_FREQ_HZ,
    };

    i2c_param_config(i2c_master_port, &conf);

    return i2c_driver_install(i2c_master_port, conf.mode, I2C_MASTER_RX_BUF_DISABLE, I2C_MASTER_TX_BUF_DISABLE, 0);
}

double calcu_angel_x(int16_t acc_x, int16_t acc_y, int16_t acc_z)
{
    double angel = 0.0;

    angel = atan(sqrt(pow(acc_y, 2)+pow(acc_z, 2))/acc_x)/3.14159*180;

    if(angel>0)
    {
        angel = 90-angel;
    }
    else
    {
        angel = -90-angel;
    }
    
    return angel;
}

double calcu_angel_y(int16_t acc_x, int16_t acc_y, int16_t acc_z)
{
    double angel = 0.0;

    angel = atan(sqrt(pow(acc_x, 2)+pow(acc_z, 2))/acc_y)/3.14159*180;
    
    if(angel>0)
    {
        angel = 90-angel;
    }
    else
    {
        angel = -90-angel;
    }

    return angel;
}

double calcu_angel_z(int16_t acc_x, int16_t acc_y, int16_t acc_z)
{
    double angel = 0.0;

    angel = atan(sqrt(pow(acc_x, 2)+pow(acc_y, 2))/acc_z)/3.14159*180;
    
    return angel;
}

my_adxl345_info_t my_adxl345_info;

my_adxl345_info_t* MyADXL345_Get_ADXL345_Info(void)
{
    return &my_adxl345_info;
}


#define ADXL345_INT1_GPIO_INPUT_PIN_SEL  (1ULL<<18)
#define ADXL345_INT2_GPIO_INPUT_PIN_SEL  (1ULL<<8)
void MyAdxl345_Task(void* param)
{
    memset(&my_adxl345_info, 0, sizeof(my_adxl345_info));

	int runtimes = 0;
    vTaskDelay(3000 / portTICK_PERIOD_MS);

    gpio_config_t io_conf;

    //interrupt of rising edge
    io_conf.intr_type = GPIO_PIN_INTR_POSEDGE;
    //bit mask of the pins, use GPIO4/5 here
    io_conf.pin_bit_mask = ADXL345_INT1_GPIO_INPUT_PIN_SEL;
    //set as input mode    
    io_conf.mode = GPIO_MODE_INPUT;
    //enable pull-up mode
    io_conf.pull_up_en = 0;
    io_conf.pull_down_en = 0;
    gpio_config(&io_conf);

    //interrupt of rising edge
    io_conf.intr_type = GPIO_PIN_INTR_POSEDGE;
    //bit mask of the pins, use GPIO4/5 here
    io_conf.pin_bit_mask = ADXL345_INT2_GPIO_INPUT_PIN_SEL;
    //set as input mode    
    io_conf.mode = GPIO_MODE_INPUT;
    //enable pull-up mode
    io_conf.pull_up_en = 0;
    io_conf.pull_down_en = 0;
    gpio_config(&io_conf);


    MyGpioInputPinRegister(6);
    MyGpioInputPinRegister(7);

    uint8_t data[32];

    memset(data, 0, sizeof(data));

    ESP_ERROR_CHECK(i2c_master_init());
    ESP_LOGI(TAG, "I2C initialized successfully");

    vTaskDelay(1000/portTICK_PERIOD_MS);

    int read_cnt = 0;

    ESP_ERROR_CHECK(mpuadxl345_register_write_byte(0x31, 0x0B));
    ESP_ERROR_CHECK(mpuadxl345_register_write_byte(0x2C, 0x08));
    ESP_ERROR_CHECK(mpuadxl345_register_write_byte(0x2D, 0x08));
    ESP_ERROR_CHECK(mpuadxl345_register_write_byte(0x2E, 0xFF));
    ESP_ERROR_CHECK(mpuadxl345_register_write_byte(0x1E, 0x00));
    ESP_ERROR_CHECK(mpuadxl345_register_write_byte(0x1F, 0x00));
    ESP_ERROR_CHECK(mpuadxl345_register_write_byte(0x20, 0x00));
    // ESP_ERROR_CHECK(mpuadxl345_register_write_byte(0x2A, 0x04));
    // ESP_ERROR_CHECK(mpuadxl345_register_write_byte(0x27, 0x77));
    // ESP_ERROR_CHECK(mpuadxl345_register_write_byte(0x24, 20));
    // ESP_ERROR_CHECK(mpuadxl345_register_write_byte(0x25, 20));
    // ESP_ERROR_CHECK(mpuadxl345_register_write_byte(0x26, 20));
    // ESP_ERROR_CHECK(mpuadxl345_register_write_byte(0x1D, 0x30));
    // ESP_ERROR_CHECK(mpuadxl345_register_write_byte(0x21, 0x10));
    // ESP_ERROR_CHECK(mpuadxl345_register_write_byte(0x22, 0x10));
    // ESP_ERROR_CHECK(mpuadxl345_register_write_byte(0x23, 0x40));

    /* Read the MPU9250 WHO_AM_I register, on power up the register should have the value 0x71 */
    int err = mpuadxl345_register_read(ADXL345_REG_DEVID, data+read_cnt, 1); read_cnt++;
    // ESP_ERROR_CHECK(err)
    ESP_LOGI(TAG, "err = 0x%X", err);
    ESP_LOGI(TAG, "ADXL345_REG_DEVID = %X", data[0]);

    #define start_reg_addr 0x1d
    int16_t data_x = 0;
    int16_t data_y = 0;
    int16_t data_z = 0;
    double angel_x = 0.0;
    double angel_y = 0.0;
    double angel_z = 0.0;

    int16_t data_x_init = 0;
    int16_t data_y_init = 0;
    int16_t data_z_init = 0;

    int16_t data_x_dis = 0;
    int16_t data_y_dis = 0;
    int16_t data_z_dis = 0;

    int move_event = 0;

    #define adxl_dis_limit_max 20

    int last_move_runtime = 0;

    time_t last_attitude_check_time = 0;
    #define attitude_check_time_time    60

    while(1)
    {
        runtimes++;

        for(uint8_t cur_reg_addr=start_reg_addr, i=read_cnt; cur_reg_addr<0x39; cur_reg_addr++,i++)
        {
            ESP_ERROR_CHECK(mpuadxl345_register_read(cur_reg_addr, data+i, 1));
        }
        data_x = data[22]+(data[23]<<8);
        data_y = data[24]+(data[25]<<8);
        data_z = data[26]+(data[27]<<8);

        my_adxl345_info.data_x = data_x;
        my_adxl345_info.data_y = data_y;
        my_adxl345_info.data_z = data_z;

        if(runtimes==10)
        {
            data_x_init = data_x;
            data_y_init = data_y;
            data_z_init = data_z;
        }

        if(runtimes>10)
        {
            data_x_dis = abs(data_x - data_x_init);
            data_y_dis = abs(data_y - data_y_init);
            data_z_dis = abs(data_z - data_z_init);

            // ESP_LOGI("app_main", "data_x_dis=%d, data_y_dis=%d, data_z_dis=%d", data_x_dis, data_y_dis, data_z_dis);

            if(data_x_dis >adxl_dis_limit_max)
            {
                // printf("\033[31m");
                // printf("move event x");
                // printf("\033[0m");
                last_move_runtime = runtimes;
                data_x_init = data_x;
                move_event = 1;
            }
            if(data_y_dis >adxl_dis_limit_max)
            {
                // printf("\033[31m");
                // printf("move event y");
                // printf("\033[0m");
                last_move_runtime = runtimes;
                data_y_init = data_y;
                move_event = 1;
            }
            if(data_z_dis >adxl_dis_limit_max)
            {
                // printf("\033[31m");
                // printf("move event z");
                // printf("\033[0m");
                last_move_runtime = runtimes;
                data_z_init = data_z;
                move_event = 1;
            }

            if(move_event==1)
            {
                // MyLed_Use_Once(ledmode_flash, 5);
                move_event = 0;
                my_adxl345_info.move_event = 1;
                my_adxl345_info.unread_mark = 1;
            }

            if(last_move_runtime-runtimes>=100)
            {
                data_x_init = data_x;
                data_y_init = data_y;
                data_z_init = data_z;
            }
        }
        
        // printf("data=");
        // for(int i=0; i<sizeof(data); i++)
        // {
        //     if(i==22||i==23)
        //     {
        //         printf("\033[31m");
        //         printf("%02X ", *(data+i));
        //         printf("\033[0m");
        //     }
        //     else if(i==24||i==25)
        //     {
        //         printf("\033[32m");
        //         printf("%02X ", *(data+i));
        //         printf("\033[0m");
        //     }
        //     else if(i==26||i==27)
        //     {
        //         printf("\033[33m");
        //         printf("%02X ", *(data+i));
        //         printf("\033[0m");
        //     }
        //     else
        //     {
        //         printf("%02X ", *(data+i));
        //     }
        // }
        // printf(" -- ");
        // printf("\033[31m");
        angel_x = calcu_angel_x(data_x, data_y, data_z);
        // printf("%d(%.2f) ", data_x, angel_x);
        // printf("\033[32m");
        angel_y = calcu_angel_y(data_x, data_y, data_z);
        // printf("%d(%.2f) ", data_y, angel_y);
        // printf("\033[33m");
        angel_z = calcu_angel_z(data_x, data_y, data_z);
        // printf("%d(%.2f) ", data_z, angel_z);
        // printf("\033[0m");

        my_adxl345_info.angel_x = angel_x;
        my_adxl345_info.angel_y = angel_y;
        my_adxl345_info.angel_z = angel_z;
        
        // printf("\n");

        {
            int angel_x_int=0, angel_y_int=0, angel_z_int=0;
            angel_x_int = my_adxl345_info.angel_x;
            angel_y_int = my_adxl345_info.angel_y;
            angel_z_int = my_adxl345_info.angel_z;

            // printf("\n");
            // printf("\033[31m");
            // printf("angel_x = %d(%.2f) ", angel_x_int, my_adxl345_info.angel_x);
            // printf("\033[32m");
            // printf("angel_y = %d(%.2f) ", angel_y_int, my_adxl345_info.angel_y);
            // printf("\033[33m");
            // printf("angel_z = %d(%.2f) ", angel_z_int, my_adxl345_info.angel_z);
            // printf("\033[30m");
            // printf("\n");

            uint8_t dev_attitude_err = 0;

            if(angel_z<0)
            {
                if(!((angel_x>0?angel_x<=20:angel_x>=-20)&&(angel_y>=60))||angel_z>-60)
                dev_attitude_err = 1;
            }
            if(angel_z>0)
            {
                if(!((angel_x>0?angel_x<=20:angel_x>=-20)&&(angel_y>=80))||angel_z<80)
                dev_attitude_err = 1;
            }
            

            
            time_t cur_time = MyTime_GetTime();
            if(cur_time - last_attitude_check_time >= attitude_check_time_time)
            {
                last_attitude_check_time = cur_time;
                if(dev_attitude_err)
                {
                    my_adxl345_info.dev_attitude_err = 1;
                    my_adxl345_info.unread_mark = 1;
                }
            }

            // if(dev_attitude_err)
            // {
            //     MyLed_SetMode(ledmode_fade);
            // }
            // else
            // {
            //     MyLed_SetMode(ledmode_on);
            // }
            
        }

        vTaskDelay(500/portTICK_PERIOD_MS);
    }

    ESP_ERROR_CHECK(i2c_driver_delete(I2C_MASTER_NUM));
}

void My_ADXL345_Init(void)
{
    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    p_task_stack = calloc(1, 4096*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
			xTaskCreateStaticPinnedToCore(&MyAdxl345_Task, "MyAdxl345_Task", 4096, NULL, 8, p_task_stack, p_task_data, 0);
        }
    }
}


#endif