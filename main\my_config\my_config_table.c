#include "my_config_table.h"

#include "my_dev_paramid.h"
#include "my_config.h"
#include "my_workmode.h"
#include "sensor.h"
#include "my_camera.h"
#include "my_iot_box.h"





#define MY_CONFIG_LIST_NVS_FORM_NAME    "MCLVFN_1"

#define myconftable_devPI_asl_appId_default   appid_EVG
#define myconftable_devPI_asl_loginFlag_default   1
#define myconftable_devPI_asl_dic_default   0x00000000
#define myconftable_devPI_asl_token_default   0x00000000
#define myconftable_devPI_asl_last_sync_param_timestamp_default 0
#define myconftable_devPI_asl_sync_param_effect_time_default    (24*3600)
my_config_table_t my_conf_table_asl_default[6]=
{
    {
        .item_id = devPI_asl_appId,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "ASL_APPID",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconftable_devPI_asl_appId_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 2
    },
    {
        .item_id = devPI_asl_loginFlag,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "ASL_LOGINFLAG",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconftable_devPI_asl_loginFlag_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_asl_dic,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "ASL_DIC",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconftable_devPI_asl_dic_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_asl_token,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "ASL_TOKEN",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconftable_devPI_asl_token_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_asl_last_sync_param_timestamp,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "ASL_LPST",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconftable_devPI_asl_last_sync_param_timestamp_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_asl_sync_param_effect_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "ASL_PSET",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconftable_devPI_asl_sync_param_effect_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
};

#define myconftable_devPI_tsfc_frame_max_size_send_default   (1280+48)
#define myconftable_devPI_tsfc_frame_max_size_rcv_default   4096
#define myconftable_devPI_tsfc_sendFail_max_repeat_default   3

#define TSFC_AESKEY_SIZE    16
uint8_t myconf_devPI_tsfc_aesKey_default[TSFC_AESKEY_SIZE]=
{
    0x35, 0x63, 0x33, 0x63,
    0x32, 0x33, 0x37, 0x39,
    0x63, 0x37, 0x65, 0x64,
    0x31, 0x63, 0x32, 0x65
};
my_config_table_t my_conf_table_tsfc_default[4]=
{
    {
        .item_id = devPI_tsfc_frame_max_size_send,
        .item_name = NULL,

        .saving_to_nvs = not_save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "TSFC_FMS_SEND",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconftable_devPI_tsfc_frame_max_size_send_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_tsfc_frame_max_size_rcv,
        .item_name = NULL,

        .saving_to_nvs = not_save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "TSFC_FMS_RCV",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconftable_devPI_tsfc_frame_max_size_rcv_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_tsfc_sendFail_max_repeat,
        .item_name = NULL,

        .saving_to_nvs = not_save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "TSFC_MAXREP",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconftable_devPI_tsfc_sendFail_max_repeat_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_tsfc_aesKey,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "TSFC_AESKEY",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = myconf_devPI_tsfc_aesKey_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = sizeof(myconf_devPI_tsfc_aesKey_default),
    },
};

// char myconf_devPI_net_server_ipv4_address_default[64]=
// {
//     "test21.sunshine66.cn"
// };
// char myconf_devPI_net_server_ipv4_address_default[64]=
// {
//     "*************"
// };
char myconf_devPI_net_server_ipv4_address_default[64]=
{
    "iotserver.nengliba.com"
};
#define myconf_devPI_net_server_ipv4_address_type_string    0
#define myconf_devPI_net_server_ipv4_address_type_int   1
#define myconf_devPI_net_server_ipv4_address_type_default   myconf_devPI_net_server_ipv4_address_type_string

#define myconf_devPI_devPI_net_server_port_default  30509
uint8_t myconf_devPI_net_server_ota_ipv4_address_default[4]=
{
    192, 168, 1, 2,
};
#define myconf_devPI_net_server_ota_port_default  5678
my_config_table_t my_conf_table_net_default[5]=
{
    {
        .item_id = devPI_net_server_ipv4_address,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "NET_IPV4_ADR",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = myconf_devPI_net_server_ipv4_address_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = sizeof(myconf_devPI_net_server_ipv4_address_default)
    },
    {
        .item_id = devPI_net_server_ipv4_address_type,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "NET_IPV4_T",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_net_server_ipv4_address_type_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_net_server_port,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "NET_PORT",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_devPI_net_server_port_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_net_server_ota_ipv4_address,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "NET_IPV4_ADR_O",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 4,
        .p_data = myconf_devPI_net_server_ota_ipv4_address_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = sizeof(myconf_devPI_net_server_ota_ipv4_address_default)
    },
    {
        .item_id = devPI_net_server_ota_port,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "NET_PORT_O",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_net_server_ota_port_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
};

// workplan_list_item_t myconf_devPI_workmode_worktimeList_default[5]=
// {
//     {12, 34},
//     {23, 45},
//     {34, 56},
//     {45, 67},
//     {56, 78},
// };

#define myconf_devPI_workmode_curdevstate_default 0
#define myconf_devPI_workmode_curMode_default workmode1_realtime
#define myconf_devPI_workmode_debugmode_default 0

#define myconf_devPI_workmode_worktimeList_1_start_time_default (0)
#define myconf_devPI_workmode_worktimeList_1_end_time_default (0)
#define myconf_devPI_workmode_worktimeList_2_start_time_default (0)
#define myconf_devPI_workmode_worktimeList_2_end_time_default (0)
#define myconf_devPI_workmode_worktimeList_3_start_time_default 0
#define myconf_devPI_workmode_worktimeList_3_end_time_default 0
#define myconf_devPI_workmode_worktimeList_4_start_time_default (0)
#define myconf_devPI_workmode_worktimeList_4_end_time_default (0)
#define myconf_devPI_workmode_worktimeList_5_start_time_default 0
#define myconf_devPI_workmode_worktimeList_5_end_time_default 0

#define myconf_devPI_workmode_vrc1_start_time_default (7*3600+30*60)
#define myconf_devPI_workmode_vrc1_keep_time_default (22*3600)
#define myconf_devPI_workmode_vrc2_start_time_default (0)
#define myconf_devPI_workmode_vrc2_keep_time_default (0)
#define myconf_devPI_workmode_vrc3_start_time_default (0)
#define myconf_devPI_workmode_vrc3_keep_time_default (0)
#define myconf_devPI_workmode_vrc4_start_time_default (0)
#define myconf_devPI_workmode_vrc4_keep_time_default (0)
#define myconf_devPI_workmode_vrc5_start_time_default (0)
#define myconf_devPI_workmode_vrc5_keep_time_default (0)

#define myconf_devPI_workmode_timermodeSleepTime_default (5*60)
#define myconf_devPI_workmode_imageReportInterval_default (5*60)
#define myconf_devPI_workmode_measReportInterval_default (2*3600)
#define myconf_devPI_workmode_measReportmode_default 0
#define myconf_devPI_workmode_reley1_mode_default 2
#define myconf_devPI_workmode_reley1_Xmode_keep_time_default 1
#define myconf_devPI_workmode_channel_door_mode_default 1
my_config_table_t my_conf_table_workmode_default[29]=
{
    {
        .item_id = devPI_workmode_curMode,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_C_WMD",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_curMode_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_workmode_worktimeList_1_start_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_TP1_ST",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_worktimeList_1_start_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_worktimeList_1_keep_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_TP1_KT",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_worktimeList_1_end_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_worktimeList_2_start_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_TP2_ST",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_worktimeList_2_start_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_worktimeList_2_keep_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_TP2_KT",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_worktimeList_2_end_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_worktimeList_3_start_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_TP3_ST",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_worktimeList_3_start_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_worktimeList_3_keep_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_TP3_KT",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_worktimeList_3_end_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_worktimeList_4_start_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_TP4_ST",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_worktimeList_4_start_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_worktimeList_4_keep_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_TP4_KT",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_worktimeList_4_end_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_worktimeList_5_start_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_TP5_ST",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_worktimeList_5_start_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_worktimeList_5_keep_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_TP5_KT",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_worktimeList_5_end_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_timermodeSleepTime,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_TMR_S_T",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_timermodeSleepTime_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_imageReportInterval,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_IMG_T",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_imageReportInterval_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_measReportInterval,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_MRP_T",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_measReportInterval_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_measReportmode,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_MRP_M",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_measReportmode_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 2
    },

    {
        .item_id = devPI_workmode_vrc1_start_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_VRC1_ST",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_vrc1_start_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_vrc1_keep_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_VRC1_KT",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_vrc1_keep_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_vrc2_start_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_VRC2_ST",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_vrc2_start_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_vrc2_keep_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_VRC2_KT",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_vrc2_keep_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_vrc3_start_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_VRC3_ST",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_vrc3_start_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_vrc3_keep_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_VRC3_KT",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_vrc3_keep_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_vrc4_start_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_VRC4_ST",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_vrc4_start_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_vrc4_keep_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_VRC4_KT",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_vrc4_keep_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_vrc5_start_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_VRC5_ST",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_vrc5_start_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_vrc5_keep_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_VRC5_KT",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_vrc5_keep_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_debugmode,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_DB_MD",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_debugmode_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_workmode_relay1_mode,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_R1M",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_reley1_mode_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_workmode_relay1_Xmode_keep_time,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_RXMKT",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_reley1_Xmode_keep_time_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_workmode_channeldoor_mode,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WMD_CNDMD",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_workmode_channel_door_mode_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
};

// #if MY_BLE_FUNC
#define myconf_devPI_ble_enable_default 1
const char myconf_devPI_ble_name_default[MY_BLE_NAME_LEN_MAX]="SWQZ";
#define myconf_devPI_ble_adv_type_default   my_ble_adv_type_custom
uint8_t myconf_devPI_ble_advData_default[MY_BLE_ADV_CUSTOM_DATA_LEN_MAX]={0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC, 0xDD, 0xEE, 0xFF};
#define myconf_devPI_ble_service_uuid_a_default 0x1122
#define myconf_devPI_ble_char_uuid_a_default    0x3344
#define myconf_devPI_ble_service_uuid_b_default 0x5566
#define myconf_devPI_ble_char_uuid_b_default    0x7788
const char myconf_devPI_ble_mac_default[32]="012345678912345";
my_config_table_t my_conf_table_ble_default[11]=
{
    {
        .item_id = devPI_ble_enable_state,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "BLE_EN_S",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_ble_enable_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_ble_cur_state,
        .item_name = NULL,

        .saving_to_nvs = not_save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "BLE_C_S",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_ble_enable_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_ble_name,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "BLE_NAME",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = myconf_devPI_ble_name_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = MY_BLE_NAME_LEN_MAX
    },
    {
        .item_id = devPI_ble_advType,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "BLE_ADV_TYPE",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_ble_adv_type_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_ble_advData,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "BLE_ADV_DATA",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = myconf_devPI_ble_advData_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = MY_BLE_ADV_CUSTOM_DATA_LEN_MAX
    },
    {
        .item_id = devPI_ble_advData_len,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "BLE_ADV_D_LEN",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = sizeof(myconf_devPI_ble_advData_default),
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_ble_service_uuid_a,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "BLE_SUUIDA",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_ble_service_uuid_a_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = sizeof(myconf_devPI_ble_service_uuid_a_default)
    },
    {
        .item_id = devPI_ble_char_uuid_a,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "BLE_CUUIDA",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_ble_char_uuid_a_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = sizeof(myconf_devPI_ble_char_uuid_a_default)
    },
    {
        .item_id = devPI_ble_service_uuid_b,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "BLE_SUUIDB",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_ble_service_uuid_b_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = sizeof(myconf_devPI_ble_service_uuid_b_default)
    },
    {
        .item_id = devPI_ble_char_uuid_b,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "BLE_CUUIDB",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_ble_char_uuid_b_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = sizeof(myconf_devPI_ble_char_uuid_b_default)
    },
    {
        .item_id = devPI_ble_mac,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "BLE_MAC",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = myconf_devPI_ble_mac_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 32
    },
};
// #endif

#define devPI_power_poweroff_alarm_enable_state_default     1
#define devPI_power_shutdown_after_poweroffAlarm_default    1
my_config_table_t my_conf_table_power_default[3]=
{
    {
        .item_id = devPI_power_state,
        .item_name = NULL,

        .saving_to_nvs = not_save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "PWR_STATE",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = 1,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_power_poweroff_alarm_enable_state,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "PWR_OFF_ALM_S",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = devPI_power_poweroff_alarm_enable_state_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_power_shutdown_after_poweroffAlarm,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "PWR_SD_AFT_ALM",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = devPI_power_shutdown_after_poweroffAlarm_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
};

#define devPI_factory_devstate_default 0
const char devPI_factory_factoryDate_default[16]="2025.1.1";
#define devPI_factory_poweron_reason_default 0
#define devPI_factory_poweronCount_default 0
#define devPI_factory_runtime_since_poweron_default 0
const char devPI_factory_mac_default[6]="012345";
const char devPI_factory_imei_default[32]="012345678912345";
const char devPI_factory_iccid_default[32]="01234567890123456789";
#define devPI_factory_ota_check_type_default 2
#define devPI_factory_ota_vercode_default MY_IOTBOX_devPI_factory_ota_vercode_default
const char devPI_factory_ota_vername_default[16]=MY_IOTBOX_devPI_factory_ota_vername_default;
#define devPI_factory_devType_default MY_IOT_BOX_DEVICE_TYPE
const char devPI_factory_devModel_default[40]="SWQZ-EVG-01";
const char devPI_factory_ota_filename_default[100]=MY_IOTBOX_devPI_factory_ota_filename_default;
const char devPI_factory_devId_default[20]="01234567890123456789";
const char devPI_factory_httpota_url_default[256]="http://www.swqziotbox.com";

my_config_table_t my_conf_table_factory_default[16]=
{
    {
        .item_id = devPI_factory_devstate,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "FCTY_DS",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = devPI_factory_devstate_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_factory_factoryDate,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "FCTY_DATE",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = devPI_factory_factoryDate_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 16
    },
    {
        .item_id = devPI_factory_poweron_reason,
        .item_name = NULL,

        .saving_to_nvs = not_save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "FCTY_PWRON_RSN",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = devPI_factory_poweron_reason_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_factory_poweronCount,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "FCTY_PWRON_CNT",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = devPI_factory_poweronCount_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_factory_runtime_since_poweron,
        .item_name = NULL,

        .saving_to_nvs = not_save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "FCTY_RT_S_PO",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = devPI_factory_runtime_since_poweron_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_factory_mac,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "FCTY_MAC",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = devPI_factory_mac_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 6
    },
    {
        .item_id = devPI_factory_imei,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "FCTY_IMEI",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = devPI_factory_imei_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 32
    },
    {
        .item_id = devPI_factory_iccid,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "FCTY_ICCID",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = devPI_factory_iccid_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 32
    },
    {
        .item_id = devPI_factory_ota_vercode,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "FCTY_SVC",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = devPI_factory_ota_vercode_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
    {
        .item_id = devPI_factory_ota_vername,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "FCTY_SVN",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = devPI_factory_ota_vername_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = sizeof(devPI_factory_ota_vername_default)
    },
    {
        .item_id = devPI_factory_ota_filename,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "FCTY_SFN",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = devPI_factory_ota_filename_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = sizeof(devPI_factory_ota_filename_default)
    },
    {
        .item_id = devPI_factory_ota_check_type,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "FCTY_OTACT",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = devPI_factory_ota_check_type_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_factory_devType,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "FCTY_DEVTYPE",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = devPI_factory_devType_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 2
    },
    {
        .item_id = devPI_factory_devModel,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "FCTY_DEVMODEL",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = devPI_factory_devModel_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 40
    },
    {
        .item_id = devPI_factory_devId,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "FCTY_DEVID",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = devPI_factory_devId_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 20
    },
    {
        .item_id = devPI_factory_httpota_url,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "FCTY_OURL",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = devPI_factory_httpota_url_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = sizeof(devPI_factory_httpota_url_default)
    },
};

#define myconf_devPI_wifi_enable_state_default 1
#define myconf_devPI_wifi_cur_state_default 0
#define myconf_devPI_wifi_mode_default 3
#define myconf_devPI_wifi_cur_mode_default 0
const char myconf_devPI_wifi_station_ssid_default[MY_WIFI_SSID_LEN_MAX]="Hello-TB";
const char myconf_devPI_wifi_station_password_default[MY_WIFI_PASSWORD_LEN_MAX]="12345678";
const char myconf_devPI_wifi_ap_ssid_default[MY_WIFI_SSID_LEN_MAX]="SWQZ-RB24-3";
const char myconf_devPI_wifi_ap_password_default[MY_WIFI_PASSWORD_LEN_MAX]="ap_password";
my_config_table_t my_conf_table_wifi_default[8]=
{
    {
        .item_id = devPI_wifi_enable_state,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WIFI_SWITCH",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_wifi_enable_state_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_wifi_cur_state,
        .item_name = NULL,

        .saving_to_nvs = not_save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WIFI_C_SW_S",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_wifi_cur_state_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_wifi_mode,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WIFI_MODE",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_wifi_mode_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_wifi_cur_mode,
        .item_name = NULL,

        .saving_to_nvs = not_save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WIFI_C_MODE",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = myconf_devPI_wifi_cur_mode_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_wifi_station_ssid,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WIFI_S_SSID",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = myconf_devPI_wifi_station_ssid_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = MY_WIFI_SSID_LEN_MAX
    },
    {
        .item_id = devPI_wifi_station_password,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WIFI_S_PASSWD",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = myconf_devPI_wifi_station_password_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = MY_WIFI_PASSWORD_LEN_MAX
    },
    {
        .item_id = devPI_wifi_ap_ssid,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WIFI_A_SSID",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = myconf_devPI_wifi_ap_ssid_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = MY_WIFI_SSID_LEN_MAX
    },
    {
        .item_id = devPI_wifi_ap_password,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "WIFI_A_PASSWD",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = myconf_devPI_wifi_ap_password_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = MY_WIFI_PASSWORD_LEN_MAX
    },
};

#define devPI_lte_baudrate_default (115200UL)
my_config_table_t my_conf_table_lte_default[1]=
{
    {
        .item_id = devPI_lte_baudrate,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "LTE_DT_BDRT",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = devPI_lte_baudrate_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 4
    },
};

const char devPI_gnss_longitude_default[32]=" ";
const char devPI_gnss_latitude_default[32]=" ";
#define devPI_gnss_auto_report_switch_default 1
#define devPI_gnss_auto_report_interval_default (30)
my_config_table_t my_conf_table_gnss_default[4]=
{
    {
        .item_id = devPI_gnss_longitude,
        .item_name = NULL,

        .saving_to_nvs = not_save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "GNSS_LON",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = devPI_gnss_longitude_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = sizeof(devPI_gnss_longitude_default)
    },
    {
        .item_id = devPI_gnss_latitude,
        .item_name = NULL,

        .saving_to_nvs = not_save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "GNSS_LAT",

        .data_type = DATA_TYPE_POINTER,
        .data_uint64 = 0,
        .p_data = devPI_gnss_latitude_default,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = sizeof(devPI_gnss_latitude_default)
    },
    {
        .item_id = devPI_gnss_auto_report_switch,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "GNSS_ARS",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = devPI_gnss_auto_report_switch_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
    {
        .item_id = devPI_gnss_auto_report_interval,
        .item_name = NULL,

        .saving_to_nvs = save_to_nvs,
        .form_name = MY_CONFIG_LIST_NVS_FORM_NAME,
        .key_name = "GNSS_ARI",

        .data_type = DATA_TYPE_BASIC,
        .data_uint64 = devPI_gnss_auto_report_interval_default,
        .p_data = NULL,
        .p_data_type = DATA_POINTER_TYPE_ENTITY,
        .data_len = 1
    },
};