#include "my_config.h"
#include "led.h"
#include "my_gpio.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/timers.h"

#include "driver/gpio.h"


#if LED_FUNC

TimerHandle_t   LedTimer_Handle;

xTaskHandle LedFlashTask_Handle = NULL;

static uint16_t ledflash_remain = 0;//led剩余需要闪烁的次数（实际上是剩余需要取反的次数）
static uint16_t ledflash_keep = 0;

//static uint32_t led_flash_period = 200;//最小200
//static uint32_t led_flash_empty_time = 3000;
//static uint32_t led_flash_time = 0xffffffff;
//LED灯

#define GPIO_LED_TEST        33
#define GPIO_LED        2
#define GPIO_OUTPUT_PIN_SEL  ( (1ULL<<GPIO_LED) )

#define LED_ON  1
#define LED_OFF 0
static uint8_t led_state = LED_OFF;

void LedOn(void)
{
    //MyGPIO_SET(GPIO_LED_TEST, LED_ON_VOTAGE_TEST);
	MyGPIO_SET(GPIO_LED, LED_ON_VOTAGE);
    led_state = LED_ON;
}

void LedOff(void)
{
    //MyGPIO_SET(GPIO_LED_TEST, LED_OFF_VOTAGE_TEST);
	MyGPIO_SET(GPIO_LED, LED_OFF_VOTAGE);
    led_state = LED_OFF;
}

void LedRev(void)
{
    if(led_state==LED_ON)
    {
        LedOff();
    }
    else
    {
        LedOn();
    }
    
}

//static uint16_t led_flash_finish = 0;
void LedTimer(xTimerHandle xTimer)
{
	if(ledflash_keep)
	{
		LedRev();
	}
	else
	{
		if(ledflash_remain>0)
		{
			LedRev();
			ledflash_remain--;
		}
		else
		{
			LedOff();
			xTimerStop(LedTimer_Handle,portMAX_DELAY);
			//led_flash_finish = 1;
		}
	}
}

uint8_t get_light_state(void)
{
	return led_state;
}

void led_open(void)
{
	if(ledflash_keep)
	{
		ledflash_keep = 0;
		//led_flash_finish = 0;
		//led_flash_time = 0xffffffff;
		xTimerStop(LedTimer_Handle,portMAX_DELAY);
	}

	LedOn();
}

void led_close(void)
{
	if(ledflash_keep)
	{
		ledflash_keep = 0;
		//led_flash_finish = 1;
		//led_flash_time = 0xffffffff;
		xTimerStop(LedTimer_Handle,portMAX_DELAY);
	}

	LedOff();
}

void LedFastFlash(uint16_t period, uint16_t usetime)
{
	LedOn();
	if(usetime)
	{
		ledflash_keep = 0;
		ledflash_remain = usetime*2-1;
	}
	else
	{
		ledflash_keep = 1;
		ledflash_remain = 0;
	}

	
	if(period>=portTICK_PERIOD_MS)
		xTimerChangePeriod(LedTimer_Handle, period/portTICK_PERIOD_MS, portMAX_DELAY);
	else
	{
		xTimerChangePeriod(LedTimer_Handle, portTICK_PERIOD_MS/portTICK_PERIOD_MS, portMAX_DELAY);
	}
	//led_flash_finish = 0;
	xTimerStart(LedTimer_Handle,portMAX_DELAY);
}

/* void LedFlashTask(void* param)
{
	for(;;)
	{
		if(led_flash_time!=0xffffffff)
		{
			if(led_flash_time!=0)
			{
				LedFastFlash(led_flash_period, led_flash_time);
				while(!led_flash_finish)
				{
					vTaskDelay(100 / portTICK_PERIOD_MS);
				}
			}
		}
		vTaskDelay(led_flash_empty_time / portTICK_PERIOD_MS);
		vTaskDelay(100 / portTICK_PERIOD_MS);
	}
}

void LedFlashService(uint32_t times)
{
	led_close();
	led_flash_time = times;
} */

static void GpioInit(void)
{
    gpio_config_t io_conf;
    //disable interrupt
    io_conf.intr_type = GPIO_PIN_INTR_DISABLE;
    //set as output mode
    io_conf.mode = GPIO_MODE_OUTPUT;
    //bit mask of the pins that you want to set,e.g.GPIO18/19
    io_conf.pin_bit_mask = GPIO_OUTPUT_PIN_SEL;
    //disable pull-down mode
    io_conf.pull_down_en = 0;
    //disable pull-up mode
    io_conf.pull_up_en = 0;
    //configure GPIO with the given settings
    gpio_config(&io_conf);

    LedOff();
}

void LedInit(void)
{
    GpioInit();
	//xTaskCreate(&LedFlashTask, "LedFlash_task", 4096, NULL, 6, &LedFlashTask_Handle);
    LedTimer_Handle = xTimerCreate("LedTimer", 100/portTICK_PERIOD_MS, pdTRUE, (void*)1, LedTimer);
	xTimerStop(LedTimer_Handle,portMAX_DELAY);
}

#endif
