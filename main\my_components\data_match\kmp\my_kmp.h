#ifndef MY_KMP_H
#define MY_KMP_H

#include <lwip/netdb.h>


/**
 * @brief 数据匹配KMP算法
 * @param strings 数据指针
 * @param strings_len
 * @param object_str 目标子串
 * @param object_len
 * @return 匹配到的串起始地址相对于strings的偏移，未匹配到返回0xffff
 */
int MatchString(char const* const strings, int strings_len, char const* const object, int object_len);

/**
 * @brief 数据匹配 类strstr算法
 * @param pucSrc 数据指针
 * @param nsrcSize
 * @param pucFind 目标子串
 * @param nFindSize
 * @return 第一个匹配串的后一个字节地址，未匹配到返回NULL
 */
char* FindBinary(const char* pucSrc, int nsrcSize, const char* pucFind, int nFindSize);

#endif
