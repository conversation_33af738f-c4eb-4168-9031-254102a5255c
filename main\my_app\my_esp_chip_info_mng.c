#include "my_esp_chip_info_mng.h"

#if MY_ESP_CHIP_INFO_MNG_FUNC

#include "my_time.h"
#include "my_lowpower.h"
#include "my_nvs.h"
//#include "esp_system.h"
#include "esp_spi_flash.h"
//#include "soc/rtc_cntl_reg.h"
#include "my_debug.h"

#include "esp_attr.h"
#include "esp_sleep.h"

#include "esp_log.h"

#include "my_power_mng.h"


#if defined MY_DEBUG_PRINT_FUNC
#define DEBUG_PRINT_LEVEL_my_esp_chip_info_mng_PRINT   MY_DEBUG_PRINT_LEVEL_0
#else
#define DEBUG_PRINT_LEVEL_my_esp_chip_info_mng_PRINT   DEBUG_PRINT_LEVEL_0
#endif


my_esp_chip_info_mng_t my_esp_chip_info_mng_info;


void MyEspChipInfoMng_Refresh_mem_info(void)
{
	my_esp_chip_info_mng_info.total_free_heap_size = esp_get_free_heap_size();
	my_esp_chip_info_mng_info.internal_free_heap_size = esp_get_free_internal_heap_size();
	my_esp_chip_info_mng_info.external_free_heap_size = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
}

TaskHandle_t MyEspChipInfoMng_TaskHandle = NULL;
void MyEspChipInfoMng_Task(void* param)
{
    for(;;)
    {
		vTaskDelay(EspChipInfoMng_Task_refresh_time/portTICK_PERIOD_MS);

		if(++my_esp_chip_info_mng_info.monitor_mem_period_count>=my_esp_chip_info_mng_info.monitor_mem_period)
		{
			my_esp_chip_info_mng_info.monitor_mem_period_count = 0;
			MyEspChipInfoMng_Refresh_mem_info();
			if(my_esp_chip_info_mng_info.internal_free_heap_size<my_esp_chip_info_mng_info.history_min_iram_size)
			{
				my_esp_chip_info_mng_info.history_min_iram_size=my_esp_chip_info_mng_info.internal_free_heap_size;
			}
			if(my_esp_chip_info_mng_info.external_free_heap_size<my_esp_chip_info_mng_info.history_min_eram_size)
			{
				my_esp_chip_info_mng_info.history_min_eram_size=my_esp_chip_info_mng_info.external_free_heap_size;
			}
			#if MY_ESP_CHIP_INFO_MNG_FUNC_DEBUG
			// ESP_LOGI("MyEspChipInfoMng_Task", "total_free_heap_size: %d.%dKB", my_esp_chip_info_mng_info.total_free_heap_size/1024, my_esp_chip_info_mng_info.total_free_heap_size%1024);
			// ESP_LOGI("MyEspChipInfoMng_Task", "internal_free_heap_size: %d.%dKB", my_esp_chip_info_mng_info.internal_free_heap_size/1024, my_esp_chip_info_mng_info.internal_free_heap_size%1024);
			// ESP_LOGI("MyEspChipInfoMng_Task", "external_free_heap_size: %d.%dKB", my_esp_chip_info_mng_info.external_free_heap_size/1024, my_esp_chip_info_mng_info.external_free_heap_size%1024);
			#endif
		}
        //my_esp_chip_info_mng_info.runtime_since_reset = xTaskGetTickCount()/configTICK_RATE_HZ;
		
		MyTime_Refresh();
    }
}

void MyEspChipInfoMng_Init(void)
{
	BaseType_t ret = pdPASS;
	//MyEspChipInfoMngInfo_Init();

    esp_chip_info( &my_esp_chip_info_mng_info.chip_info );

	my_esp_chip_info_mng_info.spi_flash_size = spi_flash_get_chip_size();
	printf("========================================================\r\n");
	printf("features=0x%02x\n", my_esp_chip_info_mng_info.chip_info.features);
	printf("cores=%d\n", my_esp_chip_info_mng_info.chip_info.cores);
	printf("revision=%d\n", my_esp_chip_info_mng_info.chip_info.revision);

	printf("\n----chip_info.features----\n");
	printf("Chip has embedded flash memory:%s, size:%dKB\n", my_esp_chip_info_mng_info.chip_info.features&CHIP_FEATURE_EMB_FLASH?"yes":"no", my_esp_chip_info_mng_info.spi_flash_size/1024);
	printf("Chip has 2.4GHz WiFi:%s\n", my_esp_chip_info_mng_info.chip_info.features&CHIP_FEATURE_WIFI_BGN?"yes":"no");
	printf("Chip has Bluetooth LE:%s\n", my_esp_chip_info_mng_info.chip_info.features&CHIP_FEATURE_BLE?"yes":"no");
	printf("Chip has Bluetooth Classic:%s\n", my_esp_chip_info_mng_info.chip_info.features&CHIP_FEATURE_BT?"yes":"no");

	printf("\n----memorys----\n");
	printf("esp_get_free_heap_size: %d.%dKB\n", esp_get_free_heap_size()/1024, esp_get_free_heap_size()%1024);
	printf("heap_caps_get_free_size(MALLOC_CAP_SPIRAM): %d.%dKB\n", heap_caps_get_free_size(MALLOC_CAP_SPIRAM)/1024, heap_caps_get_free_size(MALLOC_CAP_SPIRAM)%1024);
	printf("esp_get_minimum_free_heap_size: %d.%dKB\n", esp_get_minimum_free_heap_size()/1024, esp_get_minimum_free_heap_size()%1024);

	my_esp_chip_info_mng_info.system_reset_reason = esp_reset_reason();
	printf("\n----Get reason of last reset----\n");
	
	memset(my_esp_chip_info_mng_info.system_reset_reason_str, 0, sizeof(my_esp_chip_info_mng_info.system_reset_reason_str));
	switch (my_esp_chip_info_mng_info.system_reset_reason)
	{
		case (ESP_RST_UNKNOWN):
			sprintf(my_esp_chip_info_mng_info.system_reset_reason_str, "ESP_RST_UNKNOWN");
		break;
		case (ESP_RST_POWERON):
			sprintf(my_esp_chip_info_mng_info.system_reset_reason_str, "ESP_RST_POWERON");
		break;
		case (ESP_RST_EXT):
			sprintf(my_esp_chip_info_mng_info.system_reset_reason_str, "ESP_RST_EXT");
		break;
		case (ESP_RST_SW):
			sprintf(my_esp_chip_info_mng_info.system_reset_reason_str, "ESP_RST_SW");
		break;
		case (ESP_RST_PANIC):
			sprintf(my_esp_chip_info_mng_info.system_reset_reason_str, "ESP_RST_PANIC");
		break;
		case (ESP_RST_INT_WDT):
			sprintf(my_esp_chip_info_mng_info.system_reset_reason_str, "ESP_RST_INT_WDT");
		break;
		case (ESP_RST_TASK_WDT):
			sprintf(my_esp_chip_info_mng_info.system_reset_reason_str, "ESP_RST_TASK_WDT");
		break;
		case (ESP_RST_WDT):
			sprintf(my_esp_chip_info_mng_info.system_reset_reason_str, "ESP_RST_WDT");
		break;
		case (ESP_RST_DEEPSLEEP):
			sprintf(my_esp_chip_info_mng_info.system_reset_reason_str, "ESP_RST_DEEPSLEEP");
		break;
		case (ESP_RST_BROWNOUT):
			sprintf(my_esp_chip_info_mng_info.system_reset_reason_str, "ESP_RST_BROWNOUT");
		break;
		case (ESP_RST_SDIO):
			sprintf(my_esp_chip_info_mng_info.system_reset_reason_str, "ESP_RST_SDIO");
		break;
		default:
			sprintf(my_esp_chip_info_mng_info.system_reset_reason_str, "ESP_RST=%d", my_esp_chip_info_mng_info.system_reset_reason);
		break;
	}
	printf("system_reset_reason: %s\n", my_esp_chip_info_mng_info.system_reset_reason_str);
	vTaskDelay(500 / portTICK_PERIOD_MS);

	printf("\n----Read base MAC address and set MAC address of the interface.----\n");
	esp_read_mac(my_esp_chip_info_mng_info.chip_mac_int, 0);
	for(int i=0; i<6; i++)
	{
		sprintf(&my_esp_chip_info_mng_info.chip_mac_str[i*2], "%02X", my_esp_chip_info_mng_info.chip_mac_int[i]);
	}
	my_esp_chip_info_mng_info.chip_mac_str[sizeof(my_esp_chip_info_mng_info.chip_mac_str)] = 0;
	printf("chip_mac:%s\n", my_esp_chip_info_mng_info.chip_mac_str);
	printf("\n========================================================\r\n");

// 	//-----------------
// 	typedef enum {
//     ESP_RST_UNKNOWN,    //!< Reset reason can not be determined
//     ESP_RST_POWERON,    //!< Reset due to power-on event
//     ESP_RST_EXT,        //!< Reset by external pin (not applicable for ESP32)
//     ESP_RST_SW,         //!< Software reset via esp_restart
//     ESP_RST_PANIC,      //!< Software reset due to exception/panic
//     ESP_RST_INT_WDT,    //!< Reset (software or hardware) due to interrupt watchdog
//     ESP_RST_TASK_WDT,   //!< Reset due to task watchdog
//     ESP_RST_WDT,        //!< Reset due to other watchdogs
//     ESP_RST_DEEPSLEEP,  //!< Reset after exiting deep sleep mode
//     ESP_RST_BROWNOUT,   //!< Brownout reset (software or hardware)
//     ESP_RST_SDIO,       //!< Reset over SDIO
// } esp_reset_reason_t;
	if( my_esp_chip_info_mng_info.system_reset_reason==ESP_RST_EXT||\
		my_esp_chip_info_mng_info.system_reset_reason==ESP_RST_SW||\
		my_esp_chip_info_mng_info.system_reset_reason==ESP_RST_PANIC||\
		my_esp_chip_info_mng_info.system_reset_reason==ESP_RST_INT_WDT||\
		my_esp_chip_info_mng_info.system_reset_reason==ESP_RST_TASK_WDT||\
		my_esp_chip_info_mng_info.system_reset_reason==ESP_RST_WDT||\
		my_esp_chip_info_mng_info.system_reset_reason==ESP_RST_DEEPSLEEP||\
		my_esp_chip_info_mng_info.system_reset_reason==ESP_RST_BROWNOUT||\
		my_esp_chip_info_mng_info.system_reset_reason==ESP_RST_SDIO)
	{
		MyTime_Init(0, 1);
	}
	else
	{
		MyTime_Init(1, 0);
	}

	if(my_esp_chip_info_mng_info.system_reset_reason!=ESP_RST_POWERON)
	{
		MyLowPower_Info_Init();
	}

	my_esp_chip_info_mng_info.monitor_mem_period = MONITOR_MEM_PERIOD_DEFAULT;
	MyEspChipInfoMng_Refresh_mem_info();
	my_esp_chip_info_mng_info.history_min_iram_size = my_esp_chip_info_mng_info.internal_free_heap_size;
	my_esp_chip_info_mng_info.history_min_eram_size = my_esp_chip_info_mng_info.external_free_heap_size;
	//---------------
	
	#if MY_ESP_CHIP_INFO_MNG_FUNC_DEBUG
	#if DEBUG_PRINT_LEVEL_my_esp_chip_info_mng_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
	MyDebug_PrintMemInfo("debug-MyEspChipInfoMng_Task");
	#endif
	#endif
	#if MyEspChipInfoMng_TASK_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    p_task_stack = calloc(1, MyEspChipInfoMng_Task_task_stack_size*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
			if(xTaskCreateStaticPinnedToCore(MyEspChipInfoMng_Task, "MyEspChipInfoMng_Task", MyEspChipInfoMng_Task_task_stack_size, NULL, MyEspChipInfoMng_Task_priority, p_task_stack, p_task_data, MyEspChipInfoMng_Task_COREID)!=p_task_data)
			{
				ESP_LOGE("MyEspChipInfoMng_Init()", "creat MyEspChipInfoMng_Task use xTaskCreateStaticPinnedToCore() error\n");
			}
        }
    }
	#elif MyEspChipInfoMng_TASK_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
	ret = xTaskCreatePinnedToCore(MyEspChipInfoMng_Task, "MyEspChipInfoMng_Task", MyEspChipInfoMng_Task_task_stack_size, NULL, MyEspChipInfoMng_Task_priority, &MyEspChipInfoMng_TaskHandle, MyEspChipInfoMng_Task_COREID);
	if(ret!=pdPASS)
	{
		ESP_LOGE("MyEspChipInfoMng_Init()", "creat MyEspChipInfoMng_Task use xTaskCreatePinnedToCore() error=%d\n", ret);
	}
	#endif
	#if MY_ESP_CHIP_INFO_MNG_FUNC_DEBUG
	MyDebug_PrintMemInfo("debug-MyEspChipInfoMng_Task");
	#endif
}

int MyEspChipInfoMng_GetMacStr(char* saving_mac)
{
	if(saving_mac==NULL)
	{
		return -1;
	}

	strcpy(saving_mac, my_esp_chip_info_mng_info.chip_mac_str);
	return 0;
}

int MyEspChipInfoMng_GetMacInt(char* saving_mac, int saving_len)
{
	if(saving_mac!=NULL)
	{
		if(saving_len>=sizeof(my_esp_chip_info_mng_info.chip_mac_int))
		{
			memcpy(saving_mac, my_esp_chip_info_mng_info.chip_mac_int, sizeof(my_esp_chip_info_mng_info.chip_mac_int));
		}
		else
		{
			memcpy(saving_mac, my_esp_chip_info_mng_info.chip_mac_int, saving_len);
		}
	}
	return 0;
}

uint8_t MyEspChipInfoMng_GetResetReason(void)
{
	return my_esp_chip_info_mng_info.system_reset_reason;
}

void My_esp_restart(void)
{
	#if MY_ESP_CHIP_INFO_MNG_FUNC_DEBUG
	ESP_LOGI("My_esp_restart()", "restart...");
	#endif
	//保存状态
	MyTime_SavingForReboot();

	SaveMyLowPowerConfig(0);
	//处理GPIO

	//处理CAM

	//处理LTE
	MyPowerMng_MyWHGM5_PowerOFF();

	vTaskDelay(3000 / portTICK_PERIOD_MS);

	esp_restart();
}

void My_esp_Powroff(void)
{
	#if MY_ESP_CHIP_INFO_MNG_FUNC_DEBUG
	ESP_LOGI("My_esp_restart()", "poweroff...");
	#endif

	vTaskDelay(1000 / portTICK_PERIOD_MS);
	MyPowerMng_MyWHGM5_PowerOFF();
	MyPowerMng_MobilePower_OFF();
	BAKPWR_OFF();

	vTaskDelay(3000 / portTICK_PERIOD_MS);

	esp_restart();
}

#if MY_ESP_CHIP_INFO_MNG_FUNC_DEBUG
void MyDebug_PrintMemInfo(char* name)
{
	ESP_LOGI("\n\nMyDebug_PrintMemInfo()", "---------------------------------\n%s\n\n", name);

	MyEspChipInfoMng_Refresh_mem_info();
	ESP_LOGI("MyDebug_PrintMemInfo()", "total_free_heap_size: %d.%dKB", my_esp_chip_info_mng_info.total_free_heap_size/1024, my_esp_chip_info_mng_info.total_free_heap_size%1024);
	ESP_LOGI("MyDebug_PrintMemInfo()", "internal_free_heap_size: %d.%dKB", my_esp_chip_info_mng_info.internal_free_heap_size/1024, my_esp_chip_info_mng_info.internal_free_heap_size%1024);
	ESP_LOGI("MyDebug_PrintMemInfo()", "history_min_iram_size: %d.%dKB", my_esp_chip_info_mng_info.history_min_iram_size/1024, my_esp_chip_info_mng_info.history_min_iram_size%1024);
	ESP_LOGI("MyDebug_PrintMemInfo()", "external_free_heap_size: %d.%dKB", my_esp_chip_info_mng_info.external_free_heap_size/1024, my_esp_chip_info_mng_info.external_free_heap_size%1024);
	ESP_LOGI("MyDebug_PrintMemInfo()", "history_min_eram_size: %d.%dKB", my_esp_chip_info_mng_info.history_min_eram_size/1024, my_esp_chip_info_mng_info.history_min_eram_size%1024);
	ESP_LOGI("\n\nMyDebug_PrintMemInfo()", "---------------------------------\n\n");
}
#endif

#endif