#include "tcp_client_data_analyze.h"
#include "my_tcp_client.h"
#include "my_monitor.h"
#include "simple_http_ota.h"
#include "led.h"



#if ELECTRICLOCK_FUNC
static void LockCmdAnalyse(serverdata_t const* const p)
{
    if(strstr(p->data, "LCC_UNLOCK")!=NULL)
    {
        ElectricLockService(UPPER_LAYER, UNLOCK);
        LedFastFlash(200, 1);
    }
    if(strstr(p->data, "LCC_LOCK")!=NULL)
    {
        ElectricLockService(UPPER_LAYER, LOCK);
    }
}
#endif

#if SEPCUR_FUNC
static void SepcurCmdAnalyse(serverdata_t const* const p)
{
    if(strstr(p->data, "LCC_START_EXAM")!=NULL)
    {
        SepcurService(SEPCUR_ALL_DOWN);
        LedFastFlash(200, 2);
    }
    if(strstr(p->data, "LCC_END_EXAM")!=NULL)
    {
        SepcurService(SEPCUR_ALL_UP);
        LedFastFlash(200, 3);
    }
    if(strstr(p->data, "LCC_SEPCUR1_DOWN")!=NULL)
    {
        SepcurService(SEPCUR1_DOWN);
    }
    else if(strstr(p->data, "LCC_SEPCUR1_UP")!=NULL)
    {
        SepcurService(SEPCUR1_UP);
    }
    else if(strstr(p->data, "LCC_SEPCUR1_STOP")!=NULL)
    {
        SepcurService(SEPCUR1_UP);
    }
    if(strstr(p->data, "LCC_SEPCUR2_DOWN")!=NULL)
    {
        SepcurService(SEPCUR2_DOWN);
    }
    else if(strstr(p->data, "LCC_SEPCUR2_UP")!=NULL)
    {
        SepcurService(SEPCUR2_UP);
    }
    else if(strstr(p->data, "LCC_SEPCUR2_STOP")!=NULL)
    {
        SepcurService(SEPCUR2_STOP);
    }
    if(strstr(p->data, "LCC_SEPCUR2_ALL_DOWN")!=NULL)
    {
        SepcurService(SEPCUR_ALL_DOWN);
    }
    else if(strstr(p->data, "LCC_SEPCUR2_ALL_UP")!=NULL)
    {
        SepcurService(SEPCUR_ALL_UP);
    }
    else if(strstr(p->data, "LCC_SEPCUR2_ALL_STOP")!=NULL)
    {
        SepcurService(SEPCUR_ALL_STOP);
    }
}
#endif

#if SIMPLE_HTTP_OTA_FUNC
static void OtaCmdAnalyse(serverdata_t const* const p)
{
    if(strstr(p->data, "RUN OTA")!=NULL)
    {
        // SimpleOtaInit();
    }
}

#endif
void ServerData_Analyse(serverdata_t const* const p)
{
    #if ELECTRICLOCK_FUNC
    LockCmdAnalyse(p);
    #endif

    #if SEPCUR_FUNC
    SepcurCmdAnalyse(p);
    #endif
}
