#include "myesp_sw_uart.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "driver/gpio.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/semphr.h"
#include "string.h"

static const char* TAG = "SW_UART";

// 软串口状态定义
typedef enum {
    SW_UART_STATE_IDLE = 0,
    SW_UART_STATE_START_BIT,
    SW_UART_STATE_DATA_BITS,
    SW_UART_STATE_PARITY_BIT,
    SW_UART_STATE_STOP_BITS,
} sw_uart_state_t;

// 软串口发送状态
typedef enum {
    SW_UART_TX_IDLE = 0,
    SW_UART_TX_START_BIT,
    SW_UART_TX_DATA_BITS,
    SW_UART_TX_PARITY_BIT,
    SW_UART_TX_STOP_BITS,
} sw_uart_tx_state_t;

// 软串口实例结构体
typedef struct {
    bool installed;                    // 是否已安装
    sw_uart_config_t config;          // 配置参数
    gpio_num_t tx_pin;                // TX引脚
    gpio_num_t rx_pin;                // RX引脚

    // 接收相关
    QueueHandle_t rx_queue;           // 接收队列
    uint8_t* rx_buffer;               // 接收缓冲区
    size_t rx_buffer_size;            // 接收缓冲区大小
    volatile sw_uart_state_t rx_state; // 接收状态
    volatile uint8_t rx_data;         // 当前接收的数据
    volatile uint8_t rx_bit_count;    // 接收位计数
    volatile uint64_t rx_last_edge_time; // 上次边沿时间

    // 发送相关
    SemaphoreHandle_t tx_mutex;       // 发送互斥锁
    volatile sw_uart_tx_state_t tx_state; // 发送状态
    volatile uint8_t tx_data;         // 当前发送的数据
    volatile uint8_t tx_bit_count;    // 发送位计数
    volatile bool tx_busy;            // 发送忙标志
    esp_timer_handle_t tx_timer;      // 发送定时器

    // 定时参数
    uint32_t bit_time_us;             // 每位时间(微秒)
    uint32_t half_bit_time_us;        // 半位时间(微秒)
} sw_uart_obj_t;

// 软串口实例数组
static sw_uart_obj_t sw_uart_obj[SW_UART_NUM_MAX] = {0};

// 计算校验位
static uint8_t calculate_parity(uint8_t data, uint8_t parity_type) {
    if (parity_type == SW_UART_PARITY_DISABLE) {
        return 0;
    }

    uint8_t parity = 0;
    for (int i = 0; i < 8; i++) {
        if (data & (1 << i)) {
            parity++;
        }
    }

    if (parity_type == SW_UART_PARITY_ODD) {
        return parity & 1;
    } else { // SW_UART_PARITY_EVEN
        return !(parity & 1);
    }
}

// RX引脚中断处理函数
static void IRAM_ATTR sw_uart_rx_isr_handler(void* arg) {
    sw_uart_port_t uart_num = (sw_uart_port_t)(uintptr_t)arg;
    sw_uart_obj_t* uart = &sw_uart_obj[uart_num];

    uint64_t current_time = esp_timer_get_time();
    int pin_level = gpio_get_level(uart->rx_pin);

    switch (uart->rx_state) {
        case SW_UART_STATE_IDLE:
            if (pin_level == 0) { // 检测到起始位(下降沿)
                uart->rx_state = SW_UART_STATE_START_BIT;
                uart->rx_last_edge_time = current_time;
                uart->rx_data = 0;
                uart->rx_bit_count = 0;
            }
            break;

        default:
            // 在数据接收过程中记录边沿时间
            uart->rx_last_edge_time = current_time;
            break;
    }
}

// RX数据采样任务
static void sw_uart_rx_task(void* arg) {
    sw_uart_port_t uart_num = (sw_uart_port_t)(uintptr_t)arg;
    sw_uart_obj_t* uart = &sw_uart_obj[uart_num];

    while (1) {
        if (uart->rx_state != SW_UART_STATE_IDLE) {
            uint64_t current_time = esp_timer_get_time();
            uint64_t elapsed_time = current_time - uart->rx_last_edge_time;

            switch (uart->rx_state) {
                case SW_UART_STATE_START_BIT:
                    if (elapsed_time >= uart->half_bit_time_us) {
                        // 在起始位中间采样
                        if (gpio_get_level(uart->rx_pin) == 0) {
                            uart->rx_state = SW_UART_STATE_DATA_BITS;
                            uart->rx_last_edge_time = current_time;
                        } else {
                            // 起始位错误，回到空闲状态
                            uart->rx_state = SW_UART_STATE_IDLE;
                        }
                    }
                    break;

                case SW_UART_STATE_DATA_BITS:
                    if (elapsed_time >= uart->bit_time_us) {
                        // 采样数据位
                        int bit_value = gpio_get_level(uart->rx_pin);
                        uart->rx_data |= (bit_value << uart->rx_bit_count);
                        uart->rx_bit_count++;
                        uart->rx_last_edge_time = current_time;

                        if (uart->rx_bit_count >= uart->config.data_bits) {
                            if (uart->config.parity != SW_UART_PARITY_DISABLE) {
                                uart->rx_state = SW_UART_STATE_PARITY_BIT;
                            } else {
                                uart->rx_state = SW_UART_STATE_STOP_BITS;
                            }
                        }
                    }
                    break;

                case SW_UART_STATE_PARITY_BIT:
                    if (elapsed_time >= uart->bit_time_us) {
                        // 采样校验位(暂时跳过校验)
                        uart->rx_state = SW_UART_STATE_STOP_BITS;
                        uart->rx_last_edge_time = current_time;
                    }
                    break;

                case SW_UART_STATE_STOP_BITS:
                    if (elapsed_time >= uart->bit_time_us * uart->config.stop_bits) {
                        // 接收完成，将数据放入队列
                        if (uart->rx_queue) {
                            xQueueSend(uart->rx_queue, &uart->rx_data, 0);
                        }
                        uart->rx_state = SW_UART_STATE_IDLE;
                    }
                    break;

                default:
                    uart->rx_state = SW_UART_STATE_IDLE;
                    break;
            }
        }

        vTaskDelay(1); // 1ms延时，避免占用过多CPU
    }
}

// TX定时器回调函数
static void IRAM_ATTR sw_uart_tx_timer_callback(void* arg) {
    sw_uart_port_t uart_num = (sw_uart_port_t)(uintptr_t)arg;
    sw_uart_obj_t* uart = &sw_uart_obj[uart_num];

    switch (uart->tx_state) {
        case SW_UART_TX_START_BIT:
            // 发送起始位(低电平)
            gpio_set_level(uart->tx_pin, 0);
            uart->tx_state = SW_UART_TX_DATA_BITS;
            uart->tx_bit_count = 0;
            break;

        case SW_UART_TX_DATA_BITS:
            // 发送数据位
            {
                int bit_value = (uart->tx_data >> uart->tx_bit_count) & 1;
                gpio_set_level(uart->tx_pin, bit_value);
                uart->tx_bit_count++;

                if (uart->tx_bit_count >= uart->config.data_bits) {
                    if (uart->config.parity != SW_UART_PARITY_DISABLE) {
                        uart->tx_state = SW_UART_TX_PARITY_BIT;
                    } else {
                        uart->tx_state = SW_UART_TX_STOP_BITS;
                        uart->tx_bit_count = 0;
                    }
                }
            }
            break;

        case SW_UART_TX_PARITY_BIT:
            // 发送校验位
            {
                uint8_t parity_bit = calculate_parity(uart->tx_data, uart->config.parity);
                gpio_set_level(uart->tx_pin, parity_bit);
                uart->tx_state = SW_UART_TX_STOP_BITS;
                uart->tx_bit_count = 0;
            }
            break;

        case SW_UART_TX_STOP_BITS:
            // 发送停止位(高电平)
            gpio_set_level(uart->tx_pin, 1);
            uart->tx_bit_count++;

            if (uart->tx_bit_count >= uart->config.stop_bits) {
                // 发送完成
                uart->tx_state = SW_UART_TX_IDLE;
                uart->tx_busy = false;
                esp_timer_stop(uart->tx_timer);
            }
            break;

        default:
            uart->tx_state = SW_UART_TX_IDLE;
            uart->tx_busy = false;
            esp_timer_stop(uart->tx_timer);
            break;
    }
}

// 参数配置函数
int sw_uart_param_config(sw_uart_port_t uart_num, const sw_uart_config_t *uart_config) {
    if (uart_num >= SW_UART_NUM_MAX || uart_config == NULL) {
        return SW_UART_ERR_INVALID_ARG;
    }

    sw_uart_obj_t* uart = &sw_uart_obj[uart_num];

    // 检查参数有效性
    if (uart_config->baud_rate == 0 || uart_config->baud_rate > 115200) {
        ESP_LOGE(TAG, "Invalid baud rate: %d", uart_config->baud_rate);
        return SW_UART_ERR_INVALID_ARG;
    }

    if (uart_config->data_bits < SW_UART_DATA_5_BITS || uart_config->data_bits > SW_UART_DATA_8_BITS) {
        ESP_LOGE(TAG, "Invalid data bits: %d", uart_config->data_bits);
        return SW_UART_ERR_INVALID_ARG;
    }

    if (uart_config->stop_bits < SW_UART_STOP_BITS_1 || uart_config->stop_bits > SW_UART_STOP_BITS_2) {
        ESP_LOGE(TAG, "Invalid stop bits: %d", uart_config->stop_bits);
        return SW_UART_ERR_INVALID_ARG;
    }

    // 保存配置
    uart->config = *uart_config;

    // 计算位时间
    uart->bit_time_us = 1000000 / uart_config->baud_rate;
    uart->half_bit_time_us = uart->bit_time_us / 2;

    ESP_LOGI(TAG, "SW_UART%d configured: baud=%d, data_bits=%d, parity=%d, stop_bits=%d",
             uart_num, uart_config->baud_rate, uart_config->data_bits,
             uart_config->parity, uart_config->stop_bits);

    return SW_UART_OK;
}

// 设置引脚函数
int sw_uart_set_pin(sw_uart_port_t uart_num, int tx_io_num, int rx_io_num, int rts_io_num, int cts_io_num) {
    if (uart_num >= SW_UART_NUM_MAX) {
        return SW_UART_ERR_INVALID_ARG;
    }

    sw_uart_obj_t* uart = &sw_uart_obj[uart_num];

    // 配置TX引脚
    if (tx_io_num != SW_UART_PIN_NO_CHANGE) {
        uart->tx_pin = tx_io_num;
        gpio_config_t io_conf = {
            .intr_type = GPIO_INTR_DISABLE,
            .mode = GPIO_MODE_OUTPUT,
            .pin_bit_mask = (1ULL << tx_io_num),
            .pull_down_en = 0,
            .pull_up_en = 1,
        };
        gpio_config(&io_conf);
        gpio_set_level(uart->tx_pin, 1); // 空闲状态为高电平
    }

    // 配置RX引脚
    if (rx_io_num != SW_UART_PIN_NO_CHANGE) {
        uart->rx_pin = rx_io_num;
        gpio_config_t io_conf = {
            .intr_type = GPIO_INTR_ANYEDGE,
            .mode = GPIO_MODE_INPUT,
            .pin_bit_mask = (1ULL << rx_io_num),
            .pull_down_en = 0,
            .pull_up_en = 1,
        };
        gpio_config(&io_conf);
    }

    ESP_LOGI(TAG, "SW_UART%d pins set: TX=%d, RX=%d", uart_num, tx_io_num, rx_io_num);

    return SW_UART_OK;
}

// 驱动安装函数
int sw_uart_driver_install(sw_uart_port_t uart_num, int rx_buffer_size, int tx_buffer_size,
                          int queue_size, QueueHandle_t* uart_queue, int intr_alloc_flags) {
    if (uart_num >= SW_UART_NUM_MAX || rx_buffer_size <= 0) {
        return SW_UART_ERR_INVALID_ARG;
    }

    sw_uart_obj_t* uart = &sw_uart_obj[uart_num];

    if (uart->installed) {
        ESP_LOGE(TAG, "SW_UART%d already installed", uart_num);
        return SW_UART_ERR_INVALID_STATE;
    }

    // 创建接收队列
    uart->rx_queue = xQueueCreate(rx_buffer_size, sizeof(uint8_t));
    if (uart->rx_queue == NULL) {
        ESP_LOGE(TAG, "Failed to create RX queue for SW_UART%d", uart_num);
        return SW_UART_ERR_INVALID_STATE;
    }

    // 创建发送互斥锁
    uart->tx_mutex = xSemaphoreCreateMutex();
    if (uart->tx_mutex == NULL) {
        ESP_LOGE(TAG, "Failed to create TX mutex for SW_UART%d", uart_num);
        vQueueDelete(uart->rx_queue);
        return SW_UART_ERR_INVALID_STATE;
    }

    // 创建发送定时器
    esp_timer_create_args_t timer_args = {
        .callback = sw_uart_tx_timer_callback,
        .arg = (void*)(uintptr_t)uart_num,
        .name = "sw_uart_tx_timer"
    };

    esp_err_t ret = esp_timer_create(&timer_args, &uart->tx_timer);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create TX timer for SW_UART%d", uart_num);
        vSemaphoreDelete(uart->tx_mutex);
        vQueueDelete(uart->rx_queue);
        return SW_UART_ERR_INVALID_STATE;
    }

    // 初始化状态
    uart->rx_buffer_size = rx_buffer_size;
    uart->rx_state = SW_UART_STATE_IDLE;
    uart->tx_state = SW_UART_TX_IDLE;
    uart->tx_busy = false;

    // 安装GPIO中断
    gpio_install_isr_service(ESP_INTR_FLAG_IRAM);
    gpio_isr_handler_add(uart->rx_pin, sw_uart_rx_isr_handler, (void*)(uintptr_t)uart_num);

    // 创建RX任务
    char task_name[16];
    snprintf(task_name, sizeof(task_name), "sw_uart_rx_%d", uart_num);
    BaseType_t task_ret = xTaskCreate(sw_uart_rx_task, task_name, 2048,
                                     (void*)(uintptr_t)uart_num, 5, NULL);
    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create RX task for SW_UART%d", uart_num);
        esp_timer_delete(uart->tx_timer);
        vSemaphoreDelete(uart->tx_mutex);
        vQueueDelete(uart->rx_queue);
        return SW_UART_ERR_INVALID_STATE;
    }

    uart->installed = true;
    ESP_LOGI(TAG, "SW_UART%d driver installed successfully", uart_num);

    return SW_UART_OK;
}

// 驱动卸载函数
int sw_uart_driver_delete(sw_uart_port_t uart_num) {
    if (uart_num >= SW_UART_NUM_MAX) {
        return SW_UART_ERR_INVALID_ARG;
    }

    sw_uart_obj_t* uart = &sw_uart_obj[uart_num];

    if (!uart->installed) {
        return SW_UART_ERR_INVALID_STATE;
    }

    // 停止定时器
    esp_timer_stop(uart->tx_timer);
    esp_timer_delete(uart->tx_timer);

    // 移除GPIO中断
    gpio_isr_handler_remove(uart->rx_pin);

    // 删除队列和信号量
    vQueueDelete(uart->rx_queue);
    vSemaphoreDelete(uart->tx_mutex);

    // 重置状态
    memset(uart, 0, sizeof(sw_uart_obj_t));

    ESP_LOGI(TAG, "SW_UART%d driver deleted", uart_num);

    return SW_UART_OK;
}

// 发送数据函数
int sw_uart_write_bytes(sw_uart_port_t uart_num, const void* src, size_t size) {
    if (uart_num >= SW_UART_NUM_MAX || src == NULL || size == 0) {
        return SW_UART_ERR_INVALID_ARG;
    }

    sw_uart_obj_t* uart = &sw_uart_obj[uart_num];

    if (!uart->installed) {
        return SW_UART_ERR_INVALID_STATE;
    }

    const uint8_t* data = (const uint8_t*)src;
    int bytes_sent = 0;

    // 获取发送互斥锁
    if (xSemaphoreTake(uart->tx_mutex, portMAX_DELAY) != pdTRUE) {
        return SW_UART_ERR_TIMEOUT;
    }

    for (size_t i = 0; i < size; i++) {
        // 等待上一个字节发送完成
        while (uart->tx_busy) {
            vTaskDelay(1);
        }

        // 设置发送数据
        uart->tx_data = data[i];
        uart->tx_state = SW_UART_TX_START_BIT;
        uart->tx_busy = true;

        // 启动定时器
        esp_err_t ret = esp_timer_start_periodic(uart->tx_timer, uart->bit_time_us);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to start TX timer: %s", esp_err_to_name(ret));
            break;
        }

        bytes_sent++;
    }

    // 释放发送互斥锁
    xSemaphoreGive(uart->tx_mutex);

    return bytes_sent;
}

// 接收数据函数
int sw_uart_read_bytes(sw_uart_port_t uart_num, void* buf, uint32_t length, TickType_t ticks_to_wait) {
    if (uart_num >= SW_UART_NUM_MAX || buf == NULL || length == 0) {
        return SW_UART_ERR_INVALID_ARG;
    }

    sw_uart_obj_t* uart = &sw_uart_obj[uart_num];

    if (!uart->installed) {
        return SW_UART_ERR_INVALID_STATE;
    }

    uint8_t* data = (uint8_t*)buf;
    uint32_t bytes_read = 0;

    TickType_t start_time = xTaskGetTickCount();
    TickType_t timeout = ticks_to_wait;

    while (bytes_read < length) {
        uint8_t byte;

        // 计算剩余等待时间
        if (ticks_to_wait != portMAX_DELAY) {
            TickType_t elapsed = xTaskGetTickCount() - start_time;
            if (elapsed >= ticks_to_wait) {
                break; // 超时
            }
            timeout = ticks_to_wait - elapsed;
        }

        // 从队列中接收数据
        if (xQueueReceive(uart->rx_queue, &byte, timeout) == pdTRUE) {
            data[bytes_read] = byte;
            bytes_read++;
        } else {
            break; // 超时或队列为空
        }
    }

    return bytes_read;
}

// 清空接收缓冲区
int sw_uart_flush(sw_uart_port_t uart_num) {
    if (uart_num >= SW_UART_NUM_MAX) {
        return SW_UART_ERR_INVALID_ARG;
    }

    sw_uart_obj_t* uart = &sw_uart_obj[uart_num];

    if (!uart->installed) {
        return SW_UART_ERR_INVALID_STATE;
    }

    // 清空接收队列
    xQueueReset(uart->rx_queue);

    return SW_UART_OK;
}

// 获取接收缓冲区数据长度
int sw_uart_get_buffered_data_len(sw_uart_port_t uart_num, size_t* size) {
    if (uart_num >= SW_UART_NUM_MAX || size == NULL) {
        return SW_UART_ERR_INVALID_ARG;
    }

    sw_uart_obj_t* uart = &sw_uart_obj[uart_num];

    if (!uart->installed) {
        return SW_UART_ERR_INVALID_STATE;
    }

    *size = uxQueueMessagesWaiting(uart->rx_queue);

    return SW_UART_OK;
}