#ifndef MY_ESP_CHIP_INFO_H
#define MY_ESP_CHIP_INFO_H

#include "my_config.h"

#define MY_ESP_CHIP_INFO_MNG_FUNC  1

#if MY_ESP_CHIP_INFO_MNG_FUNC

#include <lwip/netdb.h>

#define MY_ESP_CHIP_INFO_MNG_FUNC_DEBUG 1

#define MyEspChipInfoMng_TASK_CREATE_TYPE    TASK_CREATE_TYPE_DYNAMIC

#if MyEspChipInfoMng_TASK_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
#define MyEspChipInfoMng_Task_COREID    1
#define MyEspChipInfoMng_Task_priority  5
#define MyEspChipInfoMng_Task_task_stack_size   4096
#elif MyEspChipInfoMng_TASK_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
#define MyEspChipInfoMng_Task_COREID    1
#define MyEspChipInfoMng_Task_priority  5
#define MyEspChipInfoMng_Task_task_stack_size   2048
#endif


#define EspChipInfoMng_Task_refresh_time 1000//ms

#define MONITOR_MEM_PERIOD_DEFAULT  1//s


typedef struct
{
    esp_chip_info_t chip_info;
    uint8_t system_reset_reason;
    uint8_t chip_mac_int[6];
    char chip_mac_str[12+1];

    uint32_t monitor_mem_period;//内存监视周期
    uint32_t monitor_mem_period_count;//内存监视周期计数器，到达MONITOR_MEM_PERIOD_DEFAULT后获取一次内存情况，并清零计数器
    uint32_t spi_flash_size;//外置flash大小
    uint32_t total_free_heap_size;//总的可用内存大小
    uint32_t internal_free_heap_size;
    uint32_t external_free_heap_size;
    uint32_t history_min_iram_size;
    uint32_t history_min_eram_size;
    char system_reset_reason_str[32];
}my_esp_chip_info_mng_t;

#define CMNG_NVS_FORM_NAME	"NVS_CMNG"
#define CMNG_NVS_KEY_NAME	"NVS_CMNG_1"


/**
  * @brief 获取MAC地址字符串
  * @param pointer to save mac string
  * @return 0
  */
int MyEspChipInfoMng_GetMacStr(char* saving_mac);

/**
  * @brief 获取MAC地址int值
  * @param saving_mac 存储int类型mac地址数组，不应小于6byte
  * @param saving_len saving_mac的长度
  * @return int 类型mac地址
  */
int MyEspChipInfoMng_GetMacInt(char* saving_mac, int saving_len);

/**
  * @brief 获取复位原因
  * @return my_esp_chip_info_mng_info.system_reset_reason = ESP_RST_POWERON, ESP_RST_SW, ESP_RST_DEEPSLEEP...
  */
uint8_t MyEspChipInfoMng_GetResetReason(void);

void MyEspChipInfoMng_Init(void);

void My_esp_restart(void);
void My_esp_Powroff(void);

#if MY_ESP_CHIP_INFO_MNG_FUNC_DEBUG
//-----------------------------------------------------
//debug
void MyDebug_PrintMemInfo(char* name);
//-----------------------------------------------------
#endif

#endif
#endif