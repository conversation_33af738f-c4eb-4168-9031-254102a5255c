#ifndef MYCAMERA_H
#define MYCAMERA_H

#include "esp_camera.h"
#include <lwip/netdb.h>

#define CONFIG_OV2640_SUPPORT   1
// #define CONFIG_OV7725_SUPPORT    1
// #define CONFIG_OV3660_SUPPORT    1
//#define CONFIG_OV5640_SUPPORT    1
// #define CONFIG_NT99141_SUPPORT   1
// #define CONFIG_OV7670_SUPPORT    1

#define CAMERA_TYPE OV2640

#define PHOTO_COMPRESS_RATIO_GRADE_1	15
#define PHOTO_COMPRESS_RATIO_GRADE_2	18
#define PHOTO_COMPRESS_RATIO_GRADE_3	21
#define PHOTO_COMPRESS_RATIO_MAX	32

typedef struct
{
    camera_fb_t *data;
    size_t data_size;
    uint32_t picture_buf_size;
    time_t time_stamp;
    uint32_t take_time_use;//ms
    uint16_t pixel_x;
    uint16_t pixel_y;
    uint8_t compression_ratio;
    uint32_t time_of_exposure;
    uint8_t focal_distance;
    uint8_t photo_format;
    uint8_t color_mode;
    char camera_model[32];
}mypicture_t;

#define CAM_NVS_FORM_NAME	"NVS_CAM"
#define CAM_NVS_KEY_NAME	"NVS_CAM_1"

typedef struct
{
	int photo_quality;
    int photo_imagesize_index_in_list;
	int photo_imagesize_x;
    int photo_imagesize_y;
    int vflip;
    int hmirror;
    int contrast;
    int brightness;
    int saturation;
    int wb_mode;
    int special_effect;
}camera_nvs_t;

typedef camera_nvs_t my_camera_conf_t;

typedef struct
{
    my_camera_conf_t conf;
    uint8_t conf_valid;
    uint8_t conf_need_reload;
    uint32_t conf_crc;
    uint8_t init_ok;                //cam是否已经开启
    uint8_t first_init;             //开机后第一次配置
    uint16_t pixel_x;
    uint16_t pixel_y;
    uint8_t compression_ratio;
    uint32_t time_of_exposure;
    char focal_distance[32];
    char photo_format[32];
    char color_mode[32];
}my_cam_info_t;

#define MYCAMERA_FOCAL_DISTANCE_STR "4.3mm"
#define MYCAMERA_PHOTO_FORMAT_STR "jpg"
#define MYCAMERA_COLOR_MODE_STR "auto"


#if CONFIG_OV2640_SUPPORT
#define PHOTO_QUALITY_DEFAULT   PHOTO_COMPRESS_RATIO_GRADE_1
#define PHOTO_SIZE_DEFAULT      FRAMESIZE_SVGA
#define PHOTO_VFLIP_DEFAULT     0
#define PHOTO_HMIRROR_DEFAULT   0
#endif
#if CONFIG_OV5640_SUPPORT
#define PHOTO_QUALITY_DEFAULT   15
#define PHOTO_SIZE_DEFAULT      FRAMESIZE_UXGA
#define PHOTO_VFLIP_DEFAULT     0
#define PHOTO_HMIRROR_DEFAULT   1
#endif

extern uint16_t myconf_devPI_cam_support_photo_size_list_default[14][2];

/**
 * @brief 配置摄像头
 * @param conf 为空时使用默认配置，但将导致my_cam_info_t无法获取初始配置
 * @note 在开机后且在第一次调用 TakePicture() 前必须调用一次此函数，
 * 多次调用时，若cam已关闭且conf不为空且合法，将按照conf重新配置，若conf为空则相当于函数无效
 * 此函数仅在摄像头未开启时才有效
 * 调用此函数之后，摄像头一直打开，不会自动关闭
 * @return 0:成功，1:失败
 */
int MyCamera_Init(my_camera_conf_t* conf);

/**
 * @brief 重新配置摄像头
 * @param conf 为空时使用已有配置，或者默认配置
 * @note 配置后，下次调用 TakePicture() 将按照新的配置拍摄照片
 * 函数不受摄像头状态影响，若摄像头已经开启，则先关闭摄像头，然后调用 MyCamera_Init()
 * 若摄像头没有打开，则直接调用 MyCamera_Init()
 * 调用此函数之后，摄像头一直打开，不会自动关闭
 * @return 0:成功，1:失败
 */
int MyCamera_ReInit(my_camera_conf_t* conf);

/**
 * @brief 仅重启摄像头，配置不变
 * @note 若是开机后没有调用 MyCamera_Init()导致conf为空，则调用此函数后使用默认配置
 * @return 0:成功，1:失败
 */
int MyCamera_ReStart(void);

void MyCamera_Reload_Mark(void);
int MyCamera_Test(my_camera_conf_t* conf, int test_num);

/**
 * @brief 拍摄一张图片
 * @param conf 配置
 * @param p 图片存储区（外部），存储这张图片的所有信息
 * @param close_after_use 是否在拍完这张照片后关闭摄像头
 * @param timeout 超时，可能的原因有摄像头被其他任务占用
 * @return 0:成功，-1:摄像头配置失败，-2:拍摄照片后出错，-3:拍摄超时
 * @note 
 */
int TakePicture(my_camera_conf_t* conf, mypicture_t* const p, uint8_t close_after_use, uint32_t timeout);

/**
 * @brief 关闭摄像头
 */
void MyCamera_Close(void);

/**
 * @brief 打开摄像头
 */
void MyCamera_Open(void);


void SetCameraConfiguration(int photo_size, int photo_quality, int vflip, int hmirror);
int SaveCameraConfig(int reboot);

uint8_t MyCamera_Get_InitState(void);
uint8_t MyCamera_Get_PhotoSizeIndexInList(void);
uint8_t MyCamera_Get_PhotoCompressRatio(void);
uint8_t MyCamera_Get_PhotoVflip(void);
uint8_t MyCamera_Get_PhotoHmirror(void);


#endif