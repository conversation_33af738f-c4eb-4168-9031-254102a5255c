#include "evgspl.h"

#include "my_debug.h"
#include "esp_log.h"
#include "my_whgm5.h"
#include "my_kmp.h"
#include "checksum.h"
#include "my_esp_aes.h"
#include "my_endian.h"
#include "cJSON.h"
#include "my_nvs.h"
#include "my_config.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"
#include "freertos/semphr.h"

// Define a tag for logging
static const char* TAG = "EVGSPL";

TaskHandle_t My_EVGSPL_Rcv_TaskHandle = NULL;
TaskHandle_t My_EVGSPL_TaskHandle = NULL;

void My_EVGSPL_Task(void* param)
{
    ESP_LOGI(TAG, "EVGSPL main task started");

    for(;;)
    {

    }
}

void My_EVGSPL_Rcv_Task(void* param)
{
    my_whgm5_queue_item_t* p_rcv_buf = NULL;
    char* rcv_buf = (char*)calloc(4, 4096);
    int rcv_len = 0;

    if (rcv_buf == NULL) {
        ESP_LOGE(TAG, "Failed to allocate receive buffer");
        vTaskDelete(NULL);
        return;
    }

    ESP_LOGI(TAG, "Receive task started");

    for(;;)
    {
        if(!MyWHGM5_TcpRcvData(&p_rcv_buf, portMAX_DELAY))
        {
            if (p_rcv_buf != NULL && p_rcv_buf->data != NULL && p_rcv_buf->len > 0) {
                // Copy received data to our buffer
                memcpy(rcv_buf, p_rcv_buf->data, p_rcv_buf->len);
                rcv_len = p_rcv_buf->len;

                // Ensure null termination for JSON parsing
                rcv_buf[rcv_len] = '\0';

                ESP_LOGI(TAG, "Received data (%d bytes): %s", rcv_len, rcv_buf);

                // Process the received JSON data
                // ...

                // Mark buffer as processed
                p_rcv_buf->valid = 0;
            }
        }
    }
}

void evgspl_init(void)
{
    BaseType_t ret;

    // Create the receive task
    ret = xTaskCreatePinnedToCore(My_EVGSPL_Rcv_Task, "My_EVGSPL_Rcv_Task", 4096, NULL, 18, &My_EVGSPL_Rcv_TaskHandle, 1);
	if(ret != pdPASS)
	{
		ESP_LOGE(TAG, "Failed to create My_EVGSPL_Rcv_Task, error=%d", ret);
        return;
	}

    // Create the main task that sends JSON data
    ret = xTaskCreatePinnedToCore(My_EVGSPL_Task, "My_EVGSPL_Task", 4096, NULL, 17, &My_EVGSPL_TaskHandle, 1);
	if(ret != pdPASS)
	{
		ESP_LOGE(TAG, "Failed to create My_EVGSPL_Task, error=%d", ret);
        return;
	}

    ESP_LOGI(TAG, "EVGSPL protocol initialized successfully");
}