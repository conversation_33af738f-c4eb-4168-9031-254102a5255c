#include "my_config.h"
#include "my_camera.h"
#include "my_time.h"
#include "my_endian.h"
#include "checksum.h"
#include "my_debug.h"

#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "freertos/semphr.h"

#include "esp_camera.h"
#include "my_gpio.h"
#include "driver/i2c.h"
#include "driver/gpio.h"

#include <esp_log.h>

#include <stdio.h>


#if defined MY_DEBUG_PRINT_FUNC
#define DEBUG_PRINT_LEVEL_MY_CAMERA_PRINT   MY_DEBUG_PRINT_LEVEL_0
#else
#define DEBUG_PRINT_LEVEL_MY_CAMERA_PRINT   DEBUG_PRINT_LEVEL_0
#endif


#define BOARD_ESP32CAM_SWQZ    1
// WROVER-KIT PIN Map
#ifdef BOARD_WROVER_KIT

#define CAM_PIN_PWDN -1  //power down is not used
#define CAM_PIN_RESET -1 //software reset will be performed
#define CAM_PIN_XCLK 21
#define CAM_PIN_SIOD 26
#define CAM_PIN_SIOC 27

#define CAM_PIN_D7 35
#define CAM_PIN_D6 34
#define CAM_PIN_D5 39
#define CAM_PIN_D4 36
#define CAM_PIN_D3 19
#define CAM_PIN_D2 18
#define CAM_PIN_D1 5
#define CAM_PIN_D0 4
#define CAM_PIN_VSYNC 25
#define CAM_PIN_HREF 23
#define CAM_PIN_PCLK 22

#endif

// ESP32Cam (AiThinker) PIN Map
#ifdef BOARD_ESP32CAM_AITHINKER

#define CAM_PIN_PWDN 32
#define CAM_PIN_RESET -1 //software reset will be performed
#define CAM_PIN_XCLK 0
#define CAM_PIN_SIOD 26
#define CAM_PIN_SIOC 27

#define CAM_PIN_D7 35
#define CAM_PIN_D6 34
#define CAM_PIN_D5 39
#define CAM_PIN_D4 36
#define CAM_PIN_D3 21
#define CAM_PIN_D2 19
#define CAM_PIN_D1 18
#define CAM_PIN_D0 5
#define CAM_PIN_VSYNC 25
#define CAM_PIN_HREF 23
#define CAM_PIN_PCLK 22

#endif

// ESP32Cam (SWQZ) PIN Map
#ifdef BOARD_ESP32CAM_SWQZ

#define CAM_PIN_PWDN 19
#define CAM_PIN_RESET -1 //software reset will be performed
#define CAM_PIN_XCLK 0
#define CAM_PIN_SIOD 17
#define CAM_PIN_SIOC 16

#define CAM_PIN_D7 7
#define CAM_PIN_D6 6
#define CAM_PIN_D5 5
#define CAM_PIN_D4 4
#define CAM_PIN_D3 40
#define CAM_PIN_D2 42
#define CAM_PIN_D1 41
#define CAM_PIN_D0 39
#define CAM_PIN_VSYNC 15
#define CAM_PIN_HREF 2
#define CAM_PIN_PCLK 1

#endif

#define MyCamera_LowpowerSet_GPIO_PIN_SEL  ( (1ULL<<CAM_PIN_XCLK)|(1ULL<<CAM_PIN_SIOD)|(1ULL<<CAM_PIN_SIOC)|\
                                            (1ULL<<CAM_PIN_D3)|(1ULL<<CAM_PIN_D2)| (1ULL<<CAM_PIN_D1)|(1ULL<<CAM_PIN_D0)|\
                                            (1ULL<<CAM_PIN_VSYNC)|(1ULL<<CAM_PIN_HREF)|(1ULL<<CAM_PIN_PCLK))

#define MyCamera_LowpowerSet_GPIO_PIN_SEL_1  ( (1ULL<<CAM_PIN_D7)|(1ULL<<CAM_PIN_D6)|(1ULL<<CAM_PIN_D5)|(1ULL<<CAM_PIN_D4))


static camera_config_t camera_config = {
    .pin_pwdn = CAM_PIN_PWDN,
    .pin_reset = CAM_PIN_RESET,
    .pin_xclk = CAM_PIN_XCLK,
    .pin_sscb_sda = CAM_PIN_SIOD,
    .pin_sscb_scl = CAM_PIN_SIOC,

    .pin_d7 = CAM_PIN_D7,
    .pin_d6 = CAM_PIN_D6,
    .pin_d5 = CAM_PIN_D5,
    .pin_d4 = CAM_PIN_D4,
    .pin_d3 = CAM_PIN_D3,
    .pin_d2 = CAM_PIN_D2,
    .pin_d1 = CAM_PIN_D1,
    .pin_d0 = CAM_PIN_D0,
    .pin_vsync = CAM_PIN_VSYNC,
    .pin_href = CAM_PIN_HREF,
    .pin_pclk = CAM_PIN_PCLK,

    //XCLK 20MHz or 10MHz for OV2640 double FPS (Experimental)
    .xclk_freq_hz = 20000000,
    .ledc_timer = LEDC_TIMER_0,
    .ledc_channel = LEDC_CHANNEL_0,

    .pixel_format = PIXFORMAT_JPEG, //YUV422,GRAYSCALE,RGB565,JPEG
    .frame_size = FRAMESIZE_UXGA,    //QQVGA-UXGA Do not use sizes above QVGA when not JPEG

    .jpeg_quality = 12, //0-63 lower number means higher quality
    .fb_count = 1,      //if more than one, i2s runs in continuous mode. Use only with JPEG
    .grab_mode = CAMERA_GRAB_WHEN_EMPTY,
    .vflip = PHOTO_VFLIP_DEFAULT,
    .hmirror = PHOTO_HMIRROR_DEFAULT
};

static const char *TAG = "mycamera.c:";

my_cam_info_t my_cam_info;

SemaphoreHandle_t MyCamMutexSemaphore = NULL;

uint16_t myconf_devPI_cam_support_photo_size_list_default[14][2]=
{
    {96, 96},
    {160, 120},
    {176, 144},
    {240, 176},
    {240, 240},
    {320, 240},
    {400, 296},
    {480, 320},
    {640, 480},
    {800, 600},
    {1024, 768},
    {1280, 720},
    {1280, 1024},
    {1600, 1200},
};

uint16_t myconf_devPI_cam_support_photo_size_list_default_copy[14][2]=
{
    {96, 96},
    {160, 120},
    {176, 144},
    {240, 176},
    {240, 240},
    {320, 240},
    {400, 296},
    {480, 320},
    {640, 480},
    {800, 600},
    {1024, 768},
    {1280, 720},
    {1280, 1024},
    {1600, 1200},
};

void MyCamera_DebugPrint_conf(my_camera_conf_t* conf, char* tag);
void MyCamera_DebugPrint_info(my_cam_info_t* info, char* tag);


static esp_err_t init_camera()
{
    //initialize the camera
    esp_err_t err = esp_camera_init(&camera_config);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Camera Init Failed");
        return err;
    }

    return ESP_OK;
}

int MyCamera_Conf_Adapter(my_camera_conf_t* input_conf, my_camera_conf_t* output_conf);
int TakePicture(my_camera_conf_t* conf, mypicture_t* const p, uint8_t close_after_use, uint32_t timeout)
{
    if(p==NULL)return -1;
    if(p->data==NULL)return -2;
    if(p->data->buf==NULL)return -3;

    if(MyCamMutexSemaphore!=NULL)
    {
        xSemaphoreTake(MyCamMutexSemaphore, timeout/portTICK_PERIOD_MS);
    }
    else return -4;


    uint64_t start_time;
    uint32_t _timeout = 0;
    int ret = 0;

    camera_fb_t* picture_data = NULL;

    if(!my_cam_info.init_ok)
    {
        if(my_cam_info.conf_valid)
        {
            if(MyCamera_Init(&my_cam_info.conf))
            {
                ret = 1;
            }
        }
        else
        {
            if(MyCamera_Init(NULL))
            {
                ret = 1;
            }
        }
    }
    else
    {
        if(my_cam_info.conf_need_reload)
        {
            my_cam_info.conf_need_reload = 0;
            if(conf!=NULL)
            { 
                my_camera_conf_t* crc_conf_temp = calloc(1, sizeof(my_camera_conf_t));
                if(crc_conf_temp!=NULL)
                {
                    memcpy(crc_conf_temp, conf, sizeof(my_camera_conf_t));
                    #if DEBUG_PRINT_LEVEL_MY_CAMERA_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
                    MyCamera_DebugPrint_conf(&my_cam_info.conf, "TakePicture(): before MyCamera_Conf_Adapter(), &my_cam_info.conf");
                    MyCamera_DebugPrint_conf(conf, "TakePicture(): before MyCamera_Conf_Adapter(), conf");
                    #endif
                    MyCamera_Conf_Adapter(conf, crc_conf_temp);
                    #if DEBUG_PRINT_LEVEL_MY_CAMERA_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
                    MyCamera_DebugPrint_conf(crc_conf_temp, "TakePicture(): before MyCamera_Conf_Adapter(), crc_conf_temp");
                    #endif
                    ESP_LOGI("TakePicture()", "my_cam_info.conf_crc=0x%8X", my_cam_info.conf_crc);
                    ESP_LOGI("TakePicture()", "crc_conf_temp crc=0x%8X", crc_32((void*)crc_conf_temp, sizeof(my_camera_conf_t)));
                    if(crc_32((void*)crc_conf_temp, sizeof(my_camera_conf_t)) != my_cam_info.conf_crc)
                    {
                        ESP_LOGW("TakePicture()", "conf crc changed, use new conf to take picture");
                        MyCamera_ReInit(conf);
                    }
                    else
                    {
                        ESP_LOGW("TakePicture()", "conf crc not change, use exist conf to take picture");
                    }
                    free(crc_conf_temp);
                }
                else
                {
                    ESP_LOGE("TakePicture()", "crc_conf_temp==NULL, use exist conf to take picture");
                }
                
            }
            else
            {
                ESP_LOGW("TakePicture()", "conf==NULL, use exist conf to take picture");
            }
        }
    }

    if(!ret)
    {
        ESP_LOGI("", "TakePicture(): Taking picture...");
        p->time_stamp = MyTime_GetTime();
        start_time = esp_timer_get_time()/1000;
        picture_data = esp_camera_fb_get();
        p->take_time_use = esp_timer_get_time()/1000-start_time;

        // use pic->buf to access the image
        if(picture_data!=NULL)
        {
            p->pixel_x = my_cam_info.pixel_x;
            p->pixel_y = my_cam_info.pixel_y;
            p->compression_ratio = my_cam_info.compression_ratio;
            p->time_of_exposure = my_cam_info.time_of_exposure;
            p->focal_distance = my_cam_info.focal_distance;
            p->photo_format = my_cam_info.photo_format;
            p->color_mode = my_cam_info.color_mode;
            p->camera_model[0]=my_cam_info.pixel_x;
            #if CONFIG_OV2640_SUPPORT
            memcpy(p->camera_model, "OV2640", strlen("OV2640"));
            #endif
            ESP_LOGI("", "TakePicture(): Picture taken! time use %d ms, xy=(%d*%d)Its size was: %zu bytes\n", \
            p->take_time_use, my_cam_info.pixel_x, my_cam_info.pixel_y, picture_data->len);

            if(picture_data->len<=p->picture_buf_size)
            {
                #if DEBUG_PRINT_LEVEL_MY_CAMERA_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
                printf("copy picture data to p->data->buf\n");
                #endif
                if(picture_data->buf!=NULL)
                {
                    memcpy(p->data->buf, picture_data->buf, picture_data->len);
                    p->data->len = picture_data->len;
                    p->data->width = picture_data->width;
                    p->data->height = picture_data->height;
                    p->data->format = picture_data->format;
                    p->data->timestamp = picture_data->timestamp;
                }
                else
                {
                    ESP_LOGE("TakePicture()", "picture_data->buf!=NULL is false\n");
                    p->data->len = 0;
                    ret = 2;
                }
            }
            else
            {
                ESP_LOGE("TakePicture()", "picture_data->len<=p->picture_buf_size is false\n");
                ret = 3;
            }
        }
        else
        {
            ESP_LOGE("TakePicture()", "take picture error, p->data==NULL\n");
            MyCamera_Close();
            ret = 4;
        }
        esp_camera_fb_return(picture_data);
    }

    if(close_after_use)
    {
        MyCamera_Close();
    }

    xSemaphoreGive(MyCamMutexSemaphore);

    return ret;
}

#define GPIO_CAMPWR        32
#define GPIO_OUTPUT_PIN_SEL  ( (1ULL<<GPIO_CAMPWR) )
void camerapwr_GpioInit(void)
{
    gpio_config_t io_conf;
    //disable interrupt
    io_conf.intr_type = GPIO_PIN_INTR_DISABLE;
    //set as output mode
    io_conf.mode = GPIO_MODE_OUTPUT;
    //bit mask of the pins that you want to set,e.g.GPIO18/19
    io_conf.pin_bit_mask = GPIO_OUTPUT_PIN_SEL;
    //disable pull-down mode
    io_conf.pull_down_en = 0;
    //disable pull-up mode
    io_conf.pull_up_en = 0;
    //configure GPIO with the given settings
    gpio_config(&io_conf);
}

void camera_poweron(void)
{
    MyGPIO_SET(GPIO_CAMPWR, 0);
}

void camera_poweroff(void)
{
    camerapwr_GpioInit();
    MyGPIO_SET(GPIO_CAMPWR, 1);
}

void camera_reboot(void)
{
    camera_poweroff();
    vTaskDelay(2000/portTICK_PERIOD_MS);
    camera_poweron();
    vTaskDelay(5000/portTICK_PERIOD_MS);
}

static uint8_t camera_io_need_deinit = 0;
void camera_uninstall(void)
{
    if(my_cam_info.init_ok)
    {
        ESP_LOGI("camera_uninstall()", "uninstall camera\n");
        my_cam_info.init_ok = 0;
        esp_camera_deinit();
        i2c_driver_delete(CONFIG_SCCB_HARDWARE_I2C_PORT1);
        //gpio_uninstall_isr_service();
        camera_io_need_deinit = 1;
    }
    else
    {
        ESP_LOGI("camera_uninstall()", "camera driver not installed, no uninstall required\n");
    }
}

int my_init_camera(my_camera_conf_t* p_conf)
{
    camera_config.frame_size = p_conf->photo_imagesize_index_in_list;
    camera_config.jpeg_quality = p_conf->photo_quality;
    camera_config.vflip = p_conf->vflip;
    camera_config.hmirror = p_conf->hmirror;
    camera_config.contrast = p_conf->contrast;
    camera_config.brightness = p_conf->brightness;
    camera_config.saturation = p_conf->saturation;
    camera_config.wb_mode = p_conf->wb_mode;
    camera_config.special_effect = p_conf->special_effect;

    if(camera_config.jpeg_quality!=PHOTO_COMPRESS_RATIO_GRADE_1||camera_config.jpeg_quality!=PHOTO_COMPRESS_RATIO_GRADE_2||camera_config.jpeg_quality!=PHOTO_COMPRESS_RATIO_GRADE_3)
    {
        camera_config.jpeg_quality = PHOTO_QUALITY_DEFAULT;
    }
    if(camera_config.frame_size >= FRAMESIZE_INVALID)
    {
        camera_config.frame_size = PHOTO_SIZE_DEFAULT;
    }

    int init_cam_count = 0;
    do
    {
        if(init_camera()==ESP_OK)
        {
            my_cam_info.init_ok = 1;
            ESP_LOGI("MyInitCamera()", "Camera Init Succeed\n");
            return 0;
        }
        init_cam_count++;
        camera_reboot();
        ESP_LOGE("MyInitCamera()", "camera init failed:%d\n", init_cam_count);
    } while (init_cam_count<5);

    ESP_LOGE("MyInitCamera()", "Camera Init Failed\n");
    return 1;
}

int MyCamera_Update_SizeInfo(uint8_t new_size);
int MyCamera_Update_CompressRatioInfo(uint8_t new_compressratio);

int MyCamera_Find_sizeindex_in_image_size_list(int x, int y)
{
    int size_num = sizeof(myconf_devPI_cam_support_photo_size_list_default_copy)/sizeof(uint16_t)/2;

    #if DEBUG_PRINT_LEVEL_MY_CAMERA_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
    printf("MyCamera_Find_sizeindex_in_image_size_list():size_num = %d\n", size_num);
    printf("MyCamera_Find_sizeindex_in_image_size_list():x = %d, y = %d\n", x, y);
    #endif

    int i=0;
    for(; i<size_num; i++)
    {
        if(x==myconf_devPI_cam_support_photo_size_list_default_copy[i][0]\
            &&y==myconf_devPI_cam_support_photo_size_list_default_copy[i][1])
        {
            ESP_LOGI("MyCamera_Find_sizeindex_in_image_size_list()", "match 1");
            return i;
        }
    }
    if(!(i<size_num))
    {
        for(int i=0; i<size_num; i++)
        {
            if(x==myconf_devPI_cam_support_photo_size_list_default_copy[i][0])
            {
                ESP_LOGI("MyCamera_Find_sizeindex_in_image_size_list()", "match 2");
                return i;
            }
        }
    }
    if(!(i<size_num))
    {
        for(int i=0; i<size_num; i++)
        {
            if(y==myconf_devPI_cam_support_photo_size_list_default_copy[i][1])
            {
                ESP_LOGI("MyCamera_Find_sizeindex_in_image_size_list()", "match 3");
                return i;
            }
        }
    }
    ESP_LOGI("MyCamera_Find_sizeindex_in_image_size_list()", "match 4");
    return -1;
}

int myconf_devPI_cam_support_photo_size_list_Set_Bigendian(void)
{
    uint16_t* p_size_data = myconf_devPI_cam_support_photo_size_list_default;
    for(int i=0; i<sizeof(myconf_devPI_cam_support_photo_size_list_default)/sizeof(uint16_t); i++)
    {
        *(p_size_data+i) = my_endian_conversion_16(*(p_size_data+i));
    }

    return 0;
}

int MyCamera_Conf_Adapter(my_camera_conf_t* input_conf, my_camera_conf_t* output_conf)
{
    if(input_conf==NULL)
    {
        return -1;
    }
    if(output_conf==NULL)
    {
        return -2;
    }
    if(input_conf->photo_quality!=PHOTO_COMPRESS_RATIO_GRADE_1&&input_conf->photo_quality!=PHOTO_COMPRESS_RATIO_GRADE_2&&input_conf->photo_quality!=PHOTO_COMPRESS_RATIO_GRADE_3)
    {
        output_conf->photo_quality = PHOTO_QUALITY_DEFAULT;
    }
    // if(my_cam_info.conf.photo_imagesize >= FRAMESIZE_INVALID)
    // {
    //     my_cam_info.conf.photo_imagesize = PHOTO_SIZE_DEFAULT;
    // }
    output_conf->photo_imagesize_index_in_list = \
    MyCamera_Find_sizeindex_in_image_size_list(input_conf->photo_imagesize_x, input_conf->photo_imagesize_y);

    if(output_conf->photo_imagesize_index_in_list==-1)
    {
        output_conf->photo_imagesize_index_in_list = PHOTO_SIZE_DEFAULT;
    }

    output_conf->photo_imagesize_x = myconf_devPI_cam_support_photo_size_list_default_copy[output_conf->photo_imagesize_index_in_list][0];
    output_conf->photo_imagesize_y = myconf_devPI_cam_support_photo_size_list_default_copy[output_conf->photo_imagesize_index_in_list][1];

    return 0;
}

int MyCamera_Init(my_camera_conf_t* conf)
{
    if(!my_cam_info.init_ok)
    {
        if(!my_cam_info.first_init)
        {
            MyCamMutexSemaphore = xSemaphoreCreateMutex();
            myconf_devPI_cam_support_photo_size_list_Set_Bigendian();
        }

        if(conf!=NULL)
        {
            if(conf!=&my_cam_info.conf)
            {
                memset(&my_cam_info.conf, 0, sizeof(my_cam_info.conf));
                memcpy(&my_cam_info.conf, conf, sizeof(my_camera_conf_t));
            }
            my_cam_info.conf_valid = 1;
            my_cam_info.conf_need_reload = 0;
        }
        else
        {
            if(!my_cam_info.first_init)
            {
                memset(&my_cam_info.conf, 0, sizeof(my_cam_info.conf));
                my_cam_info.conf.photo_quality = PHOTO_QUALITY_DEFAULT;
                my_cam_info.conf.photo_imagesize_index_in_list = PHOTO_SIZE_DEFAULT;
                my_cam_info.conf.vflip = PHOTO_VFLIP_DEFAULT;
                my_cam_info.conf.hmirror = PHOTO_HMIRROR_DEFAULT;
            }
        }

        #if DEBUG_PRINT_LEVEL_MY_CAMERA_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
        MyCamera_DebugPrint_conf(&my_cam_info.conf, "MyCamera_Init(): before MyCamera_Conf_Adapter(), &my_cam_info.conf");
        #endif

        MyCamera_Conf_Adapter(&my_cam_info.conf, &my_cam_info.conf);

        #if DEBUG_PRINT_LEVEL_MY_CAMERA_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
        MyCamera_DebugPrint_conf(&my_cam_info.conf, "MyCamera_Init(): after MyCamera_Conf_Adapter(), &my_cam_info.conf");
        #endif

        my_cam_info.conf_crc = crc_32((void*)(&my_cam_info.conf), sizeof(my_camera_conf_t));
        ESP_LOGI("MyCamera_Init()", "my_cam_info.conf_crc=0x%8X", my_cam_info.conf_crc);

        if(my_init_camera(&my_cam_info.conf))
        {
            return 1;
        }

        #if DEBUG_PRINT_LEVEL_MY_CAMERA_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
        MyCamera_DebugPrint_conf(&my_cam_info.conf, "MyCamera_Init(): after my_init_camera(), &my_cam_info.conf");
        #endif

        // if(!my_cam_info.first_init)
        {
            MyCamera_Update_SizeInfo(my_cam_info.conf.photo_imagesize_index_in_list);
            MyCamera_Update_CompressRatioInfo(my_cam_info.conf.photo_quality);
            my_cam_info.time_of_exposure =100;
            if(strlen(MYCAMERA_FOCAL_DISTANCE_STR)<sizeof(my_cam_info.focal_distance))
                memcpy(my_cam_info.focal_distance, MYCAMERA_FOCAL_DISTANCE_STR, strlen(MYCAMERA_FOCAL_DISTANCE_STR));
            if(strlen(MYCAMERA_PHOTO_FORMAT_STR)<sizeof(my_cam_info.photo_format))
                memcpy(my_cam_info.photo_format, MYCAMERA_PHOTO_FORMAT_STR, strlen(MYCAMERA_PHOTO_FORMAT_STR));
            if(strlen(MYCAMERA_COLOR_MODE_STR)<sizeof(my_cam_info.color_mode))
                memcpy(my_cam_info.color_mode, MYCAMERA_COLOR_MODE_STR, strlen(MYCAMERA_COLOR_MODE_STR));
        }
        #if DEBUG_PRINT_LEVEL_MY_CAMERA_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
        MyCamera_DebugPrint_info(&my_cam_info, "MyCamera_Init(): after my_init_camera(), &my_cam_info");
        #endif

        my_cam_info.first_init = 1;
        my_cam_info.init_ok = 1;
    }
    return 0;
}

void MyCamera_DebugPrint_conf(my_camera_conf_t* conf, char* tag)
{
    if(tag!=NULL)
    {
        printf("\n\nSoPL(cam conf)------------------------------------------------------------\ntag = %s\n", tag);
    }
    else
    {
        printf("\n\nSoPL(cam conf)------------------------------------------------------------\nunknow tag\n");
    }

    printf("MyCamera_DebugPrint_conf():sizeof(my_camera_conf_t)=%d\n", sizeof(my_camera_conf_t));

    printf("\n--conf:\n");
    printf("conf->photo_quality=%d\n", conf->photo_quality);
    printf("conf->photo_imagesize_index_in_list=%d\n", conf->photo_imagesize_index_in_list);
    printf("conf->photo_imagesize_x=%d\n", conf->photo_imagesize_x);
    printf("conf->photo_imagesize_y=%d\n", conf->photo_imagesize_y);
    printf("conf->vflip=%d\n", conf->vflip);
    printf("conf->hmirror=%d\n", conf->hmirror);
    printf("conf->contrast=%d\n", conf->contrast);
    printf("conf->brightness=%d\n", conf->brightness);
    printf("conf->saturation=%d\n", conf->saturation);
    printf("conf->wb_mode=%d\n", conf->wb_mode);
    printf("conf->special_effect=%d\n", conf->special_effect);

    if(tag!=NULL)
    {
        printf("\ntag = %s\nEoPL(cam conf)------------------------------------------------------------\n\n", tag);
    }
    else
    {
        printf("\nunknow tag\nEoPL(cam conf)------------------------------------------------------------\n\n");
    }
}

void MyCamera_DebugPrint_info(my_cam_info_t* info, char* tag)
{
    
    printf("\n\n------------------------------------------------------------\n\n");

    if(tag!=NULL)
    {
        printf("\n\nSoPL(cam info)------------------------------------------------------------\ntag = %s\n", tag);
    }
    else
    {
        printf("\n\nSoPL(cam info)------------------------------------------------------------\nunknow tag\n");
    }

    printf("MyCamera_DebugPrint_info():sizeof(my_camera_conf_t)=%d\n", sizeof(my_camera_conf_t));

    printf("\n--info:\n");
    printf("info->conf=0x%p\n", &info->conf);
    printf("info->conf_valid=%d\n", info->conf_valid);
    printf("info->conf_need_reload=%d\n", info->conf_need_reload);
    printf("info->conf_crc=%0x08X\n", info->conf_crc);
    printf("info->init_ok=%d\n", info->init_ok);
    printf("info->first_init=%d\n", info->first_init);
    printf("info->pixel_x=%d\n", info->pixel_x);
    printf("info->pixel_y=%d\n", info->pixel_y);
    printf("info->compression_ratio=%d\n", info->compression_ratio);
    printf("info->time_of_exposure=%d\n", info->time_of_exposure);
    printf("info->focal_distance=%s\n", info->focal_distance);
    printf("info->photo_format=%s\n", info->photo_format);
    printf("info->color_mode=%s\n", info->color_mode);

    if(tag!=NULL)
    {
        printf("\ntag = %s\nEoPL(cam info)------------------------------------------------------------\n\n", tag);
    }
    else
    {
        printf("\nunknow tag\nEoPL(cam info)------------------------------------------------------------\n\n");
    }
}

int MyCamera_Test(my_camera_conf_t* conf, int test_num)
{
    mypicture_t mypicture;
    mypicture.data = (camera_fb_t*)calloc(1, sizeof(camera_fb_t));
    #define test_picture_data_buf_size (500*1024)
    mypicture.data->buf = (uint8_t*)calloc(1, test_picture_data_buf_size);

    MyCamera_Reload_Mark();
    TakePicture(conf, &mypicture, 1, 1000);
    vTaskDelay(100 / portTICK_PERIOD_MS);
    mypicture.picture_buf_size = test_picture_data_buf_size;

    time_t test_take_picture_start_time = esp_timer_get_time();
    #define test_take_picture_num   (10)
    for(int i=0; i<test_num; i++)
    {
        TakePicture(NULL, &mypicture, 0, 1000);
    }
    time_t test_take_picture_end_time = esp_timer_get_time();
    float fps = test_take_picture_end_time - test_take_picture_start_time;
    ESP_LOGI("MyCamera_Test()", "test conditions: xy(%d*%d): taken %d pictures used %ldms, fps=%.2f", \
            conf->photo_imagesize_x, \
            conf->photo_imagesize_y, \
            test_num, \
            (test_take_picture_end_time-test_take_picture_start_time)/1000, \
            1000/((fps)/1000/test_num));
    //释放测试照片空间
    if(mypicture.data->buf!=NULL)free(mypicture.data->buf);
    if(mypicture.data!=NULL)free(mypicture.data);

    return 0;
}

int MyCamera_ReInit(my_camera_conf_t* conf)
{
    int ret = 0;

    xSemaphoreTake(MyCamMutexSemaphore, 10000/portTICK_PERIOD_MS);

    if(my_cam_info.init_ok)
    {
        camera_uninstall();
    }
    vTaskDelay(500/portTICK_PERIOD_MS);
    ret = MyCamera_Init(conf);
    
    xSemaphoreGive(MyCamMutexSemaphore);
    return ret;
}

int MyCamera_ReStart(void)
{
    return MyCamera_ReInit(NULL);
}

void MyCamera_Reload_Mark(void)
{
    my_cam_info.conf_need_reload = 1;
}

void MyCamera_LowPowerGpioSet(void)
{
    if(camera_io_need_deinit)
    {
        camera_io_need_deinit = 0;
        
        gpio_reset_pin(CAM_PIN_XCLK);
        gpio_reset_pin(CAM_PIN_SIOD);
        gpio_reset_pin(CAM_PIN_SIOC);
        gpio_reset_pin(CAM_PIN_D3);
        gpio_reset_pin(CAM_PIN_D2);
        gpio_reset_pin(CAM_PIN_D1);
        gpio_reset_pin(CAM_PIN_D0);
        gpio_reset_pin(CAM_PIN_VSYNC);
        gpio_reset_pin(CAM_PIN_HREF);
        gpio_reset_pin(CAM_PIN_PCLK);

        gpio_reset_pin(CAM_PIN_D7);
        gpio_reset_pin(CAM_PIN_D6);
        gpio_reset_pin(CAM_PIN_D5);
        gpio_reset_pin(CAM_PIN_D4);

        gpio_reset_pin(CAM_PIN_PWDN);
    }
}

void MyCamera_Close(void)
{
    // xSemaphoreTake(MyCamMutexSemaphore, 10000/portTICK_PERIOD_MS);
    if(my_cam_info.init_ok)
    {
        camera_uninstall();
	    MyCamera_LowPowerGpioSet();
    }
    // xSemaphoreGive(MyCamMutexSemaphore);
}

void MyCamera_Open(void)
{
    // xSemaphoreTake(MyCamMutexSemaphore, 10000/portTICK_PERIOD_MS);
    if(!my_cam_info.init_ok)
    {
        MyCamera_Init(NULL);
    }
    // xSemaphoreGive(MyCamMutexSemaphore);
}

uint8_t MyCamera_Get_InitState(void)
{
    return my_cam_info.init_ok;
}

uint8_t MyCamera_Get_PhotoSizeIndexInList(void)
{
    return my_cam_info.conf.photo_imagesize_index_in_list;
}

uint8_t MyCamera_Get_PhotoCompressRatio(void)
{
    return my_cam_info.conf.photo_quality;
}

uint8_t MyCamera_Get_PhotoVflip(void)
{
    return my_cam_info.conf.vflip;
}

uint8_t MyCamera_Get_PhotoHmirror(void)
{
    return my_cam_info.conf.hmirror;
}

int MyCamera_Update_SizeInfo(uint8_t new_size)
{
    if(new_size >= FRAMESIZE_INVALID)
    {
        return -1;
    }
    switch (new_size)
    {
        case FRAMESIZE_96X96: my_cam_info.pixel_x = 96; my_cam_info.pixel_y = 96; break;
        case FRAMESIZE_QQVGA: my_cam_info.pixel_x = 160; my_cam_info.pixel_y = 120; break;
        case FRAMESIZE_QCIF: my_cam_info.pixel_x = 176; my_cam_info.pixel_y = 144; break;
        case FRAMESIZE_HQVGA: my_cam_info.pixel_x = 240; my_cam_info.pixel_y = 176; break;
        case FRAMESIZE_240X240: my_cam_info.pixel_x = 240; my_cam_info.pixel_y = 240; break;
        case FRAMESIZE_QVGA: my_cam_info.pixel_x = 320; my_cam_info.pixel_y = 240; break;
        case FRAMESIZE_CIF: my_cam_info.pixel_x = 400; my_cam_info.pixel_y = 296; break;
        case FRAMESIZE_HVGA: my_cam_info.pixel_x = 480; my_cam_info.pixel_y = 320; break;
        case FRAMESIZE_VGA: my_cam_info.pixel_x = 640; my_cam_info.pixel_y = 480; break;
        case FRAMESIZE_SVGA: my_cam_info.pixel_x = 800; my_cam_info.pixel_y = 600; break;
        case FRAMESIZE_XGA: my_cam_info.pixel_x = 1024; my_cam_info.pixel_y = 768; break;
        case FRAMESIZE_HD: my_cam_info.pixel_x = 1280; my_cam_info.pixel_y = 720; break;
        case FRAMESIZE_SXGA: my_cam_info.pixel_x = 1280; my_cam_info.pixel_y = 1024; break;
        case FRAMESIZE_UXGA: my_cam_info.pixel_x = 1600; my_cam_info.pixel_y = 1200; break;
        // 3MP Sensors
        case FRAMESIZE_FHD: my_cam_info.pixel_x = 1920; my_cam_info.pixel_y = 1080; break;
        case FRAMESIZE_P_HD: my_cam_info.pixel_x = 720; my_cam_info.pixel_y = 1280; break;
        case FRAMESIZE_P_3MP: my_cam_info.pixel_x = 864; my_cam_info.pixel_y = 1536; break;
        case FRAMESIZE_QXGA: my_cam_info.pixel_x = 2048; my_cam_info.pixel_y = 1536; break;
        // 5MP Sensors
        case FRAMESIZE_QHD: my_cam_info.pixel_x = 2560; my_cam_info.pixel_y = 1440; break;
        case FRAMESIZE_WQXGA: my_cam_info.pixel_x = 2560; my_cam_info.pixel_y = 1600; break;
        case FRAMESIZE_P_FHD: my_cam_info.pixel_x = 1080; my_cam_info.pixel_y = 1920; break;
        case FRAMESIZE_QSXGA: my_cam_info.pixel_x = 2560; my_cam_info.pixel_y = 1920; break;
        default: break;
    }

    return 0;
}

int MyCamera_Set_PhotoSizeIndexInList(uint8_t new_size)
{
    if(new_size >= FRAMESIZE_INVALID)
    {
        return -1;
    }
    my_cam_info.conf.photo_imagesize_index_in_list = new_size;
    
    MyCamera_Update_SizeInfo(new_size);

    return 0;
}

int MyCamera_Update_CompressRatioInfo(uint8_t new_compressratio)
{
    if(new_compressratio==PHOTO_COMPRESS_RATIO_GRADE_1)my_cam_info.compression_ratio = 1;
    else if(new_compressratio==PHOTO_COMPRESS_RATIO_GRADE_2)my_cam_info.compression_ratio = 2;
    else if(new_compressratio==PHOTO_COMPRESS_RATIO_GRADE_3)my_cam_info.compression_ratio = 3;
    else return -1;

    return 0;
}

int MyCamera_Set_PhotoCompressRatio(uint8_t new_compressratio)
{
    if(new_compressratio!=PHOTO_COMPRESS_RATIO_GRADE_1&&new_compressratio!=PHOTO_COMPRESS_RATIO_GRADE_2&&new_compressratio!=PHOTO_COMPRESS_RATIO_GRADE_3)
    {
        return -1;
    }
    my_cam_info.conf.photo_quality = new_compressratio;

    MyCamera_Update_CompressRatioInfo(new_compressratio);

    return 0;
}

int MyCamera_Set_PhotoVflip(uint8_t vflip_state)
{
    if(vflip_state)
    {
        my_cam_info.conf.vflip = 1;
        return 0;
    }
    my_cam_info.conf.vflip = 0;
    return 0;
}

int MyCamera_Set_PhotoHmirror(uint8_t hmirror_state)
{
    if(hmirror_state)
    {
        my_cam_info.conf.hmirror = 1;
        return 0;
    }
    my_cam_info.conf.hmirror = 0;
    return 0;
}