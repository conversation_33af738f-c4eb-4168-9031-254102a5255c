// 简单的编译测试文件
#include "myesp_sw_uart.h"

// 测试函数，验证API是否正确定义
void test_sw_uart_api(void) {
    // 测试配置结构体
    sw_uart_config_t config = {
        .baud_rate = 9600,
        .data_bits = SW_UART_DATA_8_BITS,
        .parity = SW_UART_PARITY_DISABLE,
        .stop_bits = SW_UART_STOP_BITS_1,
        .flow_ctrl_enable = false
    };
    
    // 测试API调用（不实际执行，只验证编译）
    sw_uart_param_config(SW_UART_NUM_0, &config);
    sw_uart_set_pin(SW_UART_NUM_0, 4, 5, SW_UART_PIN_NO_CHANGE, SW_UART_PIN_NO_CHANGE);
    sw_uart_driver_install(SW_UART_NUM_0, 1024, 0, 0, NULL, 0);
    
    char buffer[100];
    sw_uart_write_bytes(SW_UART_NUM_0, "test", 4);
    sw_uart_read_bytes(SW_UART_NUM_0, buffer, 100, 100);
    sw_uart_flush(SW_UART_NUM_0);
    
    size_t len;
    sw_uart_get_buffered_data_len(SW_UART_NUM_0, &len);
    
    sw_uart_driver_delete(SW_UART_NUM_0);
}
