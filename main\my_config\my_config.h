#ifndef MY_CONFIG_H
#define MY_CONFIG_H

#include "my_ble.h"
#include "my_camera.h"
#include "my_whgm5.h"
#include "my_workmode.h"
#include "my_ota.h"
#include "my_nvs_ds.h"
#include "mywifi.h"
#include "my_whgm5.h"
#include "my_tcp_client.h"
#include "my_air780eg.h"



#define TASK_CREATE_TYPE_DYNAMIC    1
#define TASK_CREATE_TYPE_STATIC     2


#define MY_IOT_BOX_DEVICE_MODEL "SWQZ-radar24G-3"

#define MY_IOT_BOX_DEVICE_TYPE  devicetype_evgchanneldoor_btbox
#define MY_IOT_BOX_SW_VERCODE   1



//factory
typedef struct
{
    int dev_state;
    char factory_factoryDate[16];
    char reserve1[1];
    int poweron_count;
    int runtime_since_poweron;
    char mac[6];
    char reserve2[1];
    char imei[32];
    char reserve3[1];
    char iccid[32];
    char reserve4[1];
    my_ota_info_t my_ota_info_local;
    my_ota_info_t my_ota_info_net;
    uint8_t ota_check_type;
    uint16_t devType;
    char devModel[40];
    char reserve6[1];
    char devId[20];
    char reserve[1];
    char http_ota_url[512];
}myconf_factory_t;

//asl
typedef struct
{
    uint16_t appid;
    uint8_t loginflag;
    uint32_t dic;
    uint32_t token;
    uint32_t last_sync_param_timestamp;
    uint32_t sync_param_effect_time;
}myconf_asl_t;

//tsfc
typedef struct
{
    uint32_t frame_max_size_send;
    uint32_t frame_max_size_rcv;
    uint32_t sendFail_max_repeat;
    uint8_t aesKey[16];
}myconf_tsfc_t;

//net

typedef struct
{
    my_tcp_conf_t conf_tcp1;
    my_tcp_conf_t conf_tcp2;
    my_tcp_conf_t conf_tcp3;
    my_tcp_conf_t conf_tcp4;
    my_tcp_conf_t conf_ota;
}myconf_net_t;


//workmode
typedef struct
{
    my_workmode_conf_t conf;
    int imageReportInterval;
    int measReportInterval;
    int16_t measReportMode;
    uint8_t relay1_mode;
    int relay1_Xmode_keep_time;
    int channeldoor_mode;
}myconf_workmode_t;

// #if MY_BLE_FUNC
typedef enum
{
    my_ble_adv_type_imei = 1,
    my_ble_adv_type_iccid = 2,
    my_ble_adv_type_mac = 3,
    my_ble_adv_type_device_id_out = 4,
    my_ble_adv_type_custom = 5
}myconf_ble_adv_type_t;
typedef struct
{
    my_ble_conf_t conf;
    int adv_type;
}myconf_ble_t;
// #endif


//cam
typedef struct
{
    my_camera_conf_t conf;
    uint8_t pickpicture_switch;
}myconf_cam_t;

typedef struct
{
    uint8_t poweroff_alarm_enable_state;
    uint8_t shutdown_after_poweroffAlarm;
}myconf_power_t;

typedef struct
{
    my_wifi_conf_t conf;
}myconf_wifi_t;

#if MY_TCP_CLIENT_FUNC
typedef struct
{
    my_tcp_client_conf_t conf;
}myconf_tcp_client_t;
#endif

typedef struct
{
    uint8_t auto_report_switch;
    int auto_report_interval;
    char longitude[32];
    char latitude[32];
}myconf_gnss_t;




typedef enum
{
    appid_default = 0x0000,
    appid_VPMask = 0x0001,
    appid_OTA = 0x0002,
    appid_IoTServer = 0x0003,
    appid_G60 = 0x0004,
    appid_unknown = 0x0005,
    appid_single_old_home = 0x0006,
    appid_EVG = 0x0007
}myconf_tsfc_appid_t;

typedef enum
{
    devicetype_box2_0=0x01,
    devicetype_box2_5=0x02,
    devicetype_box2_75=0x03,
    devicetype_box2_8=0x04,
    devicetype_radarbox=0x05,
    devicetype_evgchanneldoor_btbox=0x07,
}myconf_devicetype_t;



typedef struct
{
    myconf_asl_t asl;

    myconf_tsfc_t tsfc;

    myconf_net_t net;

    myconf_workmode_t workmode;

    // #if MY_BLE_FUNC
    myconf_ble_t ble;
    // #endif

    myconf_power_t power;

    myconf_factory_t factory;

    myconf_wifi_t wifi;

    myconf_lte_t lte;

    #if MY_TCP_CLIENT_FUNC
    myconf_tcp_client_t tcp_client;
    #endif

    myconf_gnss_t gnss;
}my_config_nvs_t;

#define GCT_NVS_FORM_NAME	"NVS_GCT"
#define GCT_NVS_KEY_NAME	"NVS_GCT_1"

my_nvs_d2link_list_item_t* MyConfig_Get_ConfigItem_FromId(int id);

/**
 * @brief 从NVS中加载所有配置
 * @param conf 如果conf不为空，则conf存储加载的配置表
 */
my_config_nvs_t* MyConfig_LoadFromNVS(void);
my_config_nvs_t* MyConfig_ResetAllConf_To_Default(void);

my_config_nvs_t* MyConfig_GetGCT_Cur_p(void);

my_config_nvs_t* MyConfig_GetGCT_NVS_p(void);

/**
 * @brief 获取配置表
 * @param conf 如果conf不为空，则conf存储加载的配置表
 * @return 成功0，失败-1(saving为空)
 */
int MyConfig_GetGCT(my_config_nvs_t* saving);

int MyConfig_GetConfItem_Modifiable(int param_id);

/**
 * @brief 保存所有配置到NVS
 * @return 0
 */
int MyConfig_SavingAllConf_To_NVS(void);

int MyConfig_DebugPrint_my_config_nvs_t_All(char* tag);


int MyConfig_Modify_Asl_appId(uint16_t new_value, uint8_t saving_to_nvs);
int MyConfig_Modify_Asl_loginFlag(uint8_t new_value, uint8_t saving_to_nvs);
int MyConfig_Modify_Asl_dic(uint32_t new_value, uint8_t saving_to_nvs);
int MyConfig_Modify_Asl_token(uint32_t new_value, uint8_t saving_to_nvs);
int MyConfig_Modify_Asl_last_sync_param_timestamp(uint32_t new_value, uint8_t saving_to_nvs);
int MyConfig_Modify_Asl_sync_param_effect_time(uint32_t new_value, uint8_t saving_to_nvs);

int MyConfig_Modify_Tsfc_aesKey(uint8_t* new_data, uint16_t new_data_len, uint8_t saving_to_nvs);

int MyConfig_Modify_Net_ipv4Address(uint8_t ip_address_type, uint8_t* new_value, int new_value_len, uint8_t saving_to_nvs);
int MyConfig_Modify_Net_ipv4AddressType(uint8_t newvalue, uint8_t saving_to_nvs);
int MyConfig_Modify_Net_serverPort(uint32_t new_value, uint8_t saving_to_nvs);
int MyConfig_Modify_Net_otaIpv4Address(uint8_t* new_value, uint8_t saving_to_nvs);
int MyConfig_Modify_Net_otaServerPort(uint32_t new_value, uint8_t saving_to_nvs);

int MyConfig_Modify_Workmode_mode(uint8_t new_value, uint8_t saving_to_nvs);
int MyConfig_Modify_Debug_mode(uint8_t new_value, uint8_t saving_to_nvs);
int MyConfig_Modify_Workmode_timePointer1(int start_time, int keep_time, uint8_t saving_to_nvs);
int MyConfig_Modify_Workmode_timePointer2(int start_time, int keep_time, uint8_t saving_to_nvs);
int MyConfig_Modify_Workmode_timePointer3(int start_time, int keep_time, uint8_t saving_to_nvs);
int MyConfig_Modify_Workmode_timePointer4(int start_time, int keep_time, uint8_t saving_to_nvs);
int MyConfig_Modify_Workmode_timePointer5(int start_time, int keep_time, uint8_t saving_to_nvs);
int MyConfig_Modify_Workmode_vrctimePointer1(int start_time, int keep_time, uint8_t saving_to_nvs);
int MyConfig_Modify_Workmode_vrctimePointer2(int start_time, int keep_time, uint8_t saving_to_nvs);
int MyConfig_Modify_Workmode_vrctimePointer3(int start_time, int keep_time, uint8_t saving_to_nvs);
int MyConfig_Modify_Workmode_vrctimePointer4(int start_time, int keep_time, uint8_t saving_to_nvs);
int MyConfig_Modify_Workmode_vrctimePointer5(int start_time, int keep_time, uint8_t saving_to_nvs);
int MyConfig_Modify_Workmode_timermodeSleepTime(int newvalue, uint8_t saving_to_nvs);
int MyConfig_Modify_Workmode_imageReportInterval(int newvalue, uint8_t saving_to_nvs);
int MyConfig_Modify_Workmode_measReportInterval(int newvalue, uint8_t saving_to_nvs);
int MyConfig_Modify_Workmode_measReportMode(int newvalue, uint8_t saving_to_nvs);
int MyConfig_Modify_Workmode_Relay1_mode(int newvalue, uint8_t saving_to_nvs);
int MyConfig_Modify_Workmode_Relay1_Xmode_keeptime(int newvalue, uint8_t saving_to_nvs);
int MyConfig_Modify_Workmode_channeldoor_mode(int newvalue, uint8_t saving_to_nvs);

// #if MY_BLE_FUNC
int MyConfig_Modify_BLE_enableState(uint8_t newvalue, uint8_t saving_to_nvs, uint8_t eff_im);
int MyConfig_Modify_BLE_CurState(uint32_t newvalue);
int MyConfig_Modify_BLE_name(char const* newstr, uint8_t newstr_len, uint8_t saving_to_nvs, uint8_t eff_im);
int MyConfig_Modify_BLE_advType(uint8_t newvalue, uint8_t saving_to_nvs, uint8_t eff_im);
int MyConfig_Modify_BLE_advData(char const* newdata, uint8_t newdata_len, uint8_t saving_to_nvs, uint8_t eff_im);
int MyConfig_Modify_BLE_advDateLen(uint8_t newvalue, uint8_t saving_to_nvs, uint8_t eff_im);
int MyConfig_Modify_BLE_service_uuid_a(uint16_t newvalue, uint8_t saving_to_nvs, uint8_t eff_im);
int MyConfig_Modify_BLE_char_uuid_a(uint16_t newvalue, uint8_t saving_to_nvs, uint8_t eff_im);
int MyConfig_Modify_BLE_service_uuid_b(uint16_t newvalue, uint8_t saving_to_nvs, uint8_t eff_im);
int MyConfig_Modify_BLE_char_uuid_b(uint16_t newvalue, uint8_t saving_to_nvs, uint8_t eff_im);
int MyConfig_Modify_BLE_mac(char const* newstr, uint8_t newstr_len, uint8_t saving_to_nvs, uint8_t eff_im);
// #endif

int MyConfig_Modify_power_poweroff_alarm_enable_state(uint8_t newvalue, uint8_t saving_to_nvs);
int MyConfig_Modify_power_shutdown_after_poweroffAlarm(uint8_t newvalue, uint8_t saving_to_nvs);
int MyConfig_Modify_ExternalPowerstate(uint8_t newvalue);

int MyConfig_Modify_factory_devstate(uint8_t new_value, uint8_t saving_to_nvs);
int MyConfig_Modify_factory_factoryDate(char const* newstr, uint8_t newstr_len, uint8_t saving_to_nvs);
int MyConfig_Modify_factory_poweron_reason(uint8_t newvalue);
int MyConfig_Modify_factory_poweron_count(uint32_t newvalue, uint8_t saving_to_nvs);
int MyConfig_Modify_factory_runtime_since_poweron(uint32_t newvalue);
int MyConfig_Modify_factory_mac(char const* newdata, uint8_t newdata_len, uint8_t saving_to_nvs);
int MyConfig_Modify_factory_IMEI(char const* newstr, uint8_t newstr_len, uint8_t saving_to_nvs);
int MyConfig_Modify_factory_ICCID(char const* newstr, uint8_t newstr_len, uint8_t saving_to_nvs);
int MyConfig_Modify_factory_verCode_local(uint32_t newvalue, uint8_t saving_to_nvs);
int MyConfig_Modify_factory_vername_local(char const* newstr, uint8_t newdata_len, uint8_t saving_to_nvs);
int MyConfig_Modify_factory_verfilename_local(char const* newstr, uint8_t newdata_len, uint8_t saving_to_nvs);
int MyConfig_Modify_factory_verCode_net(uint32_t newvalue, uint8_t saving_to_nvs);
int MyConfig_Modify_factory_vername_net(char const* newstr, uint8_t newdata_len, uint8_t saving_to_nvs);
int MyConfig_Modify_factory_verfilename_net(char const* newstr, uint8_t newdata_len, uint8_t saving_to_nvs);
int MyConfig_Modify_factory_ota_check_type(uint8_t new_value, uint8_t saving_to_nvs);
int MyConfig_Modify_factory_devType(uint16_t newvalue, uint8_t saving_to_nvs);
int MyConfig_Modify_factory_devModel(char const* newstr, uint8_t newstr_len, uint8_t saving_to_nvs);
int MyConfig_Modify_factory_devId(char const* newstr, uint8_t newdata_len, uint8_t saving_to_nvs);
int MyConfig_Modify_factory_otaurl(char const* newstr, uint8_t newdata_len, uint8_t saving_to_nvs);

int MyConfig_Modify_lte_baudrate(int newvalue, uint8_t saving_to_nvs);
#endif