#ifndef MY_ASL_H
#define MY_ASL_H


#define MY_ASL_FUNC  1

#include "my_config.h"
#include "my_tsfc.h"

#if MY_ASL_FUNC

#include <lwip/netdb.h>

#define MY_ASL_DEBUG 1

#define MyASL_Send_TASK_CREATE_TYPE    TASK_CREATE_TYPE_DYNAMIC

#if MyASL_Send_TASK_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
#define MyASL_Send_Task_COREID    1
#define MyASL_Send_Task_priority  5
#define MyASL_Send_Task_task_stack_size   4096
#elif MyASL_Send_TASK_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
#define MyASL_Send_Task_COREID    1
#define MyASL_Send_Task_priority  14
#define MyASL_Send_Task_task_stack_size   3072
#endif

#define MyASL_Rcv_TASK_CREATE_TYPE    TASK_CREATE_TYPE_DYNAMIC

#if MyASL_Rcv_TASK_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
#define MyASL_Rcv_Task_COREID    1
#define MyASL_Rcv_Task_priority  5
#define MyASL_Rcv_Task_task_stack_size   4096
#elif MyASL_Rcv_TASK_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
#define MyASL_Rcv_Task_COREID    1
#define MyASL_Rcv_Task_priority  19
#define MyASL_Rcv_Task_task_stack_size   3072
#endif




#define MY_ASL_PD   1


typedef enum
{
    dev_reg_req=0x01,                       //设备注册请求
    dev_reg_res=0x02,                       //设备注册响应
    dev_login_req=0x03,                     //设备登陆请求
    dev_login_res=0x04,                     //设备登陆响应
    net_time_sync_req=0x05,                 //网络时钟同步请求
    net_time_sync_res=0x06,                 //网络时钟同步响应
    dev_param_req=0x11,                     //设备参数请求
    dev_param_res=0x12,                     //设备参数响应
    dev_param_update_indication=0x13,       //设备参数更新指示
    dev_param_update_res=0x14,              //设备参数更新响应
    dev_param_query_indication=0x15,        //设备参数查询指示
    dev_param_query_res=0x16,               //设备参数查询响应
    dev_param_report=0x17,                  //设备参数报告
    dev_param_report_res=0x18,              //设备参数报告响应
    dev_ctrl_param_indication=0x19,         //设备控制参数指示
    dev_ctrl_param_res=0x1A,                //设备控制参数响应
    dev_measure_report=0X21,                //设备测量报告
    dev_event_report=0x23,                  //设备事件报告
    dev_event_res=0x24,                     //设备事件响应
    file_upload_req=0x31,                   //文件上传请求
    file_upload_res=0x32,                   //文件上传响应
    file_down_req=0x33,                     //文件下载请求
    file_down_res=0x34,                     //文件下载响应
    data_direct_tsf_req=0x35,               //数据直接传输请求
    data_direct_tsf_res=0x36,               //数据直接传输响应
    file_tsf_finish=0x37,                   //文件传输完成
    datablock_tsf=0x38                      //数据块传输
}my_asl_data_type_t;

typedef enum
{
    aslrc_Success=0x0000,                         //成功接收和处理
    aslrc_Fail=0x0001,                            //未知异常
    aslrc_PartialSuccess=0x0002,                  //部分成功

    aslrc_MessageTypeUnrecognized=0x0010,         //未识别的消息类型
    aslrc_VerCodeUnsupported=0x0011,              //协议版本号不支持
    aslrc_InsufficientBodyLength=0x0013,          //消息体长度不足

    aslrc_DeviceTypeUnkown=0x0020,                //设备类型未定义
    aslrc_DICUnrecognized=0x0021,                 //未识别的设备识别码
    aslrc_TokenWrong=0x0022,                      //授权码错误，可能过期
    aslrc_DeviceUnregistered=0x0023,              //设备未注册
    aslrc_LoginFail=0x0024,                       //登录失败，可能密码错误
    aslrc_DeviceIdChanged=0x0025,                 //设备ID与注册时不同
    aslrc_DeviceDisabled=0x0026,                  //设备禁用，禁止接入

    aslrc_ReportTypeunrecognized=0x0030,          //未识别的测量报告类型
    aslrc_InsufficientReportLength=0x0031,        //测量报告长度不足

    aslrc_FileTypeUnrecognized=0x0040,            //未识别的文件类型
    aslrc_FileLengthMismatch=0x0041               //文件长度不匹配
}my_asl_resultcode_t;

typedef enum
{
    Downlink=0x00,
    Uplink=0x01
}my_asl_ud_t;

typedef struct
{
    uint8_t valid;
    my_asl_data_type_t type;
    void* data;
    uint32_t len;
    void* taskhandle;
}my_asl_rcvdata_cache_t;

typedef struct
{
    uint8_t type;       //消息类型，参见my_asl_data_type_t
    uint8_t vercode;    //协议版本
    uint16_t len;       //消息体部分数据长度
}my_asl_head_t;

typedef struct
{
    uint32_t dic;   //设备识别码
    uint32_t token; //设备访问授权码
}dev_id_info_t;

typedef struct
{
    uint64_t server_rcv_time;
    uint64_t server_send_time;
}time_sync_info_t;

typedef struct
{
    uint16_t block_offset;
    uint16_t block_cache;
}my_asl_file_bpct_info_t;

typedef enum
{
    fileadd_rt_upload_image_file = 0x0100,
    fileadd_rt_upload_log_file = 0x0200,
    fileadd_rt_download_package_file = 0x0300,
    fileadd_rt_download_config_file = 0x0400,
}my_asl_file_additional_report_type_t;

typedef struct
{
    uint16_t report_type;
    uint16_t report_len;
    char* report_data;
}my_asl_file_additional_t;

typedef enum
{
    rd_rt_upload_image_file = 0x01,
    rd_rt_upload_log_file = 0x02,
    rd_rt_download_package_file = 0x03,
    rd_rt_download_config_file = 0x04,
}my_asl_report_data_report_type_t;

typedef struct
{
    uint8_t report_data_type;
    uint16_t width;
    uint16_t height;
}my_asl_fileupload_image_report_data_t;

typedef struct
{
    uint8_t report_data_type;
}my_asl_fileupload_log_report_data_t;

typedef struct
{
    uint8_t report_data_type;
    char filename[100];
}my_asl_download_package_report_data_t;

typedef struct
{
    uint8_t report_data_type;
    char filename[100];
}my_asl_download_config_report_data_t;


typedef struct
{
    uint8_t file_type;
    uint8_t file_sn;
    uint32_t file_crc;
    uint32_t file_len;
}my_asl_file_base_info_t;

typedef enum
{
    my_asl_report_type_ota=0x0064,
}my_asl_report_type_t;




int MyASL_ServicePort(my_asl_data_type_t service_type, void* data, int data_len, void** saving, int* saving_len, int timeout, int wait_res);


/**
 * @brief 应用层调用此函数向应用服务层发送一个应用层原始数据
 * @param type 数据类型，参见“三旺奇智IoT设备通信协议设计5.2.3.2”
 * @param sn 应用层文件号或消息编号
 * @param data 应用层原始数据
 * @param len data数据大小
 * @param timeout 队列超时，单位ms
 * @param datablock 0非阻塞发送，1阻塞发送
 * @return 0成功入列，1入列失败
 */
uint8_t MyASL_Send(my_asl_data_type_t type, uint8_t sn, char* data, uint32_t len, uint32_t timeout, uint8_t id, uint8_t datablock);

#define MY_ASL_QUEUE_LEN WHGM5_QUEUE_LEN
#define MY_ASL_PACK_SIZE_MAX    TSFC_FRAME_DATA_SIZE_MAX
typedef struct
{
    my_asl_data_type_t my_asl_data_type;
    uint8_t sn;
    uint8_t id;
	uint8_t valid;
	uint64_t time_stamp;
    uint32_t len;
	char* data;         //应用层数据
}my_asl_queue_t;

/**
 * @brief 初始化应用层传输服务
 * @return 0:成功
 */
uint8_t MyASL_Init(void);

void MyAsl_ECCB_Register(int (*callback_func)(int));

typedef enum
{
    txt_file=0x01,
    xml_file=0x02,
    bin_file=0x03,
    img_file_jpg=0x04,
    img_file_png=0x05,
    datablock=0x06,
}my_asl_file_type_t;


#if MY_ASL_DEBUG
//-----------------------------------------------------
//debug

//-----------------------------------------------------
#endif

#endif
#endif