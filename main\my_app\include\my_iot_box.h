#ifndef MY_IOT_BOX_H
#define MY_IOT_BOX_H


#define MY_IOT_BOX_FUNC  1

#include "my_config.h"

#if MY_IOT_BOX_FUNC

#include <lwip/netdb.h>

#define MY_IOT_BOX_FUNC_DEBUG 0

#define My_IotBox_TimerTask_CREATE_TYPE    TASK_CREATE_TYPE_DYNAMIC

#if My_IotBox_TimerTask_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
#define My_IotBox_TimerTask_COREID    1
#define My_IotBox_TimerTask_priority  5
#define My_IotBox_TimerTask_task_stack_size   4096
#elif My_IotBox_TimerTask_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
#define My_IotBox_TimerTask_COREID    1
#define My_IotBox_TimerTask_priority  21
#define My_IotBox_TimerTask_task_stack_size   3072
#endif

#define My_IotBox_EventRcvTask_CREATE_TYPE    TASK_CREATE_TYPE_DYNAMIC

#if My_IotBox_EventRcvTask_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
#define My_IotBox_EventRcvTask_COREID    1
#define My_IotBox_EventRcvTask_priority  5
#define My_IotBox_EventRcvTask_task_stack_size   4096
#elif My_IotBox_EventRcvTask_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
#define My_IotBox_EventRcvTask_COREID    1
#define My_IotBox_EventRcvTask_priority  21
#define My_IotBox_EventRcvTask_task_stack_size   2048
#endif

#define My_IotBox_BlecTask_CREATE_TYPE    TASK_CREATE_TYPE_DYNAMIC

#if My_IotBox_BlecTask_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
#define My_IotBox_BlecTask_COREID    1
#define My_IotBox_BlecTask_priority  5
#define My_IotBox_BlecTask_task_stack_size   4096
#elif My_IotBox_BlecTask_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
#define My_IotBox_BlecTask_COREID    1
#define My_IotBox_BlecTask_priority  21
#define My_IotBox_BlecTask_task_stack_size   4096
#endif


#define My_IotBox_IotdaemonTask_CREATE_TYPE    TASK_CREATE_TYPE_DYNAMIC

#if My_IotBox_IotdaemonTask_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
#define My_IotBox_IotdaemonTask_COREID    1
#define My_IotBox_IotdaemonTask_priority  5
#define My_IotBox_IotdaemonTask_task_stack_size   4096
#elif My_IotBox_IotdaemonTask_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
#define My_IotBox_IotdaemonTask_COREID    1
#define My_IotBox_IotdaemonTask_priority  21
#define My_IotBox_IotdaemonTask_task_stack_size   3072
#endif

typedef struct
{
    uint32_t last_takepicture_time;
    uint8_t login_state;
    uint8_t ota_check_after_poweron;
    uint8_t ota_checked_upgrade_time;
    uint8_t ota_info_sync_once;
    time_t last_enter_energy_mode_time;
    uint8_t check_poweron_lock;
    uint8_t check_poweroff_lock;
    uint8_t last_workmode;
    mypicture_t mypicture;
    uint8_t up_link_busy;
    uint8_t up_link_lock;
    uint8_t link_busy;
    uint32_t last_checkrti_time;
    time_t last_radar_regular_report_time;
    time_t next_check_ota_time;
    uint32_t ota_check_err_count;
    time_t last_time_sync_time;
}my_iotbox_info_t;

#define MY_IOTBOX_TIMESYNC_VALIDTIME    (1*3600)//单位：秒，对时的时间信息有效期，距上一次对时超过这个时间将再次对时


#define MY_IOTBOX_devPI_factory_ota_vercode_default (1)
#define MY_IOTBOX_devPI_factory_ota_vername_default "V1.1"
#define MY_IOTBOX_devPI_factory_ota_filename_default "SWQZ_EVG_01_V1_1.bin"


int My_IotBox_Init(void);

void MyLowPower_Init_BeforeSleep(void);
void MyLowPower_Init_AfterWakeup(int wakeup_reason);


#if MY_IOT_BOX_FUNC_DEBUG
//-----------------------------------------------------
//debug
void MyDebug_PrintMemInfo(char* name);
//-----------------------------------------------------
#endif

#endif
#endif