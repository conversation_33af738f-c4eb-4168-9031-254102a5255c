#ifndef LTE_AT_TABLE_H
#define LTE_AT_TABLE_H


#include <lwip/netdb.h>

#define LTE_CMD_LENGTH_LIMIT    256
typedef struct
{
    uint8_t cmd_id;//编号：对应命令编号，用于跳转
    char cmdstr[LTE_CMD_LENGTH_LIMIT];//命令：向LTE发送的命令
    char keystr[LTE_CMD_LENGTH_LIMIT];//关键串：一个命令发送完成后，需要等待LTE回复的内容（包含）
    uint8_t sucnext;//成功：此命令成功（LTE应答包括关键串）后，下一步要跳转的编号
    uint8_t failnext;//失败：同“成功”
    uint16_t pridelay;//前：发送此命令前的延时，单位：ms
    uint16_t lagdelay;//后：发送此命令，且成功得到应答后的延时，单位：ms
    uint16_t faillagdelay;//失败延时：此命令发送失败或无应答或应答不正确后的延时，单位：ms
    uint16_t timeout_count;//耗时：一个命令发送完成后，若LTE一直无应答时的最长等待时间，单位：ms
    uint8_t keystrexist;//关键：此命令需不需要LTE应答，1：需要应答，0：不需要应答
    uint8_t failedcount;//这条命令的失败次数
    uint8_t failedlimit;//这条命令的失败次数上限，达到将会复位LTE、ESP32
}AT_Config_Routine_t;

extern AT_Config_Routine_t lte_config_check_chip_matching[2];
extern AT_Config_Routine_t lte_config_check_networkmode_tcp[2];
extern AT_Config_Routine_t lte_config_check_networkmode_http[2];
extern AT_Config_Routine_t lte_config_check_hearten[2];
extern AT_Config_Routine_t lte_config_check_tcp[6];
extern AT_Config_Routine_t lte_config_check_http[6];
extern AT_Config_Routine_t lte_config_check_http_method_GET[2];
extern AT_Config_Routine_t lte_config_check_http_method_POST[2];
extern AT_Config_Routine_t lte_config_check_saving[1];
extern AT_Config_Routine_t lte_config_check_restart[1];
extern AT_Config_Routine_t lte_config_check_uart[2];
extern AT_Config_Routine_t lte_config_check_http_server[2];
extern AT_Config_Routine_t lte_config_check_http_head[2];
extern AT_Config_Routine_t lte_config_check_http_url[2];
extern AT_Config_Routine_t lte_config_check_http_head_filter_enable[2];
extern AT_Config_Routine_t lte_config_check_http_head_filter_disable[2];
extern AT_Config_Routine_t lte_config_check_http_timeout[2];
extern AT_Config_Routine_t lte_config_check_test[1];

extern AT_Config_Routine_t lte_cmd_mode_routine[3];
extern AT_Config_Routine_t lte_data_mode_routine[1];
extern AT_Config_Routine_t lte_restart_routine[1];

#endif