#ifndef MY_ADXL345_H
#define MY_ADXL345_H

#define USE_ADXL345_FUNC    1
#if USE_ADXL345_FUNC

#include <lwip/netdb.h>


typedef struct{
    int unread_mark;
    int move_event;
    int dev_attitude_err;

    int16_t data_x;
    int16_t data_y;
    int16_t data_z;
    double angel_x;
    double angel_y;
    double angel_z;
}my_adxl345_info_t;

void My_ADXL345_Init(void);
my_adxl345_info_t* MyADXL345_Get_ADXL345_Info(void);

#endif

#endif