# 1. 注册
**数据格式(请求)**
```json
    {
        "type": "regist",
        "data": {
            "deviceNo": "",
            "iccid": "",
            "imei": "",
            "ble": "",
            "model": "",
            "userName": "",
            "password": ""
        }
    }
```
**数据格式(响应格式)**
```json
    {
        "type": "regist_response",
        "data": {
            "password": ""
        }
    }
```

# 2. 登录
**数据格式(请求)**
```json
    {
        "type": "auth",
        "data": {
            "password": ""
        }
    }
```
**数据格式(响应)**
```json
    {
        "type": "auth_response",
        "data": {
            "deviceNo": ""
        }
    }
```

# 3. 心跳
**数据格式(请求)**
```json
    {
        "type": "heartbeat",
        "data": {
            "deviceNo": ""
        }
    }
```
**数据格式(响应)**
```json
    {
        "type": "heartbeat_response",
        "data": {
            "deviceNo": ""
        }
    }
```

# 4. 数据上报
**数据格式(请求)**
```json
    {
        "type": "data",
        "data": {
            "deviceNo": "",
            "...": ""
        }
    }
```
**数据格式(响应)**
```json
    {
        "type": "data_response",
        "data": {
            "deviceNo": ""
        }
    }
```
