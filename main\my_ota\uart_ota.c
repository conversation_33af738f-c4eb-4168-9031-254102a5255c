#include "my_config.h"
#include "uart_ota.h"
#include "my_debug.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/timers.h"

#include "esp_system.h"
#include "esp_log.h"
#include "driver/uart.h"
#include "driver/gpio.h"
#include <esp_ota_ops.h>
#include <errno.h>

#include <stdio.h>
#include "string.h"
#include "stdlib.h"

#if UART_OTA_FUNC


#if defined MY_DEBUG_PRINT_FUNC
#define DEBUG_PRINT_LEVEL_my_uart_ota_PRINT   MY_DEBUG_PRINT_LEVEL_0
#else
#define DEBUG_PRINT_LEVEL_my_uart_ota_PRINT   DEBUG_PRINT_LEVEL_0
#endif


xTaskHandle simple_ota_example_task_Handle = NULL;

TimerHandle_t   UartOtaOutTime_Handle = NULL;
#define UART_OTA_TIMEOUT    (15*60*1000)//ms


static uint8_t uart_ota_exist = 0;

//-------------------------------------------------------------------

typedef void *esp_uart_ota_handle_t;

#define DEFAULT_OTA_BUF_SIZE 1000
static const char *TAG = "uart_ota";

#define OTA_SIZE_UNKNOWN 0xffffffff

typedef enum {
    ESP_UART_OTA_INIT,
    ESP_UART_OTA_BEGIN,
    ESP_UART_OTA_IN_PROGRESS,
    ESP_UART_OTA_SUCCESS,
} esp_uart_ota_state;

struct esp_uart_ota_handle {
    esp_ota_handle_t update_handle;
    const esp_partition_t *update_partition;
    char *ota_upgrade_buf;
    size_t ota_upgrade_buf_size;
    int binary_file_len;
    esp_uart_ota_state state;
};
typedef struct esp_uart_ota_handle esp_uart_ota_t;


#define ESP_ERR_UART_OTA_BASE            (0x9000)
#define ESP_ERR_UART_OTA_IN_PROGRESS     (ESP_ERR_UART_OTA_BASE + 1)  /* OTA operation in progress */




static esp_err_t esp_uart_ota_write(esp_uart_ota_t *uart_ota_handle, const void *buffer, size_t buf_len)
{
    if (buffer == NULL || uart_ota_handle == NULL) {
        return ESP_FAIL;
    }
    esp_err_t err = esp_ota_write(uart_ota_handle->update_handle, buffer, buf_len);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Error: esp_ota_write failed! err=0x%d", err);
    } else {
        uart_ota_handle->binary_file_len += buf_len;
        ESP_LOGD(TAG, "Written image length %d", uart_ota_handle->binary_file_len);
        err = ESP_ERR_UART_OTA_IN_PROGRESS;
    }
    return err;
}

esp_err_t esp_uart_ota_begin(esp_uart_ota_handle_t *handle)
{
    esp_err_t err;

    if (handle == NULL) {
        ESP_LOGE(TAG, "esp_uart_ota_begin: Invalid argument");
        if (handle) {
            *handle = NULL;
        }
        return ESP_ERR_INVALID_ARG;
    }

    esp_uart_ota_t *uart_ota_handle = calloc(1, sizeof(esp_uart_ota_t));
    if (!uart_ota_handle) {
        ESP_LOGE(TAG, "Couldn't allocate memory to upgrade data buffer");
        *handle = NULL;
        return ESP_ERR_NO_MEM;
    }

    uart_ota_handle->update_partition = NULL;
    ESP_LOGI(TAG, "Starting OTA...");
    uart_ota_handle->update_partition = esp_ota_get_next_update_partition(NULL);
    if (uart_ota_handle->update_partition == NULL) {
        ESP_LOGE(TAG, "Passive OTA partition not found");
        err = ESP_FAIL;
        goto uart_ota_cleanup;
    }
    ESP_LOGI(TAG, "Writing to partition subtype %d at offset 0x%x",
        uart_ota_handle->update_partition->subtype, uart_ota_handle->update_partition->address);

    const int alloc_size = DEFAULT_OTA_BUF_SIZE;
    uart_ota_handle->ota_upgrade_buf = (char *)malloc(alloc_size);
    if (!uart_ota_handle->ota_upgrade_buf) {
        ESP_LOGE(TAG, "Couldn't allocate memory to upgrade data buffer");
        err = ESP_ERR_NO_MEM;
        goto uart_ota_cleanup;
    }
    uart_ota_handle->ota_upgrade_buf_size = alloc_size;

    uart_ota_handle->binary_file_len = 0;
    *handle = (esp_uart_ota_handle_t)uart_ota_handle;
    uart_ota_handle->state = ESP_UART_OTA_BEGIN;
    return ESP_OK;

uart_ota_cleanup:
    free(uart_ota_handle);
    *handle = NULL;
    return err;
}

static char uart_ota_sec_buff[DEFAULT_OTA_BUF_SIZE];
static int volatile uart_ota_sec_down_finished = 0;
static int volatile uart_ota_sec_write_finished = 0;
static int volatile uart_ota_sec_size = 0;
static int volatile uart_ota_write_alldata_finished = 0;

int esp_uart_ota_data_read(char *buffer, int len)
{
    while(!uart_ota_sec_down_finished)
    {
        vTaskDelay(10/portTICK_PERIOD_MS);
    }
    uart_ota_sec_down_finished = 0;
    memcpy(buffer, uart_ota_sec_buff, len);
    return uart_ota_sec_size;
}

esp_err_t esp_uart_ota_perform(esp_uart_ota_handle_t uart_ota_handle)
{
    esp_uart_ota_t *handle = (esp_uart_ota_t *)uart_ota_handle;
    if (handle == NULL) {
        ESP_LOGE(TAG, "esp_uart_ota_perform: Invalid argument");
        return ESP_ERR_INVALID_ARG;
    }
    if (handle->state < ESP_UART_OTA_BEGIN) {
        ESP_LOGE(TAG, "esp_uart_ota_perform: Invalid state");
        return ESP_FAIL;
    }

    esp_err_t err;
    int data_read;
    switch (handle->state) {
        case ESP_UART_OTA_BEGIN:
            err = esp_ota_begin(handle->update_partition, OTA_SIZE_UNKNOWN, &handle->update_handle);
            if (err != ESP_OK) {
                ESP_LOGE(TAG, "esp_ota_begin failed (%s)", esp_err_to_name(err));
                return err;
            }
            handle->state = ESP_UART_OTA_IN_PROGRESS;

            if (handle->binary_file_len) {
                err = esp_uart_ota_write(handle, (const void *)handle->ota_upgrade_buf, handle->binary_file_len);
                if(err==ESP_ERR_UART_OTA_IN_PROGRESS)
                {
                    uart_ota_sec_write_finished = 1;
                }
                else
                {
                    uart_ota_sec_write_finished = 0;
                    #if DEBUG_PRINT_LEVEL_my_uart_ota_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
                    printf("error:esp_uart_ota_write\n");
                    #endif
                }
                return err;
            }
        case ESP_UART_OTA_IN_PROGRESS:

            data_read = esp_uart_ota_data_read(handle->ota_upgrade_buf, handle->ota_upgrade_buf_size);
            if (data_read == 0) {
                ESP_LOGI(TAG, "Connection closed");
            } else if (data_read > 0) {
                err = esp_uart_ota_write(handle, (const void *)handle->ota_upgrade_buf, data_read);
                if(err==ESP_ERR_UART_OTA_IN_PROGRESS)
                {
                    uart_ota_sec_write_finished = 1;
                }
                else
                {
                    uart_ota_sec_write_finished = 0;
                    #if DEBUG_PRINT_LEVEL_my_uart_ota_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
                    printf("error:esp_uart_ota_write\n");
                    #endif
                }
                if(uart_ota_write_alldata_finished)
                {
                    uart_ota_sec_write_finished = 1;
                    handle->state = ESP_UART_OTA_SUCCESS;
                    break;
                }
                return err;
            }
            uart_ota_sec_write_finished = 1;
            handle->state = ESP_UART_OTA_SUCCESS;
            break;
         default:
            ESP_LOGE(TAG, "Invalid ESP UART OTA State");
            return ESP_FAIL;
            break;
    }
    return ESP_OK;
}

esp_err_t esp_uart_ota_finish(esp_uart_ota_handle_t uart_ota_handle)
{
    esp_uart_ota_t *handle = (esp_uart_ota_t *)uart_ota_handle;
    if (handle == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    if (handle->state < ESP_UART_OTA_BEGIN) {
        return ESP_FAIL;
    }

    esp_err_t err = ESP_OK;
    switch (handle->state) {
        case ESP_UART_OTA_SUCCESS:
        case ESP_UART_OTA_IN_PROGRESS:
            err = esp_ota_end(handle->update_handle);
            /* falls through */
        case ESP_UART_OTA_BEGIN:
            if (handle->ota_upgrade_buf) {
                free(handle->ota_upgrade_buf);
            }
            break;
        default:
            ESP_LOGE(TAG, "Invalid ESP UART OTA State");
            break;
    }

    if ((err == ESP_OK) && (handle->state == ESP_UART_OTA_SUCCESS)) {
        esp_err_t err = esp_ota_set_boot_partition(handle->update_partition);
        if (err != ESP_OK) {
            ESP_LOGE(TAG, "esp_ota_set_boot_partition failed! err=0x%d", err);
        }
    }
    free(handle);
    return err;
}

esp_err_t esp_uart_ota(void)
{ 
    esp_uart_ota_handle_t uart_ota_handle = NULL;
    esp_err_t err = esp_uart_ota_begin(&uart_ota_handle);
    if (uart_ota_handle == NULL) {
        return ESP_FAIL;
    }

    ESP_LOGI("esp_uart_ota()", "OK1, err=%x", err);

    while (1) {
        err = esp_uart_ota_perform(uart_ota_handle);
        if (err != ESP_ERR_UART_OTA_IN_PROGRESS) {
            break;
        }
    }

    ESP_LOGI("esp_uart_ota()", "OK2, err=%x", err);

    esp_err_t ota_finish_err = esp_uart_ota_finish(uart_ota_handle);
    ESP_LOGI("esp_uart_ota()", "OK3, ota_finish_err=%x", ota_finish_err);
    if (err != ESP_OK) {
        return err;
    } else if (ota_finish_err != ESP_OK) {
        return ota_finish_err;
    }
    ESP_LOGI("esp_uart_ota()", "OK4");
    return ESP_OK;
}

void UartOtaClear(void)
{
    uart_ota_exist = 0;

    if(UartOtaOutTime_Handle!=NULL)
    {
        xTimerDelete(UartOtaOutTime_Handle,portMAX_DELAY);
    }
    if(simple_ota_example_task_Handle!=NULL)
    {
        vTaskDelete(simple_ota_example_task_Handle);
    }
}

int uart_ota_failed = 0;
int uart_ota_finished = 0;
static void simple_ota_example_task(void *pvParameter)
{
    ESP_LOGI(TAG, "Starting OTA example");

    esp_err_t ret = esp_uart_ota();
    if (ret == ESP_OK) {
        ESP_LOGE(TAG, "OTA succeed\n");
        // esp_restart();
    } else {
        ESP_LOGE(TAG, "Firmware upgrade failed");
        uart_ota_failed = 1;
    }

    //esp_restart();

    ESP_LOGI("simple_ota_example_task", "simple_ota_example_task finished");
    
    uart_ota_finished = 1;

    vTaskDelay(5000 / portTICK_PERIOD_MS);

    UartOtaClear();

    while(1)
    {
        vTaskDelay(10000 / portTICK_PERIOD_MS);
    }
}

int Ota_Check(void)
{
    while(!uart_ota_failed)
    {
        #if LED_FUNC
        vTaskDelay(900/portTICK_PERIOD_MS);
		led_open();
		vTaskDelay(100/portTICK_PERIOD_MS);
		led_close();
        #else
        vTaskDelay(1000/portTICK_PERIOD_MS);
        #endif
    }
    uart_ota_failed = 0;
    return 1;
}

void UartOtaOutTimer(xTimerHandle xTimer)
{
    #if DEBUG_PRINT_LEVEL_my_uart_ota_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\n!!!uart ota timeout!!!\n\n");
    #endif
    esp_restart();
    uart_ota_failed = 1;
    UartOtaClear();
}
//-------------------------------------------------------------------------

void UartOtaInit(void)
{
    if(simple_ota_example_task_Handle==NULL)
    {
        //xTaskCreate(&simple_ota_example_task, "ota_example_task", 8192, NULL, 10, &simple_ota_example_task_Handle);
        StackType_t* p_task_stack = calloc(1, 4096*sizeof(StackType_t));
        if(p_task_stack!=NULL)
        {
            StaticTask_t* p_task_data = calloc(1, sizeof(StaticTask_t));
            if(p_task_data!=NULL)
            {
                simple_ota_example_task_Handle = xTaskCreateStatic(&simple_ota_example_task, "simple_ota_example_task", 4096, NULL, 7, p_task_stack, p_task_data);
            }
        }
    }

    if(UartOtaOutTime_Handle==NULL)
    {
        UartOtaOutTime_Handle = xTimerCreate("UartOtaOutTimer", UART_OTA_TIMEOUT/portTICK_PERIOD_MS, pdTRUE, (void*)2, UartOtaOutTimer);
        xTimerStart(UartOtaOutTime_Handle, portMAX_DELAY);
    }

    uart_ota_exist = 1;
}


int UartOTA_Routine(char* p_ota_file, int file_len)
{
    ESP_LOGI("UartOTA_Routine()", "start ota routine");

    uart_ota_sec_down_finished = 0;
    uart_ota_sec_write_finished = 0;

    UartOtaInit();
    vTaskDelay(1000/portTICK_PERIOD_MS);

    int data_block_num = file_len/DEFAULT_OTA_BUF_SIZE;
    int end_block_size = file_len%DEFAULT_OTA_BUF_SIZE;
    int data_block_count = 0;

    ESP_LOGI("UartOTA_Routine()", "data_block_num=%d, end_block_size=%d", data_block_num, end_block_size);

    while(data_block_count<data_block_num)
    {
        memcpy(uart_ota_sec_buff, p_ota_file+data_block_count*DEFAULT_OTA_BUF_SIZE, DEFAULT_OTA_BUF_SIZE);
        uart_ota_sec_size = DEFAULT_OTA_BUF_SIZE;
        if(end_block_size==0)
        {
            if(data_block_count+1==data_block_num)
            {
                uart_ota_write_alldata_finished = 1;
            }
        }
        uart_ota_sec_down_finished = 1;
        while(!uart_ota_sec_write_finished)
        {
            vTaskDelay(10/portTICK_PERIOD_MS);
        }
        uart_ota_sec_down_finished = 0;
        uart_ota_sec_write_finished = 0;
        data_block_count++;
        // ESP_LOGI("UartOTA_Routine()", "data_block_count=%d", data_block_count);
    }
    if(end_block_size>0)
    {
        // ESP_LOGI("UartOTA_Routine()", "write end of ota data block");
        memcpy(uart_ota_sec_buff, p_ota_file+data_block_count*DEFAULT_OTA_BUF_SIZE, end_block_size);
        uart_ota_sec_size = end_block_size;
        uart_ota_write_alldata_finished = 1;
        uart_ota_sec_down_finished = 1;
        while(!uart_ota_sec_write_finished)
        {
            vTaskDelay(10/portTICK_PERIOD_MS);
        }
        uart_ota_sec_down_finished = 0;
        uart_ota_sec_write_finished = 0;
        data_block_count++;
    }
    ESP_LOGI("UartOTA_Routine()", "end of ota routine");

    while(!uart_ota_finished)
    {
        vTaskDelay(10/portTICK_PERIOD_MS);
    }
    if(!uart_ota_failed)
    {
        ESP_LOGI("UartOTA_Routine()", "uart ota succeed!");
        return 0;
    }

    return uart_ota_failed;
}

#endif