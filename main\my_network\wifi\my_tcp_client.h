#ifndef NETWORK_H
#define NETWORK_H


#define MY_TCP_CLIENT_FUNC 1

#if MY_TCP_CLIENT_FUNC

#include <lwip/netdb.h>


//选择字节序
#define AUTO_ENDIAN 0//如果开启了自动字节序，则优先使用自动字节序
#define RECV_ENDIAN_REVERSE 0
#define SEND_ENDIAN_REVERSE 0

#define RECONNECT_SERVER    0//仅使用网络调试助手调试时可将此宏关闭。若关闭了此宏，则在连接到WIFI后，默认认为与服务器已经建立连接，并且不进行检查与重连

#define PROMPT_DEBUG_NETWORK    0//网络连接提示
#define KEYPROMPT_DEBUG_NETWORK 0//网络连接提示（详细提示）
#define PROMPT_DEBUG_NETWORK_STATE  0//是否每隔一段时间串口输出当前网络状态
#define PRINTF_SERVER_RCVD_DATA 1//是否输出从服务器接收到的数据


#define My_TcpCleintTask_CREATE_TYPE    TASK_CREATE_TYPE_DYNAMIC

#if My_TcpCleintTask_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
#define My_TcpCleintTask_COREID    1
#define My_TcpCleintTask_priority  5
#define My_TcpCleintTask_task_stack_size   4096
#elif My_TcpCleintTask_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
#define My_TcpCleintTask_COREID    1
#define My_TcpCleintTask_priority  21
#define My_TcpCleintTask_task_stack_size   4096
#endif


#define My_PickpicTask_CREATE_TYPE    TASK_CREATE_TYPE_DYNAMIC

#if My_PickpicTask_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
#define My_PickpicTask_COREID    1
#define My_PickpicTask_priority  5
#define My_PickpicTask_task_stack_size   4096
#elif My_PickpicTask_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
#define My_PickpicTask_COREID    1
#define My_PickpicTask_priority  21
#define My_PickpicTask_task_stack_size   4096
#endif


typedef struct
{
    char serveraddr[32];
    int serverport;
}my_tcp_client_conf_t;

typedef struct
{
    int addr_family;
    int ip_protocol;
    struct timeval tv_out;
    struct sockaddr_in dest_addr;
    char addr_str[200];
}my_tcp_client_tcpconf_t;

#define MY_TCP_CLIENT_SOCK_RCV_TIMEOUT_TIME_DEFAULT (10)

typedef struct
{
    my_tcp_client_conf_t conf;
    my_tcp_client_tcpconf_t tcpconf;
    int sock_invalid;
    int sock_check_type;
    int sock_auto_reconnect;
    int sock_create_time;
    int sock_fd;
    int server_online;
    int server_dis_count;
    int sock_rcv_timeout_time;
}my_tcp_socket_info_t;

#define my_tcp_socket_list_item_max 32
typedef struct
{
    uint8_t init_ok;
    my_tcp_socket_info_t my_tcp_socket_list[my_tcp_socket_list_item_max];
}my_tcp_client_info_t;


int My_TcpClient_Init(void);
int My_TcpClient_CreateSocket(char* address, int port);
int My_TcpClient_SendDataToServer(int main_id, const char* data, uint32_t len);
int My_TcpClient_RecvDataFromServer(int main_id, void* data, int buf_len, int object_len, int timeout);
int My_TcpClient_Get_state(int main_id);

#endif
#endif
