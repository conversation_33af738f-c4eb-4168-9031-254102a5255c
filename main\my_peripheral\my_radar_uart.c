#include "mytest_radar.h"
#include "my_radar_uart.h"

#if MY_RADAR_FUNC

// #include "my_gpio.h"


#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"
#include "freertos/semphr.h"
#include "esp_system.h"
#include "esp_log.h"
#include "driver/uart.h"
#include "driver/gpio.h"

#include "string.h"
#include "stdlib.h"



TaskHandle_t UsartARcvTask_Handle = NULL;

#define A_RX_BUF_SIZE   1024

#define A_BAUDRATE 256000UL

#define A_TXD_PIN (GPIO_NUM_11)
#define A_RXD_PIN (GPIO_NUM_12)

#define A_UART_NUM UART_NUM_2


static void Uart_A_Init(void)
{
    const uart_config_t uart_config = {
        .baud_rate = A_BAUDRATE,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE
    };
    uart_param_config(A_UART_NUM, &uart_config);
    uart_set_pin(A_UART_NUM, A_TXD_PIN, A_RXD_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    // We won't use a buffer for sending data.
    uart_driver_install(A_UART_NUM, A_RX_BUF_SIZE, 0, 0, NULL, 0);
}

int AsendData(const char* logName, const char* data, uint16_t len)
{
    const int txBytes = uart_write_bytes(A_UART_NUM, data, len);
    // ESP_LOGI(logName, "Wrote %d bytes", txBytes);
    return txBytes;
}

void UsartARcvTask( void *pvParameters )
{
    static const char *RX_TASK_TAG = "UsartARcvTask";
    char* data = (char*)calloc(A_RX_BUF_SIZE+1, sizeof(char));
    uint32_t rxbytes = 0;

    for(;;)
    {
        rxbytes = uart_read_bytes(A_UART_NUM, (void*)data, A_RX_BUF_SIZE, 50 / portTICK_PERIOD_MS);
        if(rxbytes)
        {
            // ESP_LOGI("UsartARcvTask()", "rxbytes=%d", rxbytes);
            // for(int i=0; i<rxbytes; i++)
            // {
            //     printf("%02X ", *(data+i));
            // }
            // printf("\n####\n");
            My_Test_Radar_Send(data, rxbytes, 1000);
            rxbytes = 0;
            memset(data, 0, A_RX_BUF_SIZE);
        }
    }
    free(data);
}

void MyRadarUart_Init(void)
{
    Uart_A_Init();

    xTaskCreate(UsartARcvTask,  "UsartARcvTask",    8192,    NULL, 5, &UsartARcvTask_Handle);
}

#endif