#include "my_ds.h"

#include "my_endian.h"

#include <esp_log.h>

#include <stdio.h>
#include <string.h>
#include <stdlib.h>




int My_D2Link_Append_Item(my_d2link_list_t* p_list, int id, void* p_data, uint8_t p_data_type, uint64_t data_uint64, uint32_t data_len, char* service_name)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_Append_Item():", "p_list==NULL");
        return -1;
    }
    my_d2link_list_item_t* newitem = NULL;
    newitem = (my_d2link_list_item_t*)calloc(1, sizeof(my_d2link_list_item_t));
    int service_name_len = 0;
    my_d2link_list_item_t* item_index = p_list->first_item;

    #if MY_D2LINK_LIST_LIST_NAME_USED
    // printf("My_D2Link_Append_Item(): append item to %s\n", p_list->list_name);

    if(service_name!=NULL)
    {
        // ESP_LOGI("My_D2Link_Append_Item()", "add item: id=%d, service_name=%s", id, service_name);
    }
    else
    {
        // ESP_LOGI("My_D2Link_Append_Item()", "add item: id=%d, service_name=NULL", id);
    }
    #endif
    
    if(p_list->list_item_num>0)
    {
        do
        {
            if(item_index->id==id)
            {
                break;
            }
            if(item_index->next_item!=NULL)
            {
                item_index = item_index->next_item;
            }
        } while (item_index!=p_list->end_item);
        if(item_index->id==id)
        {
            if(newitem!=NULL)free(newitem);
            // ESP_LOGI("My_D2Link_Append_Item()", "add item failed,1(already exists)\n");
            return 1;
        }
    }

    if(newitem!=NULL)
    {
        #if MY_D2LINK_LIST_ITEM_ITEM_NAME_USED
        if(service_name!=NULL)
        {
            service_name_len = strlen(service_name);
            if(service_name_len>31)
            {
                service_name_len = 31;
            }
            memcpy(newitem->item_name, service_name, service_name_len);
        }
        #endif

        newitem->id = id;
        //------------------------------------
        if(p_data!=NULL)
        {
            newitem->data_type = 1;
            newitem->p_data_type = p_data_type;
            if(!p_data_type)
            {
                newitem->p_data = p_data;
            }
            else
            {
                newitem->p_data = calloc(1, data_len);
                if(newitem->p_data!=NULL)
                {
                    memcpy(newitem->p_data, p_data, data_len);
                }
            }
        }
        else
        {
            newitem->data_type = 0;
            newitem->data_uint64 = data_uint64;
        }
        newitem->data_len = data_len;
        p_list->list_data_len += data_len;
        //------------------------------------

        newitem->next_item = NULL;
        if(p_list->list_item_num==0)
        {
            p_list->first_item = newitem;
            newitem->pri_item = NULL;
            newitem->next_item = NULL;
        }
        else
        {
            p_list->end_item->next_item = newitem;
            newitem->pri_item = p_list->end_item;
            newitem->next_item = NULL;
        }
        p_list->end_item = newitem;
        p_list->list_item_num++;
        // ESP_LOGI("My_D2Link_Append_Item()", "add item succeed, p_list->list_item_num=%d\n", p_list->list_item_num);
        return 0;
    }
    else
    {
        // ESP_LOGI("My_D2Link_Append_Item()", "add item failed,3\n");
        return 1;
    }
    return 0;
}

int My_D2Link_Delete_Item(my_d2link_list_t* p_list, int id)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_Delete_Item():", "p_list==NULL");
        return -1;
    }
    ESP_LOGI("My_D2Link_Delete_Item()", "delete item from p_list(%p)", p_list);
    if(p_list->list_item_num==0)
    {
        ESP_LOGI("My_D2Link_Delete_Item()", "error, p_list already empty\n");
        return 1;
    }

    #if MY_D2LINK_LIST_LIST_NAME_USED
    printf("My_D2Link_Delete_Item(): delete item from %s\n", p_list->list_name);
    #endif

    my_d2link_list_item_t* item_index = p_list->first_item;
    do
    {
        if(item_index->id==id)
        {
            break;
        }
        if(item_index->next_item!=NULL)
        {
            item_index = item_index->next_item;
        }
    } while (item_index!=p_list->end_item);
    if(item_index->id==id)
    {
        #if MY_D2LINK_LIST_ITEM_ITEM_NAME_USED
        if(item_index->item_name!=NULL)
        {
            // ESP_LOGI("My_D2Link_Delete_Item()", "delete id=%d, service_name=%s", item_index->id, item_index->item_name);
        }
        else
        {
            // ESP_LOGI("My_D2Link_Delete_Item()", "delete id=%d, service_name=NULL", item_index->id);
        }
        #endif
        if((item_index!=p_list->first_item)&&(item_index!=p_list->end_item))
        {
            item_index->pri_item->next_item = item_index->next_item;
            item_index->next_item->pri_item = item_index->pri_item;
        }
        else
        {
            if(item_index==p_list->first_item)
            {
                p_list->first_item = item_index->next_item;
            }
            if(item_index==p_list->end_item)
            {
                p_list->end_item = item_index->pri_item;
                if(p_list->end_item!=NULL)
                {
                    p_list->end_item->next_item = NULL;
                }
            }
        }

        p_list->list_data_len -= item_index->data_len;

        //---------------------------------------------
        if(item_index->p_data_type)
        {
            if(item_index->p_data!=NULL)
            {
                free(item_index->p_data);
            }
        }
        //---------------------------------------------
        
        free(item_index);
        if(p_list->list_item_num>0)
        {
            p_list->list_item_num--;
        }
        return 0;
    }
    else
    {
        // ESP_LOGI("My_D2Link_Delete_Item()", "the item to delete was not found\n");
        return 1;
    }
    return 0;
}

int My_D2Link_Insert_Item(my_d2link_list_t* p_list, int insert_id, int insert_direct, int id, void* p_data, uint8_t p_data_type, uint64_t data_uint64, uint8_t data_len, char* service_name)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_Insert_Item():", "p_list==NULL");
        return -1;
    }
    my_d2link_list_item_t* newitem = NULL;
    newitem = (my_d2link_list_item_t*)calloc(1, sizeof(my_d2link_list_item_t));
    int service_name_len = 0;
    my_d2link_list_item_t* item_index = p_list->first_item;

    #if MY_D2LINK_LIST_LIST_NAME_USED
    // printf("My_D2Link_Insert_Item(): insert item to %s\n", p_list->list_name);
    #endif

    if(service_name!=NULL)
    {
        // ESP_LOGI("My_D2Link_Insert_Item()", "insert_id=%d, insert_direct=%d, insert item: id=%d, service_name=%s", insert_id, insert_direct, id, service_name);
    }
    else
    {
        // ESP_LOGI("My_D2Link_Insert_Item()", "insert_id=%d, insert_direct=%d, insert item: id=%d, service_name=NULL", insert_id, insert_direct, id);
    }
    
    if(p_list->list_item_num>0)
    {
        do
        {
            if(item_index->id==id)
            {
                break;
            }
            if(item_index->next_item!=NULL)
            {
                item_index = item_index->next_item;
            }
        } while (item_index!=p_list->end_item);
        if(item_index->id==id)
        {
            if(newitem!=NULL)free(newitem);
            // ESP_LOGI("My_D2Link_Insert_Item()", "insert item failed,1(already exists)\n");
            return 1;
        }
    }

    if(newitem!=NULL)
    {
        #if MY_D2LINK_LIST_ITEM_ITEM_NAME_USED
        if(service_name!=NULL)
        {
            service_name_len = strlen(service_name);
            if(service_name_len>31)
            {
                service_name_len = 31;
            }
            memcpy(newitem->item_name, service_name, service_name_len);
        }
        #endif

        newitem->id = id;
        //------------------------------------
        if(p_data!=NULL)
        {
            newitem->data_type = 1;
            newitem->p_data_type = p_data_type;

            if(!p_data_type)
            {
                newitem->p_data = p_data;
            }
            else
            {
                newitem->p_data = calloc(1, data_len);
                if(newitem->p_data!=NULL)
                {
                    memcpy(newitem->p_data, p_data, data_len);
                }
            }
        }
        else
        {
            newitem->data_type = 0;
            newitem->data_uint64 = data_uint64;
        }
        newitem->data_len = data_len;
        p_list->list_data_len += data_len;
        //------------------------------------

        //newitem->next_item = NULL;
        if(p_list->list_item_num==0)//链表为空
        {
            p_list->first_item = newitem;
            newitem->pri_item = NULL;
            newitem->next_item = NULL;
            p_list->end_item = newitem;
        }
        else//链表不为空
        {
            if(insert_id==-1)//指定插入到最前面
            {
                newitem->pri_item = NULL;
                newitem->next_item = p_list->first_item;
                p_list->first_item->pri_item = newitem;
                p_list->first_item = newitem;
            }
            else if(insert_id==-2)//指定追加到尾部
            {
                newitem->pri_item = p_list->end_item;
                newitem->next_item = NULL;
                p_list->end_item->next_item = newitem;
                p_list->end_item = newitem;
            }
            else//指定了id
            {
                my_d2link_list_item_t* find_id_item = NULL;
                find_id_item = p_list->first_item;
                for(int i=0; (i<p_list->list_item_num)&&(find_id_item!=NULL); i++)
                {
                    if(find_id_item->id!=insert_id)
                    {
                        find_id_item = find_id_item->next_item;
                    }
                    else
                    {
                        break;
                    }
                }
                if(find_id_item==NULL)//未找到目标id，追加到尾部
                {
                    newitem->pri_item = p_list->end_item;
                    newitem->next_item = NULL;
                    p_list->end_item->next_item = newitem;
                    p_list->end_item = newitem;
                }
                else//找到了目标id
                {
                    if(insert_direct)//后向插入
                    {
                        if(find_id_item==p_list->end_item)//目标id已经是最后一项
                        {
                            newitem->pri_item = p_list->end_item;
                            newitem->next_item = NULL;
                            p_list->end_item->next_item = newitem;
                            p_list->end_item = newitem;
                        }
                        else//目标id不是最后一项
                        {
                            newitem->pri_item = find_id_item;
                            newitem->next_item = find_id_item->next_item;
                            find_id_item->next_item->pri_item = newitem;
                            find_id_item->next_item = newitem;
                        }
                    }
                    else//前向插入
                    {
                        if(find_id_item==p_list->first_item)//目标id已经是第一项
                        {
                            newitem->pri_item = NULL;
                            newitem->next_item = p_list->first_item;
                            p_list->first_item->pri_item = newitem;
                            p_list->first_item = newitem;
                        }
                        else//目标id不是第一项
                        {
                            newitem->pri_item = find_id_item->pri_item;
                            newitem->next_item = find_id_item;
                            find_id_item->pri_item->next_item = newitem;
                            find_id_item->pri_item = newitem;
                        }
                    }
                }
            }
        }
        p_list->list_item_num++;
        // ESP_LOGI("My_D2Link_Insert_Item()", "insert item succeed, p_list->list_item_num=%d\n", p_list->list_item_num);
        return 0;
    }
    else
    {
        // ESP_LOGI("My_D2Link_Insert_Item()", "insert item failed,3\n");
        return 1;
    }
    return 0;
}

int My_D2Link_Delete(my_d2link_list_t** p_list)
{
    if((*p_list)==NULL)
    {
        ESP_LOGE("My_D2Link_Delete():", "p_list==NULL");
        return -1;
    }

    #if MY_D2LINK_LIST_LIST_NAME_USED
    // printf("My_D2Link_Delete():delete %s, list_item_num=%d\n", (*p_list)->list_name, (*p_list)->list_item_num);
    #endif

    my_d2link_list_item_t* p = (*p_list)->first_item;
    my_d2link_list_item_t* p_next = NULL;
    for(int i=0; (i<(*p_list)->list_item_num)&&(p!=NULL); i++)
    {
        p_next = p->next_item;

        //-----------------------------------
        if(p->p_data_type)
        {
            if(p->p_data!=NULL)
            {
                free(p->p_data);
            }
        }
        //-----------------------------------

        free(p);
        p = p_next;
    }

    free(*p_list);
    *p_list = NULL;
    // printf("\nMy_D2Link_Delete finished\n\n");

    return 0;
}

int My_D2Link_match_id_count_statistic(my_d2link_list_t* p_list, int id_begin, int id_end)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_match_id_count_statistic():", "p_list==NULL");
        return -1;
    }
    if(p_list->list_item_num==0)
    {
        // printf("\nMy_D2Link_match_id_count_statistic():list already empty\n");
        return 1;
    }

    #if MY_D2LINK_LIST_LIST_NAME_USED
    // printf("My_D2Link_match_id_count_statistic(): statistic match id count in %s\n", p_list->list_name);
    #endif

    my_d2link_list_item_t* p = p_list->first_item;
    int matching_id_counter = 0;
    for(int i=0; (i<p_list->list_item_num)&&(p!=NULL); i++)
    {
        if((p->id>=id_begin)&&(p->id<=id_end))
        {
            matching_id_counter++;
        }
        p = p->next_item;
    }

    // printf("My_D2Link_Data_Packer():matching_id_counter=%d\n", matching_id_counter);

    return matching_id_counter;
}

int My_D2Link_Data_Packer(my_d2link_list_t* p_list, void* saving, uint32_t saving_len)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_Data_Packer():", "p_list==NULL");
        return -1;
    }
    if(saving==NULL)
    {
        ESP_LOGE("My_D2Link_Data_Packer():", "saving==NULL");
        return -1;
    }
    if(p_list->list_item_num==0)
    {
        // printf("\nMy_D2Link_Data_Packer_uint64():list already empty\n");
        return 1;
    }

    #if MY_D2LINK_LIST_LIST_NAME_USED
    // printf("My_D2Link_Data_Packer(): packer %s\n", p_list->list_name);
    #endif

    my_d2link_list_item_t* p = p_list->first_item;

    char* p_saving_byte = (char*)saving;
    uint32_t copy_counter = 0;
    uint32_t copy_size = 0;
    for(int i=0; (i<p_list->list_item_num)&&(p!=NULL); i++)
    {
        if((copy_size+p->data_len)<=saving_len)
        {
            if(p->data_type==0)
            {
                my_endian_conversion_custom(&p->data_uint64, p->data_len);
                memcpy(p_saving_byte, &p->data_uint64, p->data_len);
                my_endian_conversion_custom(&p->data_uint64, p->data_len);
            }
            else if(p->data_type==1)
            {
                if(p->p_data!=NULL)
                {
                    memcpy(p_saving_byte, p->p_data, p->data_len);
                }
            }
            p_saving_byte += p->data_len;
            copy_counter++;
            copy_size += p->data_len;
            p = p->next_item;
        }
        else
        {
            ESP_LOGE("My_D2Link_Data_Packer():", "saving_len not enough");
            return 2;
        }
    }

    // printf("My_D2Link_Data_Packer():copy_counter=%d, copy_size=%d\n\n", copy_counter, copy_size);

    return 0;
}

void* My_D2Link_GetItem_MinId(my_d2link_list_t* p_list)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_GetItem_Storage_ById():", "p_list==NULL");
        return NULL;
    }
    // ESP_LOGI("My_D2Link_GetItem_MinId()", "find item(%d) from p_list(%p)", id, p_list);
    if(p_list->list_item_num==0)
    {
        // ESP_LOGI("My_D2Link_GetItem_MinId()", "error, p_list already empty\n");
        return NULL;
    }

    #if MY_D2LINK_LIST_LIST_NAME_USED
    // printf("My_D2Link_GetItem_MinId(): get item storage by id in %s\n", p_list->list_name);
    #endif

    my_d2link_list_item_t* item_index = p_list->first_item;

    my_d2link_list_item_t* min_id_item = p_list->first_item;
    for(int i=0; i<p_list->list_item_num; i++)
    {
        if(min_id_item->id > item_index->id)
        {
            min_id_item = item_index;
        }
        item_index = item_index->next_item;
    }
    return min_id_item;
}

void* My_D2Link_GetItem_MaxId(my_d2link_list_t* p_list)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_GetItem_MaxId():", "p_list==NULL");
        return NULL;
    }
    // ESP_LOGI("My_D2Link_GetItem_MaxId()", "find item(%d) from p_list(%p)", id, p_list);
    if(p_list->list_item_num==0)
    {
        // ESP_LOGI("My_D2Link_GetItem_MaxId()", "error, p_list already empty\n");
        return NULL;
    }

    #if MY_D2LINK_LIST_LIST_NAME_USED
    // printf("My_D2Link_GetItem_MaxId(): get item storage by id in %s\n", p_list->list_name);
    #endif

    my_d2link_list_item_t* item_index = p_list->first_item;

    my_d2link_list_item_t* max_id_item = p_list->first_item;
    for(int i=0; i<p_list->list_item_num; i++)
    {
        if(max_id_item->id < item_index->id)
        {
            max_id_item = item_index;
        }
        item_index = item_index->next_item;
    }
    return max_id_item;
}

void* My_D2Link_GetItem_IdGreaterThan(my_d2link_list_t* p_list, my_d2link_list_item_t* p_item)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_GetItem_IdGreaterThan():", "p_list==NULL");
        return NULL;
    }
    if(p_item==NULL)
    {
        ESP_LOGE("My_D2Link_GetItem_IdGreaterThan():", "p_item==NULL");
        return NULL;
    }
    // ESP_LOGI("My_D2Link_GetItem_IdGreaterThan()", "find item(%d) from p_list(%p)", id, p_list);
    if(p_list->list_item_num==0)
    {
        // ESP_LOGI("My_D2Link_GetItem_IdGreaterThan()", "error, p_list already empty\n");
        return NULL;
    }

    #if MY_D2LINK_LIST_LIST_NAME_USED
    // printf("My_D2Link_GetItem_IdGreaterThan(): get item storage by id in %s\n", p_list->list_name);
    #endif

    my_d2link_list_item_t* item_index = p_list->first_item;
    my_d2link_list_item_t* next_min_id_item = My_D2Link_GetItem_MaxId(p_list);

    for(int i=0; (i<p_list->list_item_num)&&(item_index!=NULL); i++)
    {
        if((item_index->id > p_item->id) && (item_index->id < next_min_id_item->id))
        {
            next_min_id_item = item_index;
        }
        item_index = item_index->next_item;
    }
    return next_min_id_item;
}

int My_D2Link_Data_Packer_accordingID(my_d2link_list_t* p_list, void* saving, uint32_t saving_len, uint8_t direct)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_Data_Packer():", "p_list==NULL");
        return -1;
    }
    if(saving==NULL)
    {
        ESP_LOGE("My_D2Link_Data_Packer():", "saving==NULL");
        return -1;
    }
    if(p_list->list_item_num==0)
    {
        // printf("\nMy_D2Link_Data_Packer_uint64():list already empty\n");
        return 1;
    }

    #if MY_D2LINK_LIST_LIST_NAME_USED
    // printf("My_D2Link_Data_Packer(): packer %s\n", p_list->list_name);
    #endif

    char* p_saving_byte = (char*)saving;
    uint32_t copy_counter = 0;
    uint32_t copy_size = 0;
    if(direct==0)
    {
        my_d2link_list_item_t* start_item = My_D2Link_GetItem_MinId(p_list);
        my_d2link_list_item_t* max_id_item = My_D2Link_GetItem_MaxId(p_list);
        ESP_LOGI("###", "start_item->id=%d, max_id_item=%d", start_item->id, max_id_item->id);
        if(start_item!=NULL)
        {
            my_d2link_list_item_t* next_item = start_item;
            for(int i=0; (i<p_list->list_item_num)&&(next_item!=NULL); i++)
            {
                if((copy_size+next_item->data_len)<=saving_len)
                {
                    // ESP_LOGI("###", "packing item->id=%d", next_item->id);
                    if(next_item->data_type==0)
                    {
                        my_endian_conversion_custom(&next_item->data_uint64, next_item->data_len);
                        memcpy(p_saving_byte, &next_item->data_uint64, next_item->data_len);
                    }
                    else if(next_item->data_type==1)
                    {
                        if(next_item->p_data!=NULL)
                        {
                            memcpy(p_saving_byte, next_item->p_data, next_item->data_len);
                        }
                    }
                    p_saving_byte += next_item->data_len;
                    copy_counter++;
                    copy_size += next_item->data_len;
                    next_item = My_D2Link_GetItem_IdGreaterThan(p_list, next_item);
                }
                else
                {
                    ESP_LOGE("My_D2Link_Data_Packer():", "saving_len not enough");
                    return 2;
                }
            }
        }
    }
    else if(direct==1)
    {

    }
    else
    {

    }

    // printf("My_D2Link_Data_Packer():copy_counter=%d, copy_size=%d\n\n", copy_counter, copy_size);

    return 0;
}

int My_D2Link_Data_Sort_Data_accordingID(my_d2link_list_t* p_list, uint8_t direct)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_Data_Packer():", "p_list==NULL");
        return -1;
    }
    if(p_list->list_item_num==0)
    {
        // printf("\nMy_D2Link_Data_Packer_uint64():list already empty\n");
        return 1;
    }

    #if MY_D2LINK_LIST_LIST_NAME_USED
    // printf("My_D2Link_Data_Packer(): packer %s\n", p_list->list_name);
    #endif

    if(direct==0)
    {
        my_d2link_list_item_t* max_id_item = My_D2Link_GetItem_MaxId(p_list);
        my_d2link_list_item_t* next_min_id_item = My_D2Link_GetItem_MinId(p_list);

        //提取出最后一项
        char* p_temp_tail_1 = calloc(1, 8192);
        int p_temp_tail_1_len = 0;
        int p_temp_tail_1_id = -1;
        if(max_id_item!=NULL)
        {
            memcpy(p_temp_tail_1, max_id_item->p_data, max_id_item->data_len);
            p_temp_tail_1_len = max_id_item->data_len;
            p_temp_tail_1_id = max_id_item->id;
        }

        my_d2link_list_item_t* cur_item = p_list->first_item;

        if(cur_item!=NULL)
        {
            char* temp_data = calloc(1, 8192);
            int temp_data_len = 0;
            int temp_id = 0;

            next_min_id_item = My_D2Link_GetItem_MinId(p_list);
            cur_item = p_list->first_item;

            for(;;)
            {
                if(cur_item!=NULL&&next_min_id_item!=NULL)
                {
                    if(cur_item->id!=next_min_id_item->id)
                    {
                        if((cur_item!=max_id_item)&&(next_min_id_item!=max_id_item))
                        {
                            // exchange data
                            temp_id = cur_item->id;
                            memcpy(temp_data, cur_item->p_data, cur_item->data_len);
                            temp_data_len = cur_item->data_len;

                            cur_item->id = next_min_id_item->id;
                            memcpy(cur_item->p_data, next_min_id_item->p_data, temp_data_len);

                            next_min_id_item->id = temp_id;
                            memcpy(next_min_id_item->p_data, temp_data, temp_data_len);

                            next_min_id_item = My_D2Link_GetItem_IdGreaterThan(p_list, cur_item);
                            cur_item = cur_item->next_item;
                        }
                        else
                        {
                            if(cur_item==max_id_item)
                            {
                                cur_item = cur_item->next_item;
                            }
                            else if(next_min_id_item!=max_id_item)
                            {
                                // cur_item = cur_item->next_item;
                                // next_min_id_item = My_D2Link_GetItem_IdGreaterThan(p_list, next_min_id_item);
                            }
                        }
                    }
                    else
                    {
                        next_min_id_item = My_D2Link_GetItem_IdGreaterThan(p_list, cur_item);
                        cur_item = cur_item->next_item;
                    }
                }
                else
                {
                    break;
                }
            }
            if(max_id_item!=NULL)
            {
                if(max_id_item!=p_list->end_item)
                {
                    int data_len_since_max_id = 0;
                    my_d2link_list_item_t* item_index = max_id_item->next_item;
                    while(item_index!=NULL)
                    {
                        data_len_since_max_id += item_index->data_len;
                        item_index = item_index->next_item;
                    }
                    memcpy(max_id_item->p_data, max_id_item->next_item->p_data, data_len_since_max_id);
                    memcpy(max_id_item->p_data+data_len_since_max_id, p_temp_tail_1, p_temp_tail_1_len);

                    item_index = max_id_item;
                    if(item_index->next_item!=NULL)
                    {
                        item_index->data_len = item_index->next_item->data_len;
                        item_index->id = item_index->next_item->id;
                        item_index = item_index->next_item;
                    }
                    
                    while(item_index!=p_list->end_item)
                    {
                        item_index->p_data-=p_temp_tail_1_len;
                        if(item_index->next_item!=NULL)
                        {
                            item_index->data_len = item_index->next_item->data_len;
                            item_index->id = item_index->next_item->id;
                            item_index = item_index->next_item;
                        }
                    }
                    p_list->end_item->p_data = p_list->first_item->p_data+p_list->list_data_len-p_temp_tail_1_len;
                    p_list->end_item->id = p_temp_tail_1_id;
                    p_list->end_item->data_len = p_temp_tail_1_len;
                }
            }
        }
    }
    else if(direct==1)
    {

    }
    else
    {

    }

    // printf("My_D2Link_Data_Packer():copy_counter=%d, copy_size=%d\n\n", copy_counter, copy_size);

    return 0;
}

int My_D2Link_GetItem_ContinousMaxId_next(my_d2link_list_t* p_list, int start_id)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_GetItem_ContinousMaxId_next():", "p_list==NULL");
        return NULL;
    }
    // ESP_LOGI("My_D2Link_GetItem_MaxId()", "find item(%d) from p_list(%p)", id, p_list);
    if(p_list->list_item_num==0)
    {
        // ESP_LOGI("My_D2Link_GetItem_MaxId()", "error, p_list already empty\n");
        return NULL;
    }

    #if MY_D2LINK_LIST_LIST_NAME_USED
    // printf("My_D2Link_GetItem_MaxId(): get item storage by id in %s\n", p_list->list_name);
    #endif

    my_d2link_list_item_t* item_index = p_list->first_item;

    int j=0;
    for(; j<p_list->list_item_num; j++)
    {
        int i=0;
        item_index = p_list->first_item;
        for(; i<p_list->list_item_num&&(item_index!=NULL); i++)
        {
            if((start_id+j) == item_index->id)
            {
                break;
            }
            item_index = item_index->next_item;
        }
        if(!(i<p_list->list_item_num))
        {
            return start_id+j;
        }
    }
    
    return start_id+j;;
}

int My_D2Link_GetItem_ContinousDistribution_8_ByCurId(my_d2link_list_t* p_list, int start_id)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_GetItem_ContinousMaxId_next():", "p_list==NULL");
        return NULL;
    }
    // ESP_LOGI("My_D2Link_GetItem_MaxId()", "find item(%d) from p_list(%p)", id, p_list);
    if(p_list->list_item_num==0)
    {
        // ESP_LOGI("My_D2Link_GetItem_MaxId()", "error, p_list already empty\n");
        return NULL;
    }

    #if MY_D2LINK_LIST_LIST_NAME_USED
    // printf("My_D2Link_GetItem_MaxId(): get item storage by id in %s\n", p_list->list_name);
    #endif

    my_d2link_list_item_t* item_index = p_list->first_item;

    int ret = 0;
    int j=0;
    for(; (j<8); j++)
    {
        int i=0;
        item_index = p_list->first_item;
        for(; i<p_list->list_item_num&&(item_index!=NULL); i++)
        {
            if((start_id+j) == item_index->id)
            {
                ret|=(1<<j);
                break;
            }
            item_index = item_index->next_item;
        }
        if(!(i<p_list->list_item_num))
        {
            
        }
    }
    if(j<8)
    {

    }
    
    return ret;
}

void* My_D2Link_GetItem_Storage_ById(my_d2link_list_t* p_list, int id)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_GetItem_Storage_ById():", "p_list==NULL");
        return NULL;
    }
    // ESP_LOGI("My_D2Link_GetItem_Storage_ById()", "find item(%d) from p_list(%p)", id, p_list);
    if(p_list->list_item_num==0)
    {
        // ESP_LOGI("My_D2Link_GetItem_Storage_ById()", "error, p_list already empty\n");
        return NULL;
    }

    #if MY_D2LINK_LIST_LIST_NAME_USED
    // printf("My_D2Link_GetItem_Storage_ById(): get item storage by id in %s\n", p_list->list_name);
    #endif

    my_d2link_list_item_t* item_index = p_list->first_item;
    do
    {
        if(item_index->id==id)
        {
            break;
        }
        if(item_index->next_item!=NULL)
        {
            item_index = item_index->next_item;
        }
    } while (item_index!=p_list->end_item);
    if(item_index->id==id)
    {
        #if MY_D2LINK_LIST_ITEM_ITEM_NAME_USED
        if(item_index->item_name!=NULL)
        {
            // ESP_LOGI("My_D2Link_GetItem_Storage_ById()", "find id=%d, service_name=%s", item_index->id, item_index->item_name);
        }
        else
        {
            // ESP_LOGI("My_D2Link_GetItem_Storage_ById()", "find id=%d, service_name=NULL", item_index->id);
        }
        #endif
        
        return item_index->p_data;
    }
    else
    {
        // ESP_LOGI("My_D2Link_GetItem_Storage_ById()", "the item to find was not found\n");
        return NULL;
    }
    return NULL;
}

int My_D2Link_PrintList(my_d2link_list_t* p_list)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_PrintList():", "p_list==NULL");
        return -1;
    }
    if(p_list->list_item_num==0)
    {
        printf("\nMy_D2Link_PrintList():list already empty\n");
        return 1;
    }

    printf("\n\nMy_D2Link_PrintList---------------------------\n");
    #if MY_D2LINK_LIST_LIST_NAME_USED
    printf("list_name=%s\n", p_list->list_name);
    #endif
    printf("list_item_num=%d\n", p_list->list_item_num);
    printf("list_data_len=%d\n", p_list->list_data_len);
    printf("\n");

    my_d2link_list_item_t* p = p_list->first_item;
    for(int i=0; i<p_list->list_item_num; i++)
    {
        printf("p_list[%d].id=0x%x(%d)\n", i, p->id, p->id);
        #if MY_D2LINK_LIST_ITEM_ITEM_NAME_USED
        printf("p_list[%d].item_name=%s\n", i, p->item_name);
        #endif
        printf("p_list[%d].data_len=0x%x(%d)\n", i, p->data_len, p->data_len);
        printf("p_list[%d].data_type=0x%x(%d)\n", i, p->data_type, p->data_type);
        if(p->data_type==0)
        {
            printf("p_list[%d].data_uint64=0x%llx\n", i, p->data_uint64);
        }
        else if(p->data_type==1)
        {
            printf("p_list[%d].*p_data_type=0x%x(%d)\n", i, p->p_data_type, p->p_data_type);
            printf("p_list[%d].*p_data(%p)=", i, p->p_data);
            char* p_data_byte = (char*)p->p_data;
            if(p->data_len<512)
            {
                for(int j=0; j<p->data_len; j++)
                {
                    printf("%02x ", *(p_data_byte+j));
                }
                printf("\n");
            }
            else
            {
                printf("...\n");
            }
        }
        printf("\n");
        
        p = p->next_item;
    }
    printf("\nMy_D2Link_PrintList---------------------------\n\n");

    return 0;
}

// int My_D2Link_Creat(my_d2link_list_t* p_list)
// {
//     if(p_list==NULL)
//     {
//         ESP_LOGE("My_D2Link_Creat():", "p_list==NULL");
//         return -1;
//     }
//     memset(p_list, 0, sizeof(my_d2link_list_t));

//     p_list->first_item = NULL;
//     p_list->end_item = NULL;

//     return 0;
// }

my_d2link_list_t* My_D2Link_Creat(char* list_name)
{
    my_d2link_list_t* new_d2link_list = calloc(1, sizeof(my_d2link_list_t));

    new_d2link_list->first_item = NULL;
    new_d2link_list->end_item = NULL;
    new_d2link_list->list_item_num = 0;
    new_d2link_list->list_data_len = 0;

    #if MY_D2LINK_LIST_LIST_NAME_USED
    if(list_name!=NULL)
    {
        if(strlen(list_name)<sizeof(new_d2link_list->list_name))
        {
            memcpy(new_d2link_list->list_name, list_name, strlen(list_name));
        }
        else
        {
            memcpy(new_d2link_list->list_name, list_name, sizeof(new_d2link_list->list_name)-1);
        }
        // printf("My_D2Link_Creat(): creat new d2link list, list_name=%s\n", new_d2link_list->list_name);
    }
    else
    {
        // printf("My_D2Link_Creat(): creat new d2link list, list_name=unnamed\n");
    }
    #endif
    

    return new_d2link_list;
}