#include "my_sdcard.h"


/* SD card and FAT filesystem example.
   This example uses SPI peripheral to communicate with SD card.

   This example code is in the Public Domain (or CC0 licensed, at your option.)

   Unless required by applicable law or agreed to in writing, this
   software is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
   CONDITIONS OF ANY KIND, either express or implied.
*/

#include <string.h>
#include <sys/unistd.h>
#include <sys/stat.h>
#include "esp_vfs_fat.h"
#include "sdmmc_cmd.h"

static const char *TAG = "example";

#define MOUNT_POINT "/sdcard"

// Pin assignments can be set in menuconfig, see "SD SPI Example Configuration" menu.
// You can also change the pin assignments here by changing the following 4 lines.
#define PIN_NUM_MISO  CONFIG_EXAMPLE_PIN_MISO
#define PIN_NUM_MOSI  CONFIG_EXAMPLE_PIN_MOSI
#define PIN_NUM_CLK   CONFIG_EXAMPLE_PIN_CLK
#define PIN_NUM_CS    CONFIG_EXAMPLE_PIN_CS


int My_SDcard_test(void)
{
    esp_err_t ret;

    // Options for mounting the filesystem.
    // If format_if_mount_failed is set to true, SD card will be partitioned and
    // formatted in case when mounting fails.
    esp_vfs_fat_sdmmc_mount_config_t mount_config = {
#ifdef CONFIG_EXAMPLE_FORMAT_IF_MOUNT_FAILED
        .format_if_mount_failed = true,
#else
        .format_if_mount_failed = false,
#endif // EXAMPLE_FORMAT_IF_MOUNT_FAILED
        .max_files = 5,
        .allocation_unit_size = 16 * 1024
    };
    sdmmc_card_t *card;
    const char mount_point[] = MOUNT_POINT;
    ESP_LOGI(TAG, "Initializing SD card");

    // Use settings defined above to initialize SD card and mount FAT filesystem.
    // Note: esp_vfs_fat_sdmmc/sdspi_mount is all-in-one convenience functions.
    // Please check its source code and implement error recovery when developing
    // production applications.
    ESP_LOGI(TAG, "Using SPI peripheral");

    sdmmc_host_t host = SDSPI_HOST_DEFAULT();
    host.max_freq_khz = 20000;
    spi_bus_config_t bus_cfg = {
        .mosi_io_num = PIN_NUM_MOSI,
        .miso_io_num = PIN_NUM_MISO,
        .sclk_io_num = PIN_NUM_CLK,
        .quadwp_io_num = -1,
        .quadhd_io_num = -1,
        .max_transfer_sz = 4000,
    };
    ret = spi_bus_initialize(host.slot, &bus_cfg, SDSPI_DEFAULT_DMA);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize bus.");
        return;
    }

    // This initializes the slot without card detect (CD) and write protect (WP) signals.
    // Modify slot_config.gpio_cd and slot_config.gpio_wp if your board has these signals.
    sdspi_device_config_t slot_config = SDSPI_DEVICE_CONFIG_DEFAULT();
    slot_config.gpio_cs = PIN_NUM_CS;
    slot_config.host_id = host.slot;

    ESP_LOGI(TAG, "Mounting filesystem");
    ret = esp_vfs_fat_sdspi_mount(mount_point, &host, &slot_config, &mount_config, &card);

    if (ret != ESP_OK) {
        if (ret == ESP_FAIL) {
            ESP_LOGE(TAG, "Failed to mount filesystem. "
                     "If you want the card to be formatted, set the CONFIG_EXAMPLE_FORMAT_IF_MOUNT_FAILED menuconfig option.");
        } else {
            ESP_LOGE(TAG, "Failed to initialize the card (%s). "
                     "Make sure SD card lines have pull-up resistors in place.", esp_err_to_name(ret));
        }
        return;
    }
    ESP_LOGI(TAG, "Filesystem mounted");

    // Card has been initialized, print its properties
    sdmmc_card_print_info(stdout, card);

    return 0;

    // esp_spiffs_format(MOUNT_POINT);

    // Use POSIX and C standard library functions to work with files.

    // First create a file.
    const char *file_hello = MOUNT_POINT"/hello.bin";

    ESP_LOGI(TAG, "Opening file %s", file_hello);
    FILE *f = fopen(file_hello, "w");
    if (f == NULL) {
        ESP_LOGE(TAG, "Failed to open file for writing");
        return;
    }
    fprintf(f, "Hello %s!\n", card->cid.name);
    fclose(f);
    ESP_LOGI(TAG, "File written");

    //---------------------------------------------------------
    char* data_f_bin_p = calloc(1, 1024*1024);
    uint8_t data_cnt = 0;
    for(int i=0; i<1024*1024; i++, data_cnt++)
    {
        *(data_f_bin_p+i) = data_cnt;
    }

    FILE* file_bin_p = NULL;
    const char *file_bin_path = MOUNT_POINT"/test_file_bin.bin";
    ESP_LOGI(TAG, "Opening file %s", file_bin_path);
    file_bin_p = fopen(file_bin_path,"w");
    if(file_bin_p!=NULL)
    {
        int start_time = esp_timer_get_time()/1000;
        fwrite(data_f_bin_p, sizeof(char), 1024*1024, file_bin_p);
        int end_time = esp_timer_get_time()/1000;
        ESP_LOGI("app_main()", "write 1MB binary file to sd card time used %dms", end_time-start_time);
        fclose(file_bin_p);
    }
    else
    {
        perror("connot open or creat test_file_bin.bin");
        // printf("connot open or creat test_file_bin.bin\n");
        printf("\033[31merror:%s,in line %d\033[0m\n", __FILE__, __LINE__);
    }
    
    //---------------------------------------------------------

    const char *file_foo = MOUNT_POINT"/foo.txt";

    // Check if destination file exists before renaming
    struct stat st;
    if (stat(file_foo, &st) == 0) {
        // Delete it if it exists
        unlink(file_foo);
    }

    // Rename original file
    ESP_LOGI(TAG, "Renaming file %s to %s", file_hello, file_foo);
    if (rename(file_hello, file_foo) != 0) {
        ESP_LOGE(TAG, "Rename failed");
        return;
    }

    // Open renamed file for reading
    ESP_LOGI(TAG, "Reading file %s", file_foo);
    f = fopen(file_foo, "r");
    if (f == NULL) {
        ESP_LOGE(TAG, "Failed to open file for reading");
        return;
    }

    // Read a line from file
    char line[64];
    fgets(line, sizeof(line), f);
    fclose(f);

    // All done, unmount partition and disable SPI peripheral
    esp_vfs_fat_sdcard_unmount(mount_point, card);
    ESP_LOGI(TAG, "Card unmounted");

    //deinitialize the bus after all devices are removed
    spi_bus_free(host.slot);
}

typedef struct
{
    esp_vfs_fat_sdmmc_mount_config_t mount_config;
    sdmmc_card_t *card;
    sdmmc_host_t host;
    spi_bus_config_t bus_cfg;
    sdspi_device_config_t slot_config;
    char mount_point[32];
}my_sdcard_info_t;

my_sdcard_info_t my_sdcard_info={

    .mount_config={
        #ifdef CONFIG_EXAMPLE_FORMAT_IF_MOUNT_FAILED
                .format_if_mount_failed = true,
        #else
                .format_if_mount_failed = false,
        #endif // EXAMPLE_FORMAT_IF_MOUNT_FAILED
                .max_files = 5,
                .allocation_unit_size = 16 * 1024
    },

    .host=SDSPI_HOST_DEFAULT(),

    .bus_cfg={
        .mosi_io_num = PIN_NUM_MOSI,
        .miso_io_num = PIN_NUM_MISO,
        .sclk_io_num = PIN_NUM_CLK,
        .quadwp_io_num = -1,
        .quadhd_io_num = -1,
        .max_transfer_sz = 4000,
    },

    .slot_config = SDSPI_DEVICE_CONFIG_DEFAULT()
};

int My_SDcard_Init(void)
{
    return 0;
}

int My_SDcard_Deinit(void)
{
    return 0;
}

int My_SDcard_Mount(char* mount_point)
{
    esp_err_t ret;

    if(mount_point==NULL)
    {
        return -1;
    }

    memset(my_sdcard_info.mount_point, 0, sizeof(my_sdcard_info.mount_point));
    int mount_point_strlen = strlen(mount_point);
    if(mount_point_strlen>=sizeof(my_sdcard_info.mount_point))
    {
        mount_point_strlen = sizeof(my_sdcard_info.mount_point) - 1;
    }
    memcpy(my_sdcard_info.mount_point, mount_point, mount_point_strlen);

    my_sdcard_info.slot_config.gpio_cs = PIN_NUM_CS;
    my_sdcard_info.slot_config.host_id = my_sdcard_info.host.slot;

    ret = spi_bus_initialize(my_sdcard_info.host.slot, &my_sdcard_info.bus_cfg, SDSPI_DEFAULT_DMA);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize bus.");
        return;
    }

    ESP_LOGI(TAG, "Mounting filesystem");
    ret = esp_vfs_fat_sdspi_mount(my_sdcard_info.mount_point, &my_sdcard_info.host, &my_sdcard_info.slot_config, &my_sdcard_info.mount_config, &my_sdcard_info.card);

    if (ret != ESP_OK) {
        if (ret == ESP_FAIL) {
            ESP_LOGE(TAG, "Failed to mount filesystem. "
                     "If you want the card to be formatted, set the CONFIG_EXAMPLE_FORMAT_IF_MOUNT_FAILED menuconfig option.");
        } else {
            ESP_LOGE(TAG, "Failed to initialize the card (%s). "
                     "Make sure SD card lines have pull-up resistors in place.", esp_err_to_name(ret));
        }
        return;
    }
    ESP_LOGI(TAG, "Filesystem mounted");

    // Card has been initialized, print its properties
    sdmmc_card_print_info(stdout, my_sdcard_info.card);

    return 0;
}

int My_SDcard_Unmount(char* mount_point)
{
    // All done, unmount partition and disable SPI peripheral
    esp_vfs_fat_sdcard_unmount(my_sdcard_info.mount_point, my_sdcard_info.card);
    ESP_LOGI(TAG, "Card unmounted");

    //deinitialize the bus after all devices are removed
    spi_bus_free(my_sdcard_info.host.slot);


    return 0;
}