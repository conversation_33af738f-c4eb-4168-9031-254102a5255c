#ifndef MYWIFI_H
#define MYWIFI_H

#ifdef __cplusplus
extern "C" {
#endif

#include <lwip/netdb.h>

#define WIFI_FUNC   1
#if WIFI_FUNC

#define MYWIFI_ENABLE_DEFAULT           1
#define MYWIFI_MODE_DEFAULT             3
#define MYWIFI_STA_SSID_DEFAULT             "newguodong"
#define MYWIFI_STA_PASS_DEFAULT             "ysyhljt123"
#define MYWIFI_AP_SSID_DEFAULT              "SWQZ_WC2D8_AP"
#define MYWIFI_AP_PASS_DEFAULT              "esp32password"

#define ESP32_AP_CHANNEL        3              // 信道，中国一般是0~13
#define ESP32_MAX_CONN          3              // 最多有多少站点可以连接这个wifi(ESP32)，最大为4

#define WIFI_STATE_ONLINE   1
#define WIFI_STATE_OFFLINE  0

typedef struct
{
    int mode;
    char wifi_ssid[32];
    char wifi_password[64];
    char ap_ssid[32];
    char ap_password[64];
}wifi_nvs_t;

#define MY_WIFI_SSID_LEN_MAX    (32)
#define MY_WIFI_PASSWORD_LEN_MAX    (64)
typedef struct
{
    int enable;
    int mode;
    char sta_ssid[32];
    char sta_password[64];
    char ap_ssid[32];
    char ap_password[64];
}my_wifi_conf_t;

typedef struct
{
    my_wifi_conf_t conf;             //my_wifi_conf_t
    uint8_t init_ok;                //WIFI是否已经开启
    uint8_t first_init;             //开机后第一次配置
    int wifi_state;
    int wifi_changed;
}my_wifi_info_t;



#define WIFI_NVS_FORM_NAME	"NVS_WIFI"
#define WIFI_NVS_KEY_NAME	"NVS_WIFI_1"


int MyWifi_Init(my_wifi_conf_t* conf);
// int My_Wifi_Get_State(void);


#ifdef __cplusplus
}
#endif

#endif

#endif /*#ifndef __USER_WIFI_APSTA_H__*/