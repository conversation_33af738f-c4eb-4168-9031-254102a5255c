#ifndef MY_WHGM5_H
#define MY_WHGM5_H

#include "my_network_config.h"

#if NETWORK_TYPE&NETWORK_TYPE_LTE_WHGM5
#include <lwip/netdb.h>

#include "my_whgm5_config.h"
#include "my_uart.h"



typedef struct
{
    char address[64];
    uint16_t port;
    uint8_t id;
    uint8_t address_type;
}my_tcp_conf_t;

typedef struct
{
    uint8_t method;
    uint8_t head_filter;
    uint16_t timeout;
    char url[100];
    char head[200];
    char address[100];
    uint16_t port;
}my_http_conf_t;

typedef struct
{
    my_tcp_conf_t* tcp_channel_1;
    my_tcp_conf_t* tcp_channel_2;
    my_tcp_conf_t* tcp_channel_3;
    my_tcp_conf_t* tcp_channel_4;
    my_http_conf_t* http;
    uint8_t networkmode;
}my_lte_network_confs_t;

typedef struct
{
    int baudrate;
}myconf_lte_t;

#define HEART_P_TO_NET  1
#define HEART_P_TO_UART  2
typedef struct
{
    uint32_t UART;      //uart参数，目前只有一个波特率，1200~921600，模块默认115200
    uint16_t UARTFL;    //串口打包长度，5~4096，模块默认1024
    uint16_t UARTFT;    //串口打包时间，10~500ms，模块默认50ms
    uint16_t SOCKASL;   //socka短连接使能
    uint16_t SHORTATM;      //短连接超时，1~600s，模块默认10s
    uint16_t SOCKRSNUM;     //socket最大重连次数，1-65535，模块默认60
    uint16_t SOCKRSTIM;     //socket重连间隔，10-65535，模块默认10s
    uint8_t KEEPALIVE;  //包含四个通道，bit0-channel1,...
    uint8_t HEARTEN;    //心跳包使能，，默认打开
    uint8_t HEARTTP;        //心跳包发向哪里，串口或网络，默认发向网络
    uint16_t HEARTTM;       //心跳包发送间隔0-300,默认30
    uint8_t HEARTSORT;      //心跳包数据类型,默认user
    uint8_t NATEN;          //网络AT使能，模块默认打开
    uint8_t SDPEN;          //套接字分发协议使能，模块默认关闭，软件默认开启
    uint16_t RSTIM;          //无数据重启时间，60~65535s,默认1800s
    //char STMSG[20];         //启动信息，模块默认"WH-GM5",软件默认
    //char HEARTDT[300];      //心跳包数据，模块默认7777772E7573722E636E(www.usr.cn)
}my_whgm5_basic_conf_nvs_t;
#define WHGM5_NVS_FORM_NAME	"NVS_WHGM5"
#define WHGM5_NVS_KEY_NAME	"NVS_WHGM5_1"

#define WHGM5_WORKMODE_CMD  1
#define WHGM5_WORKMODE_DATA  2
typedef struct
{
    uint8_t init_ok;
    uint8_t workmode;
    uint8_t installed;
    uint8_t networkmode;
    uint8_t tcp_channel_1_id;
    uint8_t tcp_channel_2_id;
    uint8_t tcp_channel_3_id;
    uint8_t tcp_channel_4_id;

    char firmware_version[64];
    char firmware_buildtime[64];
    char SN[32];
    char IMEI[32];
    char ICCID[32];

    char SYSINFO[32];
    char APN[64];
    char CSQ[8];
    char IP[32];
    char LBS[128];
    char CCLK[64];
    time_t cclk_timestamp;
}my_whgm5_info_t;


#define MyWHGM5_TcpDataRcv_Task_CREATE_TYPE    TASK_CREATE_TYPE_DYNAMIC

#if MyWHGM5_TcpDataRcv_Task_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
#define MyWHGM5_TcpDataRcv_Task_COREID    1
#define MyWHGM5_TcpDataRcv_Task_priority  17
#define MyWHGM5_TcpDataRcv_Task_task_stack_size   3072
#elif MyWHGM5_TcpDataRcv_Task_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
#define MyWHGM5_TcpDataRcv_Task_COREID    0
#define MyWHGM5_TcpDataRcv_Task_priority  5
#define MyWHGM5_TcpDataRcv_Task_task_stack_size   4096
#endif

#define MyWHGM5_TcpDataSend_Task_CREATE_TYPE    TASK_CREATE_TYPE_DYNAMIC

#if MyWHGM5_TcpDataSend_Task_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
#define MyWHGM5_TcpDataSend_Task_COREID    1
#define MyWHGM5_TcpDataSend_Task_priority  16
#define MyWHGM5_TcpDataSend_Task_task_stack_size   3072
#elif MyWHGM5_TcpDataSend_Task_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
#define MyWHGM5_TcpDataSend_Task_COREID    0
#define MyWHGM5_TcpDataSend_Task_priority  5
#define MyWHGM5_TcpDataSend_Task_task_stack_size   4096
#endif

typedef struct
{
	uint8_t id;
	uint16_t len;
	uint8_t valid;
	uint64_t time_stamp;
	char* data;
}my_whgm5_queue_item_t;
#define WHGM5_QUEUE_LEN 8
#define WHGM5_DATA_PACK_SIZE        UART_A_RCV_BUF_SIZE
#define WHGM5_UART_RCV_TIMEOUT  (35+5)//921600波特率下接收4096字节数据需要35ms，加上串口分帧5ms


/**
 * @brief WHGM5 TCP方式发送数据
 * @param data 数据指针
 * @param len data数据长度
 * @param timeout 串口超时，一般不会超时
 * @param tcp_channel TCP通道，WHGM5支持A-D四个通道
 * @return 发送成功：0，超时：1，通道未开启：2
 * @note 虽然WHGM5有四个TCP通道，但主控只有一个串口与其硬件连接，所以不论向哪个TCP通道发送数据，都需要先获取串口信号量
 * @note 在函数开始处每次都检查WHGM5当前模式，以及对应通道的网络状态
 */
int MyWHGM5_TcpSendData(void const* data, uint32_t len, uint32_t timeout, uint8_t tcp_channel);

/**
 * @brief 从WHGM5接收数据
 * @param saving 数据指针
 * @param timeout 接收超时，单位ms
 * @return 0成功，1失败，2 saving为空
 */
uint8_t MyWHGM5_TcpRcvData(my_whgm5_queue_item_t** const saving, uint32_t timeout);


/**
 * @brief WHGM5 查询实时信息，如CSQ等需要不定期切换到命令模式查询的信息
 * @return 0:success
 * @note 不在my_whgm5.c中做定时调用，而是在应用层选择空闲时调用
 */
int MyWHGM5_CheckRTI(void);

/**
 * @brief 获取WHGM5的IP
 * @param saving 用来保存my_whgm5_info_t的指针，由主调函数提供
 * @return 0
 */
uint8_t MyWHGM5_GetInfo(my_whgm5_info_t* saving);

/**
 * @brief 获取WHGM5的CSQ
 * @return CSQ:0-31
 * @note 返回的CSQ并不是实时的，而是上一次MyWHGM5_CheckRTI()被调用时更新的值
 */
uint8_t MyWHGM5_GetCSQ(void);

/**
 * @brief 获取WHGM5的IP
 * @param saving 用来保存IP字符串的指针，由主调函数提供
 * @param len saving 空间大小
 * @return 0
 * @note 返回的IP并不是实时的，而是上一次MyWHGM5_CheckRTI()被调用时更新的值
 */
uint8_t MyWHGM5_GetIP(char* saving, uint8_t len);

/**
 * @brief 获取WHGM5的LBS
 * @param saving_lbs 用来保存LBS字符串的指针，由主调函数提供
 * @param len saving 空间大小
 * @return 0
 * @note 返回的LBS并不是实时的，而是上一次MyWHGM5_CheckRTI()被调用时更新的值
 */
uint8_t MyWHGM5_GetLBS(char* saving, uint8_t len);

/**
 * @brief 获取WHGM5的CCLK
 * @param saving_cclk 用来保存CCLK字符串的指针，由主调函数提供
 * @param len saving 空间大小
 * @return 0
 * @note 返回的CCLK并不是实时的，而是上一次MyWHGM5_CheckRTI()被调用时更新的值
 */
uint8_t MyWHGM5_GetCCLK(char* saving, uint8_t len);

/**
 * @brief 设置WHGM5 TCP通道IP地址
 * @param tcp_channel
 * @param adress
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetTCPAddress(uint8_t tcp_channel, char* address);

/**
 * @brief 设置WHGM5 TCP通道端口
 * @param tcp_channel
 * @param port
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetTCPPort(uint8_t tcp_channel, uint32_t port);

/**
 * @brief 设置WHGM5 打开或关闭TCP通道
 * @param tcp_channel
 * @param newstate:1开启，0关闭
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetTCPChannelEnState(uint8_t tcp_channel, uint8_t newstate);

/**
 * @brief 设置WHGM5 设置TCP通道连接类型
 * @param tcp_channel
 * @param newstate:1长连接，0短连接
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetTCPChannelSL(uint8_t tcp_channel, uint8_t newstate);

/**
 * @brief 设置WHGM5 设置TCP通道短连接超时
 * @param tcp_channel 针对所有通道
 * @param timeout 1~600s，模块默认10s
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetTCPChannelTATM(uint8_t tcp_channel, uint8_t timeout);

/**
 * @brief 设置WHGM5 设置TCP通道SOCKET最大重连次数，模块默认60
 * @param tcp_channel 针对所有通道
 * @param rsnum 1-65535，模块默认60
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetTCPChannelRSNUM(uint8_t tcp_channel, uint8_t rsnum);

/**
 * @brief 设置WHGM5 设置TCP通道SOCKET重连间隔，模块默认10s
 * @param tcp_channel 针对所有通道
 * @param rstim 10-65535，模块默认10s
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetTCPChannelRSTIM(uint8_t tcp_channel, uint8_t rstim);

/**
 * @brief 设置WHGM5 设置TCP通道KeepAlive参数，默认开启
 * @param tcp_channel
 * @param newstate:1开启功能，0关闭
 * @param idle 心跳周期，默认60
 * @param interval 发送间隔，默认15
 * @param count 重试次数，默认3
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetTCPChannelKeepAlive(uint8_t tcp_channel, uint8_t newstate, uint16_t idle, uint8_t interval, uint8_t count);

/**
 * @brief 设置WHGM5 设置WHGM5心跳包使能状态，默认打开
 * @param newstate:1开启功能，0关闭
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetHeartEnState(uint8_t newstate);

/**
 * @brief 设置WHGM5 设置WHGM5心跳包发送方式，默认发向网络
 * @param newstate:1发向网络，0发向串口
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetHeartTP(uint8_t newstate);

/**
 * @brief 设置WHGM5 设置WHGM5心跳包数据
 * @param heartdata
 * @param len:0-300
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetHeartDT(void* heartdata, uint16_t len);

/**
 * @brief 设置WHGM5 设置WHGM5心跳包发送间隔,默认30
 * @param newtm 1-65535
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetHeartTM(uint8_t newtm);

/**
 * @brief 设置WHGM5 设置WHGM5心跳包数据类型,默认user
 * @param newstate:1:iccid,2:imei,3:sn,4:user,5:lbs
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetHeartSORT(uint8_t newstate);

/**
 * @brief 设置WHGM5 设置WHGM5串口参数,默认115200，8位数据，1停止位，无奇偶校验，无流控
 * @param baudrate 1200~921600
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetUart(uint32_t baudrate);

/**
 * @brief 设置WHGM5 设置WHGM5串口打包长度,默认1024
 * @param fl_len 5~4096
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetUartFL(uint16_t fl_len);

/**
 * @brief 设置WHGM5 设置WHGM5串口打包时间,默认50ms
 * @param fl_len 10~500ms
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetUartFT(uint16_t ft);

/**
 * @brief 设置WHGM5 网络AT使能状态，模块默认打开
 * @param newstate 0:关闭，1:开启
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetNATEN(uint16_t newstate);

/**
 * @brief 设置WHGM5 套接字分发协议使能状态
 * @param newstate 0:关闭，1:开启
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetSDPEN(uint16_t newstate);

/**
 * @brief 设置WHGM5 无数据重启时间,默认1800s
 * @param newtim 60~65535s
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetRSTIM(uint16_t newtim);

/**
 * @brief 设置WHGM5 启动信息
 * @param stmsg <=20byte
 * @return 0
 * @note 保存在内存中，需要调用MyWHGM5_SavingConfigToNVS()
 */
uint8_t MyWHGM5_SetSTMSG(char* stmsg);

/**
 * @brief 获取WHGM5的IMEI,此函数将获取到的IMEI字符串放入saving_imei，IMEI长度一般为15-17位
 * @param saving:主调函数提供
 * @param len saving 空间大小
 * @return 0:成功，1:可能是还没有调用过MyWHGM5_CheckRTI()函数
 */
uint8_t MyWHGM5_GetIMEI(char* saving, uint8_t len);

/**
 * @brief 获取WHGM5的ICCID
 * @param saving:主调函数提供,此函数将获取到的ICCID字符串放入saving_iccid，ICCID长度一般为20位
 * @param len saving 空间大小
 * @return 0:成功，1:可能是还没有调用过MyWHGM5_CheckRTI()函数
 */
uint8_t MyWHGM5_GetICCID(char* saving, uint8_t len);

/**
 * @brief 获取WHGM5的SN,长度为20，32之内，各厂商不一
 * @param saving:主调函数提供,此函数将获取到的SN字符串放入saving_sn，SN长度一般为20位
 * @param len saving 空间大小
 * @return 0:成功，1:可能是还没有调用过MyWHGM5_CheckRTI()函数
 */
uint8_t MyWHGM5_GetSN(char* saving, uint8_t len);

/**
 * @brief 获取WHGM5的固件编译时间
 * @param saving:主调函数提供
 * @param len saving 空间大小
 * @return 0:成功，1:可能是还没有调用过MyWHGM5_CheckRTI()函数
 */
uint8_t MyWHGM5_GetBuild(char* saving, uint8_t len);

/**
 * @brief 获取WHGM5的固件版本号
 * @param saving:主调函数提供
 * @param len saving 空间大小
 * @return 0:成功，1:可能是还没有调用过MyWHGM5_CheckRTI()函数
 */
uint8_t MyWHGM5_GetVer(char* saving, uint8_t len);

/**
 * @brief 获取WHGM5的连接制式
 * @param saving:主调函数提供
 * @param len saving 空间大小
 * @return 0:成功，1:可能是还没有调用过MyWHGM5_CheckRTI()函数
 */
uint8_t MyWHGM5_GetSysInfo(char* saving, uint8_t len);

/**
 * @brief 刷新WHGM5动态参数
 * @return 0
 */
uint8_t MyWHGM5_RefreshDynamicInfo(void);

/**
 * @brief 获取WHGM5的初始化状态
 * @return 0:未配置，1:已配置
 */
uint8_t MyWHGM5_GetInitState(void);

/**
 * @brief 获取WHGM5的工作模式
 * @return WHGM5_WORKMODE_CMD, WHGM5_WORKMODE_DATA
 */
uint8_t MyWHGM5_GetWorkmode(void);


/**
 * @brief 初始化WHGM5
 * @param confs 主调函数提供
 * @param myconf_lte lte硬件配置
 * @param checkconfig 1：重新按照配置表检查配置，0：不检查
 * @return 0:成功
 */
uint8_t MyWHGM5_Init(my_lte_network_confs_t* confs, myconf_lte_t* myconf_lte, uint8_t checkconfig);

/**
 * @brief 初始化WHGM5, 仅配置为TCP单通道模式
 * @param confs 主调函数提供
 * @param myconf_lte lte硬件配置
 * @param checkconfig 1：重新按照配置表检查配置，0：不检查
 * @return 0:成功
 */
uint8_t MyWHGM5_Init_SimpleTcpMode(my_tcp_conf_t* confs, myconf_lte_t* myconf_lte, uint8_t checkconfig);

/**
 * @brief 重启WHGM5，并且不检查配置，优先使用软件重启，需要在应用层空闲时才能调用
 * @return 0:成功
 */
void MyWHGM5_RestartOnly(void);

/**
 * @brief 重启WHGM5，并且检查配置，优先使用软件重启，需要在应用层空闲时才能调用
 * @param confs 主调函数提供
 * @param myconf_lte lte硬件配置
 * @return 0:成功
 */
void MyWHGM5_Restart_Reconfig_SimpleTcpMode(my_tcp_conf_t* confs, myconf_lte_t* myconf_lte);

time_t MyWHGM5_Getcclktimestamp(void);
int MyWHGM5_timesync(void);

#endif

#endif