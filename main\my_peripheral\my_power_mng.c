#include "my_config.h"
#include "my_power_mng.h"
#include "led.h"
#include "my_gpio.h"

#include "esp_log.h"

#include "driver/adc.h"
#include "esp_adc_cal.h"


#if MY_POWER_MNG_FUNC


#if POWERCHECK_TYPE==POWERCHECK_ADC
#define poweroff_voltage    700//必须在1100-1150之间


#define DEFAULT_VREF    3300        //Use adc2_vref_to_gpio() to obtain a better estimate
#define NO_OF_SAMPLES   64          //Multisampling

static esp_adc_cal_characteristics_t *adc_chars;
static const adc_channel_t channel = ADC_CHANNEL_6;     //IO14
static const adc_atten_t atten = ADC_ATTEN_DB_11;
static const adc_unit_t unit = ADC_UNIT_2;

static void check_efuse()
{
    //Check TP is burned into eFuse
    if (esp_adc_cal_check_efuse(ESP_ADC_CAL_VAL_EFUSE_TP) == ESP_OK) {
        printf("eFuse Two Point: Supported\n");
    } else {
        printf("eFuse Two Point: NOT supported\n");
    }

    //Check Vref is burned into eFuse
    if (esp_adc_cal_check_efuse(ESP_ADC_CAL_VAL_EFUSE_VREF) == ESP_OK) {
        printf("eFuse Vref: Supported\n");
    } else {
        printf("eFuse Vref: NOT supported\n");
    }
}

static void print_char_val_type(esp_adc_cal_value_t val_type)
{
    if (val_type == ESP_ADC_CAL_VAL_EFUSE_TP) {
        printf("Characterized using Two Point Value\n");
    } else if (val_type == ESP_ADC_CAL_VAL_EFUSE_VREF) {
        printf("Characterized using eFuse Vref\n");
    } else {
        printf("Characterized using Default Vref\n");
    }
}

static int poweroff = 0;
static int power_bat_change = 0;
static int sysreset_powercheck_finish = 0;
static int powercheck_count = 0;

int GetPower(void)
{
    return power_bat_change;
}

void CheckPowerTask(void* param)
{
	int poweroff_count = 0;
	int poweron_count = 0;
	 //Check if Two Point or Vref are burned into eFuse
    check_efuse();

    //Configure ADC
    if (unit == ADC_UNIT_1) {
        adc1_config_width(ADC_WIDTH_BIT_12);
        adc1_config_channel_atten(channel, atten);
    } else {
        adc2_config_channel_atten((adc2_channel_t)channel, atten);
    }

    //Characterize ADC
    adc_chars = calloc(1, sizeof(esp_adc_cal_characteristics_t));
    esp_adc_cal_value_t val_type = esp_adc_cal_characterize(unit, atten, ADC_WIDTH_BIT_12, DEFAULT_VREF, adc_chars);
    print_char_val_type(val_type);

    //Continuously sample ADC1
    while (1) {
        uint32_t adc_reading = 0;
        //Multisampling
        for (int i = 0; i < NO_OF_SAMPLES; i++) {
            if (unit == ADC_UNIT_1) {
                adc_reading += adc1_get_raw((adc1_channel_t)channel);
            } else {
                int raw;
                adc2_get_raw((adc2_channel_t)channel, ADC_WIDTH_BIT_12, &raw);
                adc_reading += raw;
            }
        }
        adc_reading /= NO_OF_SAMPLES;
        //Convert adc_reading to voltage in mV
        uint32_t voltage = esp_adc_cal_raw_to_voltage(adc_reading, adc_chars);
        //printf("Raw: %d\tVoltage: %dmV\n", adc_reading, voltage);

		if(voltage<poweroff_voltage)
		{
			if(!poweroff)
			{
				poweroff_count++;
				ESP_LOGI("", "-------------------------------------------------------------------\n");
				ESP_LOGI("", "-------------------power voltage too low:%dmv----------------------\n", voltage);
				ESP_LOGI("", "-------------------------------------------------------------------\n");
				if(poweroff_count>=3)
				{
					power_bat_change = 1;
                    #if LED_FUNC
					led_close();
                    #endif
					poweroff_count = 0;
					poweroff = 1;
					ESP_LOGI("", "CheckPowerTask: ---------------power off------------\n");
				}
			}
		}
		else
		{
			poweroff_count = 0;
			if(poweroff)
			{
				poweron_count++;
				if(poweron_count>=3)
				{
					power_bat_change = 0;
					//LedFastFlash(200, 0);
					poweron_count = 0;
					poweroff = 0;
					ESP_LOGI("", "CheckPowerTask: ---------------power on------------\n");
				}
			}
			
		}
        vTaskDelay(1000/portTICK_PERIOD_MS);
        if(++powercheck_count==4)
        {
            sysreset_powercheck_finish = 1;
        }
    }
}

int PowerCheckGet_InitState(void)
{
    while(!sysreset_powercheck_finish)
    {
        vTaskDelay(100/portTICK_PERIOD_MS);
    }
    return 0;
}

void PowerCheckInit(void)
{
    xTaskCreate(CheckPowerTask,  "CheckPowerTask",    4096,    NULL, 14, NULL);
}

#elif POWERCHECK_TYPE==POWERCHECK_IO

my_power_mng_info_t my_power_mng_info;


#define GPIO_POWER_CHECK_PIN    41
#define GPIO_INPUT_PIN_SEL  (1ULL<<GPIO_POWER_CHECK_PIN)

#define GPIO_MOBILEPOWER_WAKEUP_PIN    -1
#define GPIO_MOBILEPOWER_WAKEUP_PIN_SEL  (1ULL<<GPIO_MOBILEPOWER_WAKEUP_PIN)


static int poweroff = 0;
static int power_bat_change = 0;
static int sysreset_powercheck_finish = 0;
static int powercheck_count = 0;

int GetPower(void)
{
    return !poweroff;
}

TaskHandle_t MyPowerMng_TaskHandle = NULL;
void MyPowerMng_Task(void* param)
{
	int poweroff_count = 0;
	int poweron_count = 0;

    int voltage = gpiolevel_array[GPIO_POWER_CHECK_PIN];

    while (1)
    {
        voltage = gpiolevel_array[GPIO_POWER_CHECK_PIN];

		if(!voltage)
		{
			if(!poweroff)
			{
				poweroff_count++;
				ESP_LOGI("", "-------------------------------------------------------------------\n");
				ESP_LOGI("", "-------------------power voltage too low:%d----------------------\n", voltage);
				ESP_LOGI("", "-------------------------------------------------------------------\n");
				if(poweroff_count>=2)
				{
					power_bat_change = 1;
                    #if LED_FUNC
					led_close();
                    #endif
					poweroff_count = 0;
					poweroff = 1;
                    my_power_mng_info.external_power_state = EXTERNAL_POWER_NO_SUPPLY;
                    my_power_mng_info.power_source = POWER_SOURCE_BAK;
					ESP_LOGI("", "CheckPowerTask: ---------------power off------------\n");
				}
			}
		}
		else
		{
			poweroff_count = 0;
			if(poweroff)
			{
				poweron_count++;
				if(poweron_count>=2)
				{
					power_bat_change = 0;
					//LedFastFlash(200, 0);
					poweron_count = 0;
					poweroff = 0;
                    my_power_mng_info.external_power_state = EXTERNAL_POWER_SUPPLY;
                    my_power_mng_info.power_source = POWER_SOURCE_EXTERNAL;
					ESP_LOGI("", "CheckPowerTask: ---------------power on------------\n");
				}
			}
			
		}
        vTaskDelay(MyPowerMng_Task_refresh_time/portTICK_PERIOD_MS);
        if(++powercheck_count==2)
        {
            sysreset_powercheck_finish = 1;
        }
    }
}

int PowerCheckGet_InitState(void)
{
    while(!sysreset_powercheck_finish)
    {
        vTaskDelay(100/portTICK_PERIOD_MS);
    }
    return 0;
}

void ExternalPowerCheckGpioInit(void)
{
    gpio_config_t io_conf;

    //interrupt of rising edge
    io_conf.intr_type = GPIO_PIN_INTR_POSEDGE;
    //bit mask of the pins, use GPIO4/5 here
    io_conf.pin_bit_mask = GPIO_INPUT_PIN_SEL;
    //set as input mode    
    io_conf.mode = GPIO_MODE_INPUT;
    //enable pull-up mode
    io_conf.pull_up_en = 0;
    io_conf.pull_down_en = 0;
    gpio_config(&io_conf);

    //change gpio intrrupt type for one pin
    gpio_set_intr_type(GPIO_POWER_CHECK_PIN, GPIO_INTR_DISABLE);

    MyGpioInputPinRegister(GPIO_POWER_CHECK_PIN);
}

void BAKPWR_ON(void)
{
    #if MY_POWER_MNG_FUNC_DEBUG
    ESP_LOGI("BAKPWR_ON()", "enable bak power\n");
    #endif
    MyGPIO_SET(BAK_POWER_CTRL_GPIO_PIN, 1);
    my_power_mng_info.bak_power_state = BAK_POWER_READY;
    my_power_mng_info.bak_power_connected = BAK_POWER_READY;
}

void BAKPWR_OFF(void)
{
    #if MY_POWER_MNG_FUNC_DEBUG
    ESP_LOGI("BAKPWR_OFF()", "disable bak power\n");
    #endif
    MyGPIO_SET(BAK_POWER_CTRL_GPIO_PIN, 0);
    my_power_mng_info.bak_power_state = BAK_POWER_NO_READY;
    my_power_mng_info.bak_power_connected = BAK_POWER_NO_READY;
}

void BakPowerCtrlGPIOInit(void)
{
    gpio_config_t io_conf;
    io_conf.intr_type = GPIO_PIN_INTR_DISABLE;
    io_conf.mode = GPIO_MODE_OUTPUT;
    io_conf.pin_bit_mask = BAK_POWER_CTRL_GPIO_PIN_SEL;
    io_conf.pull_down_en = 0;
    io_conf.pull_up_en = 0;
    gpio_config(&io_conf);

    BAKPWR_ON();
}

uint8_t MyPowerMng_GetExternalPowerCon(void)
{
    return my_power_mng_info.external_power_connected;
}

uint8_t MyPowerMng_GetExternalPowerState(void)
{
    return my_power_mng_info.external_power_state;
}

uint8_t MyPowerMng_GetBakPowerCon(void)
{
    return my_power_mng_info.bak_power_connected;
}

uint8_t MyPowerMng_GetBakPowerState(void)
{
    return my_power_mng_info.bak_power_state;
}

uint8_t MyPowerMng_GetMyWHGM5PowerCon(void)
{
    return my_power_mng_info.whgm5_power_connected;
}

uint8_t MyPowerMng_GetMyWHGM5PowerState(void)
{
    return my_power_mng_info.whgm5_power_state;
}

#define WHGM5_POWER_GPIO_PIN  42
#define WHGM5_POWER_GPIO_PIN_SEL  ( 1ULL<<WHGM5_POWER_GPIO_PIN )
static uint8_t my_whgm5_powermng_gpio_init_ok = 0;
static uint8_t mobilepowerwakeup_gpio_init_ok = 0;
static uint8_t mobilepower_state = 0;
void MyPowerMng_MyWHGM5_PowerMngGpioInit(void);
void MyPowerMng_MyWHGM5_PowerON(void)
{
    if(!my_whgm5_powermng_gpio_init_ok)
    {
        MyPowerMng_MyWHGM5_PowerMngGpioInit();
    }
    MyGPIO_SET(WHGM5_POWER_GPIO_PIN, 1);
    my_power_mng_info.whgm5_power_connected = 1;
    my_power_mng_info.whgm5_power_state = 1;
}

void MyPowerMng_MyWHGM5_PowerOFF(void)
{
    if(!my_whgm5_powermng_gpio_init_ok)
    {
        MyPowerMng_MyWHGM5_PowerMngGpioInit();
    }
    MyGPIO_SET(WHGM5_POWER_GPIO_PIN, 0);
    my_power_mng_info.whgm5_power_connected = 0;
    my_power_mng_info.whgm5_power_state = 0;
}

void MyPowerMng_MyWHGM5_PowerMngGpioInit(void)
{
    if(!my_whgm5_powermng_gpio_init_ok)
    {
        gpio_config_t io_conf;
        io_conf.intr_type = GPIO_PIN_INTR_DISABLE;
        io_conf.mode = GPIO_MODE_OUTPUT;
        io_conf.pin_bit_mask = WHGM5_POWER_GPIO_PIN_SEL;
        io_conf.pull_down_en = 0;
        io_conf.pull_up_en = 0;
        gpio_config(&io_conf);

        my_whgm5_powermng_gpio_init_ok = 1;

        MyPowerMng_MyWHGM5_PowerOFF();
    }
}

uint8_t MyPowerMng_GetMyRADARPowerCon(void)
{
    return my_power_mng_info.radar_power_connected;
}

uint8_t MyPowerMng_GetMyRADARPowerState(void)
{
    return my_power_mng_info.radar_power_state;
}

#define RADAR_POWER_GPIO_PIN  13
#define RADAR_POWER_GPIO_PIN_SEL  ( 1ULL<<RADAR_POWER_GPIO_PIN )
static uint8_t my_radar_powermng_gpio_init_ok = 0;
void MyPowerMng_MyRADAR_PowerMngGpioInit(void);
void MyPowerMng_MyRADAR_PowerON(void)
{
    if(!my_radar_powermng_gpio_init_ok)
    {
        MyPowerMng_MyRADAR_PowerMngGpioInit();
    }
    MyGPIO_SET(RADAR_POWER_GPIO_PIN, 0);
    my_power_mng_info.radar_power_connected = 1;
    my_power_mng_info.radar_power_state = 1;
}

void MyPowerMng_MyRADAR_PowerOFF(void)
{
    if(!my_radar_powermng_gpio_init_ok)
    {
        MyPowerMng_MyRADAR_PowerMngGpioInit();
    }
    MyGPIO_SET(RADAR_POWER_GPIO_PIN, 1);
    my_power_mng_info.radar_power_connected = 0;
    my_power_mng_info.radar_power_state = 0;
}

void MyPowerMng_MyRADAR_PowerMngGpioInit(void)
{
    if(!my_radar_powermng_gpio_init_ok)
    {
        gpio_config_t io_conf;
        io_conf.intr_type = GPIO_PIN_INTR_DISABLE;
        io_conf.mode = GPIO_MODE_OUTPUT_OD;
        io_conf.pin_bit_mask = RADAR_POWER_GPIO_PIN_SEL;
        io_conf.pull_down_en = 0;
        io_conf.pull_up_en = 0;
        gpio_config(&io_conf);

        my_radar_powermng_gpio_init_ok = 1;

        MyPowerMng_MyRADAR_PowerOFF();
    }
}

void MyPowerMng_MobilePowerWakeupGpioInit(void)
{
    // if(!mobilepowerwakeup_gpio_init_ok)
    // {
    //     gpio_config_t io_conf;
    //     io_conf.intr_type = GPIO_PIN_INTR_DISABLE;
    //     io_conf.mode = GPIO_MODE_OUTPUT;
    //     io_conf.pin_bit_mask = GPIO_MOBILEPOWER_WAKEUP_PIN_SEL;
    //     io_conf.pull_down_en = 0;
    //     io_conf.pull_up_en = 0;
    //     gpio_config(&io_conf);

    //     mobilepowerwakeup_gpio_init_ok = 1;

    //     MyPowerMng_MobilePower_OFF();
    // }
}

void MyPowerMng_MobilePower_OFF(void)
{
    MyGPIO_SET(GPIO_MOBILEPOWER_WAKEUP_PIN, 0);

    my_power_mng_info.external_power_connected = 0;
}

void MyPowerMng_MobilePower_ON(void)
{
    MyGPIO_SET(GPIO_MOBILEPOWER_WAKEUP_PIN, 1);

    my_power_mng_info.external_power_connected = 1;
}

uint8_t MyPowerMng_ExternalPWR_Activation(uint32_t wait_time)
{
    vTaskDelay(100 / portTICK_PERIOD_MS);

    MyPowerMng_MobilePower_OFF();
    vTaskDelay(wait_time / portTICK_PERIOD_MS);
    MyPowerMng_MobilePower_ON();
    vTaskDelay(1000 / portTICK_PERIOD_MS);

    return 0;
}

uint8_t MyPowerMng_SwitchToExternalPWR(void)
{
    uint8_t external_poweroff = 0;
    uint32_t external_pwr_acti_timer = 0;
    uint32_t external_pwr_acti_wait_time = 3000;
    uint8_t retry_count = 0;
    
    vTaskDelay(4000 / portTICK_PERIOD_MS);

    if(my_power_mng_info.power_source!=POWER_SOURCE_EXTERNAL)
    {
        MyPowerMng_SwitchToExternalPWR_retry:
        //重新激活移动电源
        if(!my_power_mng_info.bak_power_connected||(!gpio_get_level(GPIO_POWER_CHECK_PIN)))
        {
            BAKPWR_ON();
        }
        MyPowerMng_ExternalPWR_Activation(external_pwr_acti_wait_time);
        //等待外部电源被检测到，5s，一般3s内能完成检测
        #define EXTERNAL_PWR_ACTIVATION_TIMEOUT (3*1000)
        do
        {
            external_poweroff = !GetPower();
            vTaskDelay(100 / portTICK_PERIOD_MS);
            external_pwr_acti_timer+=100;
        } while (external_poweroff&&external_pwr_acti_timer<EXTERNAL_PWR_ACTIVATION_TIMEOUT);

        if(external_poweroff&&external_pwr_acti_timer>=EXTERNAL_PWR_ACTIVATION_TIMEOUT&&external_pwr_acti_wait_time==3000&&(++retry_count<3))
        {
            if(retry_count==2)
            {
                external_pwr_acti_wait_time = 33000;
            }
            
            goto MyPowerMng_SwitchToExternalPWR_retry;
        }
        //如果激活移动电源后，检测到外部供电，则关闭后备电池，将电源切换到外部，然后再重新连接后备电源
        vTaskDelay(4000 / portTICK_PERIOD_MS);
        external_poweroff = !GetPower();
        if(!external_poweroff)
        {
            if(gpio_get_level(GPIO_POWER_CHECK_PIN))
            {
                BAKPWR_OFF();
                vTaskDelay(500 / portTICK_PERIOD_MS);
                BAKPWR_ON();
                my_power_mng_info.power_source = POWER_SOURCE_EXTERNAL;
                return 1;
            }
        }
        return 0;
    }
    return 1;
}

void MyPowerMng_OtherGpioInit(void)
{
    gpio_config_t io_conf;
    io_conf.intr_type = GPIO_PIN_INTR_DISABLE;
    io_conf.mode = GPIO_MODE_OUTPUT;
	io_conf.pin_bit_mask = (1<<4);
    io_conf.pull_down_en = 0;
    io_conf.pull_up_en = 0;
    gpio_config(&io_conf);
	MyGPIO_SET(4, 0);
}

void MyPowerMngInit(void)
{
    BaseType_t ret = pdPASS;

    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    my_power_mng_info.power_source = POWER_SOURCE_EXTERNAL;
    my_power_mng_info.external_power_state = EXTERNAL_POWER_SUPPLY;
    
    //外部电源检测IO
    ExternalPowerCheckGpioInit();

    //后备电源升压输出控制IO
    BakPowerCtrlGPIOInit();

    //LTE电源控制IO
    MyPowerMng_MyWHGM5_PowerMngGpioInit();
    //开启LTE电源
    vTaskDelay(2000 / portTICK_PERIOD_MS);
    MyPowerMng_MyWHGM5_PowerON();

    //移动电源激活IO
    MyPowerMng_MobilePowerWakeupGpioInit();
    //开启移动电源与后备电池通路
    MyPowerMng_MobilePower_ON();

    MyPowerMng_OtherGpioInit();

    MyPowerMng_MyRADAR_PowerMngGpioInit();

    MyPowerMng_MyRADAR_PowerON();

    #if MyPowerMng_TASK_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    p_task_stack = calloc(1, MyPowerMng_Task_task_stack_size*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
			if(xTaskCreateStaticPinnedToCore(MyPowerMng_Task, "MyPowerMng_Task", MyPowerMng_Task_task_stack_size, NULL, MyPowerMng_Task_priority, p_task_stack, p_task_data, MyPowerMng_Task_COREID)!=p_task_data)
			{
				ESP_LOGE("MyPowerMng_Init()", "creat MyPowerMng_Task use xTaskCreateStaticPinnedToCore() error\n");
			}
        }
    }
	#elif MyPowerMng_TASK_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
	ret = xTaskCreatePinnedToCore(MyPowerMng_Task, "MyPowerMng_Task", MyPowerMng_Task_task_stack_size, NULL, MyPowerMng_Task_priority, &MyPowerMng_TaskHandle, MyPowerMng_Task_COREID);
	if(ret!=pdPASS)
	{
		ESP_LOGE("MyPowerMng_Init()", "creat MyPowerMng_Task use xTaskCreatePinnedToCore() error=%d\n", ret);
	}
	#endif
}
#endif
#endif