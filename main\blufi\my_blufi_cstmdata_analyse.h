#ifndef MY_BLUFI_CSTM_DATA_ANALYSE_H
#define MY_BLUFI_CSTM_DATA_ANALYSE_H

#include "stdint.h"

#define BLUFICDA_TAG "BLUFICDA"
#define BLUFICDA_INFO(fmt, ...)   ESP_LOGI(BLUFICDA_TAG, fmt, ##__VA_ARGS__)
#define BLUFICDA_ERROR(fmt, ...)  ESP_LOGE(BLUFICDA_TAG, fmt, ##__VA_ARGS__)

int MyBlufi_custom_data_analyse(uint8_t *data, uint16_t len);
uint8_t My_Blufi_CDA_rcv_queue_Send(char* data, uint32_t len, uint32_t timeout);
int MyBlufi_CDA_Init(void);


#endif