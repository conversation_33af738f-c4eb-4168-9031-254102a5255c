#include "audio_record.h"
#include "my_gpio.h"
#include "led.h"
#include "lte.h"

#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"
#include "freertos/semphr.h"
#include "driver/i2s.h"
#include "esp_system.h"
#include "esp_log.h"
#include "driver/uart.h"
#include "driver/gpio.h"

#include "string.h"
#include "stdlib.h"
#include <math.h>



#if AUDIO_RECORD_FUNC



#if USE_FORMAT==FORMAT_WAV
typedef struct
{
	char            riff[4];
	int				size_8;
	char            wave[4];
	char            fmt[4];
	int				fmt_size;

	short int       format_tag;
	short int       channels;
	int				samples_per_sec;
	int				avg_bytes_per_sec;
	short int       block_align;
	short int       bits_per_sample;

	char            data[4];
	int				data_size;
}wav_head_t;

#define SAMPLE_RATE     (16000)
#define I2S_NUM         (I2S_NUM_0)
#define I2S_BCK_IO      (GPIO_NUM_14)
#define I2S_WS_IO       (GPIO_NUM_15)
#define I2S_DO_IO       (-1)
#define I2S_DI_IO       (GPIO_NUM_32)

static xTaskHandle AudioRecordTask_Handle = NULL;

#define EXAMPLE_I2S_READ_LEN      (1048576UL)//1024*1024byte


//波传率16000，位深度16, 1s:约31.25KB，30s:937.5KB
static char wav_head[44] = {0x52, 0x49, 0x46, 0x46, 
                        0x24, 0xd0, 0x07, 0x00,
                        0x57, 0x41, 0x56, 0x45,
                        0x66, 0x6d, 0x74, 0x20,
                        0x10, 0x00, 0x00, 0x00,
                        0x01, 0x00, 0x01, 0x00,
                        0x80, 0x3e, 0x00, 0x00,
                        0x00, 0x7d, 0x00, 0x00,
                        0x02, 0x00, 0x10, 0x00,
                        0x64, 0x61, 0x74, 0x61,
                        0x00, 0xd0, 0x07, 0x00
                        };
//波传率4000，位深度16
/* static char wav_head[44] = {0x52, 0x49, 0x46, 0x46, 
                        0x24, 0xd0, 0x07, 0x00,
                        0x57, 0x41, 0x56, 0x45,
                        0x66, 0x6d, 0x74, 0x20,
                        0x10, 0x00, 0x00, 0x00,
                        0x01, 0x00, 0x01, 0x00,
                        0xa0, 0x0f, 0x00, 0x00,
                        0x40, 0x1f, 0x00, 0x00,
                        0x02, 0x00, 0x10, 0x00,
                        0x64, 0x61, 0x74, 0x61,
                        0x00, 0xd0, 0x07, 0x00
                        }; */
//波传率8000，位深度16
/* static char wav_head[44] = {0x52, 0x49, 0x46, 0x46, 
                        0x24, 0xd0, 0x07, 0x00,
                        0x57, 0x41, 0x56, 0x45,
                        0x66, 0x6d, 0x74, 0x20,
                        0x10, 0x00, 0x00, 0x00,
                        0x01, 0x00, 0x01, 0x00,
                        0x40, 0x1f, 0x00, 0x00,
                        0x3e, 0x80, 0x00, 0x00,
                        0x02, 0x00, 0x10, 0x00,
                        0x64, 0x61, 0x74, 0x61,
                        0x00, 0xd0, 0x07, 0x00
                        }; */


int WavToMp3(char* wav_data, char* mp3_data)
{
    int mp3_data_size = 0;
    return mp3_data_size;
}

char* g_i2s_read_buff = NULL;
void AudioRecordTask(void* param)
{
    uint32_t notifyValue = 0;
    size_t bytes_read;
    esp_err_t err;
    uint32_t record_count = 0;
    uint32_t send_wait;
    int mp3_data_size = 0;

    for(;;)
    {
        AudioRecordTask_label_1:
        send_wait = 0;
        do
        {
            xTaskNotifyWait(0x0, 0xffffffff, &notifyValue, 1000/portTICK_PERIOD_MS);
            if((send_wait++)==200)
            {
                if(g_i2s_read_buff!=NULL)
                {
                    free(g_i2s_read_buff);
                    g_i2s_read_buff = NULL;
                }
            }
        } while (!notifyValue);
        
        if(notifyValue)
        {
            if(g_i2s_read_buff == NULL)
            {
                g_i2s_read_buff = (char*) calloc(EXAMPLE_I2S_READ_LEN+44, sizeof(char));
            }
            if(g_i2s_read_buff == NULL)
            {
                ESP_LOGE("AudioRecordTask:", "get g_i2s_read_buff failed!\n");
                SendDataToServer(203, NULL, 0);
                goto AudioRecordTask_label_1;
            }
            memcpy(g_i2s_read_buff, wav_head, 44);
            err = i2s_read(I2S_NUM, (void*) (g_i2s_read_buff+44), EXAMPLE_I2S_READ_LEN, &bytes_read, portMAX_DELAY);
            if(err == ESP_OK)
            {
                if(bytes_read==EXAMPLE_I2S_READ_LEN)
                {
                    record_count++;
                    /* mp3_data_size = WavToMp3(g_i2s_read_buff, NULL);
                    SendDataToServer(202, g_i2s_read_buff, mp3_data_size); */
                    SendDataToServer(202, g_i2s_read_buff, EXAMPLE_I2S_READ_LEN+44);
                }
                else
                {
                    ESP_LOGE("AudioRecordTask:", "i2s_read error: bytes_read:%d, object size:%ldbytes(g_i2s_read_buff)\n", bytes_read, EXAMPLE_I2S_READ_LEN);
                }
            }
            else
            {
                ESP_LOGE("AudioRecordTask:", "i2s_read error: err=%d(g_i2s_read_buff)\n", err);
                SendDataToServer(204, NULL, 0);
            }
        }
        ESP_LOGI("AudioRecordTask:", "record finish, record_count:%d, total data size:%ldkb\n", record_count, EXAMPLE_I2S_READ_LEN/1024);
    }
}

void IndvI2sInit(void)
{
    esp_err_t err;
    
    i2s_config_t i2s_config = {
        .mode = I2S_MODE_MASTER | I2S_MODE_RX,
        .sample_rate = SAMPLE_RATE,
        .bits_per_sample = I2S_BITS_PER_SAMPLE_16BIT,
        .channel_format = I2S_CHANNEL_FMT_ONLY_RIGHT,                           //2-channels
        .communication_format = I2S_COMM_FORMAT_I2S | I2S_COMM_FORMAT_I2S_MSB,
        .dma_buf_count = 2,
        .dma_buf_len = 1024,
        //.use_apll = false,
        .intr_alloc_flags = ESP_INTR_FLAG_LEVEL1                                //Interrupt level 1
    };
    i2s_pin_config_t pin_config = {
        .bck_io_num = I2S_BCK_IO,
        .ws_io_num = I2S_WS_IO,
        .data_out_num = I2S_DO_IO,
        .data_in_num = I2S_DI_IO                                               //Not used
    };

    err = i2s_driver_install(I2S_NUM, &i2s_config, 0, NULL);
    if(err != ESP_OK)
    {
        ESP_LOGE("IndvI2sInit:", "i2s_driver_install failed: %d\n", err);
    }
    else
    {
        ESP_LOGE("IndvI2sInit:", "i2s_driver_install success!\n");
    }
    err = i2s_set_pin(I2S_NUM, &pin_config);
    if(err != ESP_OK)
    {
        ESP_LOGE("IndvI2sInit:", "i2s_set_pin failed: %d\n", err);
    }
    else
    {
        ESP_LOGE("IndvI2sInit:", "i2s_set_pin success!\n");
    }
}

void WavHeadConfig(wav_head_t* p)
{
    
}

void AudioRecordInit(void)
{
    IndvI2sInit();

    xTaskCreate(AudioRecordTask, "AudioRecordTask", 8192, NULL, 10, &AudioRecordTask_Handle);
}

void AudioRecordService(serverdata_t const* const p)
{
    xTaskNotify(AudioRecordTask_Handle, 1, eSetValueWithOverwrite);
}

#elif USE_FORMAT==FORMAT_MP3

#define SAMPLE_RATE     (16000)
#define I2S_NUM         (I2S_NUM_0)
#define I2S_BCK_IO      (GPIO_NUM_14)
#define I2S_WS_IO       (GPIO_NUM_15)
#define I2S_DO_IO       (-1)
#define I2S_DI_IO       (GPIO_NUM_32)

static xTaskHandle AudioRecordTask_Handle = NULL;
static xTaskHandle WavToMp3Task_Handle = NULL;

#define EXAMPLE_I2S_READ_LEN      (64000UL)//16000采样率、16位深度下录制2s


int WavToMp3(char* wav_data, char* mp3_data, int mp3_data_offset)
{
    int mp3_data_append_len = 0;
    return mp3_data_append_len;
}

char* g_i2s_read_buff = NULL;
char* g_i2s_read_buff1 = NULL;
char* g_mp3_data = NULL;
uint32_t g_mp3_data_size = 0;
static uint32_t g_i2s_read_buff_empty = 1;
static uint32_t g_i2s_read_buff1_empty = 1;
void WavToMp3Task(void* param)
{
    uint32_t notifyValue = 0;
    int32_t mp3_data_len = 0;

    for(;;)
    {
        notifyValue = 0;
        xTaskNotifyWait(0x0, 0xffffffff, &notifyValue, portMAX_DELAY);
        if(notifyValue==1)
        {
            mp3_data_len += WavToMp3(g_i2s_read_buff, g_mp3_data, mp3_data_len);
        }
        else if(notifyValue==2)
        {
            mp3_data_len += WavToMp3(g_i2s_read_buff1, g_mp3_data, mp3_data_len);
        }
        else if(notifyValue==3)
        {
            SendDataToServer(202, g_mp3_data, 0+mp3_data_len);
            mp3_data_len = 0;
        }
    }
}

void AudioRecordTask(void* param)
{
    uint32_t notifyValue = 0;
    size_t bytes_read;
    esp_err_t err;
    uint32_t record_count = 0;
    uint32_t send_wait;
    uint32_t record_time = 0;

    for(;;)
    {
        AudioRecordTask_label_1:
        record_time = 0;
        
        send_wait = 0;
        do
        {
            xTaskNotifyWait(0x0, 0xffffffff, &notifyValue, 1000/portTICK_PERIOD_MS);
            if((send_wait++)==200)
            {
                if(g_i2s_read_buff!=NULL)
                {
                    free(g_i2s_read_buff);
                    g_i2s_read_buff = NULL;
                }
                if(g_i2s_read_buff1!=NULL)
                {
                    free(g_i2s_read_buff1);
                    g_i2s_read_buff1 = NULL;
                }
                if(g_mp3_data!=NULL)
                {
                    free(g_mp3_data);
                    g_mp3_data = NULL;
                    g_mp3_data_size = 0;
                }
            }
        } while (!notifyValue);
        
        if(notifyValue>0)
        {
            record_time = (notifyValue/2)+1;
            if(record_time<2)
            {
                record_time = 1;
            }
            else if(record_time<600)
            {
                record_time = record_time/2;
            }
            else if(record_time>600)
            {
                record_time = 600/2;
            }
            if(g_i2s_read_buff == NULL)
            {
                g_i2s_read_buff = (char*) calloc(EXAMPLE_I2S_READ_LEN, sizeof(char));
                if(g_i2s_read_buff == NULL)
                {
                    ESP_LOGE("AudioRecordTask:", "get g_i2s_read_buff failed!\n");
                    SendDataToServer(203, NULL, 0);
                    goto AudioRecordTask_label_1;
                }
            }
            if(g_i2s_read_buff1 == NULL)
            {
                g_i2s_read_buff1 = (char*) calloc(EXAMPLE_I2S_READ_LEN, sizeof(char));
                if(g_i2s_read_buff1 == NULL)
                {
                    free(g_i2s_read_buff);
                    g_i2s_read_buff = NULL;

                    ESP_LOGE("AudioRecordTask:", "get g_i2s_read_buff1 failed!\n");
                    SendDataToServer(203, NULL, 0);
                    goto AudioRecordTask_label_1;
                }
            }
            if(g_mp3_data == NULL)
            {
                g_mp3_data_size = record_time*4096;
                g_mp3_data = (char*) calloc(g_mp3_data_size, sizeof(char));
                if(g_mp3_data == NULL)
                {
                    free(g_i2s_read_buff);
                    g_i2s_read_buff = NULL;

                    free(g_i2s_read_buff1);
                    g_i2s_read_buff1 = NULL;

                    ESP_LOGE("AudioRecordTask:", "get g_mp3_data failed!\n");
                    SendDataToServer(203, NULL, 0);
                    goto AudioRecordTask_label_1;
                }
            }
            
            
            while(1)
            {
                while(!g_i2s_read_buff_empty)
                {
                    vTaskDelay(10/portTICK_PERIOD_MS);
                }
                err = i2s_read(I2S_NUM, (void*) (g_i2s_read_buff), EXAMPLE_I2S_READ_LEN, &bytes_read, portMAX_DELAY);
                if(err == ESP_OK)
                {
                    if(bytes_read==EXAMPLE_I2S_READ_LEN)
                    {
                        record_count++;
                        g_i2s_read_buff_empty = 0;
                        xTaskNotify(WavToMp3Task_Handle, 1, eSetValueWithOverwrite);
                    }
                    else
                    {
                        ESP_LOGE("AudioRecordTask:", "i2s_read error: bytes_read:%d, object size:%ldbytes(g_i2s_read_buff)\n", bytes_read, EXAMPLE_I2S_READ_LEN);
                    }
                }
                else
                {
                    ESP_LOGE("AudioRecordTask:", "i2s_read error: err=%d(g_i2s_read_buff)\n", err);
                    SendDataToServer(204, NULL, 0);
                }

                while(!g_i2s_read_buff1_empty)
                {
                    vTaskDelay(10/portTICK_PERIOD_MS);
                }
                err = i2s_read(I2S_NUM, (void*) (g_i2s_read_buff1), EXAMPLE_I2S_READ_LEN, &bytes_read, portMAX_DELAY);
                if(err == ESP_OK)
                {
                    if(bytes_read==EXAMPLE_I2S_READ_LEN)
                    {
                        record_count++;
                        g_i2s_read_buff1_empty = 0;
                        xTaskNotify(WavToMp3Task_Handle, 2, eSetValueWithOverwrite);
                    }
                    else
                    {
                        ESP_LOGE("AudioRecordTask:", "i2s_read error: bytes_read:%d, object size:%ldbytes(g_i2s_read_buff1)\n", bytes_read, EXAMPLE_I2S_READ_LEN);
                    }
                }
                else
                {
                    ESP_LOGE("AudioRecordTask:", "i2s_read error: err=%d(g_i2s_read_buff1)\n", err);
                    SendDataToServer(204, NULL, 0);
                }

                ESP_LOGI("AudioRecordTask:", "record finish, record_count:%d, total data size:%ldkb\n", record_count, EXAMPLE_I2S_READ_LEN/1024);

                if(record_count>=record_time)
                {
                    record_count = 0;
                    vTaskDelay(100/portTICK_PERIOD_MS);
                    xTaskNotify(WavToMp3Task_Handle, 3, eSetValueWithOverwrite);
                    break;
                }
            }
        }
    }
}

void IndvI2sInit(void)
{
    esp_err_t err;
    
    i2s_config_t i2s_config = {
        .mode = I2S_MODE_MASTER | I2S_MODE_RX,
        .sample_rate = SAMPLE_RATE,
        .bits_per_sample = I2S_BITS_PER_SAMPLE_16BIT,
        .channel_format = I2S_CHANNEL_FMT_ONLY_RIGHT,                           //2-channels
        .communication_format = I2S_COMM_FORMAT_I2S | I2S_COMM_FORMAT_I2S_MSB,
        .dma_buf_count = 2,
        .dma_buf_len = 1024,
        //.use_apll = false,
        .intr_alloc_flags = ESP_INTR_FLAG_LEVEL1                                //Interrupt level 1
    };
    i2s_pin_config_t pin_config = {
        .bck_io_num = I2S_BCK_IO,
        .ws_io_num = I2S_WS_IO,
        .data_out_num = I2S_DO_IO,
        .data_in_num = I2S_DI_IO                                               //Not used
    };

    err = i2s_driver_install(I2S_NUM, &i2s_config, 0, NULL);
    if(err != ESP_OK)
    {
        ESP_LOGE("IndvI2sInit:", "i2s_driver_install failed: %d\n", err);
    }
    else
    {
        ESP_LOGE("IndvI2sInit:", "i2s_driver_install success!\n");
    }
    err = i2s_set_pin(I2S_NUM, &pin_config);
    if(err != ESP_OK)
    {
        ESP_LOGE("IndvI2sInit:", "i2s_set_pin failed: %d\n", err);
    }
    else
    {
        ESP_LOGE("IndvI2sInit:", "i2s_set_pin success!\n");
    }
}

void AudioRecordInit(void)
{
    IndvI2sInit();

    xTaskCreate(AudioRecordTask, "AudioRecordTask", 8192, NULL, 10, &AudioRecordTask_Handle);
    xTaskCreate(WavToMp3Task, "WavToMp3Task", 8192, NULL, 10, &WavToMp3Task_Handle);
}

void AudioRecordService(serverdata_t const* const p)
{
    uint32_t record_time = 0;
    record_time = memcpy(&record_time, p->data, sizeof(int));
    if(record_time>0)
    {
        if(AudioRecordTask_Handle!=NULL)
        {
            xTaskNotify(AudioRecordTask_Handle, record_time, eSetValueWithOverwrite);
        }
    }
}

#endif

#endif