#include "my_asl_dpu.h"

#include "my_config.h"
#include "my_asl.h"
#include "my_ds.h"
#include "my_time.h"
#include "my_esp_chip_info_mng.h"
#include "my_ble.h"
#include "my_workmode.h"
#include "my_asl_event.h"
#include "my_config.h"
#include "my_dev_paramid.h"
#include "my_endian.h"
#include "checksum.h"
#include "my_nvs_ds.h"
#include "my_config_table.h"
#include "my_debug.h"

#include "esp_log.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"
#include "freertos/semphr.h"


#if defined MY_DEBUG_PRINT_FUNC
#define DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT   MY_DEBUG_PRINT_LEVEL_0
#else
#define DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT   DEBUG_PRINT_LEVEL_0
#endif


QueueHandle_t my_asl_dpu_queue = NULL;
my_asl_dpu_queue_t* my_asl_dpu_queue_array[4];

QueueHandle_t my_asl_dpu_ctrlevent_queue = NULL;

my_asl_dpu_iotboxinfo_t my_asl_dpu_iotboxinfo;

my_asl_event_list_t my_asl_dpu_event_list;

#define MY_ASLDPU_TIMEOUT_REGISTER      10000
#define MY_ASLDPU_TIMEOUT_LOGIN         10000
#define MY_ASLDPU_TIMEOUT_TIMESYNC      10000
#define MY_ASLDPU_TIMEOUT_PARAMREQ      5000
#define MY_ASLDPU_TIMEOUT_PARAMREPORT   5000
#define MY_ASLDPU_TIMEOUT_EVENTREPORT   5000

#define MY_ASLDPU_TIMEOUT_FILEDOWNLOAD_REQ   60000
#define MY_ASLDPU_TIMEOUT_RCVDATABLOCK_REQ   60000

#define send_file_datablock_delay   0

// #define send_data_block_size_max (((TSFC_FRAME_DATA_SIZE_MAX-24)/1024)*1024)
#define send_data_block_size_max (1280)

#define test_dic                    0
#define test_download_file_sn       0
#define test_download_file_type     bin_file

int My_ASLdpu_IotBoxInfo_Init(void)
{
    my_config_nvs_t* p_my_conf = NULL;
    p_my_conf = MyConfig_GetGCT_Cur_p();

    memset(&my_asl_dpu_iotboxinfo, 0, sizeof(my_asl_dpu_iotboxinfo));

    //设备唯一编号（对外）
    MyWHGM5_GetIMEI(my_asl_dpu_iotboxinfo.device_sn, sizeof(my_asl_dpu_iotboxinfo.device_sn));
    //设备型号
    memcpy(&my_asl_dpu_iotboxinfo.device_model, MY_IOT_BOX_DEVICE_MODEL, strlen(MY_IOT_BOX_DEVICE_MODEL));
    //设备类型
    my_asl_dpu_iotboxinfo.device_type = MY_IOT_BOX_DEVICE_TYPE;
    //设备ID
    memcpy(my_asl_dpu_iotboxinfo.device_id, my_asl_dpu_iotboxinfo.device_sn, strlen(my_asl_dpu_iotboxinfo.device_sn));
    //注册密码
    memcpy(my_asl_dpu_iotboxinfo.password, "testpassword", strlen("testpassword"));

    //检查是否已注册
    if(p_my_conf->asl.dic!=0)
    {
        // my_asl_dpu_iotboxinfo.register_state = 1;
        my_asl_dpu_iotboxinfo.dic = p_my_conf->asl.dic;
    }

    //登陆状态，默认未登录
    my_asl_dpu_iotboxinfo.login_enable = p_my_conf->asl.loginflag;
    if(my_asl_dpu_iotboxinfo.login_enable)
    {
        my_asl_dpu_iotboxinfo.login_state = 0;
    }
    else
    {
        my_asl_dpu_iotboxinfo.login_state = 1;
    }
    
    // my_asl_dpu_iotboxinfo.dic = test_dic;

    //token
    //登陆后获取

    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("My_ASLdpu_ASLdpuInfo_Init()----------\n");
    printf("my_asl_dpu_iotboxinfo.device_sn=%s\n", my_asl_dpu_iotboxinfo.device_sn);
    printf("my_asl_dpu_iotboxinfo.device_model=%s\n", my_asl_dpu_iotboxinfo.device_model);
    printf("my_asl_dpu_iotboxinfo.device_type=0x%x\n", my_asl_dpu_iotboxinfo.device_type);
    printf("my_asl_dpu_iotboxinfo.device_id=%s\n", my_asl_dpu_iotboxinfo.device_id);

    printf("my_asl_dpu_iotboxinfo.password=%s\n", my_asl_dpu_iotboxinfo.password);

    printf("my_asl_dpu_iotboxinfo.register_state=%d\n", my_asl_dpu_iotboxinfo.register_state);
    printf("my_asl_dpu_iotboxinfo.login_state=%d\n", my_asl_dpu_iotboxinfo.login_state);

    printf("my_asl_dpu_iotboxinfo.dic=0x%x\n", my_asl_dpu_iotboxinfo.dic);
    printf("my_asl_dpu_iotboxinfo.token=0x%x\n", my_asl_dpu_iotboxinfo.token);

    printf("\n\n");
    #endif

    return 0;
}

int My_ASLdpu_IotBoxInfo_DebugPrint(void)
{
    printf("My_ASLdpu_ASLdpuInfo_DebugPrint()----------\n");
    printf("my_asl_dpu_iotboxinfo.device_sn=%s\n", my_asl_dpu_iotboxinfo.device_sn);
    printf("my_asl_dpu_iotboxinfo.device_model=%s\n", my_asl_dpu_iotboxinfo.device_model);
    printf("my_asl_dpu_iotboxinfo.device_type=0x%x\n", my_asl_dpu_iotboxinfo.device_type);
    printf("my_asl_dpu_iotboxinfo.device_id=%s\n", my_asl_dpu_iotboxinfo.device_id);

    printf("my_asl_dpu_iotboxinfo.password=%s\n", my_asl_dpu_iotboxinfo.password);

    printf("my_asl_dpu_iotboxinfo.register_state=%d\n", my_asl_dpu_iotboxinfo.register_state);
    printf("my_asl_dpu_iotboxinfo.login_state=%d\n", my_asl_dpu_iotboxinfo.login_state);

    printf("my_asl_dpu_iotboxinfo.dic=0x%x\n", my_asl_dpu_iotboxinfo.dic);
    printf("my_asl_dpu_iotboxinfo.token=0x%x\n", my_asl_dpu_iotboxinfo.token);

    printf("\n\n");

    return 0;
}

int My_ASLdpu_DevRegister(void)
{
    int ret = 0;

    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\n\n\n\n\n\n\n\n\n\n\nMy_ASLdpu_DevRegister():register\n");
    #endif

    if(!my_asl_dpu_iotboxinfo.register_state)
    {
        my_asl_head_t my_asl_head;
        my_asl_head.type = dev_reg_req;
        my_asl_head.vercode = MY_ASL_PD;
        my_asl_head.len = 0;

        my_d2link_list_t* my_d2link_list = My_D2Link_Creat("register req list");

        My_D2Link_Append_Item(my_d2link_list, 101, NULL, 0, my_asl_dpu_iotboxinfo.device_type, sizeof(my_asl_dpu_iotboxinfo.device_type), NULL);
        My_D2Link_Append_Item(my_d2link_list, 102, my_asl_dpu_iotboxinfo.device_model, 0, 0, sizeof(my_asl_dpu_iotboxinfo.device_model), NULL);
        My_D2Link_Append_Item(my_d2link_list, 103, my_asl_dpu_iotboxinfo.device_id, 0, 0, sizeof(my_asl_dpu_iotboxinfo.device_id), NULL);
        My_D2Link_Append_Item(my_d2link_list, 104, my_asl_dpu_iotboxinfo.password, 0, 0, sizeof(my_asl_dpu_iotboxinfo.password), NULL);
        if((my_d2link_list->list_data_len%4)!=0)
        {
            int fill_empty_num = 4 - (my_d2link_list->list_data_len%4);
            My_D2Link_Append_Item(my_d2link_list, 0xffff, NULL, 0, 0, fill_empty_num, NULL);
        }

        my_asl_head.len = my_endian_conversion_16(my_d2link_list->list_data_len);

        My_D2Link_Insert_Item(my_d2link_list, 101, 0, 100, &my_asl_head, 0, 0, sizeof(my_asl_head), NULL);


        char* param_req_pack = (char*)calloc(1, my_d2link_list->list_data_len);
        if(param_req_pack!=NULL)
        {
            My_D2Link_Data_Packer(my_d2link_list, param_req_pack, my_d2link_list->list_data_len);
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
            printf("My_ASLdpu_DevRegister():req_pack(%d)=\n", my_d2link_list->list_data_len);
            for(int i=0; i<my_d2link_list->list_data_len; i++)
            {
                printf("%02X ", *(param_req_pack+i));
            }
            printf("\n");
            #endif

            char* saving = NULL;
            int saving_len = 0;
            MyASL_ServicePort(my_asl_head.type, (void*)param_req_pack, my_d2link_list->list_data_len, (void*)&saving, &saving_len, MY_ASLDPU_TIMEOUT_REGISTER, 1);
            free(param_req_pack);

            if((saving_len>0)&&(saving!=NULL))
            {
                #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
                printf("My_ASLdpu_DevRegister():saving(%d)=\n", saving_len);
                for(int i=0; i<saving_len; i++)
                {
                    printf("%02X ", *(saving+i));
                }
                printf("\n");
                #endif

                uint16_t resultcode = 0;
                memcpy(&resultcode, saving+4, 2);
                resultcode = my_endian_conversion_16(resultcode);
                #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                printf("My_ASLdpu_DevRegister():resultcode=%x\n", resultcode);
                #endif
                if(resultcode==aslrc_Success)
                {
                    uint32_t dic = 0;
                    memcpy(&dic, saving+6, 4);
                    dic = my_endian_conversion_32(dic);
                    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                    printf("My_ASLdpu_DevRegister():dic=0x%x\n", dic);
                    #endif

                    if(dic!=0)
                    {
                        //保存dic
                        MyConfig_Modify_Asl_dic(dic, 1);
                        my_asl_dpu_iotboxinfo.dic = dic;

                        ret = 0;
                    }
                    else
                    {
                        ret = 1;
                    }
                }
                else
                {
                    ESP_LOGE("My_ASLdpu_DevRegister()", "resultcode error=%x,!!!!!!!!!!!!!!!!!", resultcode);
                    ret = 1;
                }
            }
            else
            {
                ret = -2;
                ESP_LOGE("My_ASLdpu_DevRegister()", "(saving_len>0)&&(saving!=NULL) are false,!!!!!!!!!!!!!!!!!");
            }
            if(saving!=NULL)free(saving);
        }
        My_D2Link_Delete(&my_d2link_list);
    }
    else
    {
        ESP_LOGW("My_ASLdpu_DevRegister()", "already register, dic=%x", my_asl_dpu_iotboxinfo.dic);
    }
    return ret;
}

int My_ASLdpu_DevLogin(void)
{
    ESP_LOGI("\n\n\n\n\n\n\n\n\n\n\n\n", "My_ASLdpu_DevLogin():login\n");

    if(!my_asl_dpu_iotboxinfo.login_enable)
    {
        ESP_LOGI("My_ASLdpu_DevLogin()", "my_asl_dpu_iotboxinfo.login_enable=0X%x, no need login, token=0x%08x\n", \
        my_asl_dpu_iotboxinfo.login_enable, my_asl_dpu_iotboxinfo.token);
        return 0;
    }

    int ret = 0;

    // if(my_asl_dpu_iotboxinfo.login_state==0)
    {
        my_asl_head_t my_asl_head;
        my_asl_head.type = dev_login_req;
        my_asl_head.vercode = MY_ASL_PD;
        my_asl_head.len = 0;

        my_d2link_list_t* my_d2link_list = My_D2Link_Creat("login req list");

        My_D2Link_Append_Item(my_d2link_list, 101, NULL, 0, my_asl_dpu_iotboxinfo.dic, sizeof(my_asl_dpu_iotboxinfo.dic), NULL);
        My_D2Link_Append_Item(my_d2link_list, 102, NULL, 0, my_asl_dpu_iotboxinfo.token, sizeof(my_asl_dpu_iotboxinfo.token), NULL);
        My_D2Link_Append_Item(my_d2link_list, 103, my_asl_dpu_iotboxinfo.device_id, 0, 0, sizeof(my_asl_dpu_iotboxinfo.device_id), NULL);
        My_D2Link_Append_Item(my_d2link_list, 104, my_asl_dpu_iotboxinfo.password, 0, 0, sizeof(my_asl_dpu_iotboxinfo.password), NULL);
        if((my_d2link_list->list_data_len%4)!=0)
        {
            int fill_empty_num = 4 - (my_d2link_list->list_data_len%4);
            My_D2Link_Append_Item(my_d2link_list, 0xffff, NULL, 0, 0, fill_empty_num, NULL);
        }

        my_asl_head.len = my_endian_conversion_16(my_d2link_list->list_data_len);

        My_D2Link_Insert_Item(my_d2link_list, 101, 0, 100, &my_asl_head, 0, 0, sizeof(my_asl_head), NULL);

        char* param_req_pack = (char*)calloc(1, my_d2link_list->list_data_len);
        if(param_req_pack!=NULL)
        {
            My_D2Link_Data_Packer(my_d2link_list, param_req_pack, my_d2link_list->list_data_len);
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
            printf("My_ASLdpu_DevLogin():req_pack(%d)=\n", my_d2link_list->list_data_len);
            for(int i=0; i<my_d2link_list->list_data_len; i++)
            {
                printf("%02X ", *(param_req_pack+i));
            }
            printf("\n");
            #endif

            char* saving = NULL;
            int saving_len = 0;
            MyASL_ServicePort(my_asl_head.type, (void*)param_req_pack, my_d2link_list->list_data_len, (void*)&saving, &saving_len, MY_ASLDPU_TIMEOUT_LOGIN, 1);
            free(param_req_pack);

            if((saving_len>0)&&(saving!=NULL))
            {
                #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
                printf("My_ASLdpu_DevLogin():saving(%d)=\n", saving_len);
                for(int i=0; i<saving_len; i++)
                {
                    printf("%02X ", *(saving+i));
                }
                printf("\n");
                #endif

                uint16_t resultcode = 0;
                memcpy(&resultcode, saving+4, 2);
                resultcode = my_endian_conversion_16(resultcode);
                #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                printf("My_ASLdpu_DevLogin():resultcode=%x\n", resultcode);
                #endif
                if(resultcode==aslrc_Success)
                {
                    uint32_t token = 0;
                    memcpy(&token, saving+6, 4);
                    token = my_endian_conversion_32(token);
                    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                    printf("My_ASLdpu_DevLogin():token=%x\n", token);
                    #endif

                    uint64_t t1=0, t2=0;
                    memcpy(&t1, saving+6+4+2, 8);
                    memcpy(&t2, saving+6+4+2+8, 8);
                    t1 = my_endian_conversion_64(t1);
                    t2 = my_endian_conversion_64(t2);
                    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                    printf("My_ASLdpu_DevLogin():t1=0x%llx(%lld), t2=0x%llx(%lld)\n", t1, t1, t2, t2);
                    #endif

                    //对时成功，标记最后一次对时时间
                    my_asl_dpu_iotboxinfo.last_time_sync_time = MyTime_GetTime();

                    time_t cur_time = t2/1000+8*3600;
                    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                    printf("My_ASLdpu_DevLogin():cur_time=0x%lx(%ld)\n", cur_time, cur_time);
                    #endif
                    MyTime_SetTimeAndMark(cur_time);
                    MyTime_PrintTime();

                    if(token==0)
                    {
                        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                        printf("My_ASLdpu_DevLogin(): ret_token=0 ignore\n");
                        #endif
                    }
                    else
                    {

                    }
                    my_asl_dpu_iotboxinfo.token = token;
                    my_asl_dpu_iotboxinfo.login_state = 1;
                    ret = 0;
                }
                else
                {
                    ESP_LOGE("My_ASLdpu_DevLogin()", "resultcode error=%x,!!!!!!!!!!!!!!!!!", resultcode);
                    ret = 1;
                }
            }
            else
            {
                ret = -2;
                ESP_LOGE("My_ASLdpu_DevLogin()", "(saving_len>0)&&(saving!=NULL) are false,!!!!!!!!!!!!!!!!!");
            }
            if(saving!=NULL)free(saving);
        }
        else
        {
            ret = -1;
        }
        My_D2Link_Delete(&my_d2link_list);
    }
    // else
    // {
    //     ESP_LOGI("My_ASLdpu_DevLogin()", "already login, token=%x\n", my_asl_dpu_iotboxinfo.token);
    //     ret = 0;
    // }
    ESP_LOGI("My_ASLdpu_DevLogin()", "already login, token=%x\n", my_asl_dpu_iotboxinfo.token);
    return ret;
}

int My_ASLdpu_DevTimeSync(void)
{
    int ret = 0;

    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\n\n\n\n\n\n\n\n\n\n\nMy_ASLdpu_DevTimeSync():TimeSync\n");
    #endif

    my_asl_head_t my_asl_head;
    my_asl_head.type = net_time_sync_req;
    my_asl_head.vercode = MY_ASL_PD;
    my_asl_head.len = 0;

    my_d2link_list_t* my_d2link_list = My_D2Link_Creat("TimeSync req list");

    My_D2Link_Append_Item(my_d2link_list, 101, NULL, 0, my_asl_dpu_iotboxinfo.dic, sizeof(my_asl_dpu_iotboxinfo.dic), NULL);
    My_D2Link_Append_Item(my_d2link_list, 102, NULL, 0, my_asl_dpu_iotboxinfo.token, sizeof(my_asl_dpu_iotboxinfo.token), NULL);
    if((my_d2link_list->list_data_len%4)!=0)
    {
        int fill_empty_num = 4 - (my_d2link_list->list_data_len%4);
        My_D2Link_Append_Item(my_d2link_list, 0xffff, NULL, 0, 0, fill_empty_num, NULL);
    }

    my_asl_head.len = my_endian_conversion_16(my_d2link_list->list_data_len);

    My_D2Link_Insert_Item(my_d2link_list, 101, 0, 100, &my_asl_head, 0, 0, sizeof(my_asl_head), NULL);


    char* param_req_pack = (char*)calloc(1, my_d2link_list->list_data_len);
    if(param_req_pack!=NULL)
    {
        My_D2Link_Data_Packer(my_d2link_list, param_req_pack, my_d2link_list->list_data_len);
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
        printf("My_ASLdpu_DevTimeSync():req_pack(%d)=\n", my_d2link_list->list_data_len);
        for(int i=0; i<my_d2link_list->list_data_len; i++)
        {
            printf("%02X ", *(param_req_pack+i));
        }
        printf("\n");
        #endif

        char* saving = NULL;
        int saving_len = 0;
        int send_time_in_ms = esp_timer_get_time()/1000;
        MyASL_ServicePort(my_asl_head.type, (void*)param_req_pack, my_d2link_list->list_data_len, (void*)&saving, &saving_len, MY_ASLDPU_TIMEOUT_TIMESYNC, 1);
        free(param_req_pack);

        if((saving_len>0)&&(saving!=NULL))
        {
            int res_time_in_ms = esp_timer_get_time()/1000;
            ESP_LOGI("My_ASLdpu_DevTimeSync()", "network delay = %dms\n", (res_time_in_ms-send_time_in_ms)/2);
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
            printf("My_ASLdpu_DevTimeSync():saving(%d)=\n", saving_len);
            for(int i=0; i<saving_len; i++)
            {
                printf("%02X ", *(saving+i));
            }
            printf("\n");
            #endif

            uint64_t t1=0, t2=0;
            memcpy(&t1, saving+4, 8);
            memcpy(&t2, saving+4+8, 8);
            t1 = my_endian_conversion_64(t1);
            t2 = my_endian_conversion_64(t2);
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
            printf("My_ASLdpu_DevTimeSync():t1=0x%llx(%lld), t2=0x%llx(%lld)\n", t1, t1, t2, t2);
            #endif

            //对时成功，标记最后一次对时时间
            my_asl_dpu_iotboxinfo.last_time_sync_time = MyTime_GetTime();

            time_t cur_time = t2/1000+8*3600;
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
            printf("My_ASLdpu_DevTimeSync():cur_time=0x%lx(%ld)\n", cur_time, cur_time);
            #endif
            MyTime_SetTimeAndMark(cur_time);
            MyTime_PrintTime();

            ret = 0;
        }
        else
        {
            ret = -2;
            ESP_LOGE("My_ASLdpu_DevTimeSync():", "(saving_len>0)&&(saving!=NULL) are false,!!!!!!!!!!!!!!!!!\n");
        }
        if(saving!=NULL)free(saving);
    }
    else
    {
        ret = -1;
    }
    My_D2Link_Delete(&my_d2link_list);

    return ret;
}

uint32_t MyASL_Get_LastTimeSyncTime(void)
{
    return my_asl_dpu_iotboxinfo.last_time_sync_time;
}

int MyASL_DPU_CtrlEvent_Send(int type, int value, int timeout);
int My_ASLdpu_DevParam_Modify(int param_id, char* data, int data_len)
{
    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("My_ASLdpu_DevParam_Modify():param_id=%x\n", param_id);
    #endif

    if(data==NULL)
    {
        ESP_LOGE("My_ASLdpu_DevParam_Modify()", "data==NULL");
        return -1;
    }

    if((param_id>=0x0100)&&(param_id<=0x7FFF))
    {
        if(!MyConfig_GetConfItem_Modifiable(param_id))
        {
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "param_id does not support modify");
            if((param_id!=devPI_factory_ota_info_net)&&(param_id!=devPI_factory_ota_info_local))
            {
                return 1;
            }
        }
    }

    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("My_ASLdpu_DevParam_Modify():data(%d)=", data_len);
    for(int i=0; i<data_len; i++)
    {
        printf("%02X ", *(data+i));
    }
    printf("\n");
    #endif

    // //debug
    // return 0;
    // //end of debug

    switch(param_id)
    {
        //asl
        case(devPI_asl_appId):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_asl_appId");
            MyConfig_Modify_Asl_appId(my_endian_conversion_16(*((uint16_t*)(data))), 1);
            return 0;
        case(devPI_asl_loginFlag):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_asl_loginFlag");
            MyConfig_Modify_Asl_loginFlag(*((uint8_t*)(data)), 1);
            return 0;
        case(devPI_asl_dic):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_asl_dic");
            MyConfig_Modify_Asl_dic(my_endian_conversion_32(*((uint32_t*)(data))), 1);
            return 0;
        case(devPI_asl_token):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_asl_token");
            MyConfig_Modify_Asl_token(my_endian_conversion_32(*((uint32_t*)(data))), 1);
            return 0;
        case(devPI_asl_last_sync_param_timestamp):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_asl_last_sync_param_timestamp");
            // MyConfig_Modify_Asl_last_sync_param_timestamp(my_endian_conversion_32(*((uint32_t*)(data))), 1);
            return 1;
        case(devPI_asl_sync_param_effect_time):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_asl_sync_param_effect_time");
            MyConfig_Modify_Asl_sync_param_effect_time(my_endian_conversion_32(*((uint32_t*)(data))), 1);
            return 0;

        //tsfc
        case(devPI_tsfc_frame_max_size_send):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_tsfc_frame_max_size_send");
            //modify...
            return 1;
        case(devPI_tsfc_frame_max_size_rcv):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_tsfc_frame_max_size_rcv");
            //modify...
            return 1;
        case(devPI_tsfc_sendFail_max_repeat):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_tsfc_sendFail_max_repeat");
            //modify...
            return 1;
        case(devPI_tsfc_aesKey):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_tsfc_aesKey");
            MyConfig_Modify_Tsfc_aesKey((void*)data, data_len, 1);
            return 0;

        //net
        case(devPI_net_server_ipv4_address):
            {
                ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_net_server_ipv4_address");
                int address_type = 0;
                for(int m=0; m<4; m++)
                {
                    if( !((*(data+m)>='0'&&*(data+m)<='9') \
                        || (*(data+m)>='a'&&*(data+m)<='z') \
                        || (*(data+m)>='A'&&*(data+m)<='Z') \
                        || (*(data+m)=='.') \
                        || (*(data+m)=='-')))
                    {
                        address_type = 1;
                        break;
                    }
                }
                ESP_LOGI("My_ASLdpu_DevParam_Modify()", "address_type=%d\n", address_type);
                MyConfig_Modify_Net_ipv4Address(address_type, (void*)data, data_len, 1);
            }
            return 0;
        case(devPI_net_server_ipv4_address_type):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_net_server_ipv4_address_type");
            MyConfig_Modify_Net_ipv4AddressType(*((uint8_t*)(data)), 1);
            return 0;
        case(devPI_net_server_port):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_net_server_port");
            MyConfig_Modify_Net_serverPort(my_endian_conversion_32(*((uint32_t*)(data))), 1);
            return 0;
        case(devPI_net_server_ota_ipv4_address):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_net_server_ota_ipv4_address");
            //modify...
            return 1;
        case(devPI_net_server_ota_port):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_net_server_ota_port");
            //modify...
            return 1;

        //workmode
        case(devPI_workmode_curMode):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_workmode_curMode");
            {
                uint8_t new_workmode = *((uint8_t*)(data));
                if(new_workmode!=workmode_energy_saving)
                {
                    MyConfig_Modify_Workmode_mode(*((uint8_t*)(data)), 1);
                }
                else
                {
                    ESP_LOGW("My_ASLdpu_DevParam_Modify()", "new_workmode!=workmode_energy_saving is False!!!, ignore!");
                }
            }
            return 0;
        case(devPI_workmode_worktimeList_1_start_time):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_workmode_worktimeList_1_start_time");
            MyConfig_Modify_Workmode_timePointer1(my_endian_conversion_32(*((uint32_t*)(data))), -1, 1);
            return 0;
        case(devPI_workmode_worktimeList_1_keep_time):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_workmode_worktimeList_1_keep_time");
            MyConfig_Modify_Workmode_timePointer1(-1, my_endian_conversion_32(*((uint32_t*)(data))), 1);
            return 0;
        case(devPI_workmode_worktimeList_2_start_time):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_workmode_worktimeList_2_start_time");
            MyConfig_Modify_Workmode_timePointer2(my_endian_conversion_32(*((uint32_t*)(data))), -1, 1);
            return 0;
        case(devPI_workmode_worktimeList_2_keep_time):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_workmode_worktimeList_2_keep_time");
            MyConfig_Modify_Workmode_timePointer2(-1, my_endian_conversion_32(*((uint32_t*)(data))), 1);
            return 0;
        case(devPI_workmode_worktimeList_3_start_time):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_workmode_worktimeList_3_start_time");
            MyConfig_Modify_Workmode_timePointer3(my_endian_conversion_32(*((uint32_t*)(data))), -1, 1);
            return 0;
        case(devPI_workmode_worktimeList_3_keep_time):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_workmode_worktimeList_3_keep_time");
            MyConfig_Modify_Workmode_timePointer3(-1, my_endian_conversion_32(*((uint32_t*)(data))), 1);
            return 0;
        case(devPI_workmode_worktimeList_4_start_time):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_workmode_worktimeList_4_start_time");
            MyConfig_Modify_Workmode_timePointer4(my_endian_conversion_32(*((uint32_t*)(data))), -1, 1);
            return 0;
        case(devPI_workmode_worktimeList_4_keep_time):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_workmode_worktimeList_4_keep_time");
            MyConfig_Modify_Workmode_timePointer4(-1, my_endian_conversion_32(*((uint32_t*)(data))), 1);
            return 0;
        case(devPI_workmode_worktimeList_5_start_time):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_workmode_worktimeList_5_start_time");
            MyConfig_Modify_Workmode_timePointer5(my_endian_conversion_32(*((uint32_t*)(data))), -1, 1);
            return 0;
        case(devPI_workmode_worktimeList_5_keep_time):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_workmode_worktimeList_5_keep_time");
            MyConfig_Modify_Workmode_timePointer5(-1, my_endian_conversion_32(*((uint32_t*)(data))), 1);
            return 0;
        case(devPI_workmode_timermodeSleepTime):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_workmode_timermodeSleepTime");
            MyConfig_Modify_Workmode_timermodeSleepTime(my_endian_conversion_32(*((uint32_t*)(data))), 1);
            return 0;
        case(devPI_workmode_imageReportInterval):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_workmode_imageReportInterval");
            MyConfig_Modify_Workmode_imageReportInterval(my_endian_conversion_32(*((uint32_t*)(data))), 1);
            return 0;
        case(devPI_workmode_measReportInterval):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_workmode_measReportInterval");
            MyConfig_Modify_Workmode_measReportInterval(my_endian_conversion_32(*((uint32_t*)(data))), 1);
            return 0;
        case(devPI_workmode_measReportmode):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_workmode_measReportmode");
            MyConfig_Modify_Workmode_measReportMode(my_endian_conversion_16(*((uint16_t*)(data))), 1);
            return 0;
        case(devPI_workmode_channeldoor_mode):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify MyConfig_Modify_Workmode_channeldoor_mode to %d", my_endian_conversion_32(*((uint32_t*)(data))));
            MyConfig_Modify_Workmode_channeldoor_mode(my_endian_conversion_32(*((uint32_t*)(data))), 1);
            return 0;

        // #if MY_BLE_FUNC
        //ble
        case(devPI_ble_enable_state):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_ble_enable_state");
            MyConfig_Modify_BLE_enableState(*((uint8_t*)(data)), 1, 1);
            return 0;
        case(devPI_ble_name):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_ble_name");
            MyConfig_Modify_BLE_name(data, data_len, 1, 1);
            return 0;
        case(devPI_ble_advType):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_ble_advType");
            MyConfig_Modify_BLE_advType(*((uint8_t*)(data)), 1, 1);
            return 0;
        case(devPI_ble_advData):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_ble_advData");
            MyConfig_Modify_BLE_advData(data, data_len, 1, 1);
            return 0;
        case(devPI_ble_advData_len):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_ble_advData_len");
            MyConfig_Modify_BLE_advDateLen(*((uint8_t*)(data)), 1, 1);
            return 0;
        case(devPI_ble_service_uuid_a):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_ble_service_uuid_a");
            MyConfig_Modify_BLE_service_uuid_a(my_endian_conversion_16(*((uint16_t*)(data))), 1, 1);
            return 0;
        case(devPI_ble_char_uuid_a):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_ble_char_uuid_a");
            MyConfig_Modify_BLE_char_uuid_a(my_endian_conversion_16(*((uint16_t*)(data))), 1, 1);
            return 0;
        case(devPI_ble_service_uuid_b):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_ble_service_uuid_b");
            MyConfig_Modify_BLE_service_uuid_b(my_endian_conversion_16(*((uint16_t*)(data))), 1, 1);
            return 0;
        case(devPI_ble_char_uuid_b):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_ble_char_uuid_b");
            MyConfig_Modify_BLE_char_uuid_b(my_endian_conversion_16(*((uint16_t*)(data))), 1, 1);
            return 0;
        // #endif

        //power
        case(devPI_power_state):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_power_state");
            //modify...
            return 1;
        case(devPI_power_poweroff_alarm_enable_state):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_power_poweroff_alarm_enable_state");
            MyConfig_Modify_power_poweroff_alarm_enable_state(*((uint8_t*)(data)), 1);
            return 0;
        case(devPI_power_shutdown_after_poweroffAlarm):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_power_shutdown_after_poweroffAlarm");
            MyConfig_Modify_power_shutdown_after_poweroffAlarm(*((uint8_t*)(data)), 1);
            return 0;
        
        //factory
        case(devPI_factory_devstate):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_factory_devstate");
            MyConfig_Modify_factory_devstate(*((uint8_t*)(data)), 1);
            return 0;
        case(devPI_factory_factoryDate):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_factory_factoryDate");
            MyConfig_Modify_factory_factoryDate(data, data_len, 1);
            return 0;
        case(devPI_factory_poweronCount):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_factory_poweronCount");
            //modify...
            return 1;
        case(devPI_factory_runtime_since_poweron):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_factory_runtime_since_poweron");
            //modify...
            return 1;
        case(devPI_factory_mac):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_factory_mac");
            //modify...
            return 1;
        case(devPI_factory_imei):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_factory_imei");
            //modify...
            return 1;
        case(devPI_factory_iccid):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_factory_iccid");
            //modify...
            return 1;
        case(devPI_factory_ota_info_net):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_factory_ota_info_net");
            {
                int read_cnt = 0;
                MyConfig_Modify_factory_verCode_net(my_endian_conversion_32(*((uint32_t*)(data))), 0);read_cnt+=4;
                MyConfig_Modify_factory_vername_net(data+read_cnt, 16, 0);read_cnt+=16;
                MyConfig_Modify_factory_verfilename_net(data+read_cnt, 100, 0);read_cnt+=100;
            }
            return 0;
        case(devPI_factory_ota_info_local):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_factory_ota_info_net");
            {
                int read_cnt = 0;
                MyConfig_Modify_factory_verCode_local(my_endian_conversion_32(*((uint32_t*)(data))), 0);read_cnt+=4;
                MyConfig_Modify_factory_vername_local(data+read_cnt, 16, 0);read_cnt+=16;
                MyConfig_Modify_factory_verfilename_local(data+read_cnt, 100, 0);read_cnt+=100;
            }
            return 0;
        case(devPI_factory_ota_vercode):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_factory_ota_vercode");
            MyConfig_Modify_factory_verCode_net(my_endian_conversion_32(*((uint32_t*)(data))), 0);
            return 1;
        case(devPI_factory_ota_vername):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_factory_ota_vername");
            MyConfig_Modify_factory_vername_net(data, data_len, 0);
            return 1;
        case(devPI_factory_ota_filename):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_factory_ota_filename");
            MyConfig_Modify_factory_verfilename_net(data, data_len, 0);
            return 1;
        case(devPI_factory_ota_check_type):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_factory_ota_check_type");
            MyConfig_Modify_factory_ota_check_type(*((uint8_t*)(data)), 1);
            return 0;
        case(devPI_factory_devType):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_factory_devType");
            //modify...
            return 1;
        case(devPI_factory_devModel):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_factory_devModel");
            //modify...
            return 1;
        case(devPI_factory_devId):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_factory_devId");
            MyConfig_Modify_factory_devId(data, data_len, 1);
            return 0;
        
        //lte
        case(devPI_lte_baudrate):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify devPI_lte_baudrate");
            MyConfig_Modify_lte_baudrate(my_endian_conversion_32(*((uint32_t*)(data))), 1);
            return 0;
        
        //----------------------------------------------------------------------------------
        case(devPI_ctrl_workrun_reboot):
        case(devPI_ctrl_workrun_reload_default_configs):
        case(devPI_ctrl_ble_open):
        case(devPI_ctrl_ble_close):
        case(devPI_ctrl_cam_take_picture):
        case(devPI_ctrl_ota_check):
            ESP_LOGI("My_ASLdpu_DevParam_Modify()", "modify param_id=0x%04X", param_id);
            MyASL_DPU_CtrlEvent_Send(param_id, 1, 1000);
            return 0;

        default:ESP_LOGE("My_ASLdpu_DevParam_Modify()", "unknow type of param_id");return 1;
    }
    return 0;
}

int MyASL_DPU_DevParamItem_Packer(my_d2link_list_t* p_param_list, int param_id, char* p_data, uint64_t data_uint64, int data_len)
{
    char* p_param_item = calloc(1, data_len+5);
    if(p_param_item!=NULL)
    {
        int p_index = 0;
        // memcpy(p_param_item+p_index, &param_id, 2), p_index+=2;
        *((uint16_t*)(p_param_item+p_index)) = my_endian_conversion_16(param_id);p_index+=2;

        uint8_t param_type = 0;
        // ESP_LOGI("MyASL_DPU_DevParamItem_Packer(debug)", "param_id=0x%x", param_id);
        if(param_id<=0x7FFF)param_type = 0x01;
        else if(param_id<=0xBFFF)param_type = 0x04;
        else if(param_id<=0xFFFF)param_type = 0x02;
        // memcpy(p_param_item+p_index, &param_type, 1), p_index+=1;
        *((uint8_t*)(p_param_item+p_index)) = param_type;p_index+=1;

        // memcpy(p_param_item+p_index, &data_len, 2), p_index+=2;
        *((uint16_t*)(p_param_item+p_index)) = my_endian_conversion_16(data_len);p_index+=2;

        if(data_len>0)
        {
            if(p_data==NULL)
            {
                memcpy(p_param_item+p_index, &data_uint64, data_len), p_index+=data_len;
            }
            else
            {
                memcpy(p_param_item+p_index, p_data, data_len), p_index+=data_len;
            }
        }

        My_D2Link_Append_Item(p_param_list, param_id, p_param_item, 1, 0, p_index, NULL);

        free(p_param_item);

        return 0;
    }
    
    return 1;
}

int MyASL_DPU_DevParam_Packer(my_d2link_list_t* p_param_list, int param_id)
{
    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("MyASL_DPU_DevParam_Packer():param_id=%x\n", param_id);
    #endif

    if(p_param_list==NULL)
    {
        ESP_LOGE("MyASL_DPU_DevParam_Packer()", "p_param_list==NULL");
        return -1;
    }

    my_nvs_d2link_list_item_t* p_conf_item = MyConfig_Get_ConfigItem_FromId(param_id);
    if(p_conf_item!=NULL)
    {
        if(p_conf_item->data_type==DATA_TYPE_BASIC)
        {
            uint64_t item_data_uint64 = 0;
            my_endian_conversion_custom_not_OverWrite(&item_data_uint64, &(p_conf_item->data_uint64), p_conf_item->data_len);
            MyASL_DPU_DevParamItem_Packer(p_param_list, param_id, NULL, item_data_uint64, p_conf_item->data_len);
        }
        else if(p_conf_item->data_type==DATA_TYPE_POINTER)
        {
            MyASL_DPU_DevParamItem_Packer(p_param_list, param_id, p_conf_item->p_data, 0, p_conf_item->data_len);
        }
        else
        {
            return 1;
        }
        return 0;
    }
    return 2;
}

int My_ASLdpu_DevParamRes_ParamItemHandler(int param_id, char* data, int data_len)
{
    return My_ASLdpu_DevParam_Modify(param_id, data, data_len);
}

int My_ASLdpu_DevParamRes_Analyse(char* data, int data_len, my_d2link_list_t* modify_paramid_list)
{
    if(modify_paramid_list==NULL)
    {
        return -1;
    }

    uint16_t param_num = 0;
    uint16_t read_param_num = 0;
    memcpy(&param_num, data+4+2, 2);
    param_num = my_endian_conversion_16(param_num);
    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("My_ASLdpu_DevParamRes_Analyse():param_num=%d\n", param_num);
    #endif

    uint16_t read_param_id = 0;
    uint8_t read_param_type = 0;
    uint16_t read_param_len = 0;
    #define param_data_buf_len  256
    char* p_read_param_data = calloc(1, param_data_buf_len);

    char* p_read_posi = data+4+2+2;
    char* p_read_posi_0 = p_read_posi;
    int read_len = 0;

    int modify_paramid_item_cnt = 1;
    for(; read_param_num<param_num; p_read_posi=p_read_posi_0+read_len)
    {
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
        printf("My_ASLdpu_DevParamRes_Analyse():read_param_num=%d, read_len=%d\n", read_param_num, read_len);
        #endif
        int param_item_offset = 0;
        memcpy(&read_param_id, p_read_posi+param_item_offset, sizeof(uint16_t)), param_item_offset+=sizeof(uint16_t);
        read_param_id = my_endian_conversion_16(read_param_id);
        memcpy(&read_param_type, p_read_posi+param_item_offset, sizeof(uint8_t)), param_item_offset+=sizeof(uint8_t);
        memcpy(&read_param_len, p_read_posi+param_item_offset, sizeof(uint16_t)), param_item_offset+=sizeof(uint16_t);
        read_param_len = my_endian_conversion_16(read_param_len);
        if(read_param_len<param_data_buf_len)
        {
            memcpy(p_read_param_data, p_read_posi+param_item_offset, read_param_len), param_item_offset+=read_param_len;

            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
            printf("My_ASLdpu_DevParamRes_Analyse():read_param_id=%x, read_param_type=%x, read_param_len=%x\n",\
            read_param_id, read_param_type, read_param_len);
            printf("My_ASLdpu_DevParamRes_Analyse():p_read_param_data(%d)=\n", read_param_len);
            for(int i=0; i<read_param_len; i++)
            {
                printf("%02X ", *(p_read_param_data+i));
            }
            printf("\n");
            #endif

            if(My_ASLdpu_DevParamRes_ParamItemHandler(read_param_id, p_read_param_data, read_param_len)==0)
            {
                My_D2Link_Append_Item(modify_paramid_list, modify_paramid_item_cnt++, NULL, 0, read_param_id, sizeof(read_param_id), NULL);
            }

            read_len+=param_item_offset;
            read_param_num++;
        }
        else
        {
            ESP_LOGE("My_ASLdpu_DevParamRes_Analyse():", "read_param_len<param_data_buf_len is false");
            break;
        }
    }

    uint16_t param_num_error = 0;
    memcpy(&param_num_error, p_read_posi, 2);
    param_num_error = my_endian_conversion_16(param_num_error);
    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("My_ASLdpu_DevParamRes_Analyse():param_num_error=%d\n", param_num_error);
    #endif

    if(p_read_param_data!=NULL)free(p_read_param_data);

    return 0;
}

int _My_ASLdpu_DevParamReq(my_d2link_list_t* request_paramid_list)
{
    int ret = 0;

    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\n\n\n\n\n\n\n\n\n\n\nMy_ASLdpu_DevParamReq():ParamReq\n");
    #endif

    if(request_paramid_list==NULL)
    {
        return -1;
    }

    if(request_paramid_list->list_item_num<=0)
    {
        return 1;
    }

    my_asl_head_t my_asl_head;
    my_asl_head.type = dev_param_req;
    my_asl_head.vercode = MY_ASL_PD;
    my_asl_head.len = 0;

    my_d2link_list_t* my_d2link_list = My_D2Link_Creat("ParamReq req list");

    My_D2Link_Append_Item(my_d2link_list, 1, NULL, 0, my_asl_dpu_iotboxinfo.dic, sizeof(my_asl_dpu_iotboxinfo.dic), NULL);
    My_D2Link_Append_Item(my_d2link_list, 2, NULL, 0, my_asl_dpu_iotboxinfo.token, sizeof(my_asl_dpu_iotboxinfo.token), NULL);

    char* param_req_id_pack = (char*)calloc(1, request_paramid_list->list_data_len);
    My_D2Link_Data_Packer(request_paramid_list, param_req_id_pack, request_paramid_list->list_data_len);
    My_D2Link_Append_Item(my_d2link_list, 101, param_req_id_pack, 0, 0, request_paramid_list->list_data_len, NULL);

    My_D2Link_Insert_Item(my_d2link_list, 101, 0, 100, NULL, 0, request_paramid_list->list_item_num, 2, NULL);

    if((my_d2link_list->list_data_len%4)!=0)
    {
        int fill_empty_num = 4 - (my_d2link_list->list_data_len%4);
        My_D2Link_Append_Item(my_d2link_list, 0xffff, NULL, 0, 0, fill_empty_num, NULL);
    }

    my_asl_head.len = my_endian_conversion_16(my_d2link_list->list_data_len);
    My_D2Link_Insert_Item(my_d2link_list, 1, 0, 0, &my_asl_head, 0, 0, sizeof(my_asl_head), NULL);

    char* param_req_pack = (char*)calloc(1, my_d2link_list->list_data_len);
    if(param_req_pack!=NULL)
    {
        My_D2Link_Data_Packer(my_d2link_list, param_req_pack, my_d2link_list->list_data_len);
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
        printf("_My_ASLdpu_DevParamReq():req_pack(%d)=\n", my_d2link_list->list_data_len);
        for(int i=0; i<my_d2link_list->list_data_len; i++)
        {
            printf("%02X ", *(param_req_pack+i));
        }
        printf("\n");
        #endif

        char* saving = NULL;
        int saving_len = 0;
        MyASL_ServicePort(my_asl_head.type, (void*)param_req_pack, my_d2link_list->list_data_len, (void*)&saving, &saving_len, MY_ASLDPU_TIMEOUT_PARAMREQ, 1);
        free(param_req_pack);

        if((saving_len>0)&&(saving!=NULL))
        {
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
            printf("_My_ASLdpu_DevParamReq():saving(%d)=\n", saving_len);
            for(int i=0; i<saving_len; i++)
            {
                printf("%02X ", *(saving+i));
            }
            printf("\n");
            #endif

            uint16_t resultcode = 0;
            memcpy(&resultcode, saving+4, 2);
            resultcode = my_endian_conversion_16(resultcode);
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
            printf("_My_ASLdpu_DevParamReq():resultcode=%x\n", resultcode);
            #endif
            if((resultcode==aslrc_Success)||(resultcode==aslrc_PartialSuccess))
            {
                uint16_t res_param_num = 0;
                memcpy(&res_param_num, saving+4+2, 2);
                res_param_num = my_endian_conversion_16(res_param_num);
                #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                printf("_My_ASLdpu_DevParamReq():res_param_num=%x(%d)\n", res_param_num, res_param_num);
                #endif
                //-------------------------------------
                my_d2link_list_t* modify_paramid_list = My_D2Link_Creat("modify_paramid_list");
                My_ASLdpu_DevParamRes_Analyse(saving, saving_len, modify_paramid_list);

                My_ASLdpu_DevParamReport_Partial(modify_paramid_list);
                My_D2Link_Delete(&modify_paramid_list);
                //-------------------------------------
                ret = 0;
            }
            else
            {
                ESP_LOGE("_My_ASLdpu_DevParamReq()", "resultcode error=%x,!!!!!!!!!!!!!!!!!\n", resultcode);
                ret = 1;
            }
        }
        else
        {
            ESP_LOGE("_My_ASLdpu_DevParamReq()", "(saving_len>0)&&(saving!=NULL) are false,!!!!!!!!!!!!!!!!!\n");
            ret = 2;
        }
        if(saving!=NULL)free(saving);
    }
    else
    {
        ret = -1;
    }

    if(param_req_id_pack!=NULL)free(param_req_id_pack);

    My_D2Link_Delete(&my_d2link_list);

    return ret;
}

int My_ASLdpu_DevParamReq_Partial(my_d2link_list_t* request_paramid_list)
{
    return _My_ASLdpu_DevParamReq(request_paramid_list);
}

void My_ASLdpu_DevParamReq_All(void)
{
    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\n\n\n\n\n\n\n\n\n\n\nMy_ASLdpu_DevParamReq_All():My_ASLdpu_DevParamReq_All\n");
    #endif


    uint16_t req_param_list[]={
        devPI_asl_appId,
        devPI_asl_loginFlag,
        // devPI_asl_dic,
        // devPI_asl_token,
        devPI_asl_sync_param_effect_time,

        // devPI_tsfc_frame_max_size_send,
        // devPI_tsfc_frame_max_size_rcv,
        // devPI_tsfc_sendFail_max_repeat,
        devPI_tsfc_aesKey,

        // devPI_net_server_ipv4_address,
        // devPI_net_server_ipv4_address_type,
        // devPI_net_server_port,

        devPI_workmode_curMode,
        devPI_workmode_worktimeList_1_start_time,
        devPI_workmode_worktimeList_1_keep_time,
        devPI_workmode_worktimeList_2_start_time,
        devPI_workmode_worktimeList_2_keep_time,
        devPI_workmode_worktimeList_3_start_time,
        devPI_workmode_worktimeList_3_keep_time,
        devPI_workmode_worktimeList_4_start_time,
        devPI_workmode_worktimeList_4_keep_time,
        devPI_workmode_worktimeList_5_start_time,
        devPI_workmode_worktimeList_5_keep_time,
        devPI_workmode_timermodeSleepTime,
        devPI_workmode_imageReportInterval,
        devPI_workmode_measReportInterval,
        devPI_workmode_measReportmode,
        devPI_workmode_channeldoor_mode,

        // devPI_ble_enable_state,
        // devPI_ble_cur_state,
        // devPI_ble_name,
        // devPI_ble_advType,
        // devPI_ble_advData,
        // devPI_ble_advData_len,

        // devPI_cam_model,
        // devPI_cam_support_photo_size_list,
        // devPI_cam_support_compress_list,
        // devPI_cam_flip_list,
        // devPI_cam_format_list,
        // devPI_cam_pickpicture_switch,
        // devPI_cam_cur_format,
        // devPI_cam_cur_photo_size_x,
        // devPI_cam_cur_photo_size_y,
        // devPI_cam_cur_photo_compress,
        // devPI_cam_cur_photo_flip_h,
        // devPI_cam_cur_photo_flip_v,
        // devPI_cam_cur_photo_rotate_90,
        // devPI_cam_cur_photo_rotate_180,
        // devPI_cam_cur_photo_rotate_270,
        // devPI_cam_cur_photo_contrast,
        // devPI_cam_cur_photo_brightness,
        // devPI_cam_cur_photo_saturation,
        // devPI_cam_cur_wb_mode,
        // devPI_cam_cur_special_effect,

        // devPI_power_state,
        devPI_power_poweroff_alarm_enable_state,
        devPI_power_shutdown_after_poweroffAlarm,

        // devPI_factory_devstate,
        // devPI_factory_factoryDate,
        // devPI_factory_poweron_reason,
        // devPI_factory_poweronCount,
        // devPI_factory_runtime_since_poweron,
        // devPI_factory_mac,
        // devPI_factory_imei,
        // devPI_factory_iccid,
        // devPI_factory_ota_info_local,
        // devPI_factory_ota_info_net,
        devPI_factory_ota_check_type,
        // devPI_factory_ota_vercode,
        // devPI_factory_ota_vername,
        // devPI_factory_ota_filename,
        // devPI_factory_devType,
        // devPI_factory_devModel,
        // devPI_factory_devId,
        0xffff
    };

    my_d2link_list_t* my_d2link_list = My_D2Link_Creat("param req param item list");
    int item_cnt = 1;
    for(int i=0; req_param_list[i]!=0xffff; i++)
    {
        My_D2Link_Append_Item(my_d2link_list, item_cnt++, NULL, 0, req_param_list[i], sizeof(uint16_t), NULL);
    }

    _My_ASLdpu_DevParamReq(my_d2link_list);

    My_D2Link_Delete(&my_d2link_list);

    return 0;
}

int My_ASLdpu_DevParamQuery_DebugTrigger(void)
{
    int ret = 0;

    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\n\n\n\n\n\n\n\n\n\n\nMy_ASLdpu_DevParamQuery_DebugTrigger():ParamQuery_DebugTrigger\n");
    #endif

    my_asl_head_t my_asl_head;
    my_asl_head.type = 0x88;
    my_asl_head.vercode = MY_ASL_PD;
    my_asl_head.len = 0;

    my_d2link_list_t* my_d2link_list = My_D2Link_Creat("DebugTrigger list");

    if((my_d2link_list->list_data_len%4)!=0)
    {
        int fill_empty_num = 4 - (my_d2link_list->list_data_len%4);
        My_D2Link_Append_Item(my_d2link_list, 0xffff, NULL, 0, 0, fill_empty_num, NULL);
    }

    my_asl_head.len = my_endian_conversion_16(my_d2link_list->list_data_len);
    My_D2Link_Insert_Item(my_d2link_list, 1, 0, 0, &my_asl_head, 0, 0, sizeof(my_asl_head), NULL);

    char* param_req_pack = (char*)calloc(1, my_d2link_list->list_data_len);
    if(param_req_pack!=NULL)
    {
        My_D2Link_Data_Packer(my_d2link_list, param_req_pack, my_d2link_list->list_data_len);
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
        printf("My_ASLdpu_DevParamQuery_DebugTrigger():req_pack(%d)=\n", my_d2link_list->list_data_len);
        for(int i=0; i<my_d2link_list->list_data_len; i++)
        {
            printf("%02X ", *(param_req_pack+i));
        }
        printf("\n");
        #endif

        char* saving = NULL;
        int saving_len = 0;
        //MyASL_ServicePort(my_asl_head.type, (void*)param_req_pack, my_d2link_list.list_data_len, (void*)&saving, &saving_len, 10000, 1);

        // printf("My_ASLdpu_DevParamQuery_DebugTrigger():MyASL_Send\n");
        MyASL_Send(my_asl_head.type, 1, (void*)param_req_pack, my_d2link_list->list_data_len, 15000, 1, 1);

        free(param_req_pack);

        if(saving!=NULL)free(saving);
    }
    else
    {
        ret = -1;
    }

    My_D2Link_Delete(&my_d2link_list);

    return ret;
}

int My_ASLdpu_DevParamUpdate_DebugTrigger(void)
{
    int ret = 0;

    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\n\n\n\n\n\n\n\n\n\n\nMy_ASLdpu_DevParamUpdate_DebugTrigger():ParamUpdate_DebugTrigger\n");
    #endif

    my_asl_head_t my_asl_head;
    my_asl_head.type = 0x89;
    my_asl_head.vercode = MY_ASL_PD;
    my_asl_head.len = 0;

    my_d2link_list_t* my_d2link_list = My_D2Link_Creat("DebugTrigger list");

    if((my_d2link_list->list_data_len%4)!=0)
    {
        int fill_empty_num = 4 - (my_d2link_list->list_data_len%4);
        My_D2Link_Append_Item(my_d2link_list, 0xffff, NULL, 0, 0, fill_empty_num, NULL);
    }

    my_asl_head.len = my_endian_conversion_16(my_d2link_list->list_data_len);
    My_D2Link_Insert_Item(my_d2link_list, 1, 0, 0, &my_asl_head, 0, 0, sizeof(my_asl_head), NULL);

    char* param_req_pack = (char*)calloc(1, my_d2link_list->list_data_len);
    if(param_req_pack!=NULL)
    {
        My_D2Link_Data_Packer(my_d2link_list, param_req_pack, my_d2link_list->list_data_len);
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
        printf("My_ASLdpu_DevParamUpdate_DebugTrigger():req_pack(%d)=\n", my_d2link_list->list_data_len);
        for(int i=0; i<my_d2link_list->list_data_len; i++)
        {
            printf("%02X ", *(param_req_pack+i));
        }
        printf("\n");
        #endif

        char* saving = NULL;
        int saving_len = 0;
        //MyASL_ServicePort(my_asl_head.type, (void*)param_req_pack, my_d2link_list.list_data_len, (void*)&saving, &saving_len, 10000, 1);

        // printf("My_ASLdpu_DevParamUpdate_DebugTrigger():MyASL_Send\n");
        MyASL_Send(my_asl_head.type, 1, (void*)param_req_pack, my_d2link_list->list_data_len, 15000, 1, 1);

        free(param_req_pack);

        if(saving!=NULL)free(saving);
    }
    else
    {
        ret = -1;
    }

    My_D2Link_Delete(&my_d2link_list);

    return ret;
}

int My_ASLdpu_DevCtrlParamUpdate_DebugTrigger(void)
{
    int ret = 0;

    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\n\n\n\n\n\n\n\n\n\n\nMy_ASLdpu_DevCtrlParamUpdate_DebugTrigger():CtrlParamUpdate_DebugTrigger\n");
    #endif

    my_asl_head_t my_asl_head;
    my_asl_head.type = 0x8A;
    my_asl_head.vercode = MY_ASL_PD;
    my_asl_head.len = 0;

    my_d2link_list_t* my_d2link_list = My_D2Link_Creat("DebugTrigger list");

    if((my_d2link_list->list_data_len%4)!=0)
    {
        int fill_empty_num = 4 - (my_d2link_list->list_data_len%4);
        My_D2Link_Append_Item(my_d2link_list, 0xffff, NULL, 0, 0, fill_empty_num, NULL);
    }

    my_asl_head.len = my_endian_conversion_16(my_d2link_list->list_data_len);
    My_D2Link_Insert_Item(my_d2link_list, 1, 0, 0, &my_asl_head, 0, 0, sizeof(my_asl_head), NULL);


    char* param_req_pack = (char*)calloc(1, my_d2link_list->list_data_len);
    if(param_req_pack!=NULL)
    {
        My_D2Link_Data_Packer(my_d2link_list, param_req_pack, my_d2link_list->list_data_len);
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
        printf("My_ASLdpu_DevCtrlParamUpdate_DebugTrigger():req_pack(%d)=\n", my_d2link_list->list_data_len);
        for(int i=0; i<my_d2link_list->list_data_len; i++)
        {
            printf("%02X ", *(param_req_pack+i));
        }
        printf("\n");
        #endif

        char* saving = NULL;
        int saving_len = 0;
        //MyASL_ServicePort(my_asl_head.type, (void*)param_req_pack, my_d2link_list.list_data_len, (void*)&saving, &saving_len, 10000, 1);

        // printf("My_ASLdpu_DevCtrlParamUpdate_DebugTrigger():MyASL_Send\n");
        MyASL_Send(my_asl_head.type, 1, (void*)param_req_pack, my_d2link_list->list_data_len, 15000, 1, 1);

        free(param_req_pack);

        if(saving!=NULL)free(saving);
    }
    else
    {
        ret = -1;
    }

    My_D2Link_Delete(&my_d2link_list);

    return ret;
}

int _MyASL_DPU_DevParamQuery_Packer(my_d2link_list_t* p_param_list, int param_id)
{
    return MyASL_DPU_DevParam_Packer(p_param_list, param_id);
}

int MyASL_DPU_DevParamQeury_Packer(my_d2link_list_t* p_param_list, char* saving, int saving_len, int* pack_len)
{
    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("MyASL_DPU_DevParamQeury_Packer()\n");
    #endif

    if(p_param_list==NULL)
    {
        ESP_LOGE("MyASL_DPU_DevParamQeury_Packer()", "p_param_list==NULL");
        return -1;
    }
    if(saving==NULL)
    {
        ESP_LOGE("MyASL_DPU_DevParamQeury_Packer()", "saving==NULL");
        return -2;
    }

    int ret = 0;

    my_d2link_list_t* my_d2link_list = My_D2Link_Creat("param req correct param list");
    my_d2link_list_t* my_d2link_list_error = My_D2Link_Creat("param req error param list");

    my_d2link_list_item_t* p_param_list_item_index = p_param_list->first_item;
    for(; p_param_list_item_index!=NULL; p_param_list_item_index = p_param_list_item_index->next_item)
    {
        uint16_t param_id = 0;
        if(p_param_list_item_index->data_type==0)
        {
            memcpy(&param_id, &(p_param_list_item_index->data_uint64), sizeof(uint16_t));
        }
        else
        {
            memcpy(&param_id, p_param_list_item_index->p_data, sizeof(uint16_t));
        }
        param_id = my_endian_conversion_16(param_id);
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
        printf("MyASL_DPU_DevParamQeury_Packer():param_id=0x%x(%d)\n", param_id, param_id);
        #endif
        if(_MyASL_DPU_DevParamQuery_Packer(my_d2link_list, param_id)>0)
        {
            My_D2Link_Append_Item(my_d2link_list_error, param_id, NULL, 0, param_id, sizeof(uint16_t), NULL);
        }
    }

    //打包 my_d2link_list 与 my_d2link_list_error 到 saving

    my_d2link_list_t* my_param_res_pack_list = My_D2Link_Creat("param res list");

    my_asl_head_t my_asl_head;
    my_asl_head.type = dev_param_query_res;
    my_asl_head.vercode = MY_ASL_PD;
    my_asl_head.len = 0;

    int res_pack_list_id_index = 1;
    My_D2Link_Append_Item(my_param_res_pack_list, res_pack_list_id_index++, NULL, 0, my_asl_dpu_iotboxinfo.dic, sizeof(my_asl_dpu_iotboxinfo.dic), NULL);
    My_D2Link_Append_Item(my_param_res_pack_list, res_pack_list_id_index++, NULL, 0, my_asl_dpu_iotboxinfo.token, sizeof(my_asl_dpu_iotboxinfo.token), NULL);

    uint16_t param_res_pack_ret_code = 0;
    if(my_d2link_list_error->list_item_num==0)
    {
        param_res_pack_ret_code = aslrc_Success;
    }
    else if(my_d2link_list->list_item_num>0)
    {
        param_res_pack_ret_code = aslrc_PartialSuccess;
    }
    else if(my_d2link_list->list_item_num==0)
    {
        param_res_pack_ret_code = aslrc_Fail;
    }
    My_D2Link_Append_Item(my_param_res_pack_list, res_pack_list_id_index++, NULL, 0, param_res_pack_ret_code, 2, NULL);

    //打包正确参数
    My_D2Link_Append_Item(my_param_res_pack_list, res_pack_list_id_index++, NULL, 0, my_d2link_list->list_item_num, 2, NULL);
    if(my_d2link_list->list_item_num>0)
    {
        char* param_res_param_item_pack = calloc(1, my_d2link_list->list_data_len);
        if(param_res_param_item_pack!=NULL)
        {
            My_D2Link_Data_Packer(my_d2link_list, param_res_param_item_pack, my_d2link_list->list_data_len);
            My_D2Link_Append_Item(my_param_res_pack_list, res_pack_list_id_index++, param_res_param_item_pack, 1, 0, my_d2link_list->list_data_len, NULL);
            free(param_res_param_item_pack);
        }
        else
        {
            ESP_LOGE("MyASL_DPU_DevParamQeury_Packer()", "param_res_param_item_pack!=NULL is false");
        };
    }
    else
    {
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
        printf("MyASL_DPU_DevParamQeury_Packer(): my_d2link_list.list_item_num==0\n");
        #endif
    }
    
    //打包异常参数
    My_D2Link_Append_Item(my_param_res_pack_list, res_pack_list_id_index++, NULL, 0, my_d2link_list_error->list_item_num, 2, NULL);
    if(my_d2link_list_error->list_item_num>0)
    {
        char* param_res_param_error_item_pack = calloc(1, my_d2link_list_error->list_data_len);
        if(param_res_param_error_item_pack!=NULL)
        {
            My_D2Link_Data_Packer(my_d2link_list_error, param_res_param_error_item_pack, my_d2link_list_error->list_data_len);
            My_D2Link_Append_Item(my_param_res_pack_list, res_pack_list_id_index++, param_res_param_error_item_pack, 1, 0, my_d2link_list_error->list_data_len, NULL);
            free(param_res_param_error_item_pack);
        }
        else
        {
            ESP_LOGE("MyASL_DPU_DevParamQeury_Packer()", "param_res_param_error_item_pack!=NULL is false");
        };
    }
    else
    {
        ESP_LOGI("MyASL_DPU_DevParamQeury_Packer()", "my_d2link_list_error.list_item_num==0");
    }

    //4字节对齐
    if((my_param_res_pack_list->list_data_len%4)!=0)
    {
        int fill_empty_num = 4 - (my_param_res_pack_list->list_data_len%4);
        My_D2Link_Append_Item(my_param_res_pack_list, 0xffff, NULL, 0, 0, fill_empty_num, NULL);
    }

    my_asl_head.len = my_endian_conversion_16(my_param_res_pack_list->list_data_len);
    My_D2Link_Insert_Item(my_param_res_pack_list, 1, 0, 0, &my_asl_head, 0, 0, sizeof(my_asl_head), NULL);

    //打包 my_param_res_pack_list 到 saving
    if(my_param_res_pack_list->list_data_len<=saving_len)
    {
        My_D2Link_Data_Packer(my_param_res_pack_list, saving, saving_len);
        *pack_len = my_param_res_pack_list->list_data_len;

        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
        printf("MyASL_DPU_DevParamQeury_Packer():saving=\n");
        for(int i=0; i<*pack_len; i++)
        {
            printf("%02X ", *(saving+i));
        }
        printf("\n");
        #endif
        ret = 0;
    }
    else
    {
        *pack_len = 0;
        ESP_LOGE("MyASL_DPU_DevParamQeury_Packer()", "saving_len enough");

        ret = 1;
    }

    My_D2Link_Delete(&my_d2link_list);
    My_D2Link_Delete(&my_d2link_list_error);
    My_D2Link_Delete(&my_param_res_pack_list);

    return ret;
}

int _My_ASLdpu_DevParamReport_Packer(my_d2link_list_t* p_param_list, int param_id)
{
    return MyASL_DPU_DevParam_Packer(p_param_list, param_id);
}

int _My_ASLdpu_DevParamReport(my_d2link_list_t* report_paramid_list)
{
    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\n\n\n\n\n\n\n\n\n\n\nMy_ASLdpu_DevParamReport():ParamReport\n");
    #endif

    int ret = 0;

    if(report_paramid_list==NULL)
    {
        return -1;
    }

    if(report_paramid_list->list_item_num<=0)
    {
        return 1;
    }

    my_d2link_list_t* my_d2link_list = My_D2Link_Creat("param report param item list");

    my_d2link_list_item_t* p_list_item_index = report_paramid_list->first_item;

    for(int i=0; (p_list_item_index!=NULL)&&(i<report_paramid_list->list_item_num); p_list_item_index=p_list_item_index->next_item, i++)
    {
        int err = _My_ASLdpu_DevParamReport_Packer(my_d2link_list, p_list_item_index->data_uint64);
        if(err)
        {
            ESP_LOGE("My_ASLdpu_DevParamReport()", "err from MyASL_DPU_DevParam_Packer: err=%d, report_param_list[%d]=%04x, invalid param id", err, i, (int)p_list_item_index->data_uint64);
        }
    }
    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
    My_D2Link_PrintList(my_d2link_list);
    #endif


    my_d2link_list_t* my_param_report_pack_list = My_D2Link_Creat("param report list");

    my_asl_head_t my_asl_head;
    my_asl_head.type = dev_param_report;
    my_asl_head.vercode = MY_ASL_PD;
    my_asl_head.len = 0;

    int res_pack_list_id_index = 1;
    My_D2Link_Append_Item(my_param_report_pack_list, res_pack_list_id_index++, NULL, 0, my_asl_dpu_iotboxinfo.dic, sizeof(my_asl_dpu_iotboxinfo.dic), NULL);
    My_D2Link_Append_Item(my_param_report_pack_list, res_pack_list_id_index++, NULL, 0, my_asl_dpu_iotboxinfo.token, sizeof(my_asl_dpu_iotboxinfo.token), NULL);
    My_D2Link_Append_Item(my_param_report_pack_list, res_pack_list_id_index++, NULL, 0, my_d2link_list->list_item_num, sizeof(uint16_t), NULL);

    char* p_param_report_param_item_pack = calloc(1, my_d2link_list->list_data_len);
    if(p_param_report_param_item_pack!=NULL)
    {
        My_D2Link_Data_Packer(my_d2link_list, p_param_report_param_item_pack, my_d2link_list->list_data_len);
    }
    else
    {
        ESP_LOGE("My_ASLdpu_DevParamReport()", "p_param_report_param_item_pack!=NULL is false");
    }
    My_D2Link_Append_Item(my_param_report_pack_list, res_pack_list_id_index++, p_param_report_param_item_pack, 0, 0, my_d2link_list->list_data_len, NULL);

    //4字节对齐
    if((my_param_report_pack_list->list_data_len%4)!=0)
    {
        int fill_empty_num = 4 - (my_param_report_pack_list->list_data_len%4);
        My_D2Link_Append_Item(my_param_report_pack_list, 0xffff, NULL, 0, 0, fill_empty_num, NULL);
    }

    my_asl_head.len = my_endian_conversion_16(my_param_report_pack_list->list_data_len);
    My_D2Link_Insert_Item(my_param_report_pack_list, 1, 0, 0, &my_asl_head, 0, 0, sizeof(my_asl_head), NULL);


    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
    My_D2Link_PrintList(my_param_report_pack_list);
    #endif

    char* p_param_report_pack = calloc(1, my_param_report_pack_list->list_data_len);
    if(p_param_report_pack!=NULL)
    {
        My_D2Link_Data_Packer(my_param_report_pack_list, p_param_report_pack, my_param_report_pack_list->list_data_len);

        char* saving = NULL;
        int saving_len = 0;
        MyASL_ServicePort(my_asl_head.type, (void*)p_param_report_pack, my_param_report_pack_list->list_data_len, (void*)&saving, &saving_len, MY_ASLDPU_TIMEOUT_PARAMREPORT, 1);
        free(p_param_report_pack);

        if((saving_len>0)&&(saving!=NULL))
        {
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
            printf("My_ASLdpu_DevParamReport():saving(%d)=\n", saving_len);
            for(int i=0; i<saving_len; i++)
            {
                printf("%02X ", *(saving+i));
            }
            printf("\n");
            #endif

            uint16_t resultcode = 0;
            memcpy(&resultcode, saving+4, 2);
            resultcode = my_endian_conversion_16(resultcode);
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
            printf("My_ASLdpu_DevParamReport():resultcode=%x\n", resultcode);
            #endif
            if(resultcode==aslrc_Success)
            {
                uint16_t res_param_num = 0;
                memcpy(&res_param_num, saving+4+2, 2);
                res_param_num = my_endian_conversion_16(res_param_num);
                #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                printf("My_ASLdpu_DevParamReport():res_param_num=%x(%d)\n", res_param_num, res_param_num);
                #endif
                //-------------------------------------
                
                //-------------------------------------
                ret = 0;
            }
            else
            {
                ESP_LOGE("My_ASLdpu_DevParamReport()", "resultcode error=%x,!!!!!!!!!!!!!!!!!", resultcode);
                ret = 1;
            }
        }
        else
        {
            ESP_LOGE("My_ASLdpu_DevParamReport()", "(saving_len>0)&&(saving!=NULL) are false,!!!!!!!!!!!!!!!!!");
        }
        if(saving!=NULL)free(saving);
    }
    else
    {
        ESP_LOGE("My_ASLdpu_DevParamReport()", "p_param_report_pack!=NULL is false");
    }

    if(p_param_report_param_item_pack!=NULL)free(p_param_report_param_item_pack);
    My_D2Link_Delete(&my_d2link_list);
    My_D2Link_Delete(&my_param_report_pack_list);

    return ret;
}

void My_ASLdpu_DevParamReport_All(void)
{
    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\n\n\n\n\n\n\n\n\n\n\nMy_ASLdpu_DevParamReport_All():My_ASLdpu_DevParamReport_All\n");
    #endif


    uint16_t report_param_list[]={
        devPI_asl_appId,
        devPI_asl_loginFlag,
        devPI_asl_dic,
        devPI_asl_last_sync_param_timestamp,
        devPI_asl_sync_param_effect_time,
        // devPI_asl_token,

        devPI_tsfc_frame_max_size_send,
        devPI_tsfc_frame_max_size_rcv,
        devPI_tsfc_sendFail_max_repeat,
        // devPI_tsfc_aesKey,

        devPI_net_server_ipv4_address,
        devPI_net_server_ipv4_address_type,
        devPI_net_server_port,

        devPI_workmode_curMode,
        devPI_workmode_worktimeList_1_start_time,
        devPI_workmode_worktimeList_1_keep_time,
        devPI_workmode_worktimeList_2_start_time,
        devPI_workmode_worktimeList_2_keep_time,
        devPI_workmode_worktimeList_3_start_time,
        devPI_workmode_worktimeList_3_keep_time,
        devPI_workmode_worktimeList_4_start_time,
        devPI_workmode_worktimeList_4_keep_time,
        devPI_workmode_worktimeList_5_start_time,
        devPI_workmode_worktimeList_5_keep_time,
        devPI_workmode_timermodeSleepTime,
        devPI_workmode_imageReportInterval,
        devPI_workmode_measReportInterval,
        devPI_workmode_measReportmode,
        devPI_workmode_channeldoor_mode,


        // devPI_ble_enable_state,
        // devPI_ble_cur_state,
        devPI_ble_name,
        devPI_ble_mac,
        // devPI_ble_advType,
        // devPI_ble_advData,
        // devPI_ble_advData_len,

        // devPI_cam_model,
        // devPI_cam_support_photo_size_list,
        // devPI_cam_support_compress_list,
        // devPI_cam_flip_list,
        // devPI_cam_format_list,
        // devPI_cam_pickpicture_switch,
        // devPI_cam_cur_format,
        // devPI_cam_cur_photo_size_x,
        // devPI_cam_cur_photo_size_y,
        // devPI_cam_cur_photo_compress,
        // devPI_cam_cur_photo_flip_h,
        // devPI_cam_cur_photo_flip_v,
        // devPI_cam_cur_photo_rotate_90,
        // devPI_cam_cur_photo_rotate_180,
        // devPI_cam_cur_photo_rotate_270,
        // devPI_cam_cur_photo_contrast,
        // devPI_cam_cur_photo_brightness,
        // devPI_cam_cur_photo_saturation,
        // devPI_cam_cur_wb_mode,
        // devPI_cam_cur_special_effect,

        devPI_power_state,
        devPI_power_poweroff_alarm_enable_state,
        devPI_power_shutdown_after_poweroffAlarm,

        devPI_factory_devstate,
        devPI_factory_factoryDate,
        devPI_factory_poweron_reason,
        devPI_factory_poweronCount,
        devPI_factory_runtime_since_poweron,
        devPI_factory_mac,
        devPI_factory_imei,
        devPI_factory_iccid,
        // devPI_factory_ota_info_local,
        // devPI_factory_ota_info_net,
        devPI_factory_ota_check_type,
        devPI_factory_ota_vercode,
        devPI_factory_ota_vername,
        devPI_factory_ota_filename,
        devPI_factory_devType,
        devPI_factory_devModel,
        devPI_factory_devId,

        // devPI_gnss_longitude,
        // devPI_gnss_latitude,
        // devPI_gnss_auto_report_switch,
        // devPI_gnss_auto_report_interval,
        0xffff
    };

    my_d2link_list_t* my_d2link_list = My_D2Link_Creat("param report param item list");
    int item_cnt = 1;
    for(int i=0; report_param_list[i]!=0xffff; i++)
    {
        My_D2Link_Append_Item(my_d2link_list, item_cnt++, NULL, 0, report_param_list[i], sizeof(uint16_t), NULL);
    }

    _My_ASLdpu_DevParamReport(my_d2link_list);

    My_D2Link_Delete(&my_d2link_list);
}

int My_ASLdpu_DevParamReport_Partial(my_d2link_list_t* report_paramid_list)
{
    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\n\n\n\n\n\n\n\n\n\n\nMy_ASLdpu_DevParamReport_Partial():My_ASLdpu_DevParamReport_Partial\n");
    #endif

    int ret = 0;

    ret = _My_ASLdpu_DevParamReport(report_paramid_list);

    return ret;
}

int _My_ASLdpu_DevEventReport_Packer(my_d2link_list_t* p_param_list, int param_id)
{
    return MyASL_DPU_DevParamItem_Packer(p_param_list, param_id, NULL, 0, 0);
}

int _My_ASLdpu_DevEventReport(my_d2link_list_t* report_paramid_list)
{
    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\n\n\n\n\n\n\n\n\n\n\n_My_ASLdpu_DevEventReport():EventReport\n");
    #endif

    if(report_paramid_list==NULL)
    {
        return -1;
    }

    if(report_paramid_list->list_item_num<=0)
    {
        return 1;
    }

    int ret = 0;

    my_d2link_list_t* my_d2link_list = My_D2Link_Creat("event report param item list");

    my_d2link_list_item_t* p_list_item_index = report_paramid_list->first_item;

    uint16_t correct_param_count = 0;
    for(int i=0; (p_list_item_index!=NULL)&&(i<report_paramid_list->list_item_num); p_list_item_index=p_list_item_index->next_item, i++)
    {
        int err = _My_ASLdpu_DevEventReport_Packer(my_d2link_list, p_list_item_index->data_uint64);
        if(err>0)
        {
            ESP_LOGE("_My_ASLdpu_DevEventReport()", "err from _My_ASLdpu_DevEventReport_Packer: err=%d, report_param_list[%d]=%04x, invalid param id", err, i, (int)p_list_item_index->data_uint64);
        }
        else if(err==0)
        {
            correct_param_count++;
        }
    }


    my_d2link_list_t* my_event_report_pack_list = My_D2Link_Creat("event report list");

    my_asl_head_t my_asl_head;
    my_asl_head.type = dev_event_report;
    my_asl_head.vercode = MY_ASL_PD;
    my_asl_head.len = 0;

    int res_pack_list_id_index = 1;
    My_D2Link_Append_Item(my_event_report_pack_list, res_pack_list_id_index++, NULL, 0, my_asl_dpu_iotboxinfo.dic, sizeof(my_asl_dpu_iotboxinfo.dic), NULL);
    My_D2Link_Append_Item(my_event_report_pack_list, res_pack_list_id_index++, NULL, 0, my_asl_dpu_iotboxinfo.token, sizeof(my_asl_dpu_iotboxinfo.token), NULL);
    My_D2Link_Append_Item(my_event_report_pack_list, res_pack_list_id_index++, NULL, 0, correct_param_count, sizeof(correct_param_count), NULL);

    char* p_event_report_param_item_pack = calloc(1, my_d2link_list->list_data_len);
    if(p_event_report_param_item_pack!=NULL)
    {
        My_D2Link_Data_Packer(my_d2link_list, p_event_report_param_item_pack, my_d2link_list->list_data_len);
    }
    else
    {
        ESP_LOGE("_My_ASLdpu_DevEventReport()", "p_event_report_param_item_pack!=NULL is false");
    }
    My_D2Link_Append_Item(my_event_report_pack_list, res_pack_list_id_index++, p_event_report_param_item_pack, 0, 0, my_d2link_list->list_data_len, NULL);

    //4字节对齐
    if((my_event_report_pack_list->list_data_len%4)!=0)
    {
        int fill_empty_num = 4 - (my_event_report_pack_list->list_data_len%4);
        My_D2Link_Append_Item(my_event_report_pack_list, 0xffff, NULL, 0, 0, fill_empty_num, NULL);
    }

    my_asl_head.len = my_endian_conversion_16(my_event_report_pack_list->list_data_len);
    My_D2Link_Insert_Item(my_event_report_pack_list, 1, 0, 0, &my_asl_head, 0, 0, sizeof(my_asl_head), NULL);

    char* p_event_report_pack = calloc(1, my_event_report_pack_list->list_data_len);
    if(p_event_report_pack!=NULL)
    {
        My_D2Link_Data_Packer(my_event_report_pack_list, p_event_report_pack, my_event_report_pack_list->list_data_len);

        char* saving = NULL;
        int saving_len = 0;
        MyASL_ServicePort(my_asl_head.type, (void*)p_event_report_pack, my_event_report_pack_list->list_data_len, (void*)&saving, &saving_len, MY_ASLDPU_TIMEOUT_EVENTREPORT, 1);
        free(p_event_report_pack);

        if((saving_len>0)&&(saving!=NULL))
        {
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
            printf("_My_ASLdpu_DevEventReport():saving(%d)=\n", saving_len);
            for(int i=0; i<saving_len; i++)
            {
                printf("%02X ", *(saving+i));
            }
            printf("\n");
            #endif

            uint16_t resultcode = 0;
            memcpy(&resultcode, saving+4, 2);
            resultcode = my_endian_conversion_16(resultcode);
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
            printf("_My_ASLdpu_DevEventReport():resultcode=%x\n", resultcode);
            #endif
            if(resultcode==aslrc_Success)
            {
                uint16_t res_param_num = 0;
                memcpy(&res_param_num, saving+4+2, 2);
                #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                printf("_My_ASLdpu_DevEventReport():res_param_num=%x(%d)\n", res_param_num, res_param_num);
                #endif
                //-------------------------------------
                
                //-------------------------------------
                ret = 0;
            }
            else
            {
                ESP_LOGE("_My_ASLdpu_DevEventReport()", "resultcode error=%x,!!!!!!!!!!!!!!!!!\n", resultcode);
                ret = 1;
            }
        }
        else
        {
            ESP_LOGE("_My_ASLdpu_DevEventReport()", "(saving_len>0)&&(saving!=NULL) are false,!!!!!!!!!!!!!!!!!\n");
        }
        if(saving!=NULL)free(saving);
    }
    else
    {
        ESP_LOGE("_My_ASLdpu_DevEventReport()", "p_event_report_pack!=NULL is false");
    }

    if(p_event_report_param_item_pack!=NULL)free(p_event_report_param_item_pack);
    My_D2Link_Delete(&my_d2link_list);
    My_D2Link_Delete(&my_event_report_pack_list);

    return ret;
}

int My_ASLdpu_DevEventReport_Partial(my_d2link_list_t* report_paramid_list)
{
    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\n\n\n\n\n\n\n\n\n\n\nMy_ASLdpu_DevEventReport_Partial():My_ASLdpu_DevEventReport_Partial\n");
    #endif

    int ret = 0;

    ret = _My_ASLdpu_DevEventReport(report_paramid_list);

    return ret;
}

int My_ASLdpu_DevMeasurementReport(int report_type, int report_data_type, char* param_data, int data_len)
{
    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\n\n\n\n\n\n\n\n\n\n\n_My_ASLdpu_DevMeasurementReport():MeasurementReport\n");
    #endif

    if(param_data==NULL)
    {
        return -1;
    }

    int ret = 0;


    my_d2link_list_t* my_event_report_pack_list = My_D2Link_Creat("event report list");

    my_asl_head_t my_asl_head;
    my_asl_head.type = dev_measure_report;
    my_asl_head.vercode = MY_ASL_PD;
    my_asl_head.len = 0;

    int res_pack_list_id_index = 1;
    My_D2Link_Append_Item(my_event_report_pack_list, res_pack_list_id_index++, NULL, 0, my_asl_dpu_iotboxinfo.dic, sizeof(my_asl_dpu_iotboxinfo.dic), NULL);
    My_D2Link_Append_Item(my_event_report_pack_list, res_pack_list_id_index++, NULL, 0, my_asl_dpu_iotboxinfo.token, sizeof(my_asl_dpu_iotboxinfo.token), NULL);
    My_D2Link_Append_Item(my_event_report_pack_list, res_pack_list_id_index++, NULL, 0, report_type, 2, NULL);
    My_D2Link_Append_Item(my_event_report_pack_list, res_pack_list_id_index++, NULL, 0, data_len+1, 2, NULL);
    My_D2Link_Append_Item(my_event_report_pack_list, res_pack_list_id_index++, NULL, 0, report_data_type, 1, NULL);
    My_D2Link_Append_Item(my_event_report_pack_list, res_pack_list_id_index++, param_data, 1, 0, data_len, NULL);

    //4字节对齐
    if((my_event_report_pack_list->list_data_len%4)!=0)
    {
        int fill_empty_num = 4 - (my_event_report_pack_list->list_data_len%4);
        My_D2Link_Append_Item(my_event_report_pack_list, 0xffff, NULL, 0, 0, fill_empty_num, NULL);
    }

    my_asl_head.len = my_endian_conversion_16(my_event_report_pack_list->list_data_len);
    My_D2Link_Insert_Item(my_event_report_pack_list, 1, 0, 0, &my_asl_head, 0, 0, sizeof(my_asl_head), NULL);

    char* p_event_report_pack = calloc(1, my_event_report_pack_list->list_data_len);
    if(p_event_report_pack!=NULL)
    {
        My_D2Link_Data_Packer(my_event_report_pack_list, p_event_report_pack, my_event_report_pack_list->list_data_len);

        char* saving = NULL;
        int saving_len = 0;
        MyASL_ServicePort(my_asl_head.type, (void*)p_event_report_pack, my_event_report_pack_list->list_data_len, (void*)&saving, &saving_len, MY_ASLDPU_TIMEOUT_EVENTREPORT, 0);
        free(p_event_report_pack);

        if(saving!=NULL)free(saving);
    }
    else
    {
        ESP_LOGE("_My_ASLdpu_DevMeasurementReport()", "p_event_report_pack!=NULL is false");
    }

    My_D2Link_Delete(&my_event_report_pack_list);

    return ret;
}

int My_ASLdpu_DevParamUpdate_ParamItemHandler(int param_id, char* data, int data_len)
{
    return My_ASLdpu_DevParam_Modify(param_id, data, data_len);
}

int My_ASLdpu_DevParamUpdate_Analyse(char* data, int data_len, char** res_pack_saving, int* saving_len, my_d2link_list_t* modify_paramid_list)
{
    uint16_t param_num = 0;
    uint16_t read_param_num = 0;
    memcpy(&param_num, data+4, 2);
    param_num = my_endian_conversion_16(param_num);
    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("My_ASLdpu_DevParamUpdate_Analyse():param_num=%d\n", param_num);
    #endif

    uint16_t read_param_id = 0;
    uint8_t read_param_type = 0;
    uint16_t read_param_len = 0;
    #define param_data_buf_len  256
    char* p_read_param_data = calloc(1, param_data_buf_len);

    char* p_read_posi = data+4+2;
    char* p_read_posi_0 = p_read_posi;
    int read_len = 0;

    my_d2link_list_t* error_param_list = My_D2Link_Creat("param update err param list");

    int modify_paramid_item_cnt = 1;
    for(; read_param_num<param_num; p_read_posi=p_read_posi_0+read_len)
    {
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
        printf("My_ASLdpu_DevParamUpdate_Analyse():read_param_num=%d, read_len=%d\n", read_param_num, read_len);
        #endif
        int param_item_offset = 0;
        memcpy(&read_param_id, p_read_posi+param_item_offset, sizeof(uint16_t)), param_item_offset+=sizeof(uint16_t);
        read_param_id = my_endian_conversion_16(read_param_id);
        memcpy(&read_param_type, p_read_posi+param_item_offset, sizeof(uint8_t)), param_item_offset+=sizeof(uint8_t);
        memcpy(&read_param_len, p_read_posi+param_item_offset, sizeof(uint16_t)), param_item_offset+=sizeof(uint16_t);
        read_param_len = my_endian_conversion_16(read_param_len);
        if(read_param_len<param_data_buf_len)
        {
            memcpy(p_read_param_data, p_read_posi+param_item_offset, read_param_len), param_item_offset+=read_param_len;

            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
            printf("My_ASLdpu_DevParamUpdate_Analyse():read_param_id=%x, read_param_type=%x, read_param_len=%x\n",\
            read_param_id, read_param_type, read_param_len);
            printf("My_ASLdpu_DevParamUpdate_Analyse():p_read_param_data(%d)=\n", read_param_len);
            for(int i=0; i<read_param_len; i++)
            {
                printf("%02X ", *(p_read_param_data+i));
            }
            printf("\n");
            #endif

            if(My_ASLdpu_DevParamUpdate_ParamItemHandler(read_param_id, p_read_param_data, read_param_len))
            {
                My_D2Link_Append_Item(error_param_list, read_param_id, NULL, 0, read_param_id, 2, NULL);
            }
            else
            {
                My_D2Link_Append_Item(modify_paramid_list, modify_paramid_item_cnt++, NULL, 0, read_param_id, sizeof(read_param_id), NULL);
            }

            read_len+=param_item_offset;
            read_param_num++;
        }
        else
        {
            ESP_LOGE("My_ASLdpu_DevParamUpdate_Analyse():", "read_param_len<param_data_buf_len is false");
            break;
        }
    }
    char* error_param_item_pack = NULL;
    if(error_param_list->list_item_num>0)
    {
        error_param_item_pack = calloc(1, error_param_list->list_data_len);
        if(error_param_item_pack!=NULL)
        {
            My_D2Link_Data_Packer(error_param_list, error_param_item_pack, error_param_list->list_data_len);
        }
        else
        {
            ESP_LOGE("My_ASLdpu_DevParamUpdate_Analyse():", "error_param_item_pack!=NULL is false");
        }
    }

    my_d2link_list_t* my_param_update_pack_list = My_D2Link_Creat("param update list");

    my_asl_head_t my_asl_head;
    my_asl_head.type = dev_param_update_res;
    my_asl_head.vercode = MY_ASL_PD;
    my_asl_head.len = 0;

    int res_pack_list_id_index = 1;
    My_D2Link_Append_Item(my_param_update_pack_list, res_pack_list_id_index++, NULL, 0, my_asl_dpu_iotboxinfo.dic, sizeof(my_asl_dpu_iotboxinfo.dic), NULL);
    My_D2Link_Append_Item(my_param_update_pack_list, res_pack_list_id_index++, NULL, 0, my_asl_dpu_iotboxinfo.token, sizeof(my_asl_dpu_iotboxinfo.token), NULL);

    uint16_t param_res_pack_ret_code = 0;
    if(error_param_list->list_item_num==0)
    {
        param_res_pack_ret_code = aslrc_Success;
    }
    else if(error_param_list->list_item_num<param_num)
    {
        param_res_pack_ret_code = aslrc_PartialSuccess;
    }
    else
    {
        param_res_pack_ret_code = aslrc_Fail;
    }
    My_D2Link_Append_Item(my_param_update_pack_list, res_pack_list_id_index++, NULL, 0, param_res_pack_ret_code, 2, NULL);
    My_D2Link_Append_Item(my_param_update_pack_list, res_pack_list_id_index++, NULL, 0, error_param_list->list_item_num, 2, NULL);
    if(error_param_list->list_item_num>0)
    {
        if(error_param_item_pack!=NULL)
        {
            My_D2Link_Append_Item(my_param_update_pack_list, res_pack_list_id_index++, error_param_item_pack, 0, 0, error_param_list->list_data_len, NULL);
        }
        else
        {
            ESP_LOGE("My_ASLdpu_DevParamUpdate_Analyse():", "error_param_item_pack!=NULL is false");
        }
    }
    //4字节对齐
    if((my_param_update_pack_list->list_data_len%4)!=0)
    {
        int fill_empty_num = 4 - (my_param_update_pack_list->list_data_len%4);
        My_D2Link_Append_Item(my_param_update_pack_list, 0xffff, NULL, 0, 0, fill_empty_num, NULL);
    }

    my_asl_head.len = my_endian_conversion_16(my_param_update_pack_list->list_data_len);
    My_D2Link_Insert_Item(my_param_update_pack_list, 1, 0, 0, &my_asl_head, 0, 0, sizeof(my_asl_head), NULL);

    *res_pack_saving = calloc(1, my_param_update_pack_list->list_data_len);
    if(*res_pack_saving!=NULL)
    {
        *saving_len = my_param_update_pack_list->list_data_len;
        My_D2Link_Data_Packer(my_param_update_pack_list, *res_pack_saving, my_param_update_pack_list->list_data_len);
    }
    else
    {
        *saving_len = 0;
        ESP_LOGE("My_ASLdpu_DevParamUpdate_Analyse():", "res_pack_saving!=NULL is false");
    }


    if(p_read_param_data!=NULL)free(p_read_param_data);
    if(error_param_item_pack!=NULL)free(error_param_item_pack);
    My_D2Link_Delete(&error_param_list);
    My_D2Link_Delete(&my_param_update_pack_list);

    return 0;
}

int My_ASLdpu_DevCtrlParamUpdate_ParamItemHandler(int param_id, char* data, int data_len)
{
    return My_ASLdpu_DevParam_Modify(param_id, data, data_len);
}

int My_ASLdpu_DevCtrlParamUpdate_Analyse(char* data, int data_len, char** res_pack_saving, int* saving_len, my_d2link_list_t* modify_paramid_list)
{
    uint16_t param_num = 0;
    uint16_t read_param_num = 0;
    memcpy(&param_num, data+4, 2);
    param_num = my_endian_conversion_16(param_num);
    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("My_ASLdpu_DevCtrlParamUpdate_Analyse():param_num=%d\n", param_num);
    #endif

    uint16_t read_param_id = 0;
    uint8_t read_param_type = 0;
    uint16_t read_param_len = 0;
    #define param_data_buf_len  256
    char* p_read_param_data = calloc(1, param_data_buf_len);

    char* p_read_posi = data+4+2;
    char* p_read_posi_0 = p_read_posi;
    int read_len = 0;

    my_d2link_list_t* error_param_list = My_D2Link_Creat("param update err param list");

    int modify_paramid_item_cnt = 1;
    for(; read_param_num<param_num; p_read_posi=p_read_posi_0+read_len)
    {
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
        printf("My_ASLdpu_DevCtrlParamUpdate_Analyse():read_param_num=%d, read_len=%d\n", read_param_num, read_len);
        #endif
        int param_item_offset = 0;
        memcpy(&read_param_id, p_read_posi+param_item_offset, sizeof(uint16_t)), param_item_offset+=sizeof(uint16_t);
        read_param_id = my_endian_conversion_16(read_param_id);
        memcpy(&read_param_type, p_read_posi+param_item_offset, sizeof(uint8_t)), param_item_offset+=sizeof(uint8_t);
        memcpy(&read_param_len, p_read_posi+param_item_offset, sizeof(uint16_t)), param_item_offset+=sizeof(uint16_t);
        read_param_len = my_endian_conversion_16(read_param_len);
        if(read_param_len<param_data_buf_len)
        {
            memcpy(p_read_param_data, p_read_posi+param_item_offset, read_param_len), param_item_offset+=read_param_len;

            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
            printf("My_ASLdpu_DevCtrlParamUpdate_Analyse():read_param_id=%x, read_param_type=%x, read_param_len=%x\n",\
            read_param_id, read_param_type, read_param_len);
            printf("My_ASLdpu_DevCtrlParamUpdate_Analyse():p_read_param_data(%d)=\n", read_param_len);
            for(int i=0; i<read_param_len; i++)
            {
                printf("%02X ", *(p_read_param_data+i));
            }
            printf("\n");
            #endif

            if(My_ASLdpu_DevCtrlParamUpdate_ParamItemHandler(read_param_id, p_read_param_data, read_param_len))
            {
                My_D2Link_Append_Item(error_param_list, read_param_id, NULL, 0, read_param_id, 2, NULL);
            }
            else
            {
                My_D2Link_Append_Item(modify_paramid_list, modify_paramid_item_cnt++, NULL, 0, read_param_id, sizeof(read_param_id), NULL);
            }

            read_len+=param_item_offset;
            read_param_num++;
        }
        else
        {
            ESP_LOGE("My_ASLdpu_DevCtrlParamUpdate_Analyse():", "read_param_len<param_data_buf_len is false");
            break;
        }
    }
    char* error_param_item_pack = NULL;
    if(error_param_list->list_item_num>0)
    {
        error_param_item_pack = calloc(1, error_param_list->list_data_len);
        if(error_param_item_pack!=NULL)
        {
            My_D2Link_Data_Packer(error_param_list, error_param_item_pack, error_param_list->list_data_len);
        }
        else
        {
            ESP_LOGE("My_ASLdpu_DevCtrlParamUpdate_Analyse():", "error_param_item_pack!=NULL is false");
        }
    }

    my_d2link_list_t* my_param_update_pack_list = My_D2Link_Creat("param update list");

    my_asl_head_t my_asl_head;
    my_asl_head.type = dev_ctrl_param_res;
    my_asl_head.vercode = MY_ASL_PD;
    my_asl_head.len = 0;

    int res_pack_list_id_index = 1;
    My_D2Link_Append_Item(my_param_update_pack_list, res_pack_list_id_index++, NULL, 0, my_asl_dpu_iotboxinfo.dic, sizeof(my_asl_dpu_iotboxinfo.dic), NULL);
    My_D2Link_Append_Item(my_param_update_pack_list, res_pack_list_id_index++, NULL, 0, my_asl_dpu_iotboxinfo.token, sizeof(my_asl_dpu_iotboxinfo.token), NULL);

    uint16_t param_res_pack_ret_code = 0;
    if(error_param_list->list_item_num==0)
    {
        param_res_pack_ret_code = aslrc_Success;
    }
    else if(error_param_list->list_item_num<param_num)
    {
        param_res_pack_ret_code = aslrc_PartialSuccess;
    }
    else
    {
        param_res_pack_ret_code = aslrc_Fail;
    }
    My_D2Link_Append_Item(my_param_update_pack_list, res_pack_list_id_index++, NULL, 0, param_res_pack_ret_code, 2, NULL);
    My_D2Link_Append_Item(my_param_update_pack_list, res_pack_list_id_index++, NULL, 0, error_param_list->list_item_num, 2, NULL);
    if(error_param_list->list_item_num>0)
    {
        if(error_param_item_pack!=NULL)
        {
            My_D2Link_Append_Item(my_param_update_pack_list, res_pack_list_id_index++, error_param_item_pack, 0, 0, error_param_list->list_data_len, NULL);
        }
        else
        {
            ESP_LOGE("My_ASLdpu_DevCtrlParamUpdate_Analyse():", "error_param_item_pack!=NULL is false");
        }
    }
    //4字节对齐
    if((my_param_update_pack_list->list_data_len%4)!=0)
    {
        int fill_empty_num = 4 - (my_param_update_pack_list->list_data_len%4);
        My_D2Link_Append_Item(my_param_update_pack_list, 0xffff, NULL, 0, 0, fill_empty_num, NULL);
    }

    my_asl_head.len = my_endian_conversion_16(my_param_update_pack_list->list_data_len);
    My_D2Link_Insert_Item(my_param_update_pack_list, 1, 0, 0, &my_asl_head, 0, 0, sizeof(my_asl_head), NULL);

    *res_pack_saving = calloc(1, my_param_update_pack_list->list_data_len);
    if(*res_pack_saving!=NULL)
    {
        *saving_len = my_param_update_pack_list->list_data_len;
        My_D2Link_Data_Packer(my_param_update_pack_list, *res_pack_saving, my_param_update_pack_list->list_data_len);
    }
    else
    {
        *saving_len = 0;
        ESP_LOGE("My_ASLdpu_DevCtrlParamUpdate_Analyse():", "res_pack_saving!=NULL is false");
    }


    if(p_read_param_data!=NULL)free(p_read_param_data);
    if(error_param_item_pack!=NULL)free(error_param_item_pack);
    My_D2Link_Delete(&error_param_list);
    My_D2Link_Delete(&my_param_update_pack_list);

    return 0;
}

int My_ASLdpu_DevFileUpload_Req(my_asl_file_base_info_t* my_asl_file_base_info, \
                                my_asl_file_additional_t* my_asl_file_additional, \
                                my_asl_file_bpct_info_t* my_asl_file_bpct_info, \
                                int block_num_total)
{
    int ret = 0;

    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\n\n\n\n\n\n\n\n\n\n\nMy_ASLdpu_DevFileUpload_Req():FileUpload_Req\n");
    #endif

    my_asl_head_t my_asl_head;
    my_asl_head.type = file_upload_req;
    my_asl_head.vercode = MY_ASL_PD;
    my_asl_head.len = 0;

    my_d2link_list_t* my_d2link_list = My_D2Link_Creat("FileUpload_Req req list");

    int my_d2link_index = 1;
    My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, my_asl_dpu_iotboxinfo.dic, sizeof(my_asl_dpu_iotboxinfo.dic), NULL);
    My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, my_asl_dpu_iotboxinfo.token, sizeof(my_asl_dpu_iotboxinfo.token), NULL);
    my_d2link_index = 101;
    My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, my_asl_file_base_info->file_type, 1, NULL);
    My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, my_asl_file_base_info->file_sn, 1, NULL);
    My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, my_asl_file_base_info->file_crc, 4, NULL);
    My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, my_asl_file_base_info->file_len, 4, NULL);

    if(my_asl_file_additional!=NULL)
    {
        My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, my_asl_file_additional->report_type, 2, NULL);
        My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, my_asl_file_additional->report_len, 2, NULL);
        My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, my_asl_file_additional->report_data, 0, 0, my_asl_file_additional->report_len, NULL);
    }

    if((my_d2link_list->list_data_len%4)!=0)
    {
        int fill_empty_num = 4 - (my_d2link_list->list_data_len%4);
        My_D2Link_Append_Item(my_d2link_list, 0xffff, NULL, 0, 0, fill_empty_num, NULL);
    }

    my_asl_head.len = my_endian_conversion_16(my_d2link_list->list_data_len);
    My_D2Link_Insert_Item(my_d2link_list, 1, 0, 0, &my_asl_head, 0, 0, sizeof(my_asl_head), NULL);

    char* fileupload_req_pack = (char*)calloc(1, my_d2link_list->list_data_len);
    if(fileupload_req_pack!=NULL)
    {
        My_D2Link_Data_Packer(my_d2link_list, fileupload_req_pack, my_d2link_list->list_data_len);
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
        printf("My_ASLdpu_DevFileUpload_Req():req_pack(%d)=\n", my_d2link_list->list_data_len);
        for(int i=0; i<my_d2link_list->list_data_len; i++)
        {
            printf("%02X ", *(fileupload_req_pack+i));
        }
        printf("\n");
        #endif

        char* saving = NULL;
        int saving_len = 0;
        MyASL_ServicePort(my_asl_head.type, (void*)fileupload_req_pack, my_d2link_list->list_data_len, (void*)&saving, &saving_len, 30000, 1);
        free(fileupload_req_pack);

        if((saving_len>0)&&(saving!=NULL))
        {
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
            printf("My_ASLdpu_DevFileUpload_Req():saving(%d)=\n", saving_len);
            for(int i=0; i<saving_len; i++)
            {
                printf("%02X ", *(saving+i));
            }
            printf("\n");
            #endif

            uint16_t resultcode = 0;
            memcpy(&resultcode, saving+4, 2);
            resultcode = my_endian_conversion_16(resultcode);
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
            printf("My_ASLdpu_DevFileUpload_Req():resultcode=%x\n", resultcode);
            #endif
            if(resultcode==aslrc_Success)
            {
                //-------------------------------------
                int read_count = 4+2;
                uint8_t res_file_type = *((uint8_t*)(saving+read_count));read_count+=1;
                uint8_t res_file_sn = *((uint8_t*)(saving+read_count));read_count+=1;;
                uint32_t res_file_key = *((uint32_t*)(saving+read_count));read_count+=4;
                uint32_t res_file_len = *((uint32_t*)(saving+read_count));read_count+=4;
                res_file_key = my_endian_conversion_32(res_file_key);
                res_file_len = my_endian_conversion_32(res_file_len);
                #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                printf("My_ASLdpu_DevFileUpload_Req():res_file_type=%x, res_file_sn=%x, res_file_key=%x(%d), res_file_len=%x(%d)\n", \
                        res_file_type, res_file_sn, res_file_key, res_file_key, res_file_len, res_file_len);
                #endif
                if(res_file_type==my_asl_file_base_info->file_type\
                    &&res_file_sn==my_asl_file_base_info->file_sn\
                    &&res_file_key==my_asl_file_base_info->file_crc\
                    &&res_file_len==my_asl_file_base_info->file_len)
                {
                    memcpy(&my_asl_file_bpct_info->block_offset, saving+read_count, 2), read_count+=2;
                    my_asl_file_bpct_info->block_offset = my_endian_conversion_16(my_asl_file_bpct_info->block_offset);
                    memcpy(&my_asl_file_bpct_info->block_cache, saving+read_count, 2), read_count+=2;
                    my_asl_file_bpct_info->block_cache = my_endian_conversion_16(my_asl_file_bpct_info->block_cache);
                    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                    printf("My_ASLdpu_DevFileUpload_Req():block_offset=%x(%d), block_cache=%16x\n", \
                    my_asl_file_bpct_info->block_offset,\
                    my_asl_file_bpct_info->block_offset,\
                    my_asl_file_bpct_info->block_cache);
                    #endif

                    if(my_asl_file_bpct_info->block_offset >= block_num_total)
                    {
                        ret = 4;
                    }
                    else
                    {
                        ret = 0;
                    }
                }
                else
                {
                    ESP_LOGE("My_ASLdpu_DevFileUpload_Req()", "res_file_baseinfo error,!!!!!!!!!!!!!!!!!\n");
                    ret = 2;
                }
                //-------------------------------------
            }
            else
            {
                ESP_LOGE("My_ASLdpu_DevFileUpload_Req()", "resultcode error=%x,!!!!!!!!!!!!!!!!!\n", resultcode);
                ret = 1;
            }
        }
        else
        {
            ret = 3;
            ESP_LOGE("My_ASLdpu_DevFileUpload_Req()", "(saving_len>0)&&(saving!=NULL) are false,!!!!!!!!!!!!!!!!!\n");
        }
        if(saving!=NULL)free(saving);
    }
    else
    {
        ESP_LOGE("My_ASLdpu_DevFileUpload_Req():", "fileupload_req_pack!=NULL is false");
        ret = -1;
    }

    My_D2Link_Delete(&my_d2link_list);

    return ret;
}

int My_Asldpu_DevSendDatablock(uint8_t file_type, uint8_t file_sn, uint16_t block_offset, char* data, int data_len, int end_flag, int timeout)
{
    int ret = 0;

    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("My_Asldpu_DevSendDatablock():file_type = %d, file_sn = %d, block_offset = %d, data_len = %d, timeout = %d\n", \
            file_type, file_sn, block_offset, data_len, timeout);
    #endif

    if(data_len<=send_data_block_size_max)
    {
        my_asl_head_t my_asl_head;
        my_asl_head.type = datablock_tsf;
        my_asl_head.vercode = MY_ASL_PD;
        my_asl_head.len = 0;

        my_d2link_list_t* my_d2link_list = My_D2Link_Creat("Datablock send list");

        My_D2Link_Append_Item(my_d2link_list, 1, NULL, 0, Uplink, 1, NULL);
        My_D2Link_Append_Item(my_d2link_list, 2, NULL, 0, my_asl_dpu_iotboxinfo.dic, sizeof(my_asl_dpu_iotboxinfo.dic), NULL);
        My_D2Link_Append_Item(my_d2link_list, 3, NULL, 0, my_asl_dpu_iotboxinfo.token, sizeof(my_asl_dpu_iotboxinfo.token), NULL);
        
        My_D2Link_Append_Item(my_d2link_list, 101, NULL, 0, file_type, 1, NULL);
        My_D2Link_Append_Item(my_d2link_list, 102, NULL, 0, file_sn, 1, NULL);
        My_D2Link_Append_Item(my_d2link_list, 103, NULL, 0, end_flag, 1, NULL);
        My_D2Link_Append_Item(my_d2link_list, 104, NULL, 0, block_offset, 2, NULL);
        My_D2Link_Append_Item(my_d2link_list, 105, NULL, 0, data_len, 2, NULL);

        My_D2Link_Append_Item(my_d2link_list, 201, data, 0, 0, data_len, NULL);

        if((my_d2link_list->list_data_len%4)!=0)
        {
            int fill_empty_num = 4 - (my_d2link_list->list_data_len%4);
            My_D2Link_Append_Item(my_d2link_list, 0xffff, NULL, 0, 0, fill_empty_num, NULL);
        }

        my_asl_head.len = my_endian_conversion_16(my_d2link_list->list_data_len);
        My_D2Link_Insert_Item(my_d2link_list, 1, 0, 0, &my_asl_head, 0, 0, sizeof(my_asl_head), NULL);

        char* datablock_tsf_pack = (char*)calloc(1, my_d2link_list->list_data_len);
        if(datablock_tsf_pack!=NULL)
        {
            My_D2Link_Data_Packer(my_d2link_list, datablock_tsf_pack, my_d2link_list->list_data_len);
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
            printf("My_Asldpu_DevSendDatablock():datablock_tsf_pack(%d)=...\n", my_d2link_list->list_data_len);
            for(int i=0; i<my_d2link_list->list_data_len; i++)
            {
                printf("%02X ", *(datablock_tsf_pack+i));
            }
            printf("\n");
            #endif

            if(!MyASL_ServicePort(my_asl_head.type, (void*)datablock_tsf_pack, my_d2link_list->list_data_len, NULL, NULL, 30000, 0))
            {
                //debug
                // vTaskDelay(100 / portTICK_PERIOD_MS);
                //end of debug
                ret = 0;
            }
            else
            {
                ret = 1;
            }
            free(datablock_tsf_pack);
        }
        else
        {
            ESP_LOGE("My_Asldpu_DevSendDatablock():", "datablock_tsf_pack!=NULL is false");
            ret = -1;
        }

        My_D2Link_Delete(&my_d2link_list);
    }
    else
    {
        ret = 2;
        ESP_LOGE("My_Asldpu_DevSendDatablock():", "data_len<=send_data_block_size_max is false");
    }
    
    return ret;
}

static uint8_t my_asldpu_file_sn_counter = 0;
typedef struct
{
    uint8_t file_sn;
    void* taskhandle;
    char* fileuploadtransfinished_pack;
    int pack_len;
}myasldpu_fileup_item_t;
#define MYASL_EVENT_FILEUPLOAD_ITEM_BASE    (0x01010000)
#define MYASL_FILE_BASE_INFO_CRC_REGION     (100*1024)
int _My_ASLdpu_DevFileUpload(int file_type, char* data, int data_len, my_asl_file_additional_t* my_asl_file_additional, int timeout)
{
    if(data==NULL)
    {
        ESP_LOGE("My_ASLdpu_DevFileUpload():", "data==NULL");
        return -1;
    }
    if(data_len<=0)
    {
        ESP_LOGE("My_ASLdpu_DevFileUpload():", "data_len<=0");
        return -2;
    }


    int ret = 0;
    uint32_t notifyValue = 0;

    //文件传输请求
    my_asl_file_base_info_t my_asl_file_base_info;
    my_asl_file_bpct_info_t my_asl_file_bpct_info;
    memset(&my_asl_file_base_info, 0, sizeof(my_asl_file_base_info));
    memset(&my_asl_file_bpct_info, 0, sizeof(my_asl_file_bpct_info));

    my_asl_file_base_info.file_type = file_type;
    my_asl_file_base_info.file_sn = my_asldpu_file_sn_counter;
    my_asl_file_base_info.file_len = data_len;
    
    if(my_asl_file_base_info.file_len <= MYASL_FILE_BASE_INFO_CRC_REGION)
    {
        my_asl_file_base_info.file_crc = crc_32((void*)data, my_asl_file_base_info.file_len);
    }
    else
    {
        my_asl_file_base_info.file_crc = crc_32((void*)data, MYASL_FILE_BASE_INFO_CRC_REGION);
    }

    int data_block_num_total = (data_len/send_data_block_size_max);
    int data_block_num_total_real = data_block_num_total;
    int end_block_size = data_len%send_data_block_size_max;
    if(end_block_size==0)
    {
        if(data_block_num_total>0)
        {
            data_block_num_total--;
            end_block_size = send_data_block_size_max;
        }
    }
    else
    {
        data_block_num_total_real++;
    }

    
    int err = 0;
    err = My_ASLdpu_DevFileUpload_Req(&my_asl_file_base_info, my_asl_file_additional, &my_asl_file_bpct_info, data_block_num_total_real);

    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("My_ASLdpu_DevFileUpload():res:my_asl_file_base_info.file_type=%x, my_asl_file_base_info.file_sn=%d, my_asl_file_base_info.file_crc(first 100KB)=%08X, my_asl_file_base_info.file_len=%d\n",\
    my_asl_file_base_info.file_type, my_asl_file_base_info.file_sn, my_asl_file_base_info.file_crc, my_asl_file_base_info.file_len);
    printf("My_ASLdpu_DevFileUpload():res:my_asl_file_bpct_info.block_offset=%d, my_asl_file_bpct_info.block_cache=%d\n",\
    my_asl_file_bpct_info.block_offset, my_asl_file_bpct_info.block_cache);
    #endif

    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("-- My_ASLdpu_DevFileUpload(): err = My_ASLdpu_DevFileUpload_Req=%d\n", err);
    #endif

    //debug if(!err+1)
    if(!err)
    {
        my_asl_rcvdata_cache_t my_asl_rcvdata_cache;
        memset(&my_asl_rcvdata_cache, 0, sizeof(my_asl_rcvdata_cache));
        myasldpu_fileup_item_t myasldpu_fileup_item;
        memset(&myasldpu_fileup_item, 0, sizeof(myasldpu_fileup_item));
        myasldpu_fileup_item.file_sn = my_asl_file_base_info.file_sn;
        myasldpu_fileup_item.taskhandle = xTaskGetCurrentTaskHandle();
        myasldpu_fileup_item.fileuploadtransfinished_pack = calloc(1, 1024);

        MyAslEvent_Register(&my_asl_dpu_event_list, my_asl_file_base_info.file_sn+MYASL_EVENT_FILEUPLOAD_ITEM_BASE, &myasldpu_fileup_item, NULL);
        
        //数据块传输
        int i=my_asl_file_bpct_info.block_offset;
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
        printf("My_ASLdpu_DevFileUpload():data_block_num_total_real=%d, data_block_num_total=%d, end_block_size=%d\n", \
        data_block_num_total_real, data_block_num_total, end_block_size);
        #endif

        my_asl_file_bpct_info.block_cache &= 0x00ff;
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
        printf("My_ASLdpu_DevFileUpload():res:my_asl_file_bpct_info.block_cache=%04X\n", my_asl_file_bpct_info.block_cache);
        #endif

        if(my_asl_file_bpct_info.block_cache==0x0000)
        {
            for(; i<data_block_num_total; i++)
            {
                err = My_Asldpu_DevSendDatablock(my_asl_file_base_info.file_type, my_asl_file_base_info.file_sn, i, data+(i*send_data_block_size_max), send_data_block_size_max, 0, 10000);
                if(err)
                {
                    ESP_LOGE("My_ASLdpu_DevFileUpload()", "err1: err=%d", err);
                    continue;
                }
                #if send_file_datablock_delay>0
                vTaskDelay(send_file_datablock_delay / portTICK_PERIOD_MS);
                #endif
            }
        }
        else
        {
            int block_cache_counter = 0;
            int remain_block_num = 0;
            uint8_t cur_blockcache = my_asl_file_bpct_info.block_cache&0x00ff;

            int valid_block_cache_num = data_block_num_total_real-i;
            if(valid_block_cache_num>8)valid_block_cache_num=8;
            ESP_LOGI("My_ASLdpu_DevFileUpload()", "valid_block_cache_num=%d\n", valid_block_cache_num);
            for(uint8_t x=0; x<valid_block_cache_num; x++)
            {
                if((cur_blockcache&(1<<x))==0)
                {
                    remain_block_num++;
                }
            }
            ESP_LOGI("My_ASLdpu_DevFileUpload()", "remain_block_num=%d\n", remain_block_num);
            for(; i<data_block_num_total; i++)
            {
                if(block_cache_counter<8)
                {
                    if(!(my_asl_file_bpct_info.block_cache&0x0001))
                    {
                        err = My_Asldpu_DevSendDatablock(my_asl_file_base_info.file_type, my_asl_file_base_info.file_sn, i, data+(i*send_data_block_size_max), send_data_block_size_max, 0, 10000);
                        if(err)
                        {
                            ESP_LOGE("My_ASLdpu_DevFileUpload()", "err1: err=%d", err);
                            continue;
                        }
                        else
                        {
                            remain_block_num--;
                        }
                    }
                    else
                    {
                        printf("_My_ASLdpu_DevFileUpload(): block_cache_counter=%d, no need to send\n", block_cache_counter);
                    }
                    my_asl_file_bpct_info.block_cache>>=1;
                    block_cache_counter++;
                    
                    
                    if(remain_block_num<=0)
                    {
                        notifyValue = 0;
                        xTaskNotifyWait(0x0, 0xffffffff, &notifyValue, 4000/portTICK_PERIOD_MS);
                        if(notifyValue==2)
                        {
                            ESP_LOGW("My_ASLdpu_DevFileUpload()", "early end!!!");
                            goto _My_ASLdpu_DevFileUpload_transfinished;
                        }
                    }
                    else
                    {
                        ESP_LOGW("My_ASLdpu_DevFileUpload()", "bpct remain_block_num=%d", remain_block_num);
                    }
                }
                else
                {
                    err = My_Asldpu_DevSendDatablock(my_asl_file_base_info.file_type, my_asl_file_base_info.file_sn, i, data+(i*send_data_block_size_max), send_data_block_size_max, 0, 10000);
                    if(err)
                    {
                        ESP_LOGE("My_ASLdpu_DevFileUpload()", "err1: err=%d", err);
                        continue;
                    }
                }
                #if send_file_datablock_delay>0
                vTaskDelay(send_file_datablock_delay / portTICK_PERIOD_MS);
                #endif
            }
        }
        
        if(end_block_size)
        {
            err = My_Asldpu_DevSendDatablock(my_asl_file_base_info.file_type, my_asl_file_base_info.file_sn, i, data+(i*send_data_block_size_max), end_block_size, 1, 10000);
            if(err)
            {
                ESP_LOGE("My_ASLdpu_DevFileUpload()", "err2: err=%d", err);
            }
        }
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
        printf("My_ASLdpu_DevFileUpload():data block send finished\n");
        #endif

        
        my_asl_rcvdata_cache.data = calloc(1, 1024);

        my_asl_rcvdata_cache.taskhandle = xTaskGetCurrentTaskHandle();
        MyAslEvent_Register(&my_asl_dpu_event_list, file_tsf_finish, &my_asl_rcvdata_cache, NULL);

        //等待响应
        notifyValue = 0;
        xTaskNotifyWait(0x0, 0xffffffff, &notifyValue, timeout/portTICK_PERIOD_MS);

        if(notifyValue)
        {
            _My_ASLdpu_DevFileUpload_transfinished:
            while(0);
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
            printf("My_ASLdpu_DevFileUpload():my_asl_rcvdata_cache.data=\n");
            #endif
            char* p_byte_rcv = NULL;
            int p_byte_rcv_datalen = 0;
            if(notifyValue==1)
            {
                p_byte_rcv = (char*)my_asl_rcvdata_cache.data;
                p_byte_rcv_datalen = my_asl_rcvdata_cache.len;
            }
            else if(notifyValue==2)
            {
                p_byte_rcv = (char*)myasldpu_fileup_item.fileuploadtransfinished_pack;
                p_byte_rcv_datalen = myasldpu_fileup_item.pack_len;
            }
            else
            {
                ESP_LOGE("My_ASLdpu_DevFileUpload()", "unknow notifyValue=%d", notifyValue);
            }
            
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
            for(int i=0; i<p_byte_rcv_datalen; i++)
            {
                printf("%02x ", *(p_byte_rcv+i));
            }
            printf("\n\n");
            #endif
            
            if(p_byte_rcv!=NULL)
            {
                uint16_t resultcode = 0;
                memcpy(&resultcode, p_byte_rcv+4+1+1, 2);
                resultcode = my_endian_conversion_16(resultcode);
                #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                printf("My_ASLdpu_DevFileUpload():resultcode=%x\n", resultcode);
                #endif
                if(resultcode==aslrc_Success)
                {
                    int read_count = 4;
                    uint8_t res_link_dir = 0;
                    memcpy(&res_link_dir, p_byte_rcv+read_count, 1), read_count+=1;
                    // uint32_t res_dic = 0;
                    // memcpy(&res_dic, p_byte_rcv+read_count, 4), read_count+=4;
                    // res_dic = my_endian_conversion_32(res_dic);
                    // uint32_t res_token = 0;
                    // memcpy(&res_token, p_byte_rcv+read_count, 4), read_count+=4;
                    // res_token = my_endian_conversion_32(res_token);
                    uint8_t res_file_sn = 0;
                    memcpy(&res_file_sn, p_byte_rcv+read_count, 1), read_count+=1;

                    // if(res_dic==my_asl_dpu_iotboxinfo.dic&&res_token==my_asl_dpu_iotboxinfo.token)
                    // {
                    //     if(res_link_dir==Uplink)
                    //     {
                    //         if(res_file_sn==my_asl_file_base_info.file_sn)
                    //         {
                    //             printf("My_ASLdpu_DevFileUpload():res correct, file trans finished\n");
                    //             ret = 0;
                    //         }
                    //         else
                    //         {
                    //             ret = 1;
                    //             ESP_LOGE("My_ASLdpu_DevFileUpload()", "res_file_sn error, res_file_sn=%d!!!!!!!!!!!!!!!!!", res_file_sn);
                    //         }
                    //     }
                    //     else
                    //     {
                    //         ret = 2;
                    //         ESP_LOGE("My_ASLdpu_DevFileUpload()", "res_link_dir error!!!!!!!!!!!!!!!!!");
                    //     }
                    // }
                    // else
                    // {
                    //     ret = 3;
                    //     ESP_LOGE("My_ASLdpu_DevFileUpload()", "dev idinfo error: res_dic=%x, res_token=%x,!!!!!!!!!!!!!!!!!", res_dic, res_token);
                    // }
                    if(res_link_dir==Downlink)
                    {
                        if(res_file_sn==my_asl_file_base_info.file_sn)
                        {
                            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                            printf("My_ASLdpu_DevFileUpload():res correct, file trans finished\n");
                            #endif
                            ret = 0;
                        }
                        else
                        {
                            ret = 1;
                            ESP_LOGE("My_ASLdpu_DevFileUpload()", "res_file_sn error, res_file_sn=%d!!!!!!!!!!!!!!!!!", res_file_sn);
                        }
                    }
                    else
                    {
                        ret = 2;
                        ESP_LOGE("My_ASLdpu_DevFileUpload()", "res_link_dir error!!!!!!!!!!!!!!!!!");
                    }
                    // //debug
                    // ret = 0;
                    // //end of debug
                }
                else
                {
                    ESP_LOGE("My_ASLdpu_DevFileUpload()", "resultcode error=%x,!!!!!!!!!!!!!!!!!", resultcode);
                    ret = 4;
                }
            }
            else
            {
                ESP_LOGE("My_ASLdpu_DevFileUpload()", "p_byte_rcv==NULL");
                ret = 5;
            }
        }
        else
        {
            ESP_LOGE("My_ASLdpu_DevFileUpload()", "notifyValue=%d", notifyValue);
            MyAslEvent_Delete(&my_asl_dpu_event_list, file_tsf_finish);
            ret = 11;
        }

        MyAslEvent_Delete(&my_asl_dpu_event_list, my_asl_file_base_info.file_sn+MYASL_EVENT_FILEUPLOAD_ITEM_BASE);
        
        if(my_asl_rcvdata_cache.data!=NULL)free(my_asl_rcvdata_cache.data);
        my_asl_rcvdata_cache.data=NULL;
        if(myasldpu_fileup_item.fileuploadtransfinished_pack!=NULL)free(myasldpu_fileup_item.fileuploadtransfinished_pack);
        myasldpu_fileup_item.fileuploadtransfinished_pack=NULL;
    }
    else
    {
        ret = 20;
    }

    return ret;
}

int My_ASLdpu_DevFileUpload(int file_type, \
                            char* data, int data_len, \
                            my_asl_file_additional_t* my_asl_file_additional, \
                            int timeout, \
                            int repeat_count)
{
    int err = 0;
    int repeat_counter = 0;
    uint32_t file_crc_32_result = crc_32((void*)data, data_len);
    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_1_SIMPLE
    printf("\n\n\n\n\n\n\n\nMy_ASLdpu_DevFileUpload(): file_type=%x, file_len=%d, timeout=%d, repeat_count=%d, file_crc_32_result(complete)=%08X\n\n", \
                                                        file_type, data_len, timeout, repeat_count, file_crc_32_result);
    
    #endif
    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_1_SIMPLE
    time_t start_time=0, end_time=0;
    #endif
    do
    {
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_1_SIMPLE
        start_time = esp_timer_get_time();
        #endif
        if(err>0)
        {
            repeat_counter++;
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_1_SIMPLE
            printf("\n\n\n\n\n\n\n\nMy_ASLdpu_DevFileUpload(): failed to upload to file, repeat(%d)\n\n", repeat_counter);
            #endif
        }
        err = _My_ASLdpu_DevFileUpload(file_type, data, data_len, my_asl_file_additional, timeout);
        if(repeat_count>0)
        {
            repeat_count--;
        }
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_1_SIMPLE
        end_time = esp_timer_get_time();
        #endif

        if(err)
        {
            vTaskDelay(5000 / portTICK_PERIOD_MS);
        }
    } while (repeat_count&&err);
    
    if(!err)
    {
        my_asldpu_file_sn_counter++;
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_1_SIMPLE
        int timeuse_in_ms = (end_time-start_time)/1000;
        printf("\n\nMy_ASLdpu_DevFileUpload(): success, timeuse=%dms, speed=%dbytes/s(%dKB/s), next my_asldpu_file_sn_counter=%d\n\n\n\n\n\n\n\n", \
        timeuse_in_ms, data_len*1000/timeuse_in_ms, data_len*1000/1024/timeuse_in_ms, my_asldpu_file_sn_counter);
        #endif
    }
    return err;
}

int My_ASLdpu_DevFileUpload_Image(char* data, int data_len, \
                                    uint16_t image_width, uint16_t image_height, \
                                    int timeout, int repeat_count)
{
    if(data==NULL)
    {
        return -1;
    }

    ESP_LOGI("My_ASLdpu_DevFileUpload_Image()", "");

    my_asl_file_additional_t my_asl_file_additional;
    memset(&my_asl_file_additional, 0, sizeof(my_asl_file_additional));
    my_asl_file_additional.report_type = fileadd_rt_upload_image_file;

    my_d2link_list_t* my_d2link_list = My_D2Link_Creat("uploadimage_reportdata");
    int my_d2link_index = 0;
    My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, rd_rt_upload_image_file, 1, NULL);
    My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, image_width, 2, NULL);
    My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, image_height, 2, NULL);

    char* uploadimage_reportdata = (char*)calloc(1, my_d2link_list->list_data_len);
    if(uploadimage_reportdata!=NULL)
    {
        My_D2Link_Data_Packer(my_d2link_list, uploadimage_reportdata, my_d2link_list->list_data_len);

        my_asl_file_additional.report_len = my_d2link_list->list_data_len;
        my_asl_file_additional.report_data = uploadimage_reportdata;

        My_ASLdpu_DevFileUpload(img_file_jpg, data, data_len, &my_asl_file_additional, timeout, repeat_count);

        return 0;
    }

    return 1;
}

int My_ASLdpu_DevFileDownload_Req(my_asl_file_base_info_t* my_asl_file_base_info, \
                                my_asl_file_additional_t* my_asl_file_additional_req, \
                                my_asl_file_additional_t* my_asl_file_additional_res, \
                                my_asl_file_bpct_info_t* my_asl_file_bpct_info)
{
    int ret = 0;

    ESP_LOGI("\n\n\n\n\n\n\n\n\n\n\n\nMy_ASLdpu_DevFileDownload_Req()", "FileDownload_Req\n");

    my_asl_head_t my_asl_head;
    my_asl_head.type = file_down_req;
    my_asl_head.vercode = MY_ASL_PD;
    my_asl_head.len = 0;

    my_d2link_list_t* my_d2link_list = My_D2Link_Creat("filedownload_Req req list");

    int my_d2link_index = 1;
    My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, my_asl_dpu_iotboxinfo.dic, sizeof(my_asl_dpu_iotboxinfo.dic), NULL);
    My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, my_asl_dpu_iotboxinfo.token, sizeof(my_asl_dpu_iotboxinfo.token), NULL);
    my_d2link_index = 101;
    My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, my_asl_file_base_info->file_type, 1, NULL);
    My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, my_asl_file_base_info->file_sn, 1, NULL);
    My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, my_asl_file_base_info->file_crc, 4, NULL);
    My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, my_asl_file_base_info->file_len, 4, NULL);

    my_d2link_index = 111;
    My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, my_asl_file_bpct_info->block_offset, 2, NULL);
    My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, my_asl_file_bpct_info->block_cache, 2, NULL);

    if(my_asl_file_additional_req!=NULL)
    {
        my_d2link_index = 121;
        My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, my_asl_file_additional_req->report_type, 2, NULL);
        My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, NULL, 0, my_asl_file_additional_req->report_len, 2, NULL);
        My_D2Link_Append_Item(my_d2link_list, my_d2link_index++, my_asl_file_additional_req->report_data, 0, 0, my_asl_file_additional_req->report_len, NULL);
    }

    if((my_d2link_list->list_data_len%4)!=0)
    {
        int fill_empty_num = 4 - (my_d2link_list->list_data_len%4);
        My_D2Link_Append_Item(my_d2link_list, 0xffff, NULL, 0, 0, fill_empty_num, NULL);
    }

    my_asl_head.len = my_endian_conversion_16(my_d2link_list->list_data_len);
    My_D2Link_Insert_Item(my_d2link_list, 1, 0, 0, &my_asl_head, 0, 0, sizeof(my_asl_head), NULL);

    char* filedownload_req_pack = (char*)calloc(1, my_d2link_list->list_data_len);
    if(filedownload_req_pack!=NULL)
    {
        My_D2Link_Data_Packer(my_d2link_list, filedownload_req_pack, my_d2link_list->list_data_len);
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
        printf("My_ASLdpu_DevFileDownload_Req():req_pack(%d)=\n", my_d2link_list->list_data_len);
        for(int i=0; i<my_d2link_list->list_data_len; i++)
        {
            printf("%02X ", *(filedownload_req_pack+i));
        }
        printf("\n");
        #endif

        char* saving = NULL;
        int saving_len = 0;
        MyASL_ServicePort(my_asl_head.type, \
        (void*)filedownload_req_pack, \
        my_d2link_list->list_data_len, \
        (void*)&saving, &saving_len, \
        MY_ASLDPU_TIMEOUT_FILEDOWNLOAD_REQ, 1);
        free(filedownload_req_pack);

        if((saving_len>0)&&(saving!=NULL))
        {
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
            printf("My_ASLdpu_DevFileDownload_Req():saving(%d)=\n", saving_len);
            for(int i=0; i<saving_len; i++)
            {
                printf("%02X ", *(saving+i));
            }
            printf("\n");
            #endif

            uint16_t resultcode = 0;
            memcpy(&resultcode, saving+4, 2);
            resultcode = my_endian_conversion_16(resultcode);
            ESP_LOGI("My_ASLdpu_DevFileDownload_Req()", "resultcode=%x\n", resultcode);
            if((resultcode==aslrc_Success))
            {
                //-------------------------------------
                int read_count = 4+2;
                uint8_t res_file_type = *((uint8_t*)(saving+read_count));read_count+=1;
                uint8_t res_file_sn = *((uint8_t*)(saving+read_count));read_count+=1;
                uint32_t res_file_key = *((uint32_t*)(saving+read_count));read_count+=4;
                res_file_key = my_endian_conversion_32(res_file_key);
                uint32_t res_file_len = *((uint32_t*)(saving+read_count));read_count+=4;
                res_file_len = my_endian_conversion_32(res_file_len);
                uint16_t res_report_type = *((uint16_t*)(saving+read_count));read_count+=2;
                res_report_type = my_endian_conversion_16(res_report_type);
                uint16_t res_report_len = *((uint16_t*)(saving+read_count));read_count+=2;
                res_report_len = my_endian_conversion_16(res_report_len);
                ESP_LOGI("My_ASLdpu_DevFileDownload_Req()", "res_file_type=0x%x, res_file_sn=0x%x, res_file_key=0x%08X, res_file_len=0x%x(%d)\n", \
                res_file_type, res_file_sn, res_file_key, res_file_len, res_file_len);
                ESP_LOGI("My_ASLdpu_DevFileDownload_Req()", "res_report_type=0x%x, report_len=0x%x\n", \
                res_report_type, res_report_len);
                my_asl_file_additional_res->report_type = res_report_type;
                my_asl_file_additional_res->report_len = res_report_len;
                if(res_file_type==my_asl_file_base_info->file_type)
                {
                    /* memcpy(&my_asl_file_bpct_info->block_offset, saving+read_count, 2), read_count+=2;
                    my_asl_file_bpct_info->block_offset = my_endian_conversion_16(my_asl_file_bpct_info->block_offset);
                    memcpy(&my_asl_file_bpct_info->block_cache, saving+read_count, 2), read_count+=2;
                    my_asl_file_bpct_info->block_cache = my_endian_conversion_16(my_asl_file_bpct_info->block_cache);
                    printf("My_ASLdpu_DevFileDownload_Req():block_offset=%x(%d), block_cache=%16x\n", \
                    my_asl_file_bpct_info->block_offset,\
                    my_asl_file_bpct_info->block_offset,\
                    my_asl_file_bpct_info->block_cache); */

                    my_asl_file_base_info->file_sn = res_file_sn;
                    my_asl_file_base_info->file_crc = res_file_key;
                    my_asl_file_base_info->file_len = res_file_len;
                    ESP_LOGI("My_ASLdpu_DevFileDownload_Req()", "res_file_sn=%d, res_file_key=0x%08X, file_len=%d\n", \
                    my_asl_file_base_info->file_sn,\
                    my_asl_file_base_info->file_crc,\
                    my_asl_file_base_info->file_len);

                    if(res_report_len>0)
                    {
                        my_asl_file_additional_res->report_data = calloc(1, res_report_len);
                        if(my_asl_file_additional_res->report_data!=NULL)
                        {
                            memcpy(my_asl_file_additional_res->report_data, saving+read_count, res_report_len);
                            ret = 0;
                        }
                        else
                        {
                            ret = 1;
                            ESP_LOGE("My_ASLdpu_DevFileDownload_Req()", "my_asl_file_additional_res->report_data!=NULL is false!\n");
                        }
                    }
                    else
                    {
                        ESP_LOGE("My_ASLdpu_DevFileDownload_Req()", "res_report_len>0 is false!\n");
                        ret = 2;
                    }
                }
                else
                {
                    ESP_LOGE("My_ASLdpu_DevFileDownload_Req()", "res_file_baseinfo error,!!!!!!!!!!!!!!!!!\n");
                    ret = 3;
                }
                //-------------------------------------
            }
            else
            {
                ESP_LOGE("My_ASLdpu_DevFileDownload_Req()", "resultcode error=%x,!!!!!!!!!!!!!!!!!\n", resultcode);
                ret = 4;
            }
        }
        else
        {
            ret = 5;
            ESP_LOGE("My_ASLdpu_DevFileDownload_Req()", "(saving_len>0)&&(saving!=NULL) are false,!!!!!!!!!!!!!!!!!\n");
        }
        if(saving!=NULL)free(saving);
    }
    else
    {
        ESP_LOGE("My_ASLdpu_DevFileDownload_Req():", "filedownload_req_pack!=NULL is false!");
        ret = -1;
    }

    My_D2Link_Delete(&my_d2link_list);

    return ret;
}

int _My_ASLdpu_DevFileDownload_step1(int file_type, \
                                    my_asl_file_base_info_t* my_asl_file_base_info_res, \
                                    my_asl_file_bpct_info_t* my_asl_file_bpct_info, \
                                    my_asl_file_additional_t* my_asl_file_additional_req, \
                                    my_asl_file_additional_t* my_asl_file_additional_res, \
                                    int timeout)
{
    ESP_LOGI("\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n@_My_ASLdpu_DevFileDownload_step1()\n\n\n", "");
    if(my_asl_file_bpct_info==NULL)
    {
        ESP_LOGE("_My_ASLdpu_DevFileDownload_step1():", "my_asl_file_bpct_info==NULL");
        return -2;
    }
    if(my_asl_file_additional_req==NULL)
    {
        ESP_LOGE("_My_ASLdpu_DevFileDownload_step1():", "my_asl_file_additional_req==NULL");
        return -3;
    }
    if(my_asl_file_additional_res==NULL)
    {
        ESP_LOGE("_My_ASLdpu_DevFileDownload_step1():", "my_asl_file_additional_res==NULL");
        return -4;
    }
    if(my_asl_file_base_info_res==NULL)
    {
        ESP_LOGE("_My_ASLdpu_DevFileDownload_step1():", "my_asl_file_base_info_res==NULL");
        return -5;
    }


    int ret = 0;

    //文件传输请求
    my_asl_file_base_info_t my_asl_file_base_info;
    
    memset(&my_asl_file_base_info, 0, sizeof(my_asl_file_base_info));

    my_asl_file_base_info.file_type = file_type;
    my_asl_file_base_info.file_sn = test_download_file_sn;
    
    int err = 0;
    err = My_ASLdpu_DevFileDownload_Req(&my_asl_file_base_info, \
                                        my_asl_file_additional_req, \
                                        my_asl_file_additional_res, \
                                        my_asl_file_bpct_info);

    if(!err)
    {
        ESP_LOGI("_My_ASLdpu_DevFileDownload_step1()", "res:my_asl_file_base_info.file_type=%x, my_asl_file_base_info.file_sn=%d, my_asl_file_base_info.file_len=%d\n",\
            my_asl_file_base_info.file_type, my_asl_file_base_info.file_sn, my_asl_file_base_info.file_len);
        memcpy(my_asl_file_base_info_res, &my_asl_file_base_info, sizeof(my_asl_file_base_info_t));
        if(my_asl_file_additional_res->report_data!=NULL)
        {
            ESP_LOGI("_My_ASLdpu_DevFileDownload_step1()", "res:my_asl_file_additional_res.report_type=%x, my_asl_file_additional_res.report_len=%d\n",\
                my_asl_file_additional_res->report_type, my_asl_file_additional_res->report_len);

            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
            ESP_LOGI("_My_ASLdpu_DevFileDownload_step1()", "res:my_asl_file_additional_res.report_data(%d)=\n", \
                my_asl_file_additional_res->report_len);
            for(int i=0; i<my_asl_file_additional_res->report_len; i++)
            {
                printf("%02X ", *(my_asl_file_additional_res->report_data+i));
            }
            printf("\n");
            #endif
        }
        else
        {
            ESP_LOGE("_My_ASLdpu_DevFileDownload_step1()", "my_asl_file_additional_res->report_data!=NULL is false!\n");
        }
    }
    else
    {
        ESP_LOGE("_My_ASLdpu_DevFileDownload_step1()", "err = My_ASLdpu_DevFileDownload_Req == %d!\n", err);
    }
    

    return ret=err;
}

typedef struct
{
    char* saving;
    int saving_len;
    my_d2link_list_t* datablock_list;
    int datablock_rcved_len;
    my_asl_rcvdata_cache_t my_asl_rcvdata_cache;
}_my_asl_dpu_filedownload_info_t;

int My_ASLdpu_DevRcvDataBlock(_my_asl_dpu_filedownload_info_t* _my_asl_dpu_filedownload_info, \
                                my_asl_file_base_info_t* my_asl_file_base_info, \
                                my_asl_file_bpct_info_t* my_asl_file_bpct_info, \
                                int timeout)
{
    ESP_LOGI("\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nMy_ASLdpu_DevRcvDataBlock()", "@My_ASLdpu_DevRcvDataBlock()\n\n\n");

    if(_my_asl_dpu_filedownload_info==NULL)
    {
        ESP_LOGE("My_ASLdpu_DevRcvDataBlock():", "_my_asl_dpu_filedownload_info==NULL");
        return -1;
    }
    if(_my_asl_dpu_filedownload_info->saving==NULL)
    {
        ESP_LOGE("My_ASLdpu_DevRcvDataBlock():", "_my_asl_dpu_filedownload_info->saving==NULL");
        return -2;
    }
    if(_my_asl_dpu_filedownload_info->saving_len<=0)
    {
        ESP_LOGE("My_ASLdpu_DevRcvDataBlock():", "_my_asl_dpu_filedownload_info->saving_len<=0");
        return -3;
    }
    if(my_asl_file_base_info==NULL)
    {
        ESP_LOGE("My_ASLdpu_DevRcvDataBlock():", "my_asl_file_base_info==NULL");
        return -4;
    }
    if(my_asl_file_bpct_info==NULL)
    {
        ESP_LOGE("My_ASLdpu_DevRcvDataBlock():", "my_asl_file_bpct_info==NULL");
        return -5;
    }


    int ret = 0;
    int err = 0;

    uint32_t notifyValue = 0;
    my_asl_rcvdata_cache_t* my_asl_rcvdata_cache = &_my_asl_dpu_filedownload_info->my_asl_rcvdata_cache;
    // memset(my_asl_rcvdata_cache, 0, sizeof(my_asl_rcvdata_cache_t));
    // my_asl_rcvdata_cache->data = calloc(1, 4096);


    // my_asl_rcvdata_cache->taskhandle = xTaskGetCurrentTaskHandle();
    // MyAslEvent_Register(&my_asl_dpu_event_list, datablock_tsf, my_asl_rcvdata_cache, NULL);

    for(; _my_asl_dpu_filedownload_info->datablock_rcved_len<my_asl_file_base_info->file_len;)
    {
        //等待响应
        notifyValue = 0;
        xTaskNotifyWait(0x0, 0xffffffff, &notifyValue, MY_ASLDPU_TIMEOUT_RCVDATABLOCK_REQ/portTICK_PERIOD_MS);

        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
        ESP_LOGI("My_ASLdpu_DevRcvDataBlock()", "notifyValue=%d", notifyValue);
        #endif

        if(notifyValue)
        {
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
            ESP_LOGI("My_ASLdpu_DevRcvDataBlock()", "my_asl_rcvdata_cache.data(%d)=", my_asl_rcvdata_cache->len);
            #endif
            char* p_byte_rcv = (char*)my_asl_rcvdata_cache->data;
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
            printf("\n");
            for(int i=0; i<my_asl_rcvdata_cache->len; i++)
            {
                printf("%02x ", *(p_byte_rcv+i));
            }
            printf("\n\n");
            #endif

            int read_count = 4+1;
            uint8_t rcv_db_file_type = *((uint8_t*)(p_byte_rcv+read_count));read_count+=1;
            uint8_t rcv_db_file_sn = *((uint8_t*)(p_byte_rcv+read_count));read_count+=1;
            uint8_t rcv_db_endflag = *((uint8_t*)(p_byte_rcv+read_count));read_count+=1;
            uint16_t rcv_db_no = *((uint16_t*)(p_byte_rcv+read_count));read_count+=2;
            rcv_db_no = my_endian_conversion_16(rcv_db_no);
            uint16_t rcv_db_data_len = *((uint16_t*)(p_byte_rcv+read_count));read_count+=2;
            rcv_db_data_len = my_endian_conversion_16(rcv_db_data_len);
            char* p_byte_rcv_db_data = p_byte_rcv+read_count;

            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
            ESP_LOGI("My_ASLdpu_DevRcvDataBlock()", "rcv_db_file_type=%x,rcv_db_file_sn=%d,rcv_db_endflag=%d,rcv_db_no=%d,rcv_db_data_len=%d", \
            rcv_db_file_type, rcv_db_file_sn, rcv_db_endflag, rcv_db_no, rcv_db_data_len);
            #endif

            if(_my_asl_dpu_filedownload_info->datablock_rcved_len<my_asl_file_base_info->file_len)
            {
                if(rcv_db_data_len>0)
                {
                    if(_my_asl_dpu_filedownload_info->datablock_rcved_len<=_my_asl_dpu_filedownload_info->saving_len-rcv_db_data_len)
                    {
                        err = My_D2Link_Append_Item(_my_asl_dpu_filedownload_info->datablock_list, \
                                                rcv_db_no, _my_asl_dpu_filedownload_info->saving+_my_asl_dpu_filedownload_info->datablock_rcved_len, \
                                                0, 0, rcv_db_data_len, NULL);
                        if(!err)
                        {
                            ESP_LOGI("My_ASLdpu_DevRcvDataBlock()", "saving datablock, db_no=%d, db_len=%d", rcv_db_no, rcv_db_data_len);
                            memcpy(_my_asl_dpu_filedownload_info->saving+_my_asl_dpu_filedownload_info->datablock_rcved_len, \
                                p_byte_rcv_db_data, rcv_db_data_len);
                                _my_asl_dpu_filedownload_info->datablock_rcved_len+=rcv_db_data_len;
                            ESP_LOGI("My_ASLdpu_DevRcvDataBlock()", "_my_asl_dpu_filedownload_info->datablock_rcved_len=%d\n\n", \
                                _my_asl_dpu_filedownload_info->datablock_rcved_len);
                        }
                        else
                        {
                            ESP_LOGW("My_ASLdpu_DevRcvDataBlock()", "err = My_D2Link_Append_Item == %d", err);
                        }
                    }
                    else
                    {
                        ESP_LOGE("My_ASLdpu_DevRcvDataBlock()", "datablock_rcved_len<=saving_len-rcv_db_data_len is false!");
                    }
                }
                else
                {
                    ESP_LOGE("My_ASLdpu_DevRcvDataBlock()", "rcv_db_data_len>0 is false!");
                }
            }
            else
            {
                ESP_LOGE("My_ASLdpu_DevRcvDataBlock()", "datablock_rcved_len<my_asl_file_base_info->file_len is false!");
            }
        }
        else
        {
            my_asl_file_bpct_info->block_offset = My_D2Link_GetItem_ContinousMaxId_next(\
                                                    _my_asl_dpu_filedownload_info->datablock_list, 0);
            my_asl_file_bpct_info->block_cache = My_D2Link_GetItem_ContinousDistribution_8_ByCurId(\
                                                    _my_asl_dpu_filedownload_info->datablock_list, my_asl_file_bpct_info->block_offset);
            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
            My_D2Link_PrintList(_my_asl_dpu_filedownload_info->datablock_list);
            #endif

            #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_1_SIMPLE
            ESP_LOGI("My_ASLdpu_DevRcvDataBlock()", "my_asl_file_bpct_info->block_offset=%d", my_asl_file_bpct_info->block_offset);
            ESP_LOGI("My_ASLdpu_DevRcvDataBlock()", "my_asl_file_bpct_info->block_cache=%d", my_asl_file_bpct_info->block_cache);
            ESP_LOGE("My_ASLdpu_DevRcvDataBlock()", "notifyValue is false!");
            #endif
            // MyAslEvent_Delete(&my_asl_dpu_event_list, file_tsf_finish);
            ret = 11;

            break;
        }
    }
    
    // MyAslEvent_Delete(&my_asl_dpu_event_list, datablock_tsf);
    // if(my_asl_rcvdata_cache->data!=NULL)free(my_asl_rcvdata_cache->data);
    // my_asl_rcvdata_cache->data=NULL;

    return ret;
}

void My_ASLdpu_DevFileTransFinish_Res(uint8_t file_sn, uint8_t link_dir, uint16_t ret_code)
{
    int ret = 0;

    ESP_LOGI("\n\n\n\n\n\n\n\n\n\n\n\nMy_ASLdpu_DevFileTransFinish_Res()", "FileTransFinish\n");

    my_asl_head_t my_asl_head;
    my_asl_head.type = file_tsf_finish;
    my_asl_head.vercode = MY_ASL_PD;
    my_asl_head.len = 0;

    my_d2link_list_t* my_d2link_list = My_D2Link_Creat("FileTransFinish res list");

    //链路方向
    My_D2Link_Append_Item(my_d2link_list, 101, NULL, 0, link_dir, 1, NULL);
    //设备识别信息
    My_D2Link_Append_Item(my_d2link_list, 102, NULL, 0, my_asl_dpu_iotboxinfo.dic, 4, NULL);
    My_D2Link_Append_Item(my_d2link_list, 103, NULL, 0, my_asl_dpu_iotboxinfo.token, 4, NULL);
    //文件序号
    My_D2Link_Append_Item(my_d2link_list, 201, NULL, 0, file_sn, 1, NULL);
    //返回码
    My_D2Link_Append_Item(my_d2link_list, 202, NULL, 0, ret_code, 2, NULL);

    if((my_d2link_list->list_data_len%4)!=0)
    {
        int fill_empty_num = 4 - (my_d2link_list->list_data_len%4);
        My_D2Link_Append_Item(my_d2link_list, 0xffff, NULL, 0, 0, fill_empty_num, NULL);
    }

    my_asl_head.len = my_endian_conversion_16(my_d2link_list->list_data_len);
    My_D2Link_Insert_Item(my_d2link_list, 101, 0, 0, &my_asl_head, 0, 0, sizeof(my_asl_head), NULL);

    char* fileupload_req_pack = (char*)calloc(1, my_d2link_list->list_data_len);
    if(fileupload_req_pack!=NULL)
    {
        My_D2Link_Data_Packer(my_d2link_list, fileupload_req_pack, my_d2link_list->list_data_len);
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
        printf("My_ASLdpu_DevFileUpload_Req():req_pack(%d)=\n", my_d2link_list->list_data_len);
        for(int i=0; i<my_d2link_list->list_data_len; i++)
        {
            printf("%02X ", *(fileupload_req_pack+i));
        }
        printf("\n");
        #endif

        MyASL_ServicePort(my_asl_head.type, (void*)fileupload_req_pack, my_d2link_list->list_data_len, NULL, 0, 30000, 0);
        free(fileupload_req_pack);
    }
    My_D2Link_Delete(&my_d2link_list);
}

int My_ASLdpu_DevFileDownload_OTA(void** file_saving, int* file_len, int timeout, int repeat_count)
{
    ESP_LOGI("\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n@My_ASLdpu_DevRcvDataBlock()\n\n\n", "");
    if(file_saving==NULL)
    {
        ESP_LOGE("My_ASLdpu_DevFileDownload_OTA():", "file_saving==NULL");
        return -1;
    }
    if(file_len==NULL)
    {
        ESP_LOGE("My_ASLdpu_DevFileDownload_OTA():", "file_len==NULL");
        return -1;
    }

    
    int ret = 0;
    int err = 0;

    my_asl_file_base_info_t my_asl_file_base_info_res;
    my_asl_file_bpct_info_t my_asl_file_bpct_info;
    my_asl_file_additional_t my_asl_file_additional_req;
    my_asl_file_additional_t my_asl_file_additional_res;

    memset(&my_asl_file_base_info_res, 0, sizeof(my_asl_file_base_info_res));
    memset(&my_asl_file_bpct_info, 0, sizeof(my_asl_file_bpct_info));
    memset(&my_asl_file_additional_req, 0, sizeof(my_asl_file_additional_req));
    memset(&my_asl_file_additional_res, 0, sizeof(my_asl_file_additional_res));

    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    my_asl_download_package_report_data_t my_asl_download_package_report_data;
    memset(&my_asl_download_package_report_data, 0, sizeof(my_asl_download_package_report_data));
    my_asl_download_package_report_data.report_data_type = rd_rt_download_package_file;
    memcpy(my_asl_download_package_report_data.filename, p_my_conf->factory.my_ota_info_net.filename, sizeof(my_asl_download_package_report_data.filename));
    
    my_asl_file_additional_req.report_type = fileadd_rt_download_package_file;
    my_asl_file_additional_req.report_len = sizeof(my_asl_download_package_report_data_t);
    my_asl_file_additional_req.report_data = &my_asl_download_package_report_data;

    my_d2link_list_t* my_d2link_list = My_D2Link_Creat("filedownload db list");

    _my_asl_dpu_filedownload_info_t _my_asl_dpu_filedownload_info;
    memset(&_my_asl_dpu_filedownload_info, 0, sizeof(_my_asl_dpu_filedownload_info_t));
    // _my_asl_dpu_filedownload_info.saving = saving;
    // _my_asl_dpu_filedownload_info.saving_len = saving_len;
    _my_asl_dpu_filedownload_info.datablock_list = my_d2link_list;

    MyDebug_PrintMemInfo("\nMEM:\nMy_ASLdpu_DevFileDownload_OTA_1");

    int repeat_counter = 0;

    _my_asl_dpu_filedownload_info.my_asl_rcvdata_cache.data = calloc(1, 4096);
    _my_asl_dpu_filedownload_info.my_asl_rcvdata_cache.taskhandle = xTaskGetCurrentTaskHandle();
    MyAslEvent_Register(&my_asl_dpu_event_list, datablock_tsf, &_my_asl_dpu_filedownload_info.my_asl_rcvdata_cache, NULL);

    time_t start_time=0, end_time=0;
    start_time = esp_timer_get_time();
    do
    {
        int res_check = 0;
        if(err!=0)
        {
            repeat_counter++;
            ESP_LOGI("\n\n\n\n\n\n\n\nMy_ASLdpu_DevFileDownload_OTA()", "My_ASLdpu_DevFileDownload_OTA(): failed to download to file, repeat(%d)\n\n\n\n\n\n\n\n", \
                repeat_counter);
        }
        //请求下载文件
        err = _My_ASLdpu_DevFileDownload_step1(test_download_file_type, \
                                                &my_asl_file_base_info_res, \
                                                &my_asl_file_bpct_info, \
                                                &my_asl_file_additional_req, \
                                                &my_asl_file_additional_res, \
                                                timeout);
        if(!err)
        {
            ESP_LOGI("My_ASLdpu_DevFileDownload_OTA()", "step1 err=%d\n", err);
            //验证请求的响应
            my_asl_download_package_report_data_t* res_ota_info_req = my_asl_file_additional_req.report_data;
            my_asl_download_package_report_data_t* res_ota_info_res = my_asl_file_additional_res.report_data;
            ESP_LOGI("My_ASLdpu_DevFileDownload_OTA()", "res_ota_info_req->report_data_type=%x, res_ota_info_req->filename=%s\n", \
                    res_ota_info_req->report_data_type, res_ota_info_req->filename);
            ESP_LOGI("My_ASLdpu_DevFileDownload_OTA()", "res_ota_info_res->report_data_type=%x, res_ota_info_res->filename=%s\n", \
                    res_ota_info_res->report_data_type, res_ota_info_res->filename);

            if(res_ota_info_res!=NULL)
            {
                // if(res_ota_info_res->device_type!=res_ota_info_req->device_type)
                // {
                //     res_check++;
                //     ESP_LOGE("My_ASLdpu_DevFileDownload_OTA()", "res_check err1\n");
                // }
                // if(strcmp(res_ota_info_res->device_model, res_ota_info_req->device_model)!=0)
                // {
                //     res_check++;
                //     ESP_LOGE("My_ASLdpu_DevFileDownload_OTA()", "res_check err2\n");
                // }
                // if(strcmp(res_ota_info_res->version, res_ota_info_req->version)!=0)
                // {
                //     res_check++;
                //     ESP_LOGE("My_ASLdpu_DevFileDownload_OTA()", "res_check err3\n");
                // }

                if(!res_check)
                {
                    err = 0;
                    ESP_LOGI("My_ASLdpu_DevFileDownload_OTA()", "res_check success");
                }
                else
                {
                    err = 1;
                    ESP_LOGE("My_ASLdpu_DevFileDownload_OTA()", "res_check err!");
                }
            }
            else
            {
                err = 2;
                ESP_LOGE("My_ASLdpu_DevFileDownload_OTA()", "res_ota_info_res!=NULL is false!");
            }

            //下载文件数据块
            // res_check = 0;
            int resfilelen_greaterthan0 = 0;
            if(!err)
            {
                if(my_asl_file_base_info_res.file_len>0)
                {
                    resfilelen_greaterthan0 = 1;
                    if(_my_asl_dpu_filedownload_info.saving==NULL)
                    {
                        _my_asl_dpu_filedownload_info.saving = calloc(1, my_asl_file_base_info_res.file_len);
                        _my_asl_dpu_filedownload_info.saving_len = my_asl_file_base_info_res.file_len;
                    }
                    if(_my_asl_dpu_filedownload_info.saving==NULL)
                    {
                        err = 3;
                        ESP_LOGE("My_ASLdpu_DevFileDownload_OTA()", "_my_asl_dpu_filedownload_info.saving!=NULL is false!");
                    }
                }
                else
                {
                    err = 0;
                    resfilelen_greaterthan0 = 0;
                    ESP_LOGW("\n\nMy_ASLdpu_DevFileDownload_OTA()", "my_asl_file_base_info_res.file_len=%d", my_asl_file_base_info_res.file_len);
                }

                if((!err)&&(resfilelen_greaterthan0))
                {
                    err = My_ASLdpu_DevRcvDataBlock(&_my_asl_dpu_filedownload_info, &my_asl_file_base_info_res, &my_asl_file_bpct_info, 10000);
                    ESP_LOGI("\n\nMy_ASLdpu_DevFileDownload_OTA()", "err = My_ASLdpu_DevRcvDataBlock = %d\n", err);
                    if(!err)
                    {
                        MyDebug_PrintMemInfo("\nMEM:\nMy_ASLdpu_DevFileDownload_OTA_2");
                        if(*file_saving==NULL)
                        {
                            // *file_saving = calloc(1, my_d2link_list->list_data_len);
                            *file_saving = _my_asl_dpu_filedownload_info.saving;
                        }
                        if(*file_saving!=NULL)
                        {
                            *file_len = my_d2link_list->list_data_len;
                            // My_D2Link_Data_Packer_accordingID(my_d2link_list, *file_saving, my_d2link_list->list_data_len, 0);
                            My_D2Link_Data_Sort_Data_accordingID(my_d2link_list, 0);
                            if(my_d2link_list->list_data_len != my_asl_file_base_info_res.file_len)
                            {
                                err = 1;
                                ESP_LOGW("My_ASLdpu_DevFileDownload_OTA()", "my_d2link_list->list_data_len != my_asl_file_base_info_res.file_len!");
                            }
                            uint32_t download_crc = crc_32((void*)(*file_saving), my_d2link_list->list_data_len);
                            if(download_crc != my_asl_file_base_info_res.file_crc)
                            {
                                err = 1;
                                ESP_LOGW("My_ASLdpu_DevFileDownload_OTA()", "download_crc != my_asl_file_base_info_res.file_crc!");
                            }
                            ESP_LOGI("\n\n\n\n\n\n\n\n\n\nMy_ASLdpu_DevFileDownload_OTA()", \
                            "download_file_final len = %d, file_crc=%08X\n\n\n\n\n\n\n\n\n\n", \
                            my_d2link_list->list_data_len, download_crc);
                            MyDebug_PrintMemInfo("\nMEM:\nMy_ASLdpu_DevFileDownload_OTA_3");
                            // free(*file_saving);*file_saving=NULL;
                        }
                        else
                        {
                            ESP_LOGE("My_ASLdpu_DevFileDownload_OTA()", "*file_saving!=NULL is false!");
                        }
                        
                        //响应文件下载完成

                        My_ASLdpu_DevFileTransFinish_Res(my_asl_file_base_info_res.file_sn, Uplink, aslrc_Success);
                        // free(_my_asl_dpu_filedownload_info.saving);_my_asl_dpu_filedownload_info.saving=NULL;
                    }
                    else
                    {
                        // My_ASLdpu_DevFileTransFinish_Res(my_asl_file_base_info_res.file_sn, Downlink, aslrc_Fail);
                        ESP_LOGE("ERROR1", "");
                    }
                    // free(_my_asl_dpu_filedownload_info.saving);_my_asl_dpu_filedownload_info.saving=NULL;
                }
                else
                {
                    
                }
            }
            else
            {
                
            }
        }

        if(repeat_count>0)
        {
            repeat_count--;
        }
    } while (repeat_count&&err);

    MyAslEvent_Delete(&my_asl_dpu_event_list, datablock_tsf);
    if(_my_asl_dpu_filedownload_info.my_asl_rcvdata_cache.data!=NULL)free(_my_asl_dpu_filedownload_info.my_asl_rcvdata_cache.data);
    _my_asl_dpu_filedownload_info.my_asl_rcvdata_cache.data=NULL;

    My_D2Link_Delete(&my_d2link_list);
    MyDebug_PrintMemInfo("\nMEM:\nMy_ASLdpu_DevFileDownload_OTA_4");
    if(!err)
    {
        end_time = esp_timer_get_time();
        int timeuse_in_ms = (end_time-start_time)/1000;
        ESP_LOGI("My_ASLdpu_DevFileDownload_OTA()", "success, timeuse=%dms, speed=%dbytes/s(%dKB/s)\n\n\n\n\n\n\n\n", \
        timeuse_in_ms, my_asl_file_base_info_res.file_len*1000/timeuse_in_ms, my_asl_file_base_info_res.file_len*1000/1024/timeuse_in_ms);
    }
    else
    {
        ESP_LOGI("\n\n\n\n\n\n\n\nMy_ASLdpu_DevFileDownload_OTA()", "failed\n\n\n\n\n\n\n\n");
    }

    if(my_asl_file_additional_res.report_data!=NULL)free(my_asl_file_additional_res.report_data);
    // if(_my_asl_dpu_filedownload_info.saving!=NULL)free(_my_asl_dpu_filedownload_info.saving);

    return ret;
}

int MyASL_DPU_Getfilesn_From_FileTransFinshedPack(char* pack)
{
    uint16_t resultcode = 0;
    memcpy(&resultcode, pack+4+1+1, 2);
    resultcode = my_endian_conversion_16(resultcode);
    ESP_LOGI("MyASL_DPU_Getfilesn_From_FileTransFinshedPack()", "resultcode=%x\n", resultcode);
    if(resultcode==aslrc_Success)
    {
        int read_count = 4;
        uint8_t res_link_dir = 0;
        memcpy(&res_link_dir, pack+read_count, 1), read_count+=1;
        // uint32_t res_dic = 0;
        // memcpy(&res_dic, pack+read_count, 4), read_count+=4;
        // res_dic = my_endian_conversion_32(res_dic);
        // uint32_t res_token = 0;
        // memcpy(&res_token, pack+read_count, 4), read_count+=4;
        // res_token = my_endian_conversion_32(res_token);
        uint8_t res_file_sn = 0;
        memcpy(&res_file_sn, pack+read_count, 1), read_count+=1;

        return res_file_sn;
    }
    return -1;    
}

TaskHandle_t MyASL_DPU_TaskHandle = NULL;
void MyASL_DPU_Task(void* param)
{
    int queue_rcv_ret = pdFALSE;
    // my_asl_dpu_queue_t* queue_rcv_item = (my_asl_dpu_queue_t*)calloc(1, sizeof(my_asl_dpu_queue_t));
    my_asl_dpu_queue_t* queue_rcv_item = NULL;
    char* p_rcv_data = (char*)calloc(1, 5120+4096);

    // if(queue_rcv_item==NULL)
    // {
    //     ESP_LOGE("MyASL_DPU_Task()", "queue_rcv_item==NULL\n");
    // }

    for(;;)
    {
        //队列接收
        queue_rcv_ret = xQueueReceive(my_asl_dpu_queue, &queue_rcv_item, portMAX_DELAY);
        if(queue_rcv_ret==pdTRUE)
        {
            if(queue_rcv_item!=NULL)
            {
                #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                printf("MyASL_DPU_Task():queue_rcv_item->type=0x%x\n", queue_rcv_item->type);
                printf("MyASL_DPU_Task():queue_rcv_item->len=%d\n", queue_rcv_item->len);
                #endif

                memcpy(p_rcv_data, queue_rcv_item->data, queue_rcv_item->len);
                uint32_t data_len = queue_rcv_item->len;
                int data_type = queue_rcv_item->type;

                if(queue_rcv_item->valid)
                {
                    #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
                    printf("MyASL_DPU_Task():p_rcv_data(%d)=\n", data_len);
                    for(int i=0; i<data_len; i++)
                    {
                        printf("%02X ", *(p_rcv_data+i));
                    }
                    printf("\n");
                    #endif

                    //------------------------------------------------------------
                    if(data_type==dev_param_query_indication)
                    {
                        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                        printf("MyASL_DPU_Task():data_type==dev_param_query_indication\n");
                        #endif

                        my_d2link_list_t* my_d2link_list = My_D2Link_Creat("param query list");

                        uint16_t query_param_num = 0;
                        memcpy(&query_param_num, p_rcv_data+4+1, 2);
                        query_param_num = my_endian_conversion_16(query_param_num);
                        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                        printf("MyASL_DPU_Task():query_param_num=%d\n", query_param_num);
                        #endif

                        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                        printf("MyASL_DPU_Task():extract query param...\n");
                        #endif
                        for(int i=0; i<query_param_num; i++)
                        {
                            My_D2Link_Append_Item(my_d2link_list, 100+i, p_rcv_data+4+1+2+(i*2), 0, 0, 2, NULL);
                        }

                        char* dev_param_pack_saving = (char*)calloc(1, 1024);
                        int dev_param_pack_len = 0;
                        MyASL_DPU_DevParamQeury_Packer(my_d2link_list, dev_param_pack_saving, 1024, &dev_param_pack_len);
                        MyASL_ServicePort(dev_param_query_res, (void*)dev_param_pack_saving, dev_param_pack_len, NULL, NULL, 30000, 0);
                        My_D2Link_Delete(&my_d2link_list);
                        if(dev_param_pack_saving!=NULL)free(dev_param_pack_saving);
                    }
                    else if(data_type==dev_param_update_indication)
                    {
                        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                        printf("MyASL_DPU_Task():data_type==dev_param_update_indication\n");
                        #endif

                        char* param_update_res_pack = NULL;
                        int param_update_res_pack_len = 0;
                        my_d2link_list_t* modify_paramid_list = My_D2Link_Creat("modify_paramid_list");
                        My_ASLdpu_DevParamUpdate_Analyse(p_rcv_data, data_len, &param_update_res_pack, &param_update_res_pack_len, modify_paramid_list);
                        if(param_update_res_pack!=NULL)
                        {
                            MyASL_ServicePort(dev_param_update_res, (void*)param_update_res_pack, param_update_res_pack_len, NULL, NULL, 30000, 0);
                            free(param_update_res_pack);

                            My_ASLdpu_DevParamReport_Partial(modify_paramid_list);
                        }
                        else
                        {
                            ESP_LOGE("MyASL_DPU_Task()", "param_update_res_pack!=NULL if false");
                        }
                        My_D2Link_Delete(&modify_paramid_list);
                    }
                    else if(data_type==dev_ctrl_param_indication)
                    {
                        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                        printf("MyASL_DPU_Task():data_type==dev_ctrl_param_indication\n");
                        #endif

                        char* param_update_ctrl_res_pack = NULL;
                        int param_update_ctrl_res_pack_len = 0;
                        my_d2link_list_t* modify_paramid_list = My_D2Link_Creat("modify_paramid_list");
                        My_ASLdpu_DevCtrlParamUpdate_Analyse(p_rcv_data, data_len, &param_update_ctrl_res_pack, &param_update_ctrl_res_pack_len, modify_paramid_list);
                        if(param_update_ctrl_res_pack!=NULL)
                        {
                            MyASL_ServicePort(dev_ctrl_param_res, (void*)param_update_ctrl_res_pack, param_update_ctrl_res_pack_len, NULL, NULL, 30000, 0);
                            free(param_update_ctrl_res_pack);

                            My_ASLdpu_DevParamReport_Partial(modify_paramid_list);
                        }
                        else
                        {
                            ESP_LOGE("MyASL_DPU_Task()", "param_update_ctrl_res_pack!=NULL if false");
                        }
                        My_D2Link_Delete(&modify_paramid_list);
                    }
                    else if(data_type==file_tsf_finish)
                    {
                        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                        printf("MyASL_DPU_Task():data_type==file_tsf_finish\n");
                        #endif

                        my_asl_rcvdata_cache_t* my_asl_rcvdata_cache = (my_asl_rcvdata_cache_t*)MyAslEvent_GetItem_Storage_ById(&my_asl_dpu_event_list, file_tsf_finish);
                        if(my_asl_rcvdata_cache!=NULL)
                        {
                            if(!my_asl_rcvdata_cache->valid)
                            {
                                memcpy(my_asl_rcvdata_cache->data, p_rcv_data, data_len);
                                my_asl_rcvdata_cache->type = data_type;
                                my_asl_rcvdata_cache->len = data_len;
                                if(my_asl_rcvdata_cache->taskhandle!=NULL)
                                {
                                    xTaskNotify(my_asl_rcvdata_cache->taskhandle, 1, eSetValueWithOverwrite);
                                }
                                else
                                {
                                    ESP_LOGE("MyASL_DPU_Task()", "my_asl_rcvdata_cache->taskhandle==NULL");
                                }
                            }
                            else
                            {
                                ESP_LOGE("MyASL_DPU_Task()", "my_asl_rcvdata_cache->valid is True");
                            }
                            MyAslEvent_Delete(&my_asl_dpu_event_list, data_type);
                        }
                        else
                        {
                            ESP_LOGW("MyASL_DPU_Task()", "my_asl_rcvdata_cache==NULL");
                            int file_sn = MyASL_DPU_Getfilesn_From_FileTransFinshedPack(p_rcv_data);
                            ESP_LOGI("MyASL_DPU_Task()", "find file_sn=%d\n", file_sn);
                            myasldpu_fileup_item_t* myasldpu_fileup_item = (my_asl_rcvdata_cache_t*)MyAslEvent_GetItem_Storage_ById(&my_asl_dpu_event_list, file_sn+MYASL_EVENT_FILEUPLOAD_ITEM_BASE);
                            if(myasldpu_fileup_item!=NULL)
                            {
                                if(myasldpu_fileup_item->fileuploadtransfinished_pack!=NULL)
                                {
                                    memcpy(myasldpu_fileup_item->fileuploadtransfinished_pack, p_rcv_data, data_len);
                                    myasldpu_fileup_item->pack_len = data_len;
                                }
                                else
                                {
                                    ESP_LOGE("MyASL_DPU_Task()", "myasldpu_fileup_item->fileuploadtransfinished_pack==NULL");
                                }
                                if(myasldpu_fileup_item->taskhandle!=NULL)
                                {
                                    ESP_LOGI("MyASL_DPU_Task()", "notify file trans finished to file_sn=%d\n", file_sn);
                                    xTaskNotify(myasldpu_fileup_item->taskhandle, 2, eSetValueWithOverwrite);
                                }
                            }
                            else
                            {
                                ESP_LOGE("MyASL_DPU_Task()", "myasldpu_fileup_item==NULL");
                            }
                        }
                    }
                    else if(data_type==datablock_tsf)
                    {
                        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                        printf("MyASL_DPU_Task():data_type==datablock_tsf\n");
                        #endif

                        my_asl_rcvdata_cache_t* my_asl_rcvdata_cache = (my_asl_rcvdata_cache_t*)MyAslEvent_GetItem_Storage_ById(&my_asl_dpu_event_list, datablock_tsf);
                        if(my_asl_rcvdata_cache!=NULL)
                        {
                            if(!my_asl_rcvdata_cache->valid)
                            {
                                if(my_asl_rcvdata_cache->data!=NULL)
                                {
                                    memcpy(my_asl_rcvdata_cache->data, p_rcv_data, data_len);
                                }
                                my_asl_rcvdata_cache->type = data_type;
                                my_asl_rcvdata_cache->len = data_len;
                                if(my_asl_rcvdata_cache->taskhandle!=NULL)
                                {
                                    xTaskNotify(my_asl_rcvdata_cache->taskhandle, 1, eSetValueWithOverwrite);
                                }
                                else
                                {
                                    ESP_LOGE("MyASL_DPU_Task()", "my_asl_rcvdata_cache->taskhandle==NULL");
                                }
                            }
                            else
                            {
                                ESP_LOGE("MyASL_DPU_Task()", "my_asl_rcvdata_cache->valid is True");
                            }
                            // MyAslEvent_Delete(&my_asl_dpu_event_list, data_type);
                        }
                        else
                        {
                            ESP_LOGE("MyASL_DPU_Task()", "my_asl_rcvdata_cache==NULL");
                        }
                    }
                    else
                    {
                        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                        printf("MyASL_DPU_Task():data_type== unknow query msg type = 0x%x\n", data_type);
                        #endif
                    }
                    //------------------------------------------------------------


                    //清空队列缓冲区
                    queue_rcv_item->valid = 0;

                    printf("\n\n");
                }
            }
            else
            {
                ESP_LOGE("MyASL_DPU_Task()", "queue_rcv_item!=NULL is false\n");
            }
        }
    }
}

uint8_t MyASL_DPU_Send(my_asl_data_type_t type, char* data, uint32_t len, uint32_t timeout, uint8_t id)
{
    int queue_send_ret = pdTRUE;
    int i = 0;
    int retry_count = 0;

    //获取信号量
    //...
    MyASL_DPU_Send_retry:
    for(i=0; i<MY_ASL_DPU_QUEUE_LEN; i++)
    {
        if(!my_asl_dpu_queue_array[i]->valid)
        {
            my_asl_dpu_queue_array[i]->id = id;
            my_asl_dpu_queue_array[i]->len = len;
            my_asl_dpu_queue_array[i]->valid = 1;
            //my_asl_dpu_queue_array[i]->data = data;
            my_asl_dpu_queue_array[i]->type = type;
            if(my_asl_dpu_queue_array[i]->data!=NULL)
            {
                memcpy(my_asl_dpu_queue_array[i]->data, data, len);
            }
            break;
        }
    }
    //释放信号量
    //...
    if(i>=MY_ASL_DPU_QUEUE_LEN&&retry_count<40)
    {
        vTaskDelay(timeout / portTICK_PERIOD_MS);
        retry_count++;
        ESP_LOGE("MyASL_DPU_Send()", "retry_count=%d\n", retry_count);
        goto MyASL_DPU_Send_retry;
    }
    else if(i<MY_ASL_DPU_QUEUE_LEN)
    {
        #if DEBUG_PRINT_LEVEL_my_asl_dpu_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
        printf("MyASL_Send():i=%d\n", i);
        #endif
        if(my_asl_dpu_queue!=NULL)
            queue_send_ret = xQueueSend(my_asl_dpu_queue, &my_asl_dpu_queue_array[i], timeout / portTICK_PERIOD_MS);
    }
    else
    {
        ESP_LOGE("MyASL_DPU_Send()", "error, i=%d\n", i);
        return 1;
    }

    return 0;
}

static int MyASL_DPU_Queue_Init(my_asl_dpu_queue_t** p, uint8_t data_area)
{
    if(data_area)
    {
        char* p_temp = (char*)calloc(MY_ASL_DPU_QUEUE_LEN, MY_ASL_PACK_SIZE_MAX+1);
        if(p_temp!=NULL)
        { 
            for(int i=0; i<MY_ASL_DPU_QUEUE_LEN; i++)
            {
                p[i] = (my_asl_dpu_queue_t*)calloc(1, sizeof(my_asl_dpu_queue_t));
                if(p[i]!=NULL)
                {
                    p[i]->data = p_temp+(i*(MY_ASL_PACK_SIZE_MAX+1));
                }
                else
                {
                    ESP_LOGE("MyASL_DPU_Queue_Init()", "error1\n");
                    return 1;
                }
            }
        }
        else
        {
            ESP_LOGE("MyASL_DPU_Queue_Init()", "error2\n");
            return 2;
        }
    }
    else
    {
        for(int i=0; i<MY_ASL_DPU_QUEUE_LEN; i++)
        {
            p[i] = (my_asl_dpu_queue_t*)calloc(1, sizeof(my_asl_dpu_queue_t));
            if(p[i]==NULL)
            {
                ESP_LOGE("MyASL_DPU_Queue_Init()", "error3, i=%d\n", i);
                return i;
            }
        }
    }
    return MY_ASL_DPU_QUEUE_LEN;
}

int MyASL_DPU_CtrlEvent_Send(int type, int value, int timeout)
{
    int queue_send_ret = pdTRUE;

    my_asl_dpu_ctrlevent_queue_t my_asl_dpu_ctrlevent;
    my_asl_dpu_ctrlevent.type = type;
    my_asl_dpu_ctrlevent.value = value;

    if(my_asl_dpu_ctrlevent_queue!=NULL)
    {
        queue_send_ret = xQueueSend(my_asl_dpu_ctrlevent_queue, &my_asl_dpu_ctrlevent, timeout / portTICK_PERIOD_MS);
        return 0;
    }

    return 1;
}

int MyASL_DPU_CtrlEvent_Rcv(my_asl_dpu_ctrlevent_queue_t* my_asl_dpu_ctrlevent, int timeout)
{
    int queue_rcv_ret = pdTRUE;

    if(my_asl_dpu_ctrlevent==NULL)
    {
        return -1;
    }
    queue_rcv_ret = xQueueReceive(my_asl_dpu_ctrlevent_queue, my_asl_dpu_ctrlevent, timeout);
    if(queue_rcv_ret==pdTRUE)
    {
        return 0;
    }
    return 1;
}

uint8_t MyASL_DPU_Init(void)
{
    MyASL_DPU_Queue_Init(my_asl_dpu_queue_array, 1);
    my_asl_dpu_queue = xQueueGenericCreate(MY_ASL_DPU_QUEUE_LEN, sizeof(my_asl_dpu_queue_t*), 1);
    if(my_asl_dpu_queue==NULL)ESP_LOGE("MyASL_Init()", "my_asl_dpu_queue=NULL\n");

    my_asl_dpu_ctrlevent_queue = xQueueGenericCreate(MY_ASL_DPU_CTRL_EVENT_QUEUE_LEN, sizeof(my_asl_dpu_ctrlevent_queue_t), 1);
    if(my_asl_dpu_ctrlevent_queue==NULL)ESP_LOGE("MyASL_Init()", "my_asl_dpu_ctrlevent_queue=NULL\n");

    MyAslEvent_Init(&my_asl_dpu_event_list);


    BaseType_t ret = pdPASS;
    #if MyASL_DPU_TASK_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    p_task_stack = calloc(1, MyASL_DPU_Task_task_stack_size*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
			if(xTaskCreateStaticPinnedToCore(MyASL_DPU_Task, "MyASL_DPU_Task", MyASL_DPU_Task_task_stack_size, NULL, MyASL_DPU_Task_priority, p_task_stack, p_task_data, MyASL_DPU_Task_COREID)!=p_task_data)
			{
				ESP_LOGE("MyASL_Init()", "creat MyASL_DPU_Task use xTaskCreateStaticPinnedToCore() error\n");
                return 1;
			}
        }else return 1;
    }else return 1;
	#elif MyASL_DPU_TASK_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
	ret = xTaskCreatePinnedToCore(MyASL_DPU_Task, "MyASL_DPU_Task", MyASL_DPU_Task_task_stack_size, NULL, MyASL_DPU_Task_priority, &MyASL_DPU_TaskHandle, MyASL_DPU_Task_COREID);
	if(ret!=pdPASS)
	{
		ESP_LOGE("MyASL_Init()", "creat MyASL_DPU_Task use xTaskCreatePinnedToCore() error=%d\n", ret);
        return 1;
	}
	#endif

    return 0;
}