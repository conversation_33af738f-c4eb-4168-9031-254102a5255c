# E104-BT53C3 双模组支持

## 概述

本项目现在支持同时使用两个E104-BT53C3蓝牙模组：
- **第一个模组**: 使用ESP32-S3硬件UART (UART_NUM_2)
- **第二个模组**: 使用软串口 (SW_UART_NUM_0)

这样可以在ESP32-S3硬件UART资源不足的情况下，通过软串口扩展更多的E104模组。

## 文件结构

```
main/my_network/ebyte/
├── e104_bt53c3.c                    # 第一个模组实现 (硬件UART)
├── e104_bt53c3.h                    # 第一个模组头文件
├── e104_bt53c3_sw.c                 # 第二个模组实现 (软串口)
├── e104_bt53c3_sw.h                 # 第二个模组头文件
├── e104_bt53c3_dual_example.c       # 双模组使用示例
├── e104_bt53c3_dual_example.h       # 双模组示例头文件
├── myesp_sw_uart.c                  # 软串口实现
├── myesp_sw_uart.h                  # 软串口头文件
└── README_E104_DUAL.md              # 本文档
```

## 硬件连接

### 第一个模组 (硬件UART)
| ESP32-S3引脚 | E104模组引脚 | 功能 |
|-------------|-------------|------|
| GPIO7       | RX          | UART TX |
| GPIO6       | TX          | UART RX |
| GPIO15      | RST         | 复位控制 |
| GPIO16      | MOD         | 模式切换 |

### 第二个模组 (软串口)
| ESP32-S3引脚 | E104模组引脚 | 功能 |
|-------------|-------------|------|
| GPIO4       | RX          | 软串口TX |
| GPIO5       | TX          | 软串口RX |
| GPIO17      | RST         | 复位控制 |
| GPIO18      | MOD         | 模式切换 |

## API接口对比

### 第一个模组 (硬件UART) vs 第二个模组 (软串口)

| 功能 | 第一个模组 | 第二个模组 |
|------|-----------|-----------|
| 初始化 | `e104_bt53c3_init()` | `e104_bt53c3_sw_init()` |
| 重新初始化 | `e104_bt53c3_reinit()` | `e104_bt53c3_sw_reinit()` |
| 复位 | `e104_bt53c3_rst()` | `e104_bt53c3_sw_rst()` |
| 设置角色 | `e104_bt53c3_set_dev_role()` | `e104_bt53c3_sw_set_dev_role()` |
| 查询角色 | `e104_bt53c3_query_dev_role()` | `e104_bt53c3_sw_query_dev_role()` |
| 设置广播名称 | `e104_bt53c3_set_adv_name()` | `e104_bt53c3_sw_set_adv_name()` |
| 设置广播间隔 | `e104_bt53c3_set_adv_interval()` | `e104_bt53c3_sw_set_adv_interval()` |
| 发送数据 | `e104_bt53c3_send_data_to_master()` | `e104_bt53c3_sw_send_data_to_master()` |

## 使用方法

### 1. 基本初始化

```c
#include "e104_bt53c3.h"
#include "e104_bt53c3_sw.h"

void app_main(void) {
    // 初始化第一个模组 (硬件UART)
    e104_bt53c3_init();
    
    // 等待一段时间避免冲突
    vTaskDelay(2000 / portTICK_PERIOD_MS);
    
    // 初始化第二个模组 (软串口)
    e104_bt53c3_sw_init(GPIO_NUM_4,  // TX引脚
                       GPIO_NUM_5,  // RX引脚  
                       GPIO_NUM_17, // RST引脚
                       GPIO_NUM_18); // MOD引脚
}
```

### 2. 发送数据

```c
// 向第一个模组发送数据
e104_bt53c3_send_data_to_master("Hello from module 1", 19);

// 向第二个模组发送数据  
e104_bt53c3_sw_send_data_to_master("Hello from module 2", 19);
```

### 3. 配置模组

```c
// 配置第一个模组
e104_bt53c3_set_dev_role(0);        // 设置为从机
e104_bt53c3_set_adv_interval(100);  // 设置广播间隔
e104_bt53c3_set_power(4);           // 设置发射功率

// 配置第二个模组
e104_bt53c3_sw_set_dev_role(0);        // 设置为从机
e104_bt53c3_sw_set_adv_interval(100);  // 设置广播间隔
e104_bt53c3_sw_set_power(4);           // 设置发射功率
```

### 4. 使用示例

```c
#include "e104_bt53c3_dual_example.h"

void app_main(void) {
    // 启动双模组示例
    app_main_e104_dual_example();
}
```

## 状态变量

### 第一个模组状态变量
- `e104_role` - 设备角色
- `e104_adv_interval` - 广播间隔
- `e104_adv_power` - 发射功率
- `e104_mac` - MAC地址
- `e104_bt53c3_init_ok` - 初始化状态

### 第二个模组状态变量
- `e104_sw_role` - 设备角色
- `e104_sw_adv_interval` - 广播间隔
- `e104_sw_adv_power` - 发射功率
- `e104_sw_mac` - MAC地址
- `e104_sw_bt53c3_init_ok` - 初始化状态

## 功能特性

### 共同特性
- ✅ AT命令控制
- ✅ 蓝牙连接管理
- ✅ 数据收发
- ✅ 继电器控制命令处理
- ✅ MAC地址获取
- ✅ 参数查询和设置

### 差异对比
| 特性 | 第一个模组 | 第二个模组 |
|------|-----------|-----------|
| 通信接口 | 硬件UART | 软串口 |
| 波特率支持 | 高达5Mbps | 建议≤115200 |
| CPU占用 | 极低 | 中等 |
| 时序精度 | 极高 | 高 |
| 资源占用 | UART外设 | GPIO+定时器 |

## 注意事项

1. **引脚冲突**: 确保两个模组使用的GPIO引脚不冲突
2. **初始化顺序**: 建议先初始化硬件UART模组，再初始化软串口模组
3. **波特率限制**: 软串口建议使用115200波特率以保证稳定性
4. **CPU资源**: 软串口会占用一定的CPU资源，注意系统负载
5. **调试输出**: 两个模组的日志会有不同的TAG标识

## 故障排除

### 常见问题

1. **软串口通信异常**
   - 检查引脚连接是否正确
   - 确认波特率设置为115200
   - 检查GPIO是否被其他功能占用

2. **两个模组冲突**
   - 确保初始化时间间隔足够
   - 检查引脚配置是否冲突
   - 确认两个模组使用不同的广播名称

3. **内存不足**
   - 两个模组会占用更多内存
   - 适当调整任务堆栈大小
   - 监控系统内存使用情况

### 调试方法

1. **启用调试日志**
   ```c
   esp_log_level_set("E104_BT53C3", ESP_LOG_DEBUG);
   esp_log_level_set("E104_BT53C3_SW", ESP_LOG_DEBUG);
   esp_log_level_set("SW_UART", ESP_LOG_DEBUG);
   ```

2. **监控状态变量**
   - 检查初始化状态标志
   - 监控MAC地址获取状态
   - 观察AT命令响应

3. **测试单个模组**
   - 先单独测试硬件UART模组
   - 再单独测试软串口模组
   - 最后测试双模组同时工作

## 性能优化

1. **软串口优化**
   - 适当调整接收任务延时
   - 优化GPIO中断处理
   - 合理设置定时器精度

2. **内存优化**
   - 及时释放不用的内存
   - 优化缓冲区大小
   - 使用静态内存分配

3. **任务调度优化**
   - 合理设置任务优先级
   - 避免长时间阻塞
   - 使用适当的延时
