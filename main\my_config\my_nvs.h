#ifndef MY_NVS_H
#define MY_NVS_H

#include "my_nvs_ds.h"

//旧的函数，静态存储
int SaveDataToNvs(char* formname, char* keyname, char* data, int len, int reboot);
int LoadDataFromNvs(char* formname, char* keyname, char* data, int len);

//新的函数，动态存储
int Save_KVP_To_Nvs_FromListItem(my_nvs_d2link_list_t* p_list, int id);
int Save_KVP_List_To_Nvs(my_nvs_d2link_list_t* p_list);
int Load_KVP_List_FromNvs(my_nvs_d2link_list_t* p_list);


int My_nvs_flash_init(void);
void MyNvs_Print_Statistic(void);
int My_nvs_flash_erase(void);

#endif