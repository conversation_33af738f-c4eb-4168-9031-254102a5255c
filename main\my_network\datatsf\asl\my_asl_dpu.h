#ifndef MY_ASL_DPU_H
#define MY_ASL_DPU_H

#include <lwip/netdb.h>

#include "my_asl.h"
#include "my_ds.h"




#define MyASL_DPU_TASK_CREATE_TYPE    TASK_CREATE_TYPE_DYNAMIC

#if MyASL_DPU_TASK_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
#define MyASL_DPU_Task_COREID    1
#define MyASL_DPU_Task_priority  5
#define MyASL_DPU_Task_task_stack_size   4096
#elif MyASL_DPU_TASK_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
#define MyASL_DPU_Task_COREID    1
#define MyASL_DPU_Task_priority  19
#define MyASL_DPU_Task_task_stack_size   3072
#endif




typedef struct
{
    char device_sn[20];         //设备唯一编号，对外
    char device_model[40];      //设备型号
    uint16_t device_type;       //设备类型
    char device_id[20];         //设备id

    char password[40];          //注册密码

    uint8_t register_state;     //注册状态
    uint8_t login_state;        //登陆状态
    uint8_t login_enable;       //是否需要登录

    uint32_t dic;               //设备识别码
    uint32_t token;             //设备访问授权码

    time_t last_time_sync_time; //最后一次对时时间

}my_asl_dpu_iotboxinfo_t;

int My_ASLdpu_IotBoxInfo_Init(void);
int My_ASLdpu_IotBoxInfo_DebugPrint(void);
int My_ASLdpu_DevRegister(void);
int My_ASLdpu_DevLogin(void);
int My_ASLdpu_DevTimeSync(void);
int My_ASLdpu_DevParamReq_Partial(my_d2link_list_t* request_paramid_list);
void My_ASLdpu_DevParamReq_All(void);
int My_ASLdpu_DevParamQuery_DebugTrigger(void);
void My_ASLdpu_DevParamReport_All(void);
int My_ASLdpu_DevParamReport_Partial(my_d2link_list_t* report_paramid_list);
int My_ASLdpu_DevEventReport_Partial(my_d2link_list_t* report_paramid_list);
int My_ASLdpu_DevMeasurementReport(int report_type, int report_data_type, char* param_data, int data_len);
int My_ASLdpu_DevParamUpdate_DebugTrigger(void);
int My_ASLdpu_DevCtrlParamUpdate_DebugTrigger(void);
int My_ASLdpu_DevFileUpload_Image(char* data, int data_len, \
                                    uint16_t image_width, uint16_t image_height, \
                                    int timeout, int repeat_count);
int My_ASLdpu_DevFileDownload_OTA(void** file_saving, int* file_len, int timeout, int repeat_count);

uint32_t MyASL_Get_LastTimeSyncTime(void);


#define MY_ASL_DPU_QUEUE_LEN    4
typedef struct
{
    my_asl_data_type_t type; 
    uint8_t id;
	uint8_t valid;
    uint32_t len;
	char* data;         //应用层数据
}my_asl_dpu_queue_t;

#define MY_ASL_DPU_CTRL_EVENT_QUEUE_LEN    16
typedef struct
{
    int type;
    int value;
}my_asl_dpu_ctrlevent_queue_t;

uint8_t MyASL_DPU_Send(my_asl_data_type_t type, char* data, uint32_t len, uint32_t timeout, uint8_t id);
uint8_t MyASL_DPU_Init(void);

int MyASL_DPU_CtrlEvent_Rcv(my_asl_dpu_ctrlevent_queue_t* my_asl_dpu_ctrlevent, int timeout);

#endif