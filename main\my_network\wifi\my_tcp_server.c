#include "my_tcp_server.h"

#if TCP_SERVER_FUNC

#include <string.h>
#include <sys/param.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_system.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "esp_netif.h"
//#include "protocol_examples_common.h"

#include "lwip/err.h"
#include "lwip/sockets.h"
#include "lwip/sys.h"
#include <lwip/netdb.h>

//--------------------------------------------------
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/timers.h"
#include "esp_system.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_log.h"
#include "nvs_flash.h"

#include "lwip/err.h"
#include "lwip/sys.h"

#include "tcp_data_struct.h"
#include "my_nvs.h"
#include "my_tcp_client.h"
#include "mywifi.h"
#include "simple_http_ota.h"
#include "my_ble.h"
#include "my_camera.h"
#include "my_config.h"


#define SOFTWARE_VERSION    "V1"


//-------------------------------------
void SendDataToClient(uint32_t id, const char* data, uint32_t len);


//服务器通信数据结构

/* typedef struct
{
    uint32_t len;
    uint32_t id;
    char data[SERVER_DATA_SIZE];
}serverdata_t;//若改动了结构体，需要同时更新 network.c中的 serverdata_headersize */

//---------------------------------------
static const char *TAG = "tcp_server";

typedef struct 
{
    xTaskHandle rcv_task_handle;
    xTaskHandle send
};

TimerHandle_t   SockTimer_Handle;
SemaphoreHandle_t ServerSendMutexSemaphore = NULL;


#define PORT 6789

int listen_sock = 0;
int sock = 0;
int client_online = 1;

serverdata_t server_send_buf;
void SendDataToClient(uint32_t id, const char* data, uint32_t len)
{
    xSemaphoreTake(ServerSendMutexSemaphore, portMAX_DELAY);
    int err = 0;
    int send_len = 0;
    uint32_t sec_num = 0;
    uint32_t data_len = 0;
    uint32_t _len = len;

        memset(&server_send_buf, 0, sizeof(serverdata_t));

        server_send_buf.len = SERVER_DATA_HEADER_SIZE;
        server_send_buf.id = id;
        if(data!=NULL)
        {
            memcpy(server_send_buf.data, data, len);
            server_send_buf.len = SERVER_DATA_HEADER_SIZE+len;
        }

        err = send(sock, &server_send_buf, server_send_buf.len, 0);

    xSemaphoreGive(ServerSendMutexSemaphore);
}

char wificonfig_wifissid[32]={0};
char wificonfig_wifipassword[64]={0};
char apconfig_apssid[32]={0};
char apconfig_appassword[64]={0};
serverdata_t* server_rcv_buf = NULL;
extern my_wifi_info_t my_wifi_info;
char client_addr[32]={0};

char temp[1024];
static void do_retransmit(const int sock)
{
    int len;
    char rx_buffer[128];

    do {
        len = recv(sock, server_rcv_buf, sizeof(serverdata_t) - 1, 0);
        printf("rcv len = %d\n", len);
        if (len < 0) {
            ESP_LOGE(TAG, "Error occurred during receiving: errno %d", errno);
        } else if (len == 0) {
            ESP_LOGW(TAG, "Connection closed");
        } else {
            client_online = 1;

            printf("rcv:len=%d, id=%d\n", server_rcv_buf->len, server_rcv_buf->id);
            // send() can return less bytes than supplied length.
            // Walk-around for robust implementation. 

            if(server_rcv_buf->id==1)
            {
                SendDataToClient(1, NULL, 0);
                printf("ok101\n");
            }
            if(server_rcv_buf->id==101)
            {
                printf("rcv from client:%s\n", server_rcv_buf->data);
                SendDataToClient(101, "hello, this is server", strlen("hello, this is server"));
                printf("ok102\n");
            }

            char* p_rcv = (char*)server_rcv_buf;

            *(p_rcv+len) = 0;
            printf("rcv string(%d):\n%s\n", len, p_rcv);


            memset(server_rcv_buf, 0, len);
        }
    } while (len > 0);
}

void SockTimer(xTimerHandle xTimer)
{
    if(!client_online)
    {
        if(sock>0)
        {
            shutdown(sock, 0);
            close(sock);

            sock = 0;

            xTimerStop(SockTimer_Handle,portMAX_DELAY);
        }
    }
    client_online = 0;
}

static void tcp_server_task(void *pvParameters)
{
    char addr_str[128];
    int addr_family = (int)pvParameters;
    int ip_protocol = 0;
    struct sockaddr_in6 dest_addr;

    if(server_rcv_buf==NULL)
    {
        server_rcv_buf = (serverdata_t*)calloc(1, sizeof( serverdata_t ));
    }

    if (addr_family == AF_INET) {
        struct sockaddr_in *dest_addr_ip4 = (struct sockaddr_in *)&dest_addr;
        dest_addr_ip4->sin_addr.s_addr = htonl(INADDR_ANY);
        dest_addr_ip4->sin_family = AF_INET;
        dest_addr_ip4->sin_port = htons(PORT);
        ip_protocol = IPPROTO_IP;
    } else if (addr_family == AF_INET6) {
        bzero(&dest_addr.sin6_addr.un, sizeof(dest_addr.sin6_addr.un));
        dest_addr.sin6_family = AF_INET6;
        dest_addr.sin6_port = htons(PORT);
        ip_protocol = IPPROTO_IPV6;
    }

    listen_sock = socket(addr_family, SOCK_STREAM, ip_protocol);
    if (listen_sock < 0) {
        ESP_LOGE(TAG, "Unable to create socket: errno %d", errno);
        vTaskDelete(NULL);
        return;
    }
#if defined(CONFIG_EXAMPLE_IPV4) && defined(CONFIG_EXAMPLE_IPV6)
    // Note that by default IPV6 binds to both protocols, it is must be disabled
    // if both protocols used at the same time (used in CI)
    int opt = 1;
    setsockopt(listen_sock, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
    setsockopt(listen_sock, IPPROTO_IPV6, IPV6_V6ONLY, &opt, sizeof(opt));
#endif

    ESP_LOGI(TAG, "Socket created");

    int err = bind(listen_sock, (struct sockaddr *)&dest_addr, sizeof(dest_addr));
    if (err != 0) {
        ESP_LOGE(TAG, "Socket unable to bind: errno %d", errno);
        ESP_LOGE(TAG, "IPPROTO: %d", addr_family);
        goto CLEAN_UP;
    }
    ESP_LOGI(TAG, "Socket bound, port %d", PORT);

    err = listen(listen_sock, 1);
    if (err != 0) {
        ESP_LOGE(TAG, "Error occurred during listen: errno %d", errno);
        goto CLEAN_UP;
    }

    while (1) {

        ESP_LOGI(TAG, "Socket listening");

        struct sockaddr_in6 source_addr; // Large enough for both IPv4 or IPv6
        uint addr_len = sizeof(source_addr);
        sock = accept(listen_sock, (struct sockaddr *)&source_addr, &addr_len);
        if (sock < 0) {
            ESP_LOGE(TAG, "Unable to accept connection: errno %d", errno);
            break;
        }
        xTimerStart(SockTimer_Handle, portMAX_DELAY);

        // Convert ip address to string
        if (source_addr.sin6_family == PF_INET) {
            inet_ntoa_r(((struct sockaddr_in *)&source_addr)->sin_addr.s_addr, addr_str, sizeof(addr_str) - 1);
        } else if (source_addr.sin6_family == PF_INET6) {
            inet6_ntoa_r(source_addr.sin6_addr, addr_str, sizeof(addr_str) - 1);
        }
        ESP_LOGI(TAG, "Socket accepted ip address: %s", addr_str);

        do_retransmit(sock);

        if(sock>0)
        {
            shutdown(sock, 0);
            close(sock);
        }
    }

CLEAN_UP:
    close(listen_sock);
    vTaskDelete(NULL);
}

void tcp_server_init(void)
{
    SockTimer_Handle = xTimerCreate("SockTimer", 40000/portTICK_PERIOD_MS, pdTRUE, NULL, SockTimer);
    xTimerStop(SockTimer_Handle, portMAX_DELAY);

    ServerSendMutexSemaphore=xSemaphoreCreateMutex();

    xTaskCreate(tcp_server_task, "tcp_server", 8192, (void*)AF_INET, 5, NULL);
}

#endif