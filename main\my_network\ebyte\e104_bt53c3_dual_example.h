#ifndef E104_BT53C3_DUAL_EXAMPLE_H
#define E104_BT53C3_DUAL_EXAMPLE_H

#include "driver/gpio.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化双E104模组
 */
void e104_dual_init(void);

/**
 * @brief 配置双E104模组
 */
void e104_dual_config(void);

/**
 * @brief 发送测试数据到两个模组
 */
void e104_dual_send_test_data(void);

/**
 * @brief 查询双E104模组状态
 */
void e104_dual_query_status(void);

/**
 * @brief 双E104模组测试任务
 */
void e104_dual_test_task(void *pvParameters);

/**
 * @brief 启动双E104模组测试
 */
void e104_dual_start_test(void);

/**
 * @brief 在main函数中调用此函数来启动双E104模组
 */
void app_main_e104_dual_example(void);

#ifdef __cplusplus
}
#endif

#endif // E104_BT53C3_DUAL_EXAMPLE_H
