#ifndef MY_ASL_EVENT_H
#define MY_ASL_EVENT_H



//服务器命令解析-解析函数表
struct my_asl_event_list_item
{
    int id;
    char item_name[32];
    void* data;
    struct my_asl_event_list_item* next_item;
    struct my_asl_event_list_item* pri_item;
};
typedef struct my_asl_event_list_item my_asl_event_list_item_t;

typedef struct
{
    int list_item_num;
    my_asl_event_list_item_t* first_item;
    my_asl_event_list_item_t* end_item;
}my_asl_event_list_t;

int MyAslEvent_Register(my_asl_event_list_t* p_list, int id, void* data, char* service_name);
int MyAslEvent_Delete(my_asl_event_list_t* p_list, int id);
int MyAslEvent_Init(my_asl_event_list_t* p_list);
int MyAslEvent_PrintList(my_asl_event_list_t* p_list);
void* MyAslEvent_GetItem_Storage_ById(my_asl_event_list_t* p_list, int id);

#endif