#include "test_my_aes.h"

#include "my_soft_aes.h"
#include "my_esp_aes.h"

#include <esp_log.h>


char test_aes_in[16] = {"0123456789abcdef"};

void Test_My_Aes(void)
{
    printf("\n\nTest_My_Aes()\n\n");

    mbedtls_aes_context aes_ctx;
    //密钥数值
    unsigned char key[16] = {'e', 'c', 'b', 'p', 'a', 's', 's', 'w', 'o', 'r', 'd', '1', '2', '3', '4'};
    // //明文空间
    // unsigned char plain[16] = "test1234";
    // //解密后明文的空间
    // unsigned char dec_plain[16] = {0};
    // //密文空间
    // unsigned char cipher[16] = {0};

	#define test_aes_in_size (1*1024)
	char* aes_data = calloc(1, test_aes_in_size+1);
	for(int i=0; i<test_aes_in_size/16; i++)
	{
		memcpy(aes_data+i*16, test_aes_in, 16);
	}


    My_EspAes_Init(&aes_ctx, key, sizeof(key)*8);

	printf("aes_data: \n%s\n", aes_data);
    
	time_t esp_aes_encode_start_time = esp_timer_get_time();
	My_EspAes_Cipher(&aes_ctx, (void*)aes_data, test_aes_in_size, (void*)aes_data, test_aes_in_size);
	time_t esp_aes_encode_end_time = esp_timer_get_time();
	printf("\nesp_aes_encode(%d bytes) time used = %ldus(%ldms)\n", \
	test_aes_in_size, \
	esp_aes_encode_end_time-esp_aes_encode_start_time, (esp_aes_encode_end_time-esp_aes_encode_start_time)/1000);

	printf("aes_data: \n");
    for(int loop = 0; loop < test_aes_in_size; loop++)
    {
        printf("%02X ", aes_data[loop]);
    }
    printf("\n");


    mbedtls_aes_setkey_dec(&aes_ctx, key, 128);
    
	time_t esp_aes_decode_start_time = esp_timer_get_time();
	My_EspAes_InvCipher(&aes_ctx, (void*)aes_data, test_aes_in_size, (void*)aes_data, test_aes_in_size);
	time_t esp_aes_decode_end_time = esp_timer_get_time();
	printf("\nesp_aes_encode(%d bytes) time used = %ldus(%ldms)\n", \
	test_aes_in_size, \
	esp_aes_decode_end_time-esp_aes_decode_start_time, (esp_aes_decode_end_time-esp_aes_decode_start_time)/1000);
    printf("aes_data: \n%s\n", aes_data);

    mbedtls_aes_free(&aes_ctx);
    

	printf("\n\n--\n\n");
	MyAes_Init(key, sizeof(key));
    printf("aes_data: \n%s\n", aes_data);
	time_t my_aes_encode_start_time = esp_timer_get_time();
    MyAes_Cipher((void*)aes_data, test_aes_in_size, (void*)aes_data, test_aes_in_size);
	time_t my_aes_encode_end_time = esp_timer_get_time();
	printf("\nesp_aes_encode(%d bytes) time used = %ldus(%ldms)\n", \
	test_aes_in_size, \
	my_aes_encode_end_time-my_aes_encode_start_time, (my_aes_encode_end_time-my_aes_encode_start_time)/1000);
	printf("aes_data: \n");
    for(int loop = 0; loop < test_aes_in_size; loop++)
    {
        printf("%02X ", aes_data[loop]);
    }
	printf("\n");
	time_t my_aes_decode_start_time = esp_timer_get_time();
	MyAes_InvCipher((void*)aes_data, test_aes_in_size, (void*)aes_data, test_aes_in_size);
	time_t my_aes_decode_end_time = esp_timer_get_time();
	printf("\nesp_aes_encode(%d bytes) time used = %ldus(%ldms)\n", \
	test_aes_in_size, \
	my_aes_decode_end_time-my_aes_decode_start_time, (my_aes_decode_end_time-my_aes_decode_start_time)/1000);
	printf("aes_data: \n%s\n", aes_data);

    printf("\n\n End of Test_My_Aes()\n\n");
}