#include "my_tsfc.h"

#if MY_TSFC_FUNC

#include "my_debug.h"
#include "esp_log.h"
#include "my_whgm5.h"
#include "my_kmp.h"
#include "checksum.h"
#include "my_esp_aes.h"
#include "my_endian.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"
#include "freertos/semphr.h"

#if defined MY_DEBUG_PRINT_FUNC
#define DEBUG_PRINT_LEVEL_my_tsfc_PRINT   MY_DEBUG_PRINT_LEVEL_0
#else
#define DEBUG_PRINT_LEVEL_my_tsfc_PRINT   DEBUG_PRINT_LEVEL_0
#endif

QueueHandle_t my_tsfc_send_queue = NULL;
QueueHandle_t my_tsfc_rcv_queue = NULL;
my_tsfc_queue_t* my_tsfc_send_queue_array[MY_TSFC_QUEUE_LEN];
my_tsfc_queue_t* my_tsfc_rcv_queue_array[MY_TSFC_QUEUE_LEN];
my_tsfc_windows_t* my_tsfc_send_windows_array[MY_TSFC_WINDOWS_SIZE];
my_tsfc_windows_t* my_tsfc_rcv_windows_array[MY_TSFC_WINDOWS_SIZE];
static uint8_t my_tsfc_send_windows_frame_counter = 0;
static uint8_t my_tsfc_rcv_windows_frame_counter = 0;
static uint8_t my_tsfc_windows_enough = 0;
#define MyTSFC_Rcv_Task_rcvbuf_field_size (8)

my_tsfc_info_t my_tsfc_info;

my_tsfc_statistic_t my_tsfc_statistic;

#define MY_TSFC_DEBUG_CHECK_PD          1
#define MY_TSFC_DEBUG_CHECK_APPID       1
#define MY_TSFC_DEBUG_CHECK_CRC         1
#define MY_TSFC_DEBUG_CHECK_AES         0

#define frame_ack_time_out_time (10000)

#define TSFC_WORK_STATE_PENDING_WAIT_TIME   (30)

SemaphoreHandle_t my_tsfc_send_windowsMutexSemaphore = NULL;
uint8_t MyTSFC_Packager_To_Windows(my_tsfc_windows_t** windows_array, my_tsfc_queue_t* queue_data)
{
    int ret = 0;
    if(windows_array==NULL)
    {
        ESP_LOGE("MyTSFC_Packager_To_Windows()", "error: windows_array==NULL\n");
        return -1;
    }

    my_tsfc_windows_t* idle_fp = NULL;

    xSemaphoreTake(my_tsfc_send_windowsMutexSemaphore, portMAX_DELAY);
    //寻找空闲窗口位
    int i=0;
    for(i=0; i<MY_TSFC_WINDOWS_SIZE; i++)
    {
        if(windows_array[i]!=NULL)
        {
            if(windows_array[i]->state==tsf_win_fps_idle)
            {
                idle_fp = windows_array[i];
                break;
            }
        }
        else
        {
            ESP_LOGE("MyTSFC_Packager_To_Windows()", "error: windows_array[%d]!=NULL is false\n", i);
        }
    }
    if(i>=MY_TSFC_WINDOWS_SIZE)
    {
        ESP_LOGW("MyTSFC_Packager_To_Windows()", "error: i>=MY_TSFC_WINDOWS_SIZE\n");
        ret = 1;
        goto MyTSFC_Packager_To_Windows_return;
    }
    else
    {
        // ESP_LOGI("MyTSFC_Packager_To_Windows()", "insert ok i=%d", i);
    }

    if(idle_fp!=NULL)
    {
        if(queue_data->len <= TSFC_FRAME_DATA_SIZE_MAX)
        {
            //加入头部
            idle_fp->data->head.type = queue_data->my_tsfc_data_type;
            idle_fp->data->head.pd = MY_TSFC_PD;

            idle_fp->data->sn = my_tsfc_send_windows_frame_counter++;
            
            idle_fp->data->app_id = my_tsfc_info.app_id;
            idle_fp->data->data_len = queue_data->len;

            //加入数据部分
            memcpy(idle_fp->data->data, queue_data->data, queue_data->len);

            //补齐，16字节对齐，用于AES加密
            if(idle_fp->data->data_len%16)
            {
                memset(idle_fp->data->data+idle_fp->data->data_len, 0, 16-(idle_fp->data->data_len%16));
                idle_fp->data->data_len+=(16-idle_fp->data->data_len%16);
            }

            //加密
            #if MY_TSFC_DEBUG_CHECK_AES
            if(My_EspAes_Cipher(my_tsfc_info.aes_ctx, (void*)idle_fp->data->data, idle_fp->data->data_len, (void*)idle_fp->data->data, idle_fp->data->data_len))
            {
                ESP_LOGE("MyTSFC_Packager_To_Windows()", "error: MyAes_Cipher()\n");
                ret = 2;
                goto MyTSFC_Packager_To_Windows_return;
            }
            #endif

            idle_fp->data->app_id = my_endian_conversion_16(idle_fp->data->app_id);
            uint16_t idle_fp_data_data_len = idle_fp->data->data_len;
            idle_fp->data->data_len = my_endian_conversion_16(idle_fp->data->data_len);
            
            //计算CRC
            uint32_t* p_crc = (uint32_t*)(idle_fp->data->data+idle_fp_data_data_len);
            *p_crc = my_endian_conversion_32(crc_32((void*)idle_fp->data, idle_fp_data_data_len+8));
            uint32_t crc_ = *((uint32_t*)p_crc);
            //加入TAIL
            memcpy(p_crc+1, MY_TSF_DATAFRAME_TAIL, strlen(MY_TSF_DATAFRAME_TAIL));


            //设置状态
            idle_fp->frame_type = queue_data->my_tsfc_data_type;
            idle_fp->data_len = idle_fp_data_data_len+16;
            idle_fp->timeout_time = frame_ack_time_out_time;//ms
            idle_fp->frame_position = i;
            idle_fp->id = queue_data->id;
            
            idle_fp->frame_sn = idle_fp->data->sn;
            // my_tsfc_send_windows_frame_counter++;
            if(my_tsfc_send_windows_frame_counter%MY_TSFC_WINDOWS_SIZE==0)
            {
                // ESP_LOGI("MyTSFC_Packager()", "my_tsfc_windows_enough = 1");
                my_tsfc_windows_enough = 1;
            }

            #if DEBUG_PRINT_LEVEL_my_tsfc_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
            printf("\nMyTSFC_Packager_To_Windows():send_data_pack(%d)=\n", idle_fp->data_len);
            #endif

            #if DEBUG_PRINT_LEVEL_my_tsfc_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
            char* in_data_byte = (char*)idle_fp->data;

            if(idle_fp->data_len<=512)
            {
                for(int i=0; i<idle_fp->data_len; i++)
                {
                    printf("%02X ", *(in_data_byte+i));
                }
                printf("\n$$$$\n");
            }
            else
            {
                printf("idle_fp->data_len > 512, omit...\n");
            }
            #endif
            
            //end of debug print

            //发送到驱动传输层
            int err = MyWHGM5_TcpSendData(idle_fp->data, idle_fp->data_len, 1000, idle_fp->id);
            // ESP_LOGI("MyTSFC_Packager_To_Windows()", "err=%d", err);
            if(!err)
            {
                idle_fp->state = tsf_win_fps_send_noack;
                ESP_LOGI("MyTSFC_Packager_To_Windows()", "sn=%d, crc=0x%08x, frame_len=%d", \
                my_tsfc_send_windows_array[i]->frame_sn, crc_, idle_fp->data_len);
                idle_fp->start_timestamp = esp_timer_get_time()/1000;

                my_tsfc_statistic.up.frame_count_total++;
                my_tsfc_statistic.up.data_count_total+=idle_fp->data_len;
                if(queue_data->my_tsfc_data_type==msg_frame)
                {
                    my_tsfc_statistic.up.frame_count_type_msg++;
                    my_tsfc_statistic.up.frame_count_type_msg_valid++;
                }
                else if(queue_data->my_tsfc_data_type==data_frame)
                {
                    my_tsfc_statistic.up.frame_count_type_data++;
                    my_tsfc_statistic.up.frame_count_type_data_valid++;
                }
                else if(queue_data->my_tsfc_data_type==ack_frame)
                {
                    my_tsfc_statistic.up.frame_count_type_ack++;
                }
                else if(queue_data->my_tsfc_data_type==cd_frame)
                {
                    my_tsfc_statistic.up.frame_count_type_cc++;
                }

                ret = 0;
                goto MyTSFC_Packager_To_Windows_return;
            }
            else
            {
                ESP_LOGE("MyTSFC_Packager_To_Windows()", "MyWHGM5_TcpSendData() timeout");
            }
        }
        else
        {
            ESP_LOGE("MyTSFC_Packager_To_Windows()", "error: queue_data->len=%d\n", queue_data->len);
        }
    }
    else
    {
        ESP_LOGE("MyTSFC_Packager_To_Windows()", "idle_fp!=NULL is false\n");
        ret = 3;
        goto MyTSFC_Packager_To_Windows_return;
    }
    
    MyTSFC_Packager_To_Windows_return:
    xSemaphoreGive(my_tsfc_send_windowsMutexSemaphore);
    return ret;
}

TaskHandle_t MyTSFC_Send_TaskHandle = NULL;
void MyTSFC_Send_Task(void* param)
{
    int queue_rcv_ret = pdFALSE;
    my_tsfc_queue_t* queue_rcv_item = NULL;
    uint32_t data_packager_count = 0;


    for(;;)
    {
        //队列接收
        queue_rcv_ret = xQueueReceive(my_tsfc_send_queue, &queue_rcv_item, portMAX_DELAY);
        if(queue_rcv_ret==pdTRUE)
        {
            if(queue_rcv_item!=NULL)
            {
                if(queue_rcv_item->valid)
                {
                    if(queue_rcv_item->data!=NULL)
                    {
                        if(my_tsfc_info.tsfc_work_state == TSFC_WORK_STATE_OK)
                        {
                            //打包到窗口区
                            while(MyTSFC_Packager_To_Windows(my_tsfc_send_windows_array, queue_rcv_item))
                            {
                                ESP_LOGI("MyTSFC_Send_Task()", "MyTSFC_Packager_To_Windows() pack...");
                                vTaskDelay(500 / portTICK_PERIOD_MS);
                            }
                        }
                    }
                    //打包完成后立即清除副本队列项
                    queue_rcv_item->valid = 0;
                }
            }
            else
            {
                ESP_LOGE("MyTSFC_Send_Task()", "queue_rcv_item!=NULL is false\n");
            }
        }
    }
}

uint8_t MyTSFC_Send(my_tsf_frametype_t type, uint8_t sn, char* data, uint32_t len, uint32_t timeout, uint8_t id)
{
    if(my_tsfc_info.tsfc_work_state == TSFC_WORK_STATE_OK)
    {
        int queue_send_ret = pdTRUE;
        int i = 0;
        int retry_count = 0;

        //获取信号量
        //...
        MyTSFC_Send_retry:
        for(i=0; i<MY_TSFC_QUEUE_LEN; i++)
        {
            if(!my_tsfc_send_queue_array[i]->valid)
            {
                my_tsfc_send_queue_array[i]->id = id;
                my_tsfc_send_queue_array[i]->len = len;
                my_tsfc_send_queue_array[i]->valid = 1;
                //my_tsfc_send_queue_array[i]->data = data;
                if(my_tsfc_send_queue_array[i]->data!=NULL)
                {
                    memcpy(my_tsfc_send_queue_array[i]->data, data, len);
                    my_tsfc_send_queue_array[i]->data[len]=0;
                }
                my_tsfc_send_queue_array[i]->my_tsfc_data_type = type;
                my_tsfc_send_queue_array[i]->sn = sn;
                break;
            }
        }
        //释放信号量
        //...
        if(i>=MY_TSFC_QUEUE_LEN&&retry_count<40)
        {
            vTaskDelay(timeout / portTICK_PERIOD_MS);
            retry_count++;
            ESP_LOGW("MyTSFC_Send()", "retry_count=%d\n", retry_count);
            goto MyTSFC_Send_retry;
        }
        else if(i<MY_TSFC_QUEUE_LEN)
        {
            if(my_tsfc_send_queue!=NULL)
                queue_send_ret = xQueueSend(my_tsfc_send_queue, &my_tsfc_send_queue_array[i], timeout / portTICK_PERIOD_MS);
        }
        else
        {
            ESP_LOGE("MyTSFC_Send()", "error, i=%d\n", i);
            return 1;
        }
        return 0;
    }

    return 101;
}

uint8_t MyTSFC_Ack(uint8_t frame_sn, my_tsf_ackcode_t ack_code, uint32_t timeout, uint8_t direct_ack, uint8_t id)
{
    my_tsf_respframe_t respframe;
    respframe.head.type = ack_frame;
    respframe.head.pd = MY_TSFC_PD;
    respframe.ack_sn = frame_sn;
    respframe.ack_code = ack_code;
    respframe.crc = 0x12345678;
    memcpy(&respframe.tail, MY_TSF_DATAFRAME_TAIL, strlen(MY_TSF_DATAFRAME_TAIL));

    char* p_resp = &respframe;

    direct_ack = 1;

    respframe.crc = my_endian_conversion_32(crc_32((void*)(&respframe), 4));

    if(direct_ack)
    {
        my_tsfc_statistic.up.frame_count_type_ack++;
        if(!MyWHGM5_TcpSendData(&respframe, sizeof(respframe), timeout, id))
        {
            ESP_LOGI("MyTSFC_Ack()", "direct ack, frame_sn=%d, ack_code=0x%x\n", frame_sn, ack_code);
            return 0;
        }
    }
    else
    {
        if(!MyTSFC_Send(ack_frame, frame_sn, &respframe, sizeof(respframe), timeout, id))
        {
            ESP_LOGI("MyTSFC_Ack()", "windows ack, frame_sn=%d, ack_code=0x%x\n", frame_sn, ack_code);
            return 0;
        }
    }
    return 1;
}

uint8_t MyTSFC_ClearWindowAfterAck(uint8_t frame_sn)
{
    int i = 0;
    xSemaphoreTake(my_tsfc_send_windowsMutexSemaphore, portMAX_DELAY);
    //--------------------------------------------------跳动窗口
    for(i=0; i<MY_TSFC_WINDOWS_SIZE; i++)
    {
        /* ESP_LOGI("MyTSFC_Rcv_Task()", "%d:%d, %d:%d\n", \
        frame_sn, my_tsfc_send_windows_array[i]->frame_sn, my_tsfc_send_windows_array[i]->state, tsf_win_fps_send_noack); */
        if((frame_sn==my_tsfc_send_windows_array[i]->frame_sn)&&(my_tsfc_send_windows_array[i]->state==tsf_win_fps_send_noack))
        {
            // ESP_LOGI("MyTSFC_Rcv_Task()", "my_tsfc_send_windows_array[%d]->state=tsf_win_fps_idle\n", i);
            my_tsfc_send_windows_array[i]->state = tsf_win_fps_send_andack;
            my_tsfc_send_windows_array[i]->repeat_count = 0;
            break;
        }
    }
    if(i>=MY_TSFC_WINDOWS_SIZE)
    {
        ESP_LOGW("MyTSFC_ClearWindowAfterAck()", "ack_frame:invalid frame\n");
    }

    if(my_tsfc_windows_enough)
    {
        // ESP_LOGI("MyTSFC_ClearWindowAfterAck()", "my_tsfc_windows_enough\n");
        for(i=0; i<MY_TSFC_WINDOWS_SIZE; i++)
        {
            if(my_tsfc_send_windows_array[i]->state!=tsf_win_fps_send_andack)
            {
                // ESP_LOGI("MyTSFC_ClearWindowAfterAck()", "my_tsfc_send_windows_array[%d]->state!=tsf_win_fps_send_andack\n", i);
                break;
            }
        }
        if(i>=MY_TSFC_WINDOWS_SIZE)
        {
            ESP_LOGI("MyTSFC_ClearWindowAfterAck()", "set all frame of send windows to tsf_win_fps_idle\n");
            my_tsfc_windows_enough = 0;
            for(i=0; i<MY_TSFC_WINDOWS_SIZE; i++)
            {
                my_tsfc_send_windows_array[i]->repeat_count = 0;
                my_tsfc_send_windows_array[i]->state = tsf_win_fps_idle;
            }
        }
    }
    //--------------------------------------------------跳动窗口
    xSemaphoreGive(my_tsfc_send_windowsMutexSemaphore);
    return 0;
}

uint8_t MyTSFC_ClearQueue_AllData(my_tsfc_queue_t** p)
{
    for(int i=0; i<MY_TSFC_QUEUE_LEN; i++)
    {
        void* p_my_tsfc_queue_data_temp = p[i]->data;
        memset(p[i], 0, sizeof(my_tsfc_queue_t));
        p[i]->data = p_my_tsfc_queue_data_temp;
    }
    return 0;
}

uint8_t MyTSFC_ClearWindow_AllData(my_tsfc_windows_t** p)
{
    for(int i=0; i<MY_TSFC_WINDOWS_SIZE; i++)
    {
        void* p_my_tsfc_windows_data_temp = p[i]->data;
        memset(p[i], 0, sizeof(my_tsfc_windows_t));
        p[i]->data = p_my_tsfc_windows_data_temp;
        memset(p[i]->data, 0, sizeof(my_tsf_dataframe_t));
        p[i]->state = tsf_win_fps_idle;
    }
    if(p==my_tsfc_send_windows_array)
    {
        my_tsfc_windows_enough = 0;
    }
    return 0;
}

typedef struct
{
    char* p_frame_start;
    char* p_frame_data_start;

    uint8_t type;
    uint8_t pd;
    uint8_t sn;
    uint16_t app_id;
    uint16_t data_len;
    uint16_t frame_len;
    uint32_t crc_original;
    uint32_t crc_calcu_result;
    uint8_t ack_code;

    int rcv_windows_posi_mark[MY_TSFC_WINDOWS_SIZE];
    int rcv_windows_posi_mark_index;
}my_tsfc_rcv_frame_info_t;
my_tsf_ackcode_t MyTSFC_Rcv_Check_FrameHead(my_tsfc_rcv_frame_info_t* p_frame_info)
{
    if(p_frame_info==NULL)
    {
        return -1;
    }
    if(p_frame_info->p_frame_start==NULL)
    {
        return -2;
    }

    //读取帧头
    my_tsf_frame_head_t* my_tsf_frame_head = (my_tsf_frame_head_t*)(p_frame_info->p_frame_start);
    char* frame_start_byte = (char*)(p_frame_info->p_frame_start);
    if(!(my_tsf_frame_head->type>=my_tsf_frametype_min&&my_tsf_frame_head->type<=my_tsf_frametype_max))
    {
        p_frame_info->sn++;
        return tsf_ackcode_frametype_unrecognized;
    }
    if(my_tsf_frame_head->pd!=MY_TSFC_PD)
    {
        #if MY_TSFC_DEBUG_CHECK_PD
        ESP_LOGE("MyTSFC_Rcv_Check_FrameHead()", "error tsf_ackcode_pd_unsupported, my_tsf_frame_head->pd=0x%x\n", my_tsf_frame_head->pd);
        return tsf_ackcode_pd_unsupported;
        #else
        ESP_LOGE("MyTSFC_Rcv_Check_FrameHead()", "error tsf_ackcode_pd_unsupported, my_tsf_frame_head->pd=0x%x, ignore!\n", my_tsf_frame_head->pd);
        #endif
    }
    //获取帧头信息
    p_frame_info->type = my_tsf_frame_head->type;
    p_frame_info->pd = my_tsf_frame_head->pd;

    //获取sn、appid、data_len、crc
    p_frame_info->sn = *((uint8_t*)(frame_start_byte+2));
    if(my_tsf_frame_head->type==msg_frame||my_tsf_frame_head->type==data_frame)
    {
        p_frame_info->app_id = my_endian_conversion_16(*((uint16_t*)(frame_start_byte+4)));
        p_frame_info->data_len = my_endian_conversion_16(*((uint16_t*)(frame_start_byte+6)));
        p_frame_info->crc_original = my_endian_conversion_32(*(uint32_t*)(frame_start_byte+p_frame_info->data_len+8));
    }
    else
    {
        p_frame_info->crc_original = my_endian_conversion_32(*(uint32_t*)(frame_start_byte+4));
        if(my_tsf_frame_head->type==ack_frame)
        {
            p_frame_info->ack_code = *((uint8_t*)(frame_start_byte+3));
        }
    }

    if(my_tsf_frame_head->type==ack_frame)
    {
        if(p_frame_info->ack_code!=tsf_ackcode_success)
        {
            ESP_LOGW("MyTSFC_Rcv_Check_FrameHead()", "ack_code error, p_frame_info->ack_code=0x%x!\n", p_frame_info->ack_code);
        }
    }

    //比对app_id
    if(my_tsf_frame_head->type==msg_frame||my_tsf_frame_head->type==data_frame)
    {
        if(p_frame_info->app_id!=my_tsfc_info.app_id)
        {
            #if MY_TSFC_DEBUG_CHECK_APPID
            ESP_LOGE("MyTSFC_Rcv_Check_FrameHead()", "error tsf_ackcode_appid_unrecognized, p_frame_info->app_id=0x%x", \
            p_frame_info->app_id);
            return tsf_ackcode_appid_unrecognized;
            #else
            ESP_LOGE("MyTSFC_Rcv_Check_FrameHead()", "error tsf_ackcode_appid_unrecognized, p_frame_info->app_id=0x%x, ignore!", \
            p_frame_info->app_id);
            #endif
        }
    }

    //比对数据长度是否一致
    if(my_tsf_frame_head->type==msg_frame||my_tsf_frame_head->type==data_frame)
    {
        if(p_frame_info->frame_len < p_frame_info->data_len+16)
        {
            ESP_LOGE("MyTSFC_Rcv_Check_FrameHead()", "error tsf_ackcode_insufficient_len, p_frame_info->frame_len=%d\n", p_frame_info->frame_len);
            return tsf_ackcode_insufficient_len;
        }
    }
    else
    {
        if(p_frame_info->frame_len!=12)
        {
            ESP_LOGE("MyTSFC_Rcv_Check_FrameHead()", "error tsf_ackcode_insufficient_len, p_frame_info->frame_len=%d\n", p_frame_info->frame_len);
            return tsf_ackcode_insufficient_len;
        }
    }

    //计算crc并比对
    if(my_tsf_frame_head->type==msg_frame||my_tsf_frame_head->type==data_frame)
    {
        p_frame_info->crc_calcu_result = crc_32((void*)my_tsf_frame_head, p_frame_info->data_len+8);
    }
    else
    {
        p_frame_info->crc_calcu_result = crc_32((void*)my_tsf_frame_head, 4);
    }
    
    if(p_frame_info->crc_original!=p_frame_info->crc_calcu_result)
    {
        #if MY_TSFC_DEBUG_CHECK_CRC
        ESP_LOGE("MyTSFC_Rcv_Check_FrameHead()", "error tsf_ackcode_crc_check_error, 0x%08x:0x%08x", \
        p_frame_info->crc_original, p_frame_info->crc_calcu_result);
        return tsf_ackcode_appid_unrecognized;
        #else
        ESP_LOGE("MyTSFC_Rcv_Check_FrameHead()", "error tsf_ackcode_crc_check_error, 0x%08x:0x%08x, ignore!", \
        p_frame_info->crc_original, p_frame_info->crc_calcu_result);
        #endif
    }

    p_frame_info->p_frame_data_start = frame_start_byte+8;
    //AES解密
    #if MY_TSFC_DEBUG_CHECK_AES
    if(my_tsf_frame_head->type==msg_frame)
    {
        my_tsf_msgframe_t* p_msgframe = (my_tsf_msgframe_t*)p_frame_info->p_frame_start;
        if(My_EspAes_InvCipher(my_tsfc_info.aes_ctx, (void*)p_msgframe->data, p_frame_info->data_len, (void*)p_msgframe->data, p_frame_info->data_len))
        {
            #if MY_TSFC_DEBUG_CHECK_AES
            ESP_LOGE("MyTSFC_Rcv_Check_FrameHead()", "error tsf_ackcode_decrypt_error");
            return tsf_ackcode_decrypt_error;
            #else
            ESP_LOGE("MyTSFC_Rcv_Check_FrameHead()", "error tsf_ackcode_decrypt_error, ignore!");
            #endif
        }
    }
    else if(my_tsf_frame_head->type==data_frame)
    {
        my_tsf_dataframe_t* p_dataframe = (my_tsf_dataframe_t*)p_frame_info->p_frame_start;
        if(My_EspAes_InvCipher(my_tsfc_info.aes_ctx, (void*)p_dataframe->data, p_frame_info->data_len, (void*)p_dataframe->data, p_frame_info->data_len))
        {
            #if MY_TSFC_DEBUG_CHECK_AES
            ESP_LOGE("MyTSFC_Rcv_Check_FrameHead()", "error tsf_ackcode_decrypt_error");
            return tsf_ackcode_decrypt_error;
            #else
            ESP_LOGE("MyTSFC_Rcv_Check_FrameHead()", "error tsf_ackcode_decrypt_error, ignore!");
            #endif
        }
    }
    #endif

    return tsf_ackcode_success;
}

int MyTSFC_Rcv_Frame_Uni_Check(my_tsfc_rcv_frame_info_t* p_frame_info)
{
    if((p_frame_info->sn < 8) || (p_frame_info->sn >= 256-8))
    {
        return 0;
    }
    uint8_t sn_uni_check = 1;

    //检查sn有无重复
    int m = 0;
    for(m=0; m<MY_TSFC_WINDOWS_SIZE; m++)
    {
        if(p_frame_info->rcv_windows_posi_mark[m]==p_frame_info->sn)
        {
            break;
        }
    }
    if(m>=MY_TSFC_WINDOWS_SIZE)
    {//窗口内没有重复的sn
        sn_uni_check = 0;
        if(p_frame_info->rcv_windows_posi_mark_index>=MY_TSFC_WINDOWS_SIZE)
        {
            p_frame_info->rcv_windows_posi_mark_index = 0;
        }
        p_frame_info->rcv_windows_posi_mark[p_frame_info->rcv_windows_posi_mark_index]=p_frame_info->sn;
        p_frame_info->rcv_windows_posi_mark_index++;
    }
    else
    {
        my_tsfc_statistic.error.sn_repeat_error_count++;
        ESP_LOGE("MyTSFC_Rcv_Task()", "rcv similar frame_sn=%d, abandon the frame", p_frame_info->sn);
    }
    return sn_uni_check;
}

int MyTSFC_Rcv_Frame_Uni_Check_info_clear(my_tsfc_rcv_frame_info_t* p_frame_info)
{
    if(p_frame_info==NULL)
    {
        return -1;
    }
    memset(p_frame_info->rcv_windows_posi_mark, -1, sizeof(p_frame_info->rcv_windows_posi_mark));
    p_frame_info->rcv_windows_posi_mark_index = 0;
    
    return 0;
}

TaskHandle_t MyTSFC_Rcv_TaskHandle = NULL;
TaskHandle_t MyTSFC_Timer_TaskHandle = NULL;
void MyTSFC_Rcv_Task(void* param)
{
    my_whgm5_queue_item_t* p_rcv_buf=NULL;
    char* rcv_buf = (char*)calloc(MyTSFC_Rcv_Task_rcvbuf_field_size, sizeof(my_tsf_dataframe_t));
    int rcv_len = 0;
    int tail_pos = 0;
    char* p_frame_start = NULL;
    char* p_frame_end = NULL;
    char* cur_read_pointer_posi = rcv_buf;
    char* p_empty_position = rcv_buf;

    char* p_good_bugfix_good_tail_posi = NULL;

    ESP_LOGI("----0", "rcv_buf=%p, p_empty_position=%p, cur_read_pointer_posi=%p, p_frame_start=%p, p_frame_end=%p, rcv_len=%d, tail_pos=%d",\
                rcv_buf, p_empty_position, cur_read_pointer_posi, p_frame_start, p_frame_end, rcv_len, tail_pos);
    
    int i = 0;
    
    int rcv_frame_count = 0;


    my_tsfc_rcv_frame_info_t my_tsfc_rcv_frame_info;
    memset(my_tsfc_rcv_frame_info.rcv_windows_posi_mark, -1, sizeof(my_tsfc_rcv_frame_info.rcv_windows_posi_mark));
    my_tsfc_rcv_frame_info.rcv_windows_posi_mark_index = 0;

    int clear_uni_check_mark = 0;

    for(;;)
    {
        if(my_tsfc_info.tsfc_work_state == TSFC_WORK_STATE_OK)
        {
            clear_uni_check_mark = 0;
            if(!MyWHGM5_TcpRcvData(&p_rcv_buf, portMAX_DELAY))
            {
                memcpy(p_empty_position, p_rcv_buf->data, p_rcv_buf->len);
                rcv_len = p_rcv_buf->len;

                //----------------------------------------------
                
                p_empty_position+=rcv_len;
                do
                {
                    my_tsfc_GOOD_bugfix:
                    if(p_good_bugfix_good_tail_posi==NULL)
                    {
                        tail_pos = MatchString(cur_read_pointer_posi, p_empty_position-cur_read_pointer_posi, MY_TSF_DATAFRAME_TAIL, strlen(MY_TSF_DATAFRAME_TAIL));
                    }
                    else
                    {
                        tail_pos+=4;
                        tail_pos += MatchString(p_good_bugfix_good_tail_posi, p_empty_position-p_good_bugfix_good_tail_posi, MY_TSF_DATAFRAME_TAIL, strlen(MY_TSF_DATAFRAME_TAIL));
                        p_good_bugfix_good_tail_posi = NULL;
                    }
                    //if( (p_frame_tail = FindBinary(p_read, remain_len, MY_TSF_DATAFRAME_TAIL, strlen(MY_TSF_DATAFRAME_TAIL)) )!=NULL)MatchString
                    
                    if(tail_pos!=0xffff)
                    {
                        p_frame_start = cur_read_pointer_posi;
                        p_frame_end = p_frame_start+tail_pos+4;
                        //=============================================================================================================================================================
                        rcv_frame_count++;

                        my_tsfc_statistic.down.data_count_total+= (p_frame_end-p_frame_start);
                        #if DEBUG_PRINT_LEVEL_my_tsfc_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                        printf("\n\n\n\n\n\n\n\n\n\n");
                        ESP_LOGI("MyTSFC_Rcv_Task()", "rcv_frame_count=%d", rcv_frame_count);
                        #endif
                        //----------------------
                        //解析头部
                        my_tsfc_rcv_frame_info.p_frame_start = p_frame_start;
                        my_tsfc_rcv_frame_info.frame_len = p_frame_end-p_frame_start;
                        int err = MyTSFC_Rcv_Check_FrameHead(&my_tsfc_rcv_frame_info);
                        #if DEBUG_PRINT_LEVEL_my_tsfc_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                        printf("type=0x%x, pd=0x%x, sn=0x%x, app_id=0x%x, frame_len=0x%x, data_len=0x%x, crc_original=0x%x, crc_calcu_result=0x%x\n", \
                        my_tsfc_rcv_frame_info.type, \
                        my_tsfc_rcv_frame_info.pd, 
                        my_tsfc_rcv_frame_info.sn, \
                        my_tsfc_rcv_frame_info.app_id, \
                        my_tsfc_rcv_frame_info.frame_len, \
                        my_tsfc_rcv_frame_info.data_len, \
                        my_tsfc_rcv_frame_info.crc_original, \
                        my_tsfc_rcv_frame_info.crc_calcu_result);
                        #endif

                        #if DEBUG_PRINT_LEVEL_my_tsfc_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                        printf("\nMyTSFC_Rcv_Task(), rcv_frame(%d)=\n", my_tsfc_rcv_frame_info.frame_len);
                        #endif

                        #if DEBUG_PRINT_LEVEL_my_tsfc_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
                        char* in_data_byte = (char*)p_frame_start;
                        if(my_tsfc_rcv_frame_info.frame_len<=512)
                        {
                            for(int i=0; i<my_tsfc_rcv_frame_info.frame_len; i++)
                            {
                                printf("%02X ", *(in_data_byte+i));
                            }
                            printf("\n$$$$\n");
                        }
                        else
                        {
                            printf("my_tsfc_rcv_frame_info.frame_len > 512, omit...\n");
                        }
                        #endif
                        
                        //========================================================================================
                                    

                        if(my_tsfc_rcv_frame_info.type==msg_frame||my_tsfc_rcv_frame_info.type==data_frame)
                        {
                            if(err!=tsf_ackcode_success)
                            {

                                ESP_LOGE("MyTSFC_Rcv_Task()", "Check_FrameHead error, err=0x%x(%d)\n", err, err);
                                
                                if(err!=tsf_ackcode_frametype_unrecognized)
                                {
                                    if(err==tsf_ackcode_insufficient_len)
                                    {
                                        p_good_bugfix_good_tail_posi = p_frame_end;
                                        goto my_tsfc_GOOD_bugfix;
                                    }
                                    if(MyTSFC_Ack(my_tsfc_rcv_frame_info.sn, err, 1000, 1, 1))
                                    {
                                        ESP_LOGE("MyTSFC_Rcv_Task()", "MyTSFC_Ack error:\n");
                                    }
                                }
                                
                                int read_posi = 0;
                                #if DEBUG_PRINT_LEVEL_my_tsfc_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
                                printf("\n*p_frame_start = ");
                                printf("0x%02x ", *(p_frame_start+read_posi));read_posi++;
                                printf("0x%02x ", *(p_frame_start+read_posi));read_posi++;
                                printf("0x%02x ", *(p_frame_start+read_posi));read_posi++;
                                printf("0x%02x ", *(p_frame_start+read_posi));read_posi++;
                                printf("0x%02x ", *(p_frame_start+read_posi));read_posi++;
                                printf("0x%02x ", *(p_frame_start+read_posi));read_posi++;
                                printf("0x%02x ", *(p_frame_start+read_posi));read_posi++;
                                printf("0x%02x ", *(p_frame_start+read_posi));read_posi++;
                                printf("\n");
                                #endif

                                goto MyTSFC_Rcv_Task_frame_move;
                            }

                            int frame_uni_check = MyTSFC_Rcv_Frame_Uni_Check(&my_tsfc_rcv_frame_info);
                            // frame_uni_check = 0;
                            if(!frame_uni_check)
                            {
                                my_tsfc_rcv_windows_frame_counter++;
                                my_tsfc_statistic.down.frame_count_type_msg_valid++;

                                //发送到队列
                                int find_empty_count = 0;
                                do
                                {
                                    for(i=0; i<MY_TSFC_QUEUE_LEN; i++)
                                    {
                                        if(!my_tsfc_rcv_queue_array[i]->valid)
                                        {
                                            int queue_send_ret = pdTRUE;
                                            memcpy(my_tsfc_rcv_queue_array[i]->data, my_tsfc_rcv_frame_info.p_frame_data_start, my_tsfc_rcv_frame_info.data_len);
                                            my_tsfc_rcv_queue_array[i]->len = my_tsfc_rcv_frame_info.data_len;
                                            my_tsfc_rcv_queue_array[i]->my_tsfc_data_type = my_tsfc_rcv_frame_info.type;
                                            my_tsfc_rcv_queue_array[i]->sn = my_tsfc_rcv_frame_info.sn;
                                            my_tsfc_rcv_queue_array[i]->id = 1;
                                            my_tsfc_rcv_queue_array[i]->valid = 1;

                                            queue_send_ret = xQueueSend(my_tsfc_rcv_queue, &my_tsfc_rcv_queue_array[i], 1000 / portTICK_PERIOD_MS);
                                            if(queue_send_ret!=pdTRUE)
                                            {
                                                ESP_LOGE("MyTSFC_Rcv_Task()", "xQueueSend ERROR, queue_send_ret=%d\n", queue_send_ret);
                                            }
                                            break;
                                        }
                                    }
                                    if(i>=MY_TSFC_QUEUE_LEN)
                                    {
                                        ESP_LOGE("MyTSFC_Rcv_Task()", "not find empty in rcv queue count=%d\n", find_empty_count);
                                        vTaskDelay(50 / portTICK_PERIOD_MS);
                                    }
                                    else
                                    {
                                        break;
                                    }
                                } while ((++find_empty_count<3)&&(i>=MY_TSFC_QUEUE_LEN));
                                if(find_empty_count>=3)
                                {
                                    ESP_LOGE("MyTSFC_Rcv_Task()", "not find empty in rcv queue finally!!!\n");
                                }
                                else
                                {
                                    find_empty_count = 0;
                                }

                                if(MyTSFC_Ack(my_tsfc_rcv_frame_info.sn, tsf_ackcode_success, 1000, 1, 1))
                                {
                                    ESP_LOGE("MyTSFC_Rcv_Task()", "msg_frame:ack error:tsf_ackcode_success\n");
                                }
                            }
                            else
                            {
                                ESP_LOGE("MyTSFC_Rcv_Task()", "sn_uni_check error!\n");                                        
                                //暂时处理作 应答 处理成功
                                if(MyTSFC_Ack(my_tsfc_rcv_frame_info.sn, tsf_ackcode_success, 1000, 1, 1))
                                {
                                    ESP_LOGE("MyTSFC_Rcv_Task()", "msg_frame:ack error:tsf_ackcode_success\n");
                                }
                            }
                        }
                        
                        else if(my_tsfc_rcv_frame_info.type==ack_frame)
                        {
                            my_tsfc_statistic.down.frame_count_valid++;
                            my_tsfc_statistic.down.frame_count_type_ack++;

                            ESP_LOGI("MyTSFC_Rcv_Task()", "-ack_frame-, ack_sn=%d", my_tsfc_rcv_frame_info.sn);
                            
                            if(my_tsfc_rcv_frame_info.ack_code==tsf_ackcode_success)
                            {
                                MyTSFC_ClearWindowAfterAck(my_tsfc_rcv_frame_info.sn);
                            }
                            else if(my_tsfc_rcv_frame_info.ack_code==tsf_ackcode_frametype_unrecognized)
                            {
                                // ESP_LOGE("MyTSFC_Rcv_Task()", "ack_code error=0x%0x, resend all send_andnoack frame in send windows!\n", my_tsfc_rcv_frame_info.ack_code);
                                // xTaskNotify(MyTSFC_Timer_TaskHandle, 1, eSetValueWithOverwrite);
                                
                                // int k = 0;
                                // for( ; k<MY_TSFC_WINDOWS_SIZE; k++)
                                // {
                                //     if(my_tsfc_send_windows_array[k]->state==tsf_win_fps_send_noack)
                                //     {
                                //         if(!MyWHGM5_TcpSendData(my_tsfc_send_windows_array[k]->data, my_tsfc_send_windows_array[k]->data_len, 1000, my_tsfc_send_windows_array[k]->id))
                                //         {
                                //             ESP_LOGW("MyTSFC_Rcv_Task()", "resend_sn=%d!\n", my_tsfc_send_windows_array[k]->frame_sn);
                                //         }
                                //         {
                                //             ESP_LOGW("MyTSFC_Rcv_Task()", "failed to resend_sn=%d!\n", my_tsfc_send_windows_array[k]->frame_sn);
                                //         }
                                //     }
                                // }
                            }
                            else
                            {
                                ESP_LOGE("MyTSFC_Rcv_Task()", "ack_code error=0x%0x, resend_sn=%d!\n", my_tsfc_rcv_frame_info.ack_code, my_tsfc_rcv_frame_info.sn);
                                int k = 0;
                                for( ; k<MY_TSFC_WINDOWS_SIZE; k++)
                                {
                                    if(my_tsfc_rcv_frame_info.sn==my_tsfc_send_windows_array[k]->frame_sn)
                                    {
                                        if(my_tsfc_send_windows_array[k]->state==tsf_win_fps_send_noack)
                                            break;
                                    }
                                }
                                if(k<MY_TSFC_WINDOWS_SIZE)
                                {
                                    if(!MyWHGM5_TcpSendData(my_tsfc_send_windows_array[k]->data, my_tsfc_send_windows_array[k]->data_len, 1000, my_tsfc_send_windows_array[k]->id))
                                    {
                                        ESP_LOGW("MyTSFC_Rcv_Task()", "resend_sn=%d!\n", my_tsfc_rcv_frame_info.sn);
                                    }
                                    else
                                    {
                                        ESP_LOGW("MyTSFC_Rcv_Task()", "failed to resend_sn=%d!\n", my_tsfc_rcv_frame_info.sn);
                                    }
                                }
                                else
                                {
                                    ESP_LOGE("MyTSFC_Rcv_Task()", "not find sn=%d!\n", my_tsfc_rcv_frame_info.sn);
                                }
                            }
                        }
                        else if(my_tsfc_rcv_frame_info.type==cd_frame)
                        {
                            my_tsfc_statistic.down.frame_count_valid++;
                            my_tsfc_statistic.down.frame_count_type_cc++;
                            ESP_LOGI("MyTSFC_Rcv_Task()", "-cd_frame-");
                            //my_tsf_checkcnctframe_t* checkcnct = (my_tsf_checkcnctframe_t*)p_frame_start;

                            if(MyTSFC_Ack(my_tsfc_rcv_frame_info.sn, tsf_ackcode_success, 1000, 1, 1))
                            {
                                ESP_LOGE("MyTSFC_Rcv_Task()", "cd_frame:ack error:tsf_ackcode_success\n");
                            }
                        }
                        //=============================================================================================================================================================
                        MyTSFC_Rcv_Task_frame_move:
                        cur_read_pointer_posi+=tail_pos+4;
                    }
                    else
                    {
                        if(p_empty_position>rcv_buf+(MyTSFC_Rcv_Task_rcvbuf_field_size/2)*sizeof(my_tsf_dataframe_t))
                        {
                            #if DEBUG_PRINT_LEVEL_my_tsfc_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
                            ESP_LOGI("\n-OK1-\n", " ");
                            #endif
                            memset(rcv_buf, 0, cur_read_pointer_posi-rcv_buf);
                            memcpy(rcv_buf, cur_read_pointer_posi, p_empty_position-cur_read_pointer_posi);
                            memset(cur_read_pointer_posi, 0, p_empty_position-cur_read_pointer_posi);
                            p_empty_position = rcv_buf + (p_empty_position-cur_read_pointer_posi);
                            cur_read_pointer_posi = rcv_buf;
                        }
                        else
                        {
                            #if DEBUG_PRINT_LEVEL_my_tsfc_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
                            ESP_LOGI("\n-OK2-\n", " ");
                            #endif
                        }
                        break;
                    }
                } while (p_empty_position-cur_read_pointer_posi>0);

                if(p_empty_position==cur_read_pointer_posi)
                {
                    memset(rcv_buf, 0, p_empty_position-rcv_buf);
                    p_empty_position = rcv_buf;
                    cur_read_pointer_posi = rcv_buf;
                }
                //----------------------------------------------
                p_rcv_buf->valid = 0;
            }
        }
        else
        {
            if(!clear_uni_check_mark)
            {
                ESP_LOGE("MyTSFC_Rcv_Task()", "connection error, clear rcv_task marks!");
                clear_uni_check_mark = 1;
                memset(rcv_buf, 0, MyTSFC_Rcv_Task_rcvbuf_field_size*sizeof(my_tsf_dataframe_t));
                rcv_len = 0;
                tail_pos = 0;
                p_frame_start = NULL;
                p_frame_end = NULL;
                cur_read_pointer_posi = rcv_buf;
                p_empty_position = rcv_buf;
                
                i = 0;
                
                // rcv_frame_count = 0;
                MyTSFC_Rcv_Frame_Uni_Check_info_clear(&my_tsfc_rcv_frame_info);
            }
            vTaskDelay(100 / portTICK_PERIOD_MS);
        }
    }
}

uint8_t MyTSFC_RcvData(my_tsfc_queue_t** const saving, uint32_t timeout)
{
    int queue_rcv_ret = pdFALSE;

    if(saving!=NULL)
    {
        queue_rcv_ret = xQueueReceive(my_tsfc_rcv_queue, saving, timeout/portTICK_PERIOD_MS);
        if(queue_rcv_ret!=pdTRUE)
        {
            ESP_LOGE("MyTSFC_RcvData()", "error: recv timeout\n");
            return 1;
        }
        return 0;
    }
    else
    {
        ESP_LOGE("MyTSFC_RcvData()", "error: saving==NULL\n");
        return 2;
    }
    return 0;
}

void MyTSFC_Timer_Task(void* param)
{
    my_whgm5_queue_item_t* p_rcv_buf=NULL;
    int i = 0;
    uint64_t cur_time_in_ms;
    uint8_t restart_lte_mark = 0;
    uint32_t notifyValue = 0;
    uint64_t last_time_in_ms = 0;
    uint8_t repeat_on = 0;
    uint8_t repeat_mark = 0;

    for(;;)
    {
        // vTaskDelay(1000 / portTICK_PERIOD_MS);
        xTaskNotifyWait(0x0, 0xffffffff, &notifyValue, 100/portTICK_PERIOD_MS);
        xSemaphoreTake(my_tsfc_send_windowsMutexSemaphore, portMAX_DELAY);
        cur_time_in_ms = esp_timer_get_time()/1000;
        //ESP_LOGI("MyTSFC_Timer_Task()", "%lldms\n", cur_time_in_ms);

        #define repeat_time_out_time    3000

        if(notifyValue&&repeat_on==0)
        {
            if(cur_time_in_ms - last_time_in_ms > repeat_time_out_time)
            {
                repeat_on = 1;
                last_time_in_ms = cur_time_in_ms;
            }
        }

        if(repeat_on)
        {
            int k = 0;
            repeat_mark = 0;
            for( ; k<MY_TSFC_WINDOWS_SIZE; k++)
            {
                if(my_tsfc_send_windows_array[k]->state==tsf_win_fps_send_noack)
                {
                    if(cur_time_in_ms - my_tsfc_send_windows_array[k]->start_timestamp >= repeat_time_out_time)
                    {
                        my_tsfc_send_windows_array[k]->start_timestamp = esp_timer_get_time()/1000;
                        if(!MyWHGM5_TcpSendData(my_tsfc_send_windows_array[k]->data, my_tsfc_send_windows_array[k]->data_len, 1000, my_tsfc_send_windows_array[k]->id))
                        {
                            ESP_LOGW("MyTSFC_Timer_Task()", "resend_sn=%d!\n", my_tsfc_send_windows_array[k]->frame_sn);
                        }
                        else
                        {
                            ESP_LOGW("MyTSFC_Timer_Task()", "failed to resend_sn=%d!\n", my_tsfc_send_windows_array[k]->frame_sn);
                        }
                    }
                    repeat_mark++;
                }
            }
            if(repeat_mark==0)
            {
                repeat_on = 0;
            }
        }
        else
        {
            for(i=0; i<MY_TSFC_WINDOWS_SIZE; i++)
            {
                if(my_tsfc_send_windows_array[i]->state==tsf_win_fps_send_noack)
                {
                    if(cur_time_in_ms - my_tsfc_send_windows_array[i]->start_timestamp >= my_tsfc_send_windows_array[i]->timeout_time)
                    {
                        if(my_tsfc_send_windows_array[i]->repeat_count>=MY_TSFC_REPEAT_ULIMIT)
                        {
                            my_tsfc_send_windows_array[i]->state=tsf_win_fps_timeout_sendfailed;
                            ESP_LOGE("MyTSFC_Timer_Task()", "my_tsfc_send_windows_array[%d] send failed, frame_sn=%d\n", i, my_tsfc_send_windows_array[i]->frame_sn);
                            //产生事件，停止整个发送服务
                            my_tsfc_info.tsfc_work_state = TSFC_WORK_STATE_PENDING;
                            MyTSFC_ClearQueue_AllData(my_tsfc_send_queue_array);
                            MyTSFC_ClearWindow_AllData(my_tsfc_send_windows_array);
                        }
                        else
                        {
                            if(!MyWHGM5_TcpSendData(my_tsfc_send_windows_array[i]->data, my_tsfc_send_windows_array[i]->data_len, 1000, my_tsfc_send_windows_array[i]->id))
                            {
                                ESP_LOGE("MyTSFC_Timer_Task()", "my_tsfc_send_windows_array[%d] response timeout, frame_sn=%d, repeat_count=%d\n", i, my_tsfc_send_windows_array[i]->frame_sn, my_tsfc_send_windows_array[i]->repeat_count+1);
                                //my_tsfc_send_windows_array[i]->timeout_time += my_tsfc_send_windows_array[i]->timeout_time;
                                my_tsfc_send_windows_array[i]->start_timestamp = esp_timer_get_time()/1000;
                                my_tsfc_send_windows_array[i]->repeat_count++;
                            }
                        }
                    }
                }
                // if(my_tsfc_send_windows_array[i]->state==tsf_win_fps_timeout_sendfailed)
                // {
                //     ESP_LOGE("MyTSFC_Timer_Task()", "my_tsfc_send_windows_array[%d] send failed, frame_sn=%d\n", i, my_tsfc_send_windows_array[i]->frame_sn);

                //     if(!restart_lte_mark)
                //     {
                //         my_tsfc_statistic.error.restart_lte_count++;
                //         restart_lte_mark = 1;
                //         ESP_LOGI("MyTSFC_Timer_Task()", "MyWHGM5_RestartOnly()");
                //         MyWHGM5_RestartOnly();
                //         ESP_LOGI("MyTSFC_Timer_Task()", "MyWHGM5_RestartOnly() finished, wait 20s...");
                //         vTaskDelay(20000 / portTICK_PERIOD_MS);
                //         ESP_LOGI("MyTSFC_Timer_Task()", "wait 20s done");

                //         for(int j=0; j<MY_TSFC_WINDOWS_SIZE; j++)
                //         {
                //             if((my_tsfc_send_windows_array[j]->state==tsf_win_fps_timeout_sendfailed)||(my_tsfc_send_windows_array[j]->state==tsf_win_fps_send_noack))
                //             {
                //                 my_tsfc_send_windows_array[j]->state = tsf_win_fps_send_noack;
                //                 my_tsfc_send_windows_array[j]->repeat_count = 0;
                //                 my_tsfc_send_windows_array[i]->start_timestamp = 0;
                //             }
                //         }
                //         restart_lte_mark = 0;
                //     }
                // }
            }
            if(my_tsfc_info.tsfc_work_state == TSFC_WORK_STATE_PENDING)
            {
                my_tsfc_statistic.error.restart_lte_count++;
                restart_lte_mark = 1;
                ESP_LOGI("MyTSFC_Timer_Task()", "MyWHGM5_RestartOnly()");
                MyWHGM5_RestartOnly();
                ESP_LOGI("MyTSFC_Timer_Task()", "MyWHGM5_RestartOnly() finished, wait %ds...", TSFC_WORK_STATE_PENDING_WAIT_TIME);
                vTaskDelay(TSFC_WORK_STATE_PENDING_WAIT_TIME*1000 / portTICK_PERIOD_MS);
                my_tsfc_send_windows_frame_counter = 0;
                ESP_LOGI("MyTSFC_Timer_Task()", "wait %ds done", TSFC_WORK_STATE_PENDING_WAIT_TIME);

                my_tsfc_info.tsfc_work_state = TSFC_WORK_STATE_OK;
            }
        }
        xSemaphoreGive(my_tsfc_send_windowsMutexSemaphore);
    }
}

int MyTSFC_Get_Workstate(void)
{
    return my_tsfc_info.tsfc_work_state;
}

// static int MyTSFC_Queue_Init(my_tsfc_queue_t** p, uint8_t data_area)
// {
//     for(int i=0; i<MY_TSFC_QUEUE_LEN; i++)
//     {
//         p[i] = (my_tsfc_queue_t*)calloc(1, sizeof(my_tsfc_queue_t));
//         if(p[i]!=NULL)
//         {
//             if(data_area)
//             {
//                 p[i]->data = (char*)calloc(1, WHGM5_DATA_PACK_SIZE-8+1);
//                 if(p[i]->data==NULL)
//                 {
//                     free(p[i]);
//                     ESP_LOGE("MyTSFC_Queue_Init()", "error1, i=%d\n", i);
//                     return i;
//                 }
//             }
//             else p[i]->data=NULL;
//         }
// 		else
//         {
//             ESP_LOGE("MyTSFC_Queue_Init()", "error2, i=%d\n", i);
//             return i;
//         }
//     }
//     return MY_TSFC_QUEUE_LEN;
// }

static int MyTSFC_Queue_Init(my_tsfc_queue_t** p, uint8_t data_area)
{
    if(data_area)
    {
        char* p_temp = (char*)calloc(MY_TSFC_QUEUE_LEN, WHGM5_DATA_PACK_SIZE);
        if(p_temp!=NULL)
        { 
            for(int i=0; i<MY_TSFC_QUEUE_LEN; i++)
            {
                p[i] = (my_tsfc_queue_t*)calloc(1, sizeof(my_tsfc_queue_t));
                if(p[i]!=NULL)
                {
                    p[i]->data = p_temp+(i*(WHGM5_DATA_PACK_SIZE-8));
                }
                else
                {
                    ESP_LOGE("MyTSFC_Queue_Init()", "error1\n");
                    return 1;
                }
            }
        }
        else
        {
            ESP_LOGE("MyTSFC_Queue_Init()", "error2\n");
            return 2;
        }
    }
    else
    {
        for(int i=0; i<MY_TSFC_QUEUE_LEN; i++)
        {
            p[i] = (my_tsfc_queue_t*)calloc(1, sizeof(my_tsfc_queue_t));
            if(p[i]==NULL)
            {
                ESP_LOGE("MyTSFC_Queue_Init()", "error3, i=%d\n", i);
                return i;
            }
        }
    }
    return MY_TSFC_QUEUE_LEN;
}

// static int MyTSFC_Windows_Init(my_tsfc_windows_t** p)
// {
//     for(int i=0; i<MY_TSFC_WINDOWS_SIZE; i++)
//     {
//         p[i] = (my_tsfc_windows_t*)calloc(1, sizeof(my_tsfc_windows_t));
//         if(p[i]!=NULL)
//         {
//             p[i]->data = (char*)calloc(1, sizeof(my_tsf_dataframe_t));
//             if(p[i]->data==NULL)
//             {
//                 free(p[i]);
//                 ESP_LOGE("MyTSFC_Windows_Init()", "error1, i=%d\n", i);
//                 return i;
//             }
//             else
//             {
//                 p[i]->state = tsf_win_fps_idle;
//             }
//         }
// 		else
//         {
//             ESP_LOGE("MyTSFC_Windows_Init()", "error2, i=%d\n", i);
//             return i;
//         }
//     }
//     return MY_TSFC_QUEUE_LEN;
// }

static int MyTSFC_Windows_Init(my_tsfc_windows_t** p, my_tsf_framestate_t frame_init_state)
{
    char* p_temp = (char*)calloc(MY_TSFC_WINDOWS_SIZE, sizeof(my_tsf_dataframe_t));
    if(p_temp!=NULL)
    { 
        for(int i=0; i<MY_TSFC_WINDOWS_SIZE; i++)
        {
            p[i] = (my_tsfc_windows_t*)calloc(1, sizeof(my_tsfc_windows_t));
            if(p[i]!=NULL)
            {
                p[i]->data = p_temp+(i*(sizeof(my_tsf_dataframe_t)));
                p[i]->state = frame_init_state;
            }
            else
            {
                ESP_LOGE("MyTSFC_Windows_Init()", "error1\n");
                return 1;
            }
        }
    }
    else
    {
        ESP_LOGE("MyTSFC_Windows_Init()", "error2\n");
        return 2;
    }
    return WHGM5_QUEUE_LEN;
}

my_tsfc_statistic_t* MyTSFC_GetStatistics(void)
{
    return &my_tsfc_statistic;
}

uint8_t MyTSFC_Init(uint16_t app_id, void* aes_ctx)
{
    my_tsfc_info.app_id = app_id;
    my_tsfc_info.aes_ctx = aes_ctx;
    memset(&my_tsfc_statistic, 0, sizeof(my_tsfc_statistic));

    MyTSFC_Queue_Init(my_tsfc_send_queue_array, 1);
    MyTSFC_Queue_Init(my_tsfc_rcv_queue_array, 1);
    MyTSFC_Windows_Init(my_tsfc_send_windows_array, tsf_win_fps_idle);
    MyTSFC_Windows_Init(my_tsfc_rcv_windows_array, tsf_win_fps_idle);

    my_tsfc_send_queue = xQueueGenericCreate(MY_TSFC_QUEUE_LEN, sizeof(my_tsfc_queue_t*), 1);
    if(my_tsfc_send_queue==NULL)ESP_LOGE("MyTSFC_Init()", "my_tsfc_send_queue=NULL\n");
    my_tsfc_rcv_queue = xQueueGenericCreate(MY_TSFC_QUEUE_LEN, sizeof(my_tsfc_queue_t*), 1);
    if(my_tsfc_rcv_queue==NULL)ESP_LOGE("MyTSFC_Init()", "my_tsfc_rcv_queue=NULL\n");

    my_tsfc_send_windowsMutexSemaphore = xSemaphoreCreateMutex();

    BaseType_t ret = pdPASS;
    #if MyTSFC_Send_TASK_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    p_task_stack = calloc(1, MyTSFC_Send_Task_task_stack_size*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
			if(xTaskCreateStaticPinnedToCore(MyTSFC_Send_Task, "MyTSFC_Send_Task", MyTSFC_Send_Task_task_stack_size, NULL, MyTSFC_Send_Task_priority, p_task_stack, p_task_data, MyTSFC_Send_Task_COREID)!=p_task_data)
			{
				ESP_LOGE("MyTSFC_Init()", "creat MyTSFC_Send_Task use xTaskCreateStaticPinnedToCore() error\n");
                return 1;
			}
        }else return 1;
    }else return 1;
	#elif MyTSFC_Send_TASK_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
	ret = xTaskCreatePinnedToCore(MyTSFC_Send_Task, "MyTSFC_Send_Task", MyTSFC_Send_Task_task_stack_size, NULL, MyTSFC_Send_Task_priority, &MyTSFC_Send_TaskHandle, MyTSFC_Send_Task_COREID);
	if(ret!=pdPASS)
	{
		ESP_LOGE("MyTSFC_Init()", "creat MyTSFC_Send_Task use xTaskCreatePinnedToCore() error=%d\n", ret);
        return 1;
	}
	#endif

    #if MyTSFC_Rcv_TASK_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    p_task_stack = calloc(1, MyTSFC_Rcv_Task_task_stack_size*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
			if(xTaskCreateStaticPinnedToCore(MyTSFC_Rcv_Task, "MyTSFC_Rcv_Task", MyTSFC_Rcv_Task_task_stack_size, NULL, MyTSFC_Rcv_Task_priority, p_task_stack, p_task_data, MyTSFC_Rcv_Task_COREID)!=p_task_data)
			{
				ESP_LOGE("MyTSFC_Init()", "creat MyTSFC_Rcv_Task use xTaskCreateStaticPinnedToCore() error\n");
                return 1;
			}
        }else return 1;
    }else return 1;
	#elif MyTSFC_Rcv_TASK_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
	ret = xTaskCreatePinnedToCore(MyTSFC_Rcv_Task, "MyTSFC_Rcv_Task", MyTSFC_Rcv_Task_task_stack_size, NULL, MyTSFC_Rcv_Task_priority, &MyTSFC_Rcv_TaskHandle, MyTSFC_Rcv_Task_COREID);
	if(ret!=pdPASS)
	{
		ESP_LOGE("MyTSFC_Init()", "creat MyTSFC_Rcv_Task use xTaskCreatePinnedToCore() error=%d\n", ret);
        return 1;
	}
	#endif

    #if MyTSFC_Timer_TASK_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    p_task_stack = calloc(1, MyTSFC_Timer_Task_task_stack_size*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
			if(xTaskCreateStaticPinnedToCore(MyTSFC_Timer_Task, "MyTSFC_Timer_Task", MyTSFC_Timer_Task_task_stack_size, NULL, MyTSFC_Timer_Task_priority, p_task_stack, p_task_data, MyTSFC_Timer_Task_COREID)!=p_task_data)
			{
				ESP_LOGE("MyTSFC_Init()", "creat MyTSFC_Timer_Task use xTaskCreateStaticPinnedToCore() error\n");
                return 1;
			}
        }else return 1;
    }else return 1;
	#elif MyTSFC_Timer_TASK_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
	ret = xTaskCreatePinnedToCore(MyTSFC_Timer_Task, "MyTSFC_Timer_Task", MyTSFC_Timer_Task_task_stack_size, NULL, MyTSFC_Timer_Task_priority, &MyTSFC_Timer_TaskHandle, MyTSFC_Timer_Task_COREID);
	if(ret!=pdPASS)
	{
		ESP_LOGE("MyTSFC_Init()", "creat MyTSFC_Timer_Task use xTaskCreatePinnedToCore() error=%d\n", ret);
        return 1;
	}
	#endif

    my_tsfc_info.tsfc_work_state = TSFC_WORK_STATE_OK;

    return 0;
}

#endif