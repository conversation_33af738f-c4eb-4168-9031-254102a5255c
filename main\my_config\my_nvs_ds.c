#include "my_nvs_ds.h"

#include <esp_log.h>

#include <stdio.h>
#include <string.h>
#include <stdlib.h>


#define entity_end_reserve_size 1


int MyNvs_D2Link_Append_Item(my_nvs_d2link_list_t* p_list,\
                            int id,\
                            int saving_to_nvs,\
                            char* form_name,\
                            char* key_name,\
                            void* p_data,\
                            uint8_t p_data_type,\
                            uint64_t data_uint64,\
                            uint32_t data_len,\
                            char* service_name)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_Append_Item():", "p_list==NULL");
        return -1;
    }
    my_nvs_d2link_list_item_t* newitem = NULL;
    newitem = (my_nvs_d2link_list_item_t*)calloc(1, sizeof(my_nvs_d2link_list_item_t));
    int service_name_len = 0;
    my_nvs_d2link_list_item_t* item_index = p_list->first_item;

    #if MY_NVS_D2LINK_LIST_LIST_NAME_USED
    // printf("My_D2Link_Append_Item(): append item to %s\n", p_list->list_name);

    if(service_name!=NULL)
    {
        // ESP_LOGI("My_D2Link_Append_Item()", "add item: id=%d, service_name=%s", id, service_name);
    }
    else
    {
        // ESP_LOGI("My_D2Link_Append_Item()", "add item: id=%d, service_name=NULL", id);
    }
    #endif
    
    if(p_list->list_item_num>0)
    {
        do
        {
            if(item_index->id==id)
            {
                break;
            }
            if(item_index->next_item!=NULL)
            {
                item_index = item_index->next_item;
            }
        } while (item_index!=p_list->end_item);
        if(item_index->id==id)
        {
            if(newitem!=NULL)free(newitem);
            // ESP_LOGI("My_D2Link_Append_Item()", "add item failed,1(already exists)\n");
            return 1;
        }
    }

    if(newitem!=NULL)
    {
        #if MY_NVS_D2LINK_LIST_ITEM_ITEM_NAME_USED
        if(service_name!=NULL)
        {
            service_name_len = strlen(service_name);
            if(service_name_len>31)
            {
                service_name_len = 31;
            }
            memcpy(newitem->item_name, service_name, service_name_len);
        }
        #endif

        newitem->id = id;
        //------------------------------------
        newitem->saving_to_nvs = saving_to_nvs;
        if(p_data!=NULL)
        {
            newitem->data_type = 1;
            newitem->p_data_type = p_data_type;
            if(!p_data_type)
            {
                newitem->p_data = p_data;
            }
            else
            {
                newitem->p_data = calloc(1, data_len+entity_end_reserve_size);
                if(newitem->p_data!=NULL)
                {
                    memcpy(newitem->p_data, p_data, data_len);
                }
            }
        }
        else
        {
            newitem->data_type = 0;
            newitem->data_uint64 = data_uint64;
        }

        if(form_name!=NULL)
        {
            int form_name_len = strlen(form_name);
            if(form_name_len<sizeof(newitem->form_name))
            {
                memcpy(newitem->form_name, form_name, form_name_len);
                newitem->form_name[form_name_len] = 0;
            }
            else
            {
                memcpy(newitem->form_name, form_name, sizeof(newitem->form_name)-1);
                newitem->form_name[sizeof(newitem->form_name)-1] = 0;
            }
            
        }
        if(key_name!=NULL)
        {
            int key_name_len = strlen(key_name);
            if(key_name_len<sizeof(newitem->key_name))
            {
                memcpy(newitem->key_name, key_name, key_name_len);
                newitem->key_name[key_name_len] = 0;
            }
            else
            {
                memcpy(newitem->key_name, key_name, sizeof(newitem->key_name)-1);
                newitem->key_name[sizeof(newitem->key_name)-1] = 0;
            }
        }
        newitem->data_len = data_len;
        p_list->list_data_len += data_len;
        //------------------------------------

        newitem->next_item = NULL;
        if(p_list->list_item_num==0)
        {
            p_list->first_item = newitem;
            newitem->pri_item = NULL;
            newitem->next_item = NULL;
        }
        else
        {
            p_list->end_item->next_item = newitem;
            newitem->pri_item = p_list->end_item;
            newitem->next_item = NULL;
        }
        p_list->end_item = newitem;
        p_list->list_item_num++;
        // ESP_LOGI("My_D2Link_Append_Item()", "add item succeed, p_list->list_item_num=%d\n", p_list->list_item_num);
        return 0;
    }
    else
    {
        // ESP_LOGI("My_D2Link_Append_Item()", "add item failed,3\n");
        return 1;
    }
    return 0;
}

int MyNvs_D2Link_Delete_Item(my_nvs_d2link_list_t* p_list, int id)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_Delete_Item():", "p_list==NULL");
        return -1;
    }
    ESP_LOGI("My_D2Link_Delete_Item()", "delete item from p_list(%p)", p_list);
    if(p_list->list_item_num==0)
    {
        ESP_LOGI("My_D2Link_Delete_Item()", "error, p_list already empty\n");
        return 1;
    }

    #if MY_NVS_D2LINK_LIST_LIST_NAME_USED
    // printf("My_D2Link_Delete_Item(): delete item from %s\n", p_list->list_name);
    #endif

    my_nvs_d2link_list_item_t* item_index = p_list->first_item;
    do
    {
        if(item_index->id==id)
        {
            break;
        }
        if(item_index->next_item!=NULL)
        {
            item_index = item_index->next_item;
        }
    } while (item_index!=p_list->end_item);
    if(item_index->id==id)
    {
        #if MY_NVS_D2LINK_LIST_ITEM_ITEM_NAME_USED
        if(item_index->item_name!=NULL)
        {
            ESP_LOGI("My_D2Link_Delete_Item()", "delete id=%d, service_name=%s", item_index->id, item_index->item_name);
        }
        else
        {
            ESP_LOGI("My_D2Link_Delete_Item()", "delete id=%d, service_name=NULL", item_index->id);
        }
        #endif
        if((item_index!=p_list->first_item)&&(item_index!=p_list->end_item))
        {
            item_index->pri_item->next_item = item_index->next_item;
            item_index->next_item->pri_item = item_index->pri_item;
        }
        else
        {
            if(item_index==p_list->first_item)
            {
                p_list->first_item = item_index->next_item;
            }
            if(item_index==p_list->end_item)
            {
                p_list->end_item = item_index->pri_item;
                if(p_list->end_item!=NULL)
                {
                    p_list->end_item->next_item = NULL;
                }
            }
        }

        p_list->list_data_len -= item_index->data_len;

        //---------------------------------------------
        if(item_index->p_data_type)
        {
            if(item_index->p_data!=NULL)
            {
                free(item_index->p_data);
            }
        }
        //---------------------------------------------
        
        free(item_index);
        if(p_list->list_item_num>0)
        {
            p_list->list_item_num--;
        }
        return 0;
    }
    else
    {
        ESP_LOGI("My_D2Link_Delete_Item()", "the item to delete was not found\n");
        return 1;
    }
    return 0;
}

int MyNvs_D2Link_Insert_Item(my_nvs_d2link_list_t* p_list,\
                            int insert_id,\
                            int insert_direct,\
                            int id,\
                            int saving_to_nvs,\
                            char* form_name,\
                            char* key_name,\
                            void* p_data,\
                            uint8_t p_data_type,\
                            uint64_t data_uint64,\
                            uint32_t data_len,\
                            char* service_name)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_Insert_Item():", "p_list==NULL");
        return -1;
    }
    my_nvs_d2link_list_item_t* newitem = NULL;
    newitem = (my_nvs_d2link_list_item_t*)calloc(1, sizeof(my_nvs_d2link_list_item_t));
    int service_name_len = 0;
    my_nvs_d2link_list_item_t* item_index = p_list->first_item;

    #if MY_NVS_D2LINK_LIST_LIST_NAME_USED
    // printf("My_D2Link_Insert_Item(): insert item to %s\n", p_list->list_name);

    if(service_name!=NULL)
    {
        // ESP_LOGI("My_D2Link_Insert_Item()", "insert_id=%d, insert_direct=%d, insert item: id=%d, service_name=%s", insert_id, insert_direct, id, service_name);
    }
    else
    {
        // ESP_LOGI("My_D2Link_Insert_Item()", "insert_id=%d, insert_direct=%d, insert item: id=%d, service_name=NULL", insert_id, insert_direct, id);
    }
    #endif
    
    if(p_list->list_item_num>0)
    {
        do
        {
            if(item_index->id==id)
            {
                break;
            }
            if(item_index->next_item!=NULL)
            {
                item_index = item_index->next_item;
            }
        } while (item_index!=p_list->end_item);
        if(item_index->id==id)
        {
            if(newitem!=NULL)free(newitem);
            // ESP_LOGI("My_D2Link_Insert_Item()", "insert item failed,1(already exists)\n");
            return 1;
        }
    }

    if(newitem!=NULL)
    {
        #if MY_NVS_D2LINK_LIST_ITEM_ITEM_NAME_USED
        if(service_name!=NULL)
        {
            service_name_len = strlen(service_name);
            if(service_name_len>31)
            {
                service_name_len = 31;
            }
            memcpy(newitem->item_name, service_name, service_name_len);
        }
        #endif

        newitem->id = id;
        //------------------------------------
        newitem->saving_to_nvs = saving_to_nvs;
        if(p_data!=NULL)
        {
            newitem->data_type = 1;
            newitem->p_data_type = p_data_type;

            if(!p_data_type)
            {
                newitem->p_data = p_data;
            }
            else
            {
                newitem->p_data = calloc(1, data_len+entity_end_reserve_size);
                if(newitem->p_data!=NULL)
                {
                    memcpy(newitem->p_data, p_data, data_len);
                }
            }
        }
        else
        {
            newitem->data_type = 0;
            newitem->data_uint64 = data_uint64;
        }
        if(form_name!=NULL)
        {
            int form_name_len = strlen(form_name);
            if(form_name_len<sizeof(newitem->form_name))
            {
                memcpy(newitem->form_name, form_name, form_name_len);
                newitem->form_name[form_name_len] = 0;
            }
            else
            {
                memcpy(newitem->form_name, form_name, sizeof(newitem->form_name)-1);
                newitem->form_name[sizeof(newitem->form_name)-1] = 0;
            }
            
        }
        if(key_name!=NULL)
        {
            int key_name_len = strlen(key_name);
            if(key_name_len<sizeof(newitem->key_name))
            {
                memcpy(newitem->key_name, key_name, key_name_len);
                newitem->key_name[key_name_len] = 0;
            }
            else
            {
                memcpy(newitem->key_name, key_name, sizeof(newitem->key_name)-1);
                newitem->key_name[sizeof(newitem->key_name)-1] = 0;
            }
        }
        newitem->data_len = data_len;
        p_list->list_data_len += data_len;
        //------------------------------------

        //newitem->next_item = NULL;
        if(p_list->list_item_num==0)//链表为空
        {
            p_list->first_item = newitem;
            newitem->pri_item = NULL;
            newitem->next_item = NULL;
            p_list->end_item = newitem;
        }
        else//链表不为空
        {
            if(insert_id==-1)//指定插入到最前面
            {
                newitem->pri_item = NULL;
                newitem->next_item = p_list->first_item;
                p_list->first_item->pri_item = newitem;
                p_list->first_item = newitem;
            }
            else if(insert_id==-2)//指定追加到尾部
            {
                newitem->pri_item = p_list->end_item;
                newitem->next_item = NULL;
                p_list->end_item->next_item = newitem;
                p_list->end_item = newitem;
            }
            else//指定了id
            {
                my_nvs_d2link_list_item_t* find_id_item = NULL;
                find_id_item = p_list->first_item;
                for(int i=0; (i<p_list->list_item_num)&&(find_id_item!=NULL); i++)
                {
                    if(find_id_item->id!=insert_id)
                    {
                        find_id_item = find_id_item->next_item;
                    }
                    else
                    {
                        break;
                    }
                }
                if(find_id_item==NULL)//未找到目标id，追加到尾部
                {
                    newitem->pri_item = p_list->end_item;
                    newitem->next_item = NULL;
                    p_list->end_item->next_item = newitem;
                    p_list->end_item = newitem;
                }
                else//找到了目标id
                {
                    if(insert_direct)//后向插入
                    {
                        if(find_id_item==p_list->end_item)//目标id已经是最后一项
                        {
                            newitem->pri_item = p_list->end_item;
                            newitem->next_item = NULL;
                            p_list->end_item->next_item = newitem;
                            p_list->end_item = newitem;
                        }
                        else//目标id不是最后一项
                        {
                            newitem->pri_item = find_id_item;
                            newitem->next_item = find_id_item->next_item;
                            find_id_item->next_item->pri_item = newitem;
                            find_id_item->next_item = newitem;
                        }
                    }
                    else//前向插入
                    {
                        if(find_id_item==p_list->first_item)//目标id已经是第一项
                        {
                            newitem->pri_item = NULL;
                            newitem->next_item = p_list->first_item;
                            p_list->first_item->pri_item = newitem;
                            p_list->first_item = newitem;
                        }
                        else//目标id不是第一项
                        {
                            newitem->pri_item = find_id_item->pri_item;
                            newitem->next_item = find_id_item;
                            find_id_item->pri_item->next_item = newitem;
                            find_id_item->pri_item = newitem;
                        }
                    }
                }
            }
        }
        p_list->list_item_num++;
        // ESP_LOGI("My_D2Link_Insert_Item()", "insert item succeed, p_list->list_item_num=%d\n", p_list->list_item_num);
        return 0;
    }
    else
    {
        ESP_LOGE("My_D2Link_Insert_Item()", "insert item failed,3\n");
        return 1;
    }
    return 0;
}

int MyNvs_D2Link_Delete(my_nvs_d2link_list_t** p_list)
{
    if((*p_list)==NULL)
    {
        ESP_LOGE("My_D2Link_Delete():", "p_list==NULL");
        return -1;
    }

    #if MY_NVS_D2LINK_LIST_LIST_NAME_USED
    // printf("My_D2Link_Delete():delete %s, list_item_num=%d\n", (*p_list)->list_name, (*p_list)->list_item_num);
    #endif

    my_nvs_d2link_list_item_t* p = (*p_list)->first_item;
    my_nvs_d2link_list_item_t* p_next = NULL;
    for(int i=0; (i<(*p_list)->list_item_num)&&(p!=NULL); i++)
    {
        p_next = p->next_item;

        //-----------------------------------
        if(p->p_data_type)
        {
            if(p->p_data!=NULL)
            {
                free(p->p_data);
            }
        }
        //-----------------------------------

        free(p);
        p = p_next;
    }

    free(*p_list);
    *p_list = NULL;
    // printf("\nMy_D2Link_Delete finished\n\n");

    return 0;
}

int MyNvs_D2Link_match_id_count_statistic(my_nvs_d2link_list_t* p_list, int id_begin, int id_end)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_match_id_count_statistic():", "p_list==NULL");
        return -1;
    }
    if(p_list->list_item_num==0)
    {
        // printf("\nMy_D2Link_match_id_count_statistic():list already empty\n");
        return 1;
    }

    #if MY_NVS_D2LINK_LIST_LIST_NAME_USED
    // printf("My_D2Link_match_id_count_statistic(): statistic match id count in %s\n", p_list->list_name);
    #endif

    my_nvs_d2link_list_item_t* p = p_list->first_item;
    int matching_id_counter = 0;
    for(int i=0; (i<p_list->list_item_num)&&(p!=NULL); i++)
    {
        if((p->id>=id_begin)&&(p->id<=id_end))
        {
            matching_id_counter++;
        }
        p = p->next_item;
    }

    // printf("MyNvs_D2Link_match_id_count_statistic():matching_id_counter=%d\n", matching_id_counter);

    return matching_id_counter;
}

int MyNvs_D2Link_Data_Packer(my_nvs_d2link_list_t* p_list, void* saving, uint16_t saving_len)
{
    if(p_list==NULL)
    {
        ESP_LOGE("MyNvs_D2Link_Data_Packer():", "p_list==NULL");
        return -1;
    }
    if(saving==NULL)
    {
        ESP_LOGE("MyNvs_D2Link_Data_Packer():", "saving==NULL");
        return -1;
    }
    if(p_list->list_item_num==0)
    {
        // printf("\nMyNvs_D2Link_Data_Packer_uint64():list already empty\n");
        return 1;
    }

    #if MY_NVS_D2LINK_LIST_LIST_NAME_USED
    // printf("MyNvs_D2Link_Data_Packer(): packer %s\n", p_list->list_name);
    #endif

    my_nvs_d2link_list_item_t* p = p_list->first_item;

    char* p_saving_byte = (char*)saving;
    uint16_t copy_counter = 0;
    uint16_t copy_size = 0;
    for(int i=0; (i<p_list->list_item_num)&&(p!=NULL); i++)
    {
        if((copy_counter+p->data_len)<=saving_len)
        {
            if(p->data_type==0)
            {
                memcpy(p_saving_byte, &p->data_uint64, p->data_len);
            }
            else if(p->data_type==1)
            {
                if(p->p_data!=NULL)
                {
                    memcpy(p_saving_byte, p->p_data, p->data_len);
                }
            }
            p_saving_byte += p->data_len;
            copy_counter++;
            copy_size += p->data_len;
            p = p->next_item;
        }
        else
        {
            ESP_LOGE("MyNvs_D2Link_Data_Packer():", "saving_len not enough");
            return 2;
        }
    }

    // printf("MyNvs_D2Link_Data_Packer():copy_counter=%d, copy_size=%d\n\n", copy_counter, copy_size);

    return 0;
}

void* MyNvs_D2Link_GetItem_ById(my_nvs_d2link_list_t* p_list, int id)
{
    if(p_list==NULL)
    {
        ESP_LOGE("MyNvs_D2Link_GetItem_ById():", "p_list==NULL");
        return NULL;
    }
    #if MY_NVS_D2LINK_LIST_LIST_NAME_USED
    // ESP_LOGI("MyNvs_D2Link_GetItem_ById()", "find item(%d) from p_list %s(%p)", id, p_list->list_name, p_list);
    #endif
    if(p_list->list_item_num==0)
    {
        // ESP_LOGI("MyNvs_D2Link_GetItem_ById()", "error, p_list already empty\n");
        return NULL;
    }

    #if MY_NVS_D2LINK_LIST_LIST_NAME_USED
    // printf("MyNvs_D2Link_GetItem_ById(): get item storage by id in %s\n", p_list->list_name);
    #endif

    my_nvs_d2link_list_item_t* item_index = p_list->first_item;
    do
    {
        if(item_index->id==id)
        {
            break;
        }
        if(item_index->next_item!=NULL)
        {
            item_index = item_index->next_item;
        }
    } while (item_index!=p_list->end_item);
    if(item_index->id==id)
    {
        #if MY_NVS_D2LINK_LIST_ITEM_ITEM_NAME_USED
        if(item_index->item_name!=NULL)
        {
            // ESP_LOGI("MyNvs_D2Link_GetItem_ById()", "find id=%d, service_name=%s", item_index->id, item_index->item_name);
        }
        else
        {
            // ESP_LOGI("MyNvs_D2Link_GetItem_ById()", "find id=%d, service_name=NULL", item_index->id);
        }
        #endif
        
        return item_index;
    }
    else
    {
        // ESP_LOGI("MyNvs_D2Link_GetItem_ById()", "the item to find was not found\n");
        return NULL;
    }
    return NULL;
}

int MyNvs_D2Link_PrintList(my_nvs_d2link_list_t* p_list)
{
    if(p_list==NULL)
    {
        ESP_LOGE("My_D2Link_PrintList():", "p_list==NULL");
        return -1;
    }
    if(p_list->list_item_num==0)
    {
        printf("\nMy_D2Link_PrintList():list already empty\n");
        return 1;
    }

    printf("\n\nMy_D2Link_PrintList---------------------------\n");
    #if MY_NVS_D2LINK_LIST_LIST_NAME_USED
    printf("list_name=%s\n", p_list->list_name);
    #endif
    printf("list_item_num=%d\n", p_list->list_item_num);
    printf("list_data_len=%d\n", p_list->list_data_len);
    printf("\n");

    my_nvs_d2link_list_item_t* p = p_list->first_item;
    for(int i=0; i<p_list->list_item_num; i++)
    {
        printf("p_list[%d].id=%#x(%d)\n", i, p->id, p->id);
        #if MY_NVS_D2LINK_LIST_ITEM_ITEM_NAME_USED
        printf("p_list[%d].item_name=%s\n", i, p->item_name);
        #endif
        printf("p_list[%d].saving_to_nvs=%d\n", i, p->saving_to_nvs);
        printf("p_list[%d].form_name=%s\n", i, p->form_name);
        printf("p_list[%d].key_name=%s\n", i, p->key_name);
        printf("p_list[%d].data_type=%d\n", i, p->data_type);
        printf("p_list[%d].data_len=%d\n", i, p->data_len);
        if(p->data_type==0)
        {
            printf("p_list[%d].data_uint64=%#llx(%lld)\n", i, p->data_uint64, p->data_uint64);
        }
        else if(p->data_type==1)
        {
            printf("p_list[%d].*p_data_type=%d\n", i, p->p_data_type);
            printf("p_list[%d].*p_data(%p)=", i, p->p_data);
            char* p_data_byte = (char*)p->p_data;
            for(int j=0; j<p->data_len; j++)
            {
                printf("%02x ", *(p_data_byte+j));
            }
            printf("\n");
        }
        printf("\n");
        
        p = p->next_item;
    }
    printf("\nMy_D2Link_PrintList---------------------------\n\n");

    return 0;
}

// int My_D2Link_Creat(my_nvs_d2link_list_t* p_list)
// {
//     if(p_list==NULL)
//     {
//         ESP_LOGE("My_D2Link_Creat():", "p_list==NULL");
//         return -1;
//     }
//     memset(p_list, 0, sizeof(my_nvs_d2link_list_t));

//     p_list->first_item = NULL;
//     p_list->end_item = NULL;

//     return 0;
// }

my_nvs_d2link_list_t* MyNvs_D2Link_Creat(int id, char* list_name)
{
    my_nvs_d2link_list_t* new_d2link_list = calloc(1, sizeof(my_nvs_d2link_list_t));

    new_d2link_list->first_item = NULL;
    new_d2link_list->end_item = NULL;
    new_d2link_list->list_item_num = 0;
    new_d2link_list->list_data_len = 0;

    #if MY_NVS_D2LINK_LIST_LIST_NAME_USED
    if(list_name!=NULL)
    {
        if(strlen(list_name)<sizeof(new_d2link_list->list_name))
        {
            memcpy(new_d2link_list->list_name, list_name, strlen(list_name));
        }
        else
        {
            memcpy(new_d2link_list->list_name, list_name, sizeof(new_d2link_list->list_name)-1);
        }
        // printf("My_D2Link_Creat(): creat new d2link list, list_name=%s\n", new_d2link_list->list_name);
    }
    else
    {
        // printf("My_D2Link_Creat(): creat new d2link list, list_name=unnamed\n");
    }
    #endif
    

    return new_d2link_list;
}

int MyNvs_D2Link_CheckItemExistInList_Id(my_nvs_d2link_list_t* p_list, int id)
{
    if(p_list==NULL)
    {
        ESP_LOGE("MyNvs_D2Link_CheckItemExistInList_Id():", "p_list==NULL");
        return -1;
    }

    my_nvs_d2link_list_item_t* p = p_list->first_item;

    for(int i=0; (i<p_list->list_item_num)&&(p!=NULL); i++)
    {
        if(p->id==id)
        {
            return 1;
        }
        p = p->next_item;
    }

    return 0;
}