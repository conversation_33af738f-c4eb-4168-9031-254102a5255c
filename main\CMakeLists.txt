# get IDF version for comparison
set(idf_version "${IDF_VERSION_MAJOR}.${IDF_VERSION_MINOR}")

if(IDF_TARGET STREQUAL "esp32s3")

message(STATUS "IDF_TARGET=${IDF_TARGET}")
    set(esp_idf_path "D:/IndvProg/espressif/v4.4.3/Espressif/frameworks/esp-idf-v4.4.3")

    set(COMPONENT_SRCS

    "my_test/mytest_uart_iot_server.c"
    "my_test/mytest_radar.c"

    "my_app/main.c"
    "my_app/my_esp_chip_info_mng.c"
    "my_app/my_time.c"
    "my_app/my_monitor.c"
    "my_app/my_iot_box.c"
    "my_app/my_workmode.c"
    "my_app/my_led_mng.c"

    "my_components/data_match/kmp/my_kmp.c"

    "my_components/encryption/AES/gmult.c"
    "my_components/my_at_ctrl/my_at_ctrl.c"
    "my_components/my_endian/my_endian.c"
    "my_components/encryption/AES/gmult.c"
    "my_components/encryption/AES/aes_soft.c"
    "my_components/encryption/AES/my_soft_aes.c"
    "my_components/encryption/AES/my_esp_aes.c"

    "my_components/crc/src/crc8.c"
    "my_components/crc/src/crc16.c"
    "my_components/crc/src/crc32.c"
    "my_components/crc/src/crc64.c"
    "my_components/crc/src/crcccitt.c"
    "my_components/crc/src/crcdnp.c"
    "my_components/crc/src/crckrmit.c"
    "my_components/crc/src/crcsick.c"
    "my_components/crc/src/nmea-chk.c"


    "my_peripheral/led.c"
    # "my_peripheral/my_ble.c"
    "my_peripheral/my_gpio.c"
    "my_peripheral/my_ledc.c"
    "my_peripheral/my_lowpower.c"
    "my_peripheral/my_power_mng.c"
    "my_peripheral/my_uart.c"
    "my_peripheral/mywifi.c"
    "my_peripheral/my_sdcard.c"
    "my_peripheral/my_relay.c"

    "my_peripheral/my_radar_uart.c"
    "my_peripheral/my_radar.c"
    "my_peripheral/my_radar_ld2450.c"
    "my_peripheral/my_radar_ld2402.c"

    "my_peripheral/adxl345.c"

    "my_peripheral/my_aux_485_port.c"

    "my_config/my_nvs.c"
    "my_config/my_nvs_ds.c"
    "my_config/my_config_table.c"
    "my_config/my_config.c"


    "my_network/lte/whgm5/my_whgm5.c"
    "my_network/lte/whgm5/my_whgm5_at_table.c"

    "my_network/datatsf/tsfc/my_tsfc.c"
    "my_network/datatsf/asl/my_asl.c"
    "my_network/datatsf/asl/my_asl_event.c"
    "my_network/datatsf/asl/my_ds.c"
    "my_network/datatsf/asl/my_asl_dpu.c"
    "my_network/datatsf/evg_simple/evgspl.c"


    "my_network/wifi/my_tcp_server.c"
    "my_network/wifi/my_tcp_client.c"
    "my_network/wifi/tcp_client_data_analyze.c"

    "my_network/ebyte/e104_bt53c3.c"
    "my_network/ebyte/e104_bt53c3_dual_example.c"
    "my_network/ebyte/e104_bt53c3_sw.c"

    "my_ota/uart_ota.c"
    "my_ota/simple_http_ota.c"
    "my_ota/esp_https_ota.c"

    "blufi/blufi_example_main.c"
    "blufi/blufi_init.c"
    "blufi/blufi_security.c"
    "blufi/my_blufi_cstmdata_analyse.c"

    "my_network/myesp_sw_uart/myesp_sw_uart.c"
    "my_network/myesp_sw_uart/sw_uart_example.c"
    "my_network/myesp_sw_uart/sw_uart_test.c"

    EMBED_TXTFILES ${project_dir}/server_certs/ca_cert.pem
    )

    set(COMPONENT_ADD_INCLUDEDIRS
    ${esp_idf_path}/components/bt/include/esp32s3/include
    ${esp_idf_path}/components/bt/host/bluedroid/api/include/api
    ${esp_idf_path}/components/esp_adc_cal/include
    ${esp_idf_path}/components/driver/include/driver
    ${esp_idf_path}/components/app_update/include
    ${esp_idf_path}/components/esp_http_client/include
    ${esp_idf_path}/components/nghttp/port/include
    ${esp_idf_path}/components/esp_https_ota/include
    ${esp_idf_path}/components/bootloader_support/include
    ${esp_idf_path}/examples/common_components/protocol_examples_common/include
    ${esp_idf_path}/components/soc/esp32s3/include/soc
    ${esp_idf_path}/components/fatfs/vfs
    ${esp_idf_path}/components/fatfs/src
    ${esp_idf_path}/components/wear_levelling/include
    ${esp_idf_path}/components/sdmmc/include
    ${esp_idf_path}/components/esp_system/include
    ${esp_idf_path}/components/mbedtls/mbedtls/include/mbedtls
    ${esp_idf_path}/components/bt/common/api/include/api
    ${esp_idf_path}/components/bt/common/btc/profile/esp/blufi/include
    )

    set(COMPONENT_PRIV_INCLUDEDIRS

    "my_test"

    "include"

    "my_app/include"

    "my_peripheral"

    "my_camera/conversions/private_include"
    "my_camera/driver/include"
    "my_camera/conversions/include"
    my_camera/driver/private_include
            my_camera/sensors/private_include
            my_camera/target/private_include

    "my_network/lte/whgm5"
    "my_network/lte/724ug"
    "my_network/lte/air780eg"
    "my_network/datatsf/tsfc"
    "my_network/datatsf/asl"
    "my_network/include"
    "my_network/wifi/"
    "my_network/ebyte"
    "my_network/datatsf/evg_simple"

    "my_components/crc/include"
    "my_components/encryption/AES"
    "my_components/data_match/kmp"
    "my_components/my_at_ctrl"
    "my_components/my_endian"

    "my_ota"

    "my_config"

    "blufi"

    "my_network/myesp_sw_uart"
    )

    set(COMPONENT_REQUIRES "driver" "esp_adc_cal" "fatfs" "sdmmc" "wear_levelling" "json")

    set(min_version_for_esp_timer "4.2")
        if (idf_version VERSION_GREATER_EQUAL min_version_for_esp_timer)
            list(APPEND COMPONENT_PRIV_REQUIRES esp_timer)
        endif()

    set(COMPONENT_PRIV_REQUIRES "freertos" "nvs_flash" "esp_https_ota")

    register_component()
endif()