#include "my_esp_aes.h"



int my_esp_aes_init_ok = 0;

int My_EspAes_Cipher(mbedtls_aes_context* aes_ctx, uint8_t *in, int in_len, uint8_t *out, int out_len)
{
	if(in==NULL)
	{
		return -1;
	}
	if(out==NULL)
	{
		out = in;
		out_len = in_len;
	}

	if(in_len>0)
	{
		if(out_len>=in_len)
		{
			if(in_len%16)
			{
				return 1;
			}
		}
		else
		{
			return 3;
		}
	}
	else
	{
		return 2;
	}

	if(my_esp_aes_init_ok)
	{
		int data_block_num = in_len/16;

		for(int i=0; i<data_block_num; i++)
		{
			// aes_cipher(in+i*16 /* in */, out+i*16 /* out */, aes_key /* expanded key */);
			mbedtls_aes_crypt_ecb(aes_ctx, MBEDTLS_AES_ENCRYPT, in+i*16, out+i*16);
		}
		return 0;
	}
	else
	{
		return 1;
	}

	return 0;
}



int My_EspAes_InvCipher(mbedtls_aes_context* aes_ctx, uint8_t *in, int in_len, uint8_t *out, int out_len)
{
	if(in==NULL)
	{
		return -1;
	}
	if(out==NULL)
	{
		out = in;
		out_len = in_len;
	}

	if(in_len>0)
	{
		if(out_len>=in_len)
		{
			if(in_len%16)

			{
				return 1;
			}
		}
		else
		{
			return 3;
		}
	}
	else
	{
		return 2;
	}

	if(my_esp_aes_init_ok)
	{
		int data_block_num = in_len/16;

		for(int i=0; i<data_block_num; i++)
		{
			// aes_inv_cipher(in+i*16 /* in */, out+i*16 /* out */, aes_key /* expanded key */);
			mbedtls_aes_crypt_ecb(aes_ctx, MBEDTLS_AES_DECRYPT, in+i*16, out+i*16);
		}
		return 0;
	}
	else
	{
		return 1;
	}

	return 0;
}

void My_EspAes_Init(mbedtls_aes_context* aes_ctx, uint8_t* key, int key_len)
{
    mbedtls_aes_init(aes_ctx);
    mbedtls_aes_setkey_enc(aes_ctx, key, key_len);
    mbedtls_aes_setkey_dec(aes_ctx, key, key_len);

    my_esp_aes_init_ok = 1;
}