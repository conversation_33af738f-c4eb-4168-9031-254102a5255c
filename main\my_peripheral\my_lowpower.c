#include "my_lowpower.h"

#if MY_LOWPOWER_FUNC

#include "my_power_mng.h"
#include "my_nvs.h"
#include "my_esp_chip_info_mng.h"
#include "my_time.h"
// #include "my_iot_box.h"
#include "my_debug.h"

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <time.h>
#include <sys/time.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_sleep.h"
#include "esp_log.h"
#include "driver/uart.h"


#if defined MY_DEBUG_PRINT_FUNC
#define DEBUG_PRINT_LEVEL_my_lowpower_PRINT   MY_DEBUG_PRINT_LEVEL_0
#else
#define DEBUG_PRINT_LEVEL_my_lowpower_PRINT   DEBUG_PRINT_LEVEL_0
#endif



RTC_DATA_ATTR my_lowpower_info_t my_lowpower_info;
my_lowpower_nvs_t my_lowpower_nvs;

void MyLowPower_Info_Init(void)
{
    memset(&my_lowpower_nvs, 0, sizeof(my_lowpower_nvs_t));

	LoadDataFromNvs(LPWR_NVS_FORM_NAME, LPWR_NVS_KEY_NAME, (void*)&my_lowpower_nvs, sizeof(my_lowpower_nvs_t));

	#if DEBUG_PRINT_LEVEL_my_lowpower_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
    printf("MyLowPower_Info_Init():\n");
    printf("my_lowpower_nvs.count=%d\n", my_lowpower_nvs.count);
	printf("my_lowpower_nvs.total_time_s=%d\n", my_lowpower_nvs.total_time_s);
    printf("my_lowpower_nvs.total_time_s=%d\n", my_lowpower_nvs.wakeup_type);
	#endif

    my_lowpower_info.count = my_lowpower_nvs.count;
    my_lowpower_info.total_time_s = my_lowpower_nvs.total_time_s;
    my_lowpower_info.wakeup_type = my_lowpower_nvs.wakeup_type;
}

int SaveMyLowPowerConfig(int reboot)
{
	#if DEBUG_PRINT_LEVEL_my_lowpower_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
    printf("SaveMyLowPowerConfig():saving LPWR configuration\n");
	#endif

    my_lowpower_nvs.count = my_lowpower_info.count;
    my_lowpower_nvs.total_time_s = my_lowpower_info.total_time_s;
    my_lowpower_nvs.wakeup_type = my_lowpower_info.wakeup_type;

    return SaveDataToNvs(LPWR_NVS_FORM_NAME, LPWR_NVS_KEY_NAME, (void*)&my_lowpower_nvs, sizeof(my_lowpower_nvs_t), reboot);
}

static uint8_t MyLowPower_EnterSleep(uint8_t sleep_mode, uint32_t second)
{
    /* Get timestamp before entering sleep */
    int64_t t_before_us = 0;
    int64_t t_after_us = 0;

    if(sleep_mode>MYLOWPOWER_MODE_DEEPSLEEP||second==0)
    {
        return MYLOWPOWER_WAKEUP_TYPE_NO_SLEEP;
    }

    if(sleep_mode==MYLOWPOWER_MODE_LIGHTSLEEP)
    {
        /* Wake up in 2 seconds, or when button is pressed */
        esp_sleep_enable_timer_wakeup(second*1000000);

        ESP_LOGI("MyLowPower_EnterSleep()", "Entering light sleep %ds\n", second);
        /* To make sure the complete line is printed before entering sleep mode,
         * need to wait until UART TX FIFO is empty:
         */
        uart_wait_tx_idle_polling(CONFIG_ESP_CONSOLE_UART_NUM);

        // MyLowPower_Init_BeforeSleep();

        t_before_us = esp_timer_get_time();

        if(gpio_hold_en(44)!=ESP_OK)
        {
            ESP_LOGE("MyLightSleep_Start", "ESP_ERR_NOT_SUPPORTED, GPIO%d\n", 44);
        }

        if(gpio_hold_en(BAK_POWER_CTRL_GPIO_PIN)!=ESP_OK)
        {
            ESP_LOGE("MyLightSleep_Start", "ESP_ERR_NOT_SUPPORTED, GPIO%d\n", BAK_POWER_CTRL_GPIO_PIN);
        }
        
        gpio_deep_sleep_hold_en();

        /* Enter sleep mode */
        if(esp_light_sleep_start()!=ESP_OK)
        {
            return MYLOWPOWER_START_ERROR_START_FAILED;
        }
        /* Execution continues here after wakeup */
        else
        {
            gpio_hold_dis(BAK_POWER_CTRL_GPIO_PIN);
            gpio_hold_dis(38);
            gpio_deep_sleep_hold_dis();

            /* Get timestamp after waking up from sleep */
            t_after_us = esp_timer_get_time();

            // MyLowPower_Init_AfterWakeup();
        }
    }
    else if(sleep_mode==MYLOWPOWER_MODE_DEEPSLEEP)
    {
        t_before_us = esp_timer_get_time();
        if(gpio_hold_en(BAK_POWER_CTRL_GPIO_PIN)!=ESP_OK)
        {
            ESP_LOGE("MyLightSleep_Start", "ESP_ERR_NOT_SUPPORTED, GPIO%d\n", BAK_POWER_CTRL_GPIO_PIN);
        }
        if(gpio_hold_en(38)!=ESP_OK)
        {
            ESP_LOGE("MyLightSleep_Start", "ESP_ERR_NOT_SUPPORTED, GPIO%d\n", BAK_POWER_CTRL_GPIO_PIN);
        }
        gpio_deep_sleep_hold_en();

        ESP_LOGI("MyLowPower_EnterSleep()", "Entering deep sleep %ds\n", second);
        my_lowpower_info.count++;
        my_lowpower_info.total_time_s += second;
        my_lowpower_info.wakeup_type = MYLOWPOWER_WAKEUP_TYPE_TIMER;
        esp_deep_sleep(1000000LL * second);
        my_lowpower_info.count--;
        my_lowpower_info.total_time_s -= second;
        my_lowpower_info.wakeup_type = MYLOWPOWER_WAKEUP_TYPE_OTHER;

        gpio_hold_dis(BAK_POWER_CTRL_GPIO_PIN);
        gpio_hold_dis(38);
        gpio_deep_sleep_hold_dis();
        
        /* Get timestamp after waking up from sleep */
        t_after_us = esp_timer_get_time();
    }

    /* Determine wake up reason */
    const char* wakeup_reason_str = NULL;
    switch (esp_sleep_get_wakeup_cause()) {
        case (ESP_SLEEP_WAKEUP_TIMER):
            wakeup_reason_str = "timer";
            my_lowpower_info.wakeup_type = MYLOWPOWER_WAKEUP_TYPE_TIMER;
            break;
        default:
            wakeup_reason_str = "other";
            my_lowpower_info.wakeup_type = MYLOWPOWER_WAKEUP_TYPE_OTHER;
            break;
    }

    my_lowpower_info.count++;
    my_lowpower_info.total_time_s += second;

    ESP_LOGI("MyLowPower_EnterSleep()", "Returned from light sleep, reason: %s, slept for %lld ms\n",
            wakeup_reason_str, (t_after_us - t_before_us) / 1000);

    ESP_LOGI("MyLowPower_EnterSleep()", "count=%d, total_time_s=%d\n", my_lowpower_info.count, my_lowpower_info.total_time_s);
    
    return my_lowpower_info.wakeup_type;
}

uint32_t MyLowPower_GetSleepTotalTime(void)
{
    return my_lowpower_info.total_time_s;
}

uint32_t MyLowPower_GetSleepTotalCount(void)
{
    return my_lowpower_info.count;
}

uint8_t MyLowPower_EnterLightSleepFor(uint32_t second)
{
    return MyLowPower_EnterSleep(MYLOWPOWER_MODE_LIGHTSLEEP, second);
}

static uint8_t MyLowPower_EnterSleepUntil(uint8_t sleep_mode, time_t wakeup_time)
{
    int second = wakeup_time - MyTime_GetTime();
    if(second>0)
    {
        return MyLowPower_EnterSleep(sleep_mode, second);
    }
    else
    {
        ESP_LOGE("MyLowPower_EnterLightSleepUntil()", "MYLOWPOWER_START_ERROR_INVALID_ARG\n");
        return MYLOWPOWER_START_ERROR_INVALID_ARG;
    }
}

uint8_t MyLowPower_EnterLightSleepUntil(time_t wakeup_time)
{
    return MyLowPower_EnterSleepUntil(MYLOWPOWER_MODE_LIGHTSLEEP, wakeup_time);
}

uint8_t MyLowPower_EnterDeepSleepFor(uint32_t second)
{
    return MyLowPower_EnterSleep(MYLOWPOWER_MODE_DEEPSLEEP, second);
}

uint8_t MyLowPower_EnterDeepSleepUntil(time_t wakeup_time)
{
    return MyLowPower_EnterSleepUntil(MYLOWPOWER_MODE_DEEPSLEEP, wakeup_time);
}

#if MY_LOWPOWER_FUNC_DEBUG
void MyDebug_PrintLowPowerInfo(char* name)
{
    ESP_LOGI("\n\nMyDebug_PrintLowPowerInfo()", "---------------------------------\n%s\n\n", name);

	ESP_LOGI("MyDebug_PrintLowPowerInfo()", "count: %d\n", my_lowpower_info.count);
	ESP_LOGI("MyDebug_PrintLowPowerInfo()", "total_time_s: %ds\n", my_lowpower_info.total_time_s);
    if(my_lowpower_info.wakeup_type==MYLOWPOWER_WAKEUP_TYPE_TIMER)
    {
        ESP_LOGI("MyDebug_PrintLowPowerInfo()", "wakeup_type: timer\n");
    }
    else if(my_lowpower_info.wakeup_type==MYLOWPOWER_WAKEUP_TYPE_PIN)
    {
        ESP_LOGI("MyDebug_PrintLowPowerInfo()", "wakeup_type: pin\n");
    }
    else
    {
        ESP_LOGI("MyDebug_PrintLowPowerInfo()", "wakeup_type: other\n");
    }
	ESP_LOGI("\n\nMyDebug_PrintLowPowerInfo()", "---------------------------------\n\n");
}
#endif

#endif