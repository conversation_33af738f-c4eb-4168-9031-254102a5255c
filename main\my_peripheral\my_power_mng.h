#ifndef MY_POWER_MNG_H
#define MY_POWER_MNG_H

#include "my_config.h"

#define MY_POWER_MNG_FUNC 1

#if MY_POWER_MNG_FUNC

#include <lwip/netdb.h>

#define MY_POWER_MNG_FUNC_DEBUG 1

#define MyPowerMng_TASK_CREATE_TYPE    TASK_CREATE_TYPE_DYNAMIC

#if MyPowerMng_TASK_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
#define MyPowerMng_Task_COREID    1
#define MyPowerMng_Task_priority  5
#define MyPowerMng_Task_task_stack_size   4096
#elif MyPowerMng_TASK_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
#define MyPowerMng_Task_COREID    1
#define MyPowerMng_Task_priority  5
#define MyPowerMng_Task_task_stack_size   2048
#endif


#define POWERCHECK_ADC  1
#define POWERCHECK_IO   2
#define POWERCHECK_TYPE POWERCHECK_IO

#define CheckPowerTask_COREID    1
#define CheckPowerTask_priority  8
#define CheckPowerTask_task_stack_size   4096

#define MyPowerMng_Task_refresh_time 500//ms


#define BAK_POWER_CTRL_GPIO_PIN  40
#define BAK_POWER_CTRL_GPIO_PIN_SEL  ( 1ULL<<BAK_POWER_CTRL_GPIO_PIN )

#define EXTERNAL_POWER_SUPPLY   1
#define EXTERNAL_POWER_NO_SUPPLY    (!EXTERNAL_POWER_SUPPLY)

#define BAK_POWER_READY 1
#define BAK_POWER_NO_READY  (!BAK_POWER_READY)

#define POWER_SOURCE_EXTERNAL   1
#define POWER_SOURCE_BAK        2
typedef struct
{
    uint8_t power_source;

    uint8_t external_power_state;
    uint8_t external_power_connected;
    uint8_t bak_power_state;
    uint8_t bak_power_connected;
    uint8_t whgm5_power_state;
    uint8_t whgm5_power_connected;
    uint8_t radar_power_state;
    uint8_t radar_power_connected;
}my_power_mng_info_t;


void MyPowerMngInit(void);

int GetPower(void);
int PowerCheckGet_InitState(void);




void MyPowerMng_MyWHGM5_PowerMngGpioInit(void);
void MyPowerMng_MyWHGM5_PowerON(void);
void MyPowerMng_MyWHGM5_PowerOFF(void);

void MyPowerMng_MobilePower_OFF(void);
void MyPowerMng_MobilePower_ON(void);

void BAKPWR_ON(void);
void BAKPWR_OFF(void);

/**
 * @brief 激活外部移动电源
 * @return 激活成功:1, 失败:0
 */
uint8_t MyPowerMng_SwitchToExternalPWR(void);

uint8_t MyPowerMng_GetExternalPowerCon(void);
uint8_t MyPowerMng_GetExternalPowerState(void);

uint8_t MyPowerMng_GetBakPowerCon(void);
uint8_t MyPowerMng_GetBakPowerState(void);

uint8_t MyPowerMng_GetMyWHGM5PowerCon(void);
uint8_t MyPowerMng_GetMyWHGM5PowerState(void);

#endif

#endif