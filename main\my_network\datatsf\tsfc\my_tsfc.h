#ifndef MY_TSFC_H
#define MY_TSFC_H


#define MY_TSFC_FUNC  1

#if MY_TSFC_FUNC

#include <lwip/netdb.h>

#include "my_config.h"




#define MY_TSFC_FUNC_DEBUG 1

#define MyTSFC_Send_TASK_CREATE_TYPE    TASK_CREATE_TYPE_DYNAMIC

#if MyTSFC_Send_TASK_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
#define MyTSFC_Send_Task_COREID    1
#define MyTSFC_Send_Task_priority  5
#define MyTSFC_Send_Task_task_stack_size   4096
#elif MyTSFC_Send_TASK_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
#define MyTSFC_Send_Task_COREID    1
#define MyTSFC_Send_Task_priority  15
#define MyTSFC_Send_Task_task_stack_size   4096
#endif

#define MyTSFC_Rcv_TASK_CREATE_TYPE    TASK_CREATE_TYPE_DYNAMIC

#if MyTSFC_Rcv_TASK_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
#define MyTSFC_Rcv_Task_COREID    1
#define MyTSFC_Rcv_Task_priority  5
#define MyTSFC_Rcv_Task_task_stack_size   4096
#elif MyTSFC_Rcv_TASK_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
#define MyTSFC_Rcv_Task_COREID    1
#define MyTSFC_Rcv_Task_priority  18
#define MyTSFC_Rcv_Task_task_stack_size   4096
#endif

#define MyTSFC_Timer_TASK_CREATE_TYPE    TASK_CREATE_TYPE_DYNAMIC

#if MyTSFC_Timer_TASK_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
#define MyTSFC_Timer_Task_COREID    1
#define MyTSFC_Timer_Task_priority  5
#define MyTSFC_Timer_Task_task_stack_size   4096
#elif MyTSFC_Timer_TASK_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
#define MyTSFC_Timer_Task_COREID    1
#define MyTSFC_Timer_Task_priority  18
#define MyTSFC_Timer_Task_task_stack_size   3072
#endif




#define MY_TSFC_PD   1


typedef struct
{
    uint8_t type;
    uint8_t pd;
}my_tsf_frame_head_t;

#define TSFC_FRAME_DATA_SIZE_MAX (WHGM5_DATA_PACK_SIZE-16)
typedef struct
{
    my_tsf_frame_head_t head;
    uint8_t sn;
    uint8_t reserved;
    uint16_t app_id;
    uint16_t data_len;
    char data[TSFC_FRAME_DATA_SIZE_MAX];
    uint32_t crc;
    uint32_t tail;
}my_tsf_dataframe_t;
#define MY_TSF_DATAFRAME_TAIL   "GOOD"

typedef my_tsf_dataframe_t my_tsf_msgframe_t;

typedef struct
{
    my_tsf_frame_head_t head;
    uint8_t ack_sn;
    uint8_t ack_code;
    uint32_t crc;
    uint32_t tail;
}my_tsf_respframe_t;

typedef struct
{
    my_tsf_frame_head_t head;
    uint8_t sn;
    uint8_t reserved;
    uint32_t crc;
    uint32_t tail;
}my_tsf_checkcnctframe_t;

typedef enum
{
    msg_frame=0x01,
    data_frame=0x02,
    ack_frame=0x03,
    cd_frame=0x04,

    my_tsf_frametype_min=msg_frame,
    my_tsf_frametype_max=cd_frame
}my_tsf_frametype_t;

typedef enum
{
    tsf_ackcode_success=0x00,                   //成功接收和处理
    tsf_ackcode_fail=0x01,                      //未知异常
    tsf_ackcode_frametype_unrecognized=0x10,    //未识别的帧类型
    tsf_ackcode_pd_unsupported=0x11,            //协议号版本不支持
    tsf_ackcode_appid_unrecognized=0x12,        //未识别的appid
    tsf_ackcode_insufficient_len=0x13,          //数据块长度不足
    tsf_ackcode_decrypt_error=0x14,             //数据块解码异常
    tsf_ackcode_crc_check_error=0x15,           //crc校验异常
    tsf_ackcode_missing_frame_tailflag=0x16     //缺失帧尾标识符
}my_tsf_ackcode_t;

typedef enum
{
    tsf_win_fps_idle=0x01,
    tsf_win_fps_wait_send=0x02,
    tsf_win_fps_send_noack=0x03,
    tsf_win_fps_send_andack=0x04,
    tsf_win_fps_timeout_repeat=0x05,
    tsf_win_fps_timeout_sendfailed=0x06,
}my_tsf_framestate_t;

#define MY_TSFC_WINDOWS_SIZE    8
typedef struct
{
    my_tsf_frametype_t frame_type;
    uint8_t file_sn;//暂未使用
    uint8_t msg_sn;//暂未使用
    uint8_t frame_sn;
    uint8_t frame_position;//一帧数据在窗口中的位置，0-7
    my_tsf_framestate_t state;
    uint64_t start_timestamp;
    uint8_t repeat_count;
    uint64_t timeout_time;
    uint8_t id;
    uint32_t data_len;
    my_tsf_dataframe_t* data;                 //传输控制层一帧数据
}my_tsfc_windows_t;
#define MY_TSFC_REPEAT_ULIMIT   3

#define TSFC_WORK_STATE_INVALID     0
#define TSFC_WORK_STATE_OK          1
#define TSFC_WORK_STATE_PENDING     2
typedef struct
{
    uint16_t app_id;
    uint8_t tsfc_work_state;
    void* aes_ctx;
}my_tsfc_info_t;


typedef struct
{
    uint32_t data_count_total;          //总数据量，单位：字节
    uint32_t frame_count_total;         //总帧数
    uint32_t frame_count_valid;         //总有效帧数

    uint32_t frame_count_type_msg;          //总消息帧数量
    uint32_t frame_count_type_msg_valid;    //有效消息帧数量
    uint32_t frame_count_type_data;         //总数据帧数量
    uint32_t frame_count_type_data_valid;   //有效据帧数量
    uint32_t frame_count_type_ack;          //总应答帧数量
    uint32_t frame_count_type_cc;           //总连接检测帧数量
}my_tsfc_frame_data_statistic_t;

typedef struct
{
    uint32_t sn_repeat_error_count;
    uint32_t restart_lte_count;
}my_tsfc_error_statistic_t;

typedef struct
{
    my_tsfc_frame_data_statistic_t up;
    my_tsfc_frame_data_statistic_t down;
    my_tsfc_error_statistic_t error;
}my_tsfc_statistic_t;


// typedef struct
// {
//     uint8_t sn_index[MY_TSFC_WINDOWS_SIZE];
//     uint8_t 
// }my_tsfc_windows_t;



/**
 * @brief 应用服务层调用此函数向应用服务层发送一个应用层原始数据
 * @param type 数据类型，参见“三旺奇智IoT设备通信协议设计4.3.2.2”
 * @param sn 应用层文件号或消息编号
 * @param data 应用层原始数据
 * @param len data数据大小
 * @param timeout 队列超时，单位ms
 * @return 0成功入列，1入列失败
 */
uint8_t MyTSFC_Send(my_tsf_frametype_t type, uint8_t sn, char* data, uint32_t len, uint32_t timeout, uint8_t id);

#define MY_TSFC_QUEUE_LEN WHGM5_QUEUE_LEN
typedef struct
{
    my_tsf_frametype_t my_tsfc_data_type;
    uint8_t sn;
    uint8_t id;
	uint8_t valid;
	uint64_t time_stamp;
    uint32_t len;
	char* data;                 //应用服务层数据
}my_tsfc_queue_t;

/**
 * @brief 从TSFC接收数据
 * @param saving 数据指针
 * @param timeout 接收超时，单位ms
 * @return 0成功，1失败，2 saving为空
 */
uint8_t MyTSFC_RcvData(my_tsfc_queue_t** const saving, uint32_t timeout);


my_tsfc_statistic_t* MyTSFC_GetStatistics(void);

uint8_t MyTSFC_Init(uint16_t app_id, void* aes_ctx);

int MyTSFC_Get_Workstate(void);




#if MY_TSFC_FUNC_DEBUG
//-----------------------------------------------------
//debug
//-----------------------------------------------------
#endif

#endif
#endif