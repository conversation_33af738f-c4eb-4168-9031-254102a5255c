#ifndef E104_BT53C3_SW_H
#define E104_BT53C3_SW_H

#include <stdint.h>
#include <stdbool.h>
#include "driver/gpio.h"

#ifdef __cplusplus
extern "C" {
#endif

// 第二个E104模组的功能开关
#define E104_BT53C3_SW_ENABLE 1

#if E104_BT53C3_SW_ENABLE

// 第二个E104模组的状态变量
extern int e104_sw_role;
extern int e104_sw_adv_interval;
extern int e104_sw_adv_power;
extern int e104_sw_long_range;
extern char e104_sw_mac[32];
extern bool e104_sw_mac_got;
extern bool e104_sw_bt53c3_init_ok;
extern bool e104_sw_bt53c3_chip1_get_mac_ok;
extern char* e104_sw_chip1_mac;
extern char* e104_sw_chip1_mac_no_colons;
extern char* e104_sw_chip2_mac;
extern char* e104_sw_chip2_mac_no_colons;

/**
 * @brief 初始化第二个E104-BT53C3模组 (使用软串口)
 * 
 * @param tx_pin TX引脚号
 * @param rx_pin RX引脚号
 * @param rst_pin 复位引脚号
 * @param mod_pin 模式切换引脚号
 * @return 
 *     - 0: 成功
 *     - -1: 失败
 */
int e104_bt53c3_sw_init(gpio_num_t tx_pin, gpio_num_t rx_pin, gpio_num_t rst_pin, gpio_num_t mod_pin);

/**
 * @brief 重新初始化第二个E104模组
 * 
 * @return 
 *     - 0: 成功
 *     - -1: 失败
 */
int e104_bt53c3_sw_reinit(void);

/**
 * @brief 复位第二个E104模组
 * 
 * @return 
 *     - 0: 成功
 */
int e104_bt53c3_sw_rst(void);

/**
 * @brief 设置第二个E104模组为命令模式
 * 
 * @return 
 *     - 0: 成功
 */
int e104_bt53c3_sw_mode_cmd(void);

/**
 * @brief 设置第二个E104模组为数据模式
 * 
 * @return 
 *     - 0: 成功
 */
int e104_bt53c3_sw_mode_data(void);

/**
 * @brief 设置第二个E104模组的设备角色
 * 
 * @param role 角色 (0=从机, 1=主机)
 * @return 
 *     - 0: 成功
 */
int e104_bt53c3_sw_set_dev_role(int role);

/**
 * @brief 查询第二个E104模组的设备角色
 * 
 * @return 
 *     - 0: 成功
 */
int e104_bt53c3_sw_query_dev_role(void);

/**
 * @brief 查询第二个E104模组的MAC地址
 * 
 * @return 
 *     - 0: 成功
 */
int e104_bt53c3_sw_query_dev_mac(void);

/**
 * @brief 设置第二个E104模组的广播名称
 * 
 * @param name 广播名称
 * @param len 名称长度
 * @return 
 *     - 0: 成功
 *     - -1: 参数错误
 */
int e104_bt53c3_sw_set_adv_name(char *name, int len);

/**
 * @brief 设置第二个E104模组的广播间隔
 * 
 * @param interval 广播间隔 (单位: 0.625ms)
 * @return 
 *     - 0: 成功
 */
int e104_bt53c3_sw_set_adv_interval(int interval);

/**
 * @brief 查询第二个E104模组的广播间隔
 * 
 * @return 
 *     - 0: 成功
 */
int e104_bt53c3_sw_query_adv_interval(void);

/**
 * @brief 设置第二个E104模组的发射功率
 * 
 * @param power 发射功率
 * @return 
 *     - 0: 成功
 */
int e104_bt53c3_sw_set_power(int power);

/**
 * @brief 查询第二个E104模组的发射功率
 * 
 * @return 
 *     - 0: 成功
 */
int e104_bt53c3_sw_query_power(void);

/**
 * @brief 设置第二个E104模组的广播数据
 * 
 * @param data 广播数据
 * @param len 数据长度
 * @return 
 *     - 0: 成功
 *     - -1: 参数错误
 */
int e104_bt53c3_sw_set_adv_data(char *data, int len);

/**
 * @brief 设置第二个E104模组的长距离模式
 * 
 * @param long_range 是否启用长距离模式
 * @return 
 *     - 0: 成功
 */
int e104_bt53c3_sw_set_long_range(bool long_range);

/**
 * @brief 查询第二个E104模组的连接列表
 * 
 * @return 
 *     - 0: 成功
 */
int e104_bt53c3_sw_query_connect_list(void);

/**
 * @brief 断开第二个E104模组的所有连接
 * 
 * @return 
 *     - 0: 成功
 */
int e104_bt53c3_sw_discon_all_dev(void);

/**
 * @brief 向主机发送数据 (第二个E104模组)
 * 
 * @param data 要发送的数据
 * @param len 数据长度
 * @return 
 *     - 0: 成功
 */
int e104_bt53c3_sw_send_data_to_master(char *data, int len);

#endif // E104_BT53C3_SW_ENABLE

#ifdef __cplusplus
}
#endif

#endif // E104_BT53C3_SW_H
