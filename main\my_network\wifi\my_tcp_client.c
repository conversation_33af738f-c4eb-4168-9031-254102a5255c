#include "my_tcp_client.h"
#if MY_TCP_CLIENT_FUNC

// #include "my_gpio.h"
// #include "esp_camera.h"
// #include "sdkconfig.h"
// #include "my_camera.h"
// #include "mywifi.h"
#include "my_nvs.h"
// #include "my_ble.h"
// #include "my_config.h"
#include "my_time.h"


#include <sys/param.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"
#include "freertos/semphr.h"
#include "esp_system.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "tcpip_adapter.h"
// #include "driver/gpio.h"

#include "lwip/err.h"
#include "lwip/sockets.h"
#include "lwip/sys.h"
#include <lwip/netdb.h>

#include <string.h>




my_tcp_client_info_t my_tcp_client_info;

const uint32_t SOCK_RCV_TIMEOUT = 40;


//SemaphoreHandle_t SendDataToServerMutexSemaphore = NULL;
int My_TcpClient_SendDataToServer(int main_id, const char* data, uint32_t len)
{
    //xSemaphoreTake(SendDataToServerMutexSemaphore, portMAX_DELAY);
    int err = 0;

    if(!my_tcp_client_info.init_ok)
    {
        ESP_LOGE("My_TcpClient_SendDataToServer", "if(!my_tcp_client_info.init_ok)");
        return -1;
    }

    if(!(main_id<my_tcp_socket_list_item_max))
    {
        ESP_LOGE("My_TcpClient_SendDataToServer", "if(!(main_id<my_tcp_socket_list_item_max))");
        return -2;
    }

    if(data==NULL)
    {
        ESP_LOGE("My_TcpClient_SendDataToServer", "if(data==NULL)");
        return -3;
    }

    if(my_tcp_client_info.my_tcp_socket_list[main_id].sock_invalid)
    {
        if(my_tcp_client_info.my_tcp_socket_list[main_id].server_online)
        {
            err = send(my_tcp_client_info.my_tcp_socket_list[main_id].sock_fd, data, len, 0);
            if(err<0)
            {
                ESP_LOGE("My_TcpClient_SendDataToServer", "send data failed, fd=%d, err=%d", my_tcp_client_info.my_tcp_socket_list[main_id].sock_fd, err);
                my_tcp_client_info.my_tcp_socket_list[main_id].server_online = 0;
            }
        }
        else
        {
            ESP_LOGE("My_TcpClient_SendDataToServer", "server_online is false!");
        }
    }
    else
    {
        ESP_LOGE("My_TcpClient_SendDataToServer", "sock_invalid is false!");
    }
    
    //xSemaphoreGive(SendDataToServerMutexSemaphore);

    return err;
}

int My_TcpClient_ReconnectSocket(int main_id);
int My_TcpClient_RecvDataFromServer(int main_id, void* data, int buf_len, int object_len, int timeout)
{
    int recv_len = 0;
    int timeout_cnt = 0;

    if(!(my_tcp_client_info.my_tcp_socket_list[main_id].sock_invalid))
    {
        ESP_LOGE("My_TcpClient_RecvDataFromServer", "sock_invalid is false!");
        return -1;
    }
    if(!my_tcp_client_info.my_tcp_socket_list[main_id].server_online)
    {
        ESP_LOGW("My_TcpClient_RecvDataFromServer", "waiting for serveronline...");
        do
        {
            vTaskDelay(100 / portTICK_PERIOD_MS);
            timeout_cnt+=100;
        } while ((!my_tcp_client_info.my_tcp_socket_list[main_id].server_online)&&(timeout_cnt<timeout));
        
    }
    if(!my_tcp_client_info.my_tcp_socket_list[main_id].server_online)
    {
        ESP_LOGE("My_TcpClient_RecvDataFromServer", "serveronline failed finally!");
        return -2;
    }

    recv_len = 0;
    int recv_err_counter = 0;
    uint32_t recv_err_start_time = 0;
    uint32_t recv_err_end_time = 0;
    do
    {
        if(recv_len<0)
        {
            recv_len = 0;
        }
        recv_len = recv(my_tcp_client_info.my_tcp_socket_list[main_id].sock_fd, (char*)data+recv_len, buf_len-1, 0);
        if(recv_len<0)
        {
            recv_err_counter++;
            if(recv_err_counter==1)
            {
                recv_err_start_time = MyTime_GetTime();
            }
            if(recv_err_counter>=10)
            {
                recv_err_end_time = MyTime_GetTime();

                if(recv_err_end_time - recv_err_start_time <= 10)
                {
                    ESP_LOGI("My_TcpClient_RecvDataFromServer()", "recv exception, break...");
                    break;
                }
                else
                {
                    ESP_LOGI("My_TcpClient_RecvDataFromServer()", "recv timeout, continue recv...");
                    recv_err_counter = 0;
                }
            }
        }
        // ESP_LOGI("My_TcpClient_RecvDataFromServer()", "data(%d)=%s", recv_len, (char*)data);
    }while(recv_len<object_len);

    ESP_LOGI("My_TcpClient_RecvDataFromServer()", "recv_len=%d", recv_len);
    
    if(recv_len<=0)
    {
        my_tcp_client_info.my_tcp_socket_list[main_id].server_online = 0;
    }
    
    return recv_len;
}

extern bool My_Wifi_Get_State(void);

int My_TcpClient_CreateSocket(char* address, int port)
{
    if(My_Wifi_Get_State()!=true)
    {
        ESP_LOGW("My_TcpClient_CreateSocket", "My_Wifi_Get_State()!=true");
        return -1;
    }

    int ret = 0;
    int err = 0;
    int main_id = 0;
    for(; main_id<my_tcp_socket_list_item_max; main_id++)
    {
        if(!my_tcp_client_info.my_tcp_socket_list[main_id].sock_invalid)
        {
            err = 0;
            break;
        }
    }
    if(main_id<my_tcp_socket_list_item_max)
    {
        memcpy(my_tcp_client_info.my_tcp_socket_list[main_id].conf.serveraddr, address, strlen(address));
        my_tcp_client_info.my_tcp_socket_list[main_id].conf.serverport = port;

        my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.addr_family = AF_INET;
        my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.ip_protocol = IPPROTO_IP;
        my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.tv_out.tv_sec = SOCK_RCV_TIMEOUT;
        my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.tv_out.tv_usec = 0;
        my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.dest_addr.sin_addr.s_addr = inet_addr(address);
        my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.dest_addr.sin_family = AF_INET;
        my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.dest_addr.sin_port = htons(port);
        inet_ntoa_r(my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.dest_addr.sin_addr, my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.addr_str, sizeof(my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.addr_str) - 1);
        int tcp_client_sock = -1;
        tcp_client_sock =  socket(my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.addr_family, \
                                    SOCK_STREAM, \
                                    my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.ip_protocol);

        if (tcp_client_sock < 0)
        {
            ESP_LOGE("My_TcpClient_CreateSocket", "Unable to create socket: errno %d", errno);
        }
        else
        {
            setsockopt(tcp_client_sock, SOL_SOCKET, SO_RCVTIMEO, &my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.tv_out, sizeof(my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.tv_out));
        }
        ESP_LOGI("My_TcpClient_CreateSocket", "Socket created, connecting to %s:%d", my_tcp_client_info.my_tcp_socket_list[main_id].conf.serveraddr, my_tcp_client_info.my_tcp_socket_list[main_id].conf.serverport);

        int err = connect(tcp_client_sock, (struct sockaddr *)&my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.dest_addr, sizeof(my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.dest_addr));
        if (err != 0)
        {
            ESP_LOGE("My_TcpClient_CreateSocket", "Socket unable to connect: errno %d", errno);
        }
        else
        {
            my_tcp_client_info.my_tcp_socket_list[main_id].sock_fd = tcp_client_sock;
            ESP_LOGI("My_TcpClient_CreateSocket", "my_tcp_client_info.my_tcp_socket_list[%d].sock_fd=%d", \
            main_id, my_tcp_client_info.my_tcp_socket_list[main_id].sock_fd);
            my_tcp_client_info.my_tcp_socket_list[main_id].server_online = 1;
            my_tcp_client_info.my_tcp_socket_list[main_id].sock_invalid = 1;
            return main_id;
        }

        // memset(&my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf, 0, sizeof(my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf));
        my_tcp_client_info.my_tcp_socket_list[main_id].sock_fd = tcp_client_sock;
        ESP_LOGI("My_TcpClient_CreateSocket", "my_tcp_client_info.my_tcp_socket_list[%d].sock_fd=%d", \
        main_id, my_tcp_client_info.my_tcp_socket_list[main_id].sock_fd);
        my_tcp_client_info.my_tcp_socket_list[main_id].server_online = 0;
        my_tcp_client_info.my_tcp_socket_list[main_id].sock_invalid = 1;
        return main_id;
    }
    else
    {
        ESP_LOGE("My_TcpClient_CreateSocket()", "list up to my_tcp_socket_list_item_max");
        return -1;
    }

    return 0;
}

int My_TcpClient_ReconnectSocket(int main_id)
{
    if(My_Wifi_Get_State()!=true)
    {
        ESP_LOGW("My_TcpClient_ReconnectSocket", "My_Wifi_Get_State()!=true");
        return -1;
    }

    if(!(main_id<my_tcp_socket_list_item_max))
    {
        ESP_LOGW("My_TcpClient_ReconnectSocket", "if(!(main_id<my_tcp_socket_list_item_max))");
        return -2;
    }

    if(!(my_tcp_client_info.my_tcp_socket_list[main_id].sock_invalid))
    {
        ESP_LOGW("My_TcpClient_ReconnectSocket", "if(!(my_tcp_client_info.my_tcp_socket_list[main_id].sock_invalid))");
        return -3;
    }

    int ret = 0;
    int err = 0;

    ESP_LOGI("My_TcpClient_ReconnectSocket", "delete socket %s:%d, fd=%d", \
                my_tcp_client_info.my_tcp_socket_list[main_id].conf.serveraddr, \
                my_tcp_client_info.my_tcp_socket_list[main_id].conf.serverport, \
                my_tcp_client_info.my_tcp_socket_list[main_id].sock_fd);
    shutdown(my_tcp_client_info.my_tcp_socket_list[main_id].sock_fd, SHUT_RDWR);
    close(my_tcp_client_info.my_tcp_socket_list[main_id].sock_fd);

    // my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.addr_family = AF_INET;
    // my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.ip_protocol = IPPROTO_IP;
    // my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.tv_out.tv_sec = SOCK_RCV_TIMEOUT;
    my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.tv_out.tv_usec = 0;
    // my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.dest_addr.sin_addr.s_addr = inet_addr(address);
    // my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.dest_addr.sin_family = AF_INET;
    // my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.dest_addr.sin_port = htons(port);
    // inet_ntoa_r(my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.dest_addr.sin_addr, my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.addr_str, sizeof(my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.addr_str) - 1);
    int tcp_client_sock = -1;
    tcp_client_sock =  socket(my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.addr_family, \
                                SOCK_STREAM, \
                                my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.ip_protocol);

    if (tcp_client_sock < 0)
    {
        ESP_LOGE("My_TcpClient_ReconnectSocket", "Unable to create socket: errno %d", errno);
    }
    else
    {
        setsockopt(tcp_client_sock, SOL_SOCKET, SO_RCVTIMEO, &my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.tv_out, sizeof(my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.tv_out));
    }
    ESP_LOGI("My_TcpClient_ReconnectSocket", "Socket created, connecting to %s:%d", my_tcp_client_info.my_tcp_socket_list[main_id].conf.serveraddr, my_tcp_client_info.my_tcp_socket_list[main_id].conf.serverport);

    err = connect(tcp_client_sock, (struct sockaddr *)&my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.dest_addr, sizeof(my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf.dest_addr));
    if (err != 0)
    {
        ESP_LOGE("My_TcpClient_ReconnectSocket", "Socket unable to connect: errno %d", errno);
    }
    else
    {
        my_tcp_client_info.my_tcp_socket_list[main_id].sock_fd = tcp_client_sock;
        ESP_LOGI("My_TcpClient_ReconnectSocket", "my_tcp_client_info.my_tcp_socket_list[%d].sock_fd=%d", \
        main_id, my_tcp_client_info.my_tcp_socket_list[main_id].sock_fd);
        my_tcp_client_info.my_tcp_socket_list[main_id].server_online = 1;
        my_tcp_client_info.my_tcp_socket_list[main_id].sock_invalid = 1;
        return main_id;
    }

    // memset(&my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf, 0, sizeof(my_tcp_client_info.my_tcp_socket_list[main_id].tcpconf));

    return 0;
}

int My_TcpClient_DeleteSocket(int main_id)
{
    if(My_Wifi_Get_State()!=true)
    {
        ESP_LOGW("My_TcpClient_ReconnectSocket", "My_Wifi_Get_State()!=true");
        return -1;
    }

    if(!(main_id<my_tcp_socket_list_item_max))
    {
        ESP_LOGW("My_TcpClient_ReconnectSocket", "if(!(main_id<my_tcp_socket_list_item_max))");
        return -2;
    }

    if(!(my_tcp_client_info.my_tcp_socket_list[main_id].sock_invalid))
    {
        ESP_LOGW("My_TcpClient_ReconnectSocket", "if(!(my_tcp_client_info.my_tcp_socket_list[main_id].sock_invalid))");
        return -3;
    }

    ESP_LOGI("My_TcpClient_ReconnectSocket", "delete socket %s:%d, fd=%d", \
                my_tcp_client_info.my_tcp_socket_list[main_id].conf.serveraddr, \
                my_tcp_client_info.my_tcp_socket_list[main_id].conf.serverport, \
                my_tcp_client_info.my_tcp_socket_list[main_id].sock_fd);
    shutdown(my_tcp_client_info.my_tcp_socket_list[main_id].sock_fd, SHUT_RDWR);
    close(my_tcp_client_info.my_tcp_socket_list[main_id].sock_fd);

    memset(&my_tcp_client_info.my_tcp_socket_list[main_id], 0, sizeof(my_tcp_client_info.my_tcp_socket_list[main_id]));

    return 0;
}

static xTaskHandle My_TcpCleintTask_Handle = NULL;
static void My_TcpCleintTask(void* arg)
{
    int rcv_len = 0;
    uint32_t notifyValue = 0;

    for(;;)
    {
        if(My_Wifi_Get_State()==true)
        {
            ESP_LOGI("My_TcpCleintTask()", "check socks");
            int i=0;
            for(; i<my_tcp_socket_list_item_max; i++)
            {
                if(my_tcp_client_info.my_tcp_socket_list[i].sock_invalid)
                {
                    if(!my_tcp_client_info.my_tcp_socket_list[i].server_online)
                    {
                        My_TcpClient_ReconnectSocket(i);
                    }
                }
            }
        }
        vTaskDelay(10000/portTICK_PERIOD_MS);
    }
}

int My_TcpClient_Get_state(int main_id)
{
    if(main_id<my_tcp_socket_list_item_max)
    {
        return my_tcp_client_info.my_tcp_socket_list[main_id].server_online;;
    }
    return 0;
}

int My_TcpClient_Init(void)
{
    int ret = 0;
    
    if(!my_tcp_client_info.init_ok)
    {
        memset(&my_tcp_client_info, 0, sizeof(my_tcp_client_conf_t));
        my_tcp_client_info.init_ok = 1;

        memset(my_tcp_client_info.my_tcp_socket_list, 0, sizeof(my_tcp_client_info.my_tcp_socket_list));
        for(int i=0; i<my_tcp_socket_list_item_max; i++)
        {
            my_tcp_client_info.my_tcp_socket_list[i].sock_fd = -1;
        }

        #if My_TcpCleintTask_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
        StackType_t* p_task_stack = NULL;
        StaticTask_t* p_task_data = NULL;

        p_task_stack = calloc(1, My_TcpCleintTask_task_stack_size*sizeof(StackType_t));
        if(p_task_stack!=NULL)
        {
            p_task_data = calloc(1, sizeof(StaticTask_t));
            if(p_task_data!=NULL)
            {
                if(xTaskCreateStaticPinnedToCore(My_TcpCleintTask, "My_TcpCleintTask", My_TcpCleintTask_task_stack_size, NULL, My_TcpCleintTask_priority, p_task_stack, p_task_data, My_TcpCleintTask_COREID)!=p_task_data)
                {
                    ESP_LOGE("MyEspChipInfoMng_Init()", "creat My_TcpCleintTask use xTaskCreateStaticPinnedToCore() error\n");
                    return 1;
                }
            }else return 1;
        }else return 1;
        #elif My_TcpCleintTask_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
        ret = xTaskCreatePinnedToCore(My_TcpCleintTask, "My_TcpCleintTask", My_TcpCleintTask_task_stack_size, NULL, My_TcpCleintTask_priority, &My_TcpCleintTask_Handle, My_TcpCleintTask_COREID);
        if(ret!=pdPASS)
        {
            ESP_LOGE("My_TcpClient_Init()", "creat My_TcpCleintTask use xTaskCreatePinnedToCore() error=%d\n", ret);
            return 1;
        }
        #endif
    }
    
    return 0;
}

#endif