#ifndef MY_AIR780EG_AT_TABLE_H
#define MY_AIR780EG_AT_TABLE_H

#include "my_config.h"
#if NETWORK_TYPE&NETWORK_TYPE_LTE_AIR780EG
#include <lwip/netdb.h>

#include "lte_at_table.h"

extern AT_Config_Routine_t air780eg_UART_conf_routine[2];
extern AT_Config_Routine_t air780eg_UARTFL_conf_routine[2];
extern AT_Config_Routine_t air780eg_UARTFT_conf_routine[2];
extern AT_Config_Routine_t air780eg_SOCKASL_conf_routine[2];
extern AT_Config_Routine_t air780eg_SHORTATM_conf_routine[2];
extern AT_Config_Routine_t air780eg_SOCKRSNUM_conf_routine[2];
extern AT_Config_Routine_t air780eg_SOCKRSTIM_conf_routine[2];
extern AT_Config_Routine_t air780eg_KEEPALIVEA_conf_routine[2];
extern AT_Config_Routine_t air780eg_KEEPALIVEB_conf_routine[2];
extern AT_Config_Routine_t air780eg_KEEPALIVEC_conf_routine[2];
extern AT_Config_Routine_t air780eg_KEEPALIVED_conf_routine[2];
extern AT_Config_Routine_t air780eg_HEARTEN_conf_routine[2];
extern AT_Config_Routine_t air780eg_HEARTTP_conf_routine[2];
extern AT_Config_Routine_t air780eg_HEARTTM_conf_routine[2];
extern AT_Config_Routine_t air780eg_HEARTSORT_conf_routine[2];
extern AT_Config_Routine_t air780eg_NATEN_conf_routine[2];
extern AT_Config_Routine_t air780eg_SDPEN_conf_routine[2];
extern AT_Config_Routine_t air780eg_RSTIM_conf_routine[2];
extern AT_Config_Routine_t air780eg_SOCKA_conf_routine[2];
extern AT_Config_Routine_t air780eg_SOCKAEN_conf_routine[2];
extern AT_Config_Routine_t air780eg_SOCKB_conf_routine[2];
extern AT_Config_Routine_t air780eg_SOCKBEN_conf_routine[2];
extern AT_Config_Routine_t air780eg_SOCKC_conf_routine[2];
extern AT_Config_Routine_t air780eg_SOCKCEN_conf_routine[2];
extern AT_Config_Routine_t air780eg_SOCKD_conf_routine[2];
extern AT_Config_Routine_t air780eg_SOCKDEN_conf_routine[2];
extern AT_Config_Routine_t air780eg_WKMOD_NET_conf_routine[2];
extern AT_Config_Routine_t air780eg_WKMOD_HTTPD_conf_routine[2];
extern AT_Config_Routine_t air780eg_STMSG_conf_routine[2];
extern AT_Config_Routine_t air780eg_WORKMODE_CMD_conf_routine[1];
extern AT_Config_Routine_t air780eg_WORKMODE_DATA_conf_routine[4];
extern AT_Config_Routine_t air780eg_SAVING_conf_routine[1];
extern AT_Config_Routine_t air780eg_RESTART_conf_routine[1];
extern AT_Config_Routine_t air780eg_SIMPLE_CONFIG_conf_routine[7];
extern AT_Config_Routine_t air780eg_GNSS_conf_routine[4];


extern AT_Config_Routine_t lte_config_check_chip_matching[2];
extern AT_Config_Routine_t lte_config_check_networkmode_tcp[2];
extern AT_Config_Routine_t lte_config_check_networkmode_http[2];
extern AT_Config_Routine_t lte_config_check_hearten[2];
extern AT_Config_Routine_t lte_config_check_tcp[6];
extern AT_Config_Routine_t lte_config_check_http[6];
extern AT_Config_Routine_t lte_config_check_http_method_GET[2];
extern AT_Config_Routine_t lte_config_check_http_method_POST[2];
extern AT_Config_Routine_t lte_config_check_saving[1];
extern AT_Config_Routine_t lte_config_check_restart[1];
extern AT_Config_Routine_t lte_config_check_uart[2];
extern AT_Config_Routine_t lte_config_check_http_server[2];
extern AT_Config_Routine_t lte_config_check_http_head[2];
extern AT_Config_Routine_t lte_config_check_http_url[2];
extern AT_Config_Routine_t lte_config_check_http_head_filter_enable[2];
extern AT_Config_Routine_t lte_config_check_http_head_filter_disable[2];
extern AT_Config_Routine_t lte_config_check_http_timeout[2];
extern AT_Config_Routine_t lte_config_check_test[1];

extern AT_Config_Routine_t lte_cmd_mode_routine[3];
extern AT_Config_Routine_t lte_data_mode_routine[1];
extern AT_Config_Routine_t lte_restart_routine[1];

void ATTable_ModifyItem_SOCK_Conf(AT_Config_Routine_t* routinelist, char* address, uint16_t port);
void ATTable_ModifyItem_SOCKEN_Conf(AT_Config_Routine_t* routinelist, uint8_t newstate);
void ATTable_ModifyItem_UartBaudrate(AT_Config_Routine_t* routinelist, int newvalue);
#endif

#endif