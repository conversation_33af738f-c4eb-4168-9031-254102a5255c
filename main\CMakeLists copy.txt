# get IDF version for comparison
set(idf_version "${IDF_VERSION_MAJOR}.${IDF_VERSION_MINOR}")

if(IDF_TARGET STREQUAL "esp32s3")

message(STATUS "IDF_TARGET=${IDF_TARGET}")
    set(esp_idf_path "D:/IndvProg/espressif/v4.4.3/Espressif/frameworks/esp-idf-v4.4.3")

    set(COMPONENT_SRCS

    "my_test/mytest_uart_iot_server.c"

    "my_app/main.c"
    "my_app/my_esp_chip_info_mng.c"
    "my_app/my_time.c"
    "my_app/my_monitor.c"
    "my_app/my_iot_box.c"
    "my_app/my_workmode.c"

    "my_components/data_match/kmp/my_kmp.c"

    "my_components/encryption/AES/gmult.c"
    "my_components/encryption/AES/aes.c"
    "my_components/encryption/AES/my_aes.c"
    "my_components/my_at_ctrl/my_at_ctrl.c"
    "my_components/my_endian/my_endian.c"

    "my_components/crc/src/crc8.c"
    "my_components/crc/src/crc16.c"
    "my_components/crc/src/crc32.c"
    "my_components/crc/src/crc64.c"
    "my_components/crc/src/crcccitt.c"
    "my_components/crc/src/crcdnp.c"
    "my_components/crc/src/crckrmit.c"
    "my_components/crc/src/crcsick.c"
    "my_components/crc/src/nmea-chk.c"


    "my_peripheral/led.c"
    "my_peripheral/my_ble.c"
    "my_peripheral/my_camera.c"
    "my_peripheral/my_gpio.c"
    "my_peripheral/my_ledc.c"
    "my_peripheral/my_lowpower.c"
    "my_peripheral/my_power_mng.c"
    "my_peripheral/my_uart.c"
    "my_peripheral/mywifi.c"


    "my_config/my_nvs.c"
    "my_config/my_nvs_ds.c"
    "my_config/my_config_table.c"
    "my_config/my_config.c"


    "my_network/lte/whgm5/my_whgm5.c"
    "my_network/lte/whgm5/my_whgm5_at_table.c"
    "my_network/lte/724ug/my_724ug.c"
    "my_network/lte/724ug/my_724ug_at_table.c"

    "my_network/datatsf/tsfc/my_tsfc.c"
    "my_network/datatsf/asl/my_asl.c"
    "my_network/datatsf/asl/my_asl_event.c"
    "my_network/datatsf/asl/my_ds.c"
    "my_network/datatsf/asl/my_asl_dpu.c"


    "my_network/wifi/tcp_server.c"
    "my_network/wifi/tcp_client.c"
    "my_network/wifi/tcp_client_data_analyze.c"


    # "my_ota/uart_ota.c"
    "my_ota/simple_http_ota.c"


    "my_camera/conversions/yuv.c"
    "my_camera/conversions/to_jpg.cpp"
    "my_camera/conversions/to_bmp.c"
    "my_camera/conversions/jpge.cpp"
    "my_camera/conversions/esp_jpg_decode.c"

    EMBED_TXTFILES ${project_dir}/server_certs/ca_cert.pem
    )

    set(COMPONENT_ADD_INCLUDEDIRS
    ${esp_idf_path}/components/bt/include/esp32s3/include
    ${esp_idf_path}/components/bt/host/bluedroid/api/include/api
    ${esp_idf_path}/components/esp_adc_cal/include
    ${esp_idf_path}/components/driver/include/driver
    ${esp_idf_path}/components/app_update/include
    ${esp_idf_path}/components/esp_http_client/include
    ${esp_idf_path}/components/nghttp/port/include
    ${esp_idf_path}/components/esp_https_ota/include
    ${esp_idf_path}/components/bootloader_support/include
    ${esp_idf_path}/examples/common_components/protocol_examples_common/include
    ${esp_idf_path}/components/soc/esp32s3/include/soc
    )

    set(COMPONENT_PRIV_INCLUDEDIRS

    "my_test"

    "include"

    "my_app/include"

    "my_peripheral"

    "my_camera/conversions/private_include"
    "my_camera/driver/include"
    "my_camera/conversions/include"

    "my_network/lte/whgm5"
    "my_network/lte/724ug"
    "my_network/datatsf/tsfc"
    "my_network/datatsf/asl"
    "my_network/include"
    "my_network/wifi/"

    "my_components/crc/include"
    "my_components/encryption/AES"
    "my_components/data_match/kmp"
    "my_components/my_at_ctrl"
    "my_components/my_endian"

    "my_ota"

    "my_config"
    )

    set(COMPONENT_REQUIRES "driver" "esp_adc_cal")

    # set driver sources only for supported platforms
    if(IDF_TARGET STREQUAL "esp32" OR IDF_TARGET STREQUAL "esp32s2" OR IDF_TARGET STREQUAL "esp32s3")
        message(STATUS "--!!OK1!!--")
        list(APPEND COMPONENT_SRCS
            my_camera/driver/esp_camera.c
            my_camera/driver/cam_hal.c
            my_camera/driver/sccb.c
            my_camera/driver/sensor.c
            my_camera/sensors/ov2640.c
            my_camera/sensors/ov3660.c
            my_camera/sensors/ov5640.c
            my_camera/sensors/ov7725.c
            my_camera/sensors/ov7670.c
            my_camera/sensors/nt99141.c
            my_camera/sensors/gc0308.c
            my_camera/sensors/gc2145.c
            my_camera/sensors/gc032a.c
            my_camera/sensors/bf3005.c
            my_camera/sensors/bf20a6.c
            my_camera/sensors/sc101iot.c
            my_camera/sensors/sc030iot.c
            my_camera/sensors/sc031gs.c
            )

        list(APPEND COMPONENT_PRIV_INCLUDEDIRS
            my_camera/driver/private_include
            my_camera/sensors/private_include
            my_camera/target/private_include
            )

        if(IDF_TARGET STREQUAL "esp32")
            list(APPEND COMPONENT_SRCS
                my_camera/target/xclk.c
                my_camera/target/esp32/ll_cam.c
            )
        endif()

        if(IDF_TARGET STREQUAL "esp32s2")
            list(APPEND COMPONENT_SRCS
                my_camera/target/xclk.c
                my_camera/target/esp32s2/ll_cam.c
                my_camera/target/esp32s2/tjpgd.c
            )

            list(APPEND COMPONENT_PRIV_INCLUDEDIRS
                my_camera/target/esp32s2/private_include
            )
        endif()

        if(IDF_TARGET STREQUAL "esp32s3")
            list(APPEND COMPONENT_SRCS
                my_camera/target/esp32s3/ll_cam.c
            )
        endif()

        #   set(COMPONENT_PRIV_REQUIRES freertos nvs_flash)

        set(min_version_for_esp_timer "4.2")
        if (idf_version VERSION_GREATER_EQUAL min_version_for_esp_timer)
            list(APPEND COMPONENT_PRIV_REQUIRES esp_timer)
        endif()

    endif()

    set(COMPONENT_PRIV_REQUIRES "freertos" "nvs_flash" "esp_https_ota")

    register_component()
endif()