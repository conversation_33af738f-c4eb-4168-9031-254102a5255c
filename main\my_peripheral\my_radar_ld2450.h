#ifndef MY_RADAR_LD2450_H
#define MY_RADAR_LD2450_H

#include <lwip/netdb.h>

#define MY_RADAR_LD2450_FUNC 1

int My_Radar_LD2450_StartDetect(void);
int My_Radar_LD2450_StopDetect(void);
int My_Radar_LD2450_Config_Trace_Single(void);
int My_Radar_LD2450_Config_Trace_Multi(void);
int My_Radar_LD2450_Config_Query_FrameVersion(void);
int My_Radar_LD2450_Config_Set_Baudrate(uint32_t newvalue);
int My_Radar_LD2450_Config_RFS(void);
int My_Radar_LD2450_Config_Reboot(void);
int My_Radar_LD2450_Config_Open_Bluetooth(void);
int My_Radar_LD2450_Config_Close_Bluetooth(void);
int My_Radar_LD2450_Config_Query_Mac(void);
int My_Radar_LD2450_Config_Query_CurAreaFilterConfig(void);
int My_Radar_LD2450_Config_Set_CurAreaFilterConfig(uint8_t filter_type, void* filter_data);



#endif