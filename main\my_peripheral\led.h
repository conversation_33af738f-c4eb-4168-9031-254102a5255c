#ifndef LED_H
#define LED_H 

#include <lwip/netdb.h>


#define LED_FUNC    0


#if LED_FUNC


void LedInit(void);

/* #define LED_FLASH_MODE_1    1
#define LED_FLASH_MODE_2    2
#define LED_FLASH_MODE_3    3
#define LED_FLASH_MODE_4    4
#define LED_FLASH_MODE_5    5
#define LED_FAST_FLASH_OFF  0xffffffffUL
void LedFlashService(uint32_t times); */

#define LED_ON_VOTAGE  1
#define LED_OFF_VOTAGE 0

#define LED_ON_VOTAGE_TEST  0
#define LED_OFF_VOTAGE_TEST 1

#define LED_ON  1
#define LED_OFF 0

void LedFastFlash(uint16_t period, uint16_t usetime);//单位：ms
void led_open(void);
void led_close(void);
uint8_t get_light_state(void);

#endif //LED_FUNCTION
#endif //LED_H
