#include "my_whgm5.h"

#if NETWORK_TYPE&NETWORK_TYPE_LTE_WHGM5

#include "my_nvs.h"
#include "my_whgm5_at_table.h"
#include "my_gpio.h"
#include "my_at_ctrl.h"
#include "my_uart.h"
#include "my_time.h"
#include "my_power_mng.h"
#include "my_esp_chip_info_mng.h"
#include "mytest_uart_iot_server.h"
#include "my_debug.h"

#include "esp_log.h"

#include "driver/uart.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"
#include "freertos/semphr.h"

#if defined MY_DEBUG_PRINT_FUNC
#define DEBUG_PRINT_LEVEL_my_whgm5_PRINT   MY_DEBUG_PRINT_LEVEL_2_INFO
#else
#define DEBUG_PRINT_LEVEL_my_whgm5_PRINT   DEBUG_PRINT_LEVEL_0
#endif


my_whgm5_basic_conf_nvs_t my_whgm5_basic_conf_nvs;
my_whgm5_info_t my_whgm5_info;
SemaphoreHandle_t MyWHGM5_CmdModeSendMutexSemaphore = NULL;
SemaphoreHandle_t MyWHGM5_CmdModeRcvMutexSemaphore = NULL;

myconf_lte_t myconf_lte_info;

static uint8_t MyWHGM5_LoadBaiscConfToATTable(my_whgm5_basic_conf_nvs_t* confs)
{
    //串口参数

    //TCP配置参数

    //心跳包

    //其他
    return 0;
}

static void MyWHGM5_LoadBasicConf()
{
    memset(&my_whgm5_basic_conf_nvs, 0, sizeof(my_whgm5_basic_conf_nvs_t));

    my_whgm5_basic_conf_nvs.UART = 921600;
    my_whgm5_basic_conf_nvs.UARTFL = 1024;
    my_whgm5_basic_conf_nvs.UARTFT = 50;
    my_whgm5_basic_conf_nvs.SOCKASL = 0;
    my_whgm5_basic_conf_nvs.SHORTATM = 60;
    my_whgm5_basic_conf_nvs.SOCKRSNUM = 60;
    my_whgm5_basic_conf_nvs.SOCKRSTIM = 10;
    my_whgm5_basic_conf_nvs.KEEPALIVE = 0x0f;
    my_whgm5_basic_conf_nvs.HEARTEN = 0;
    my_whgm5_basic_conf_nvs.HEARTTP = HEART_P_TO_NET;
    my_whgm5_basic_conf_nvs.HEARTTM = 30;
    my_whgm5_basic_conf_nvs.HEARTSORT = 0;
    my_whgm5_basic_conf_nvs.NATEN = 0;
    my_whgm5_basic_conf_nvs.SDPEN = 1;
    my_whgm5_basic_conf_nvs.RSTIM = 1800;

	LoadDataFromNvs(WHGM5_NVS_FORM_NAME, WHGM5_NVS_KEY_NAME, (void*)&my_whgm5_basic_conf_nvs, sizeof(my_whgm5_basic_conf_nvs_t));

    MyWHGM5_LoadBaiscConfToATTable(&my_whgm5_basic_conf_nvs);
}

void MyWHGM5_Print_SOCK_conf_routine(void)
{
    printf("\n\n-------------------------------------------------------------------------------\n");
    printf("MyWHGM5_Print_SOCK_conf_routine()\n");
    printf("whgm5_SOCKA_conf_routine[0].cmdstr=%s\n", whgm5_SOCKA_conf_routine[0].cmdstr);
    printf("whgm5_SOCKA_conf_routine[0].keystr=%s\n", whgm5_SOCKA_conf_routine[0].keystr);
    printf("whgm5_SOCKA_conf_routine[1].cmdstr=%s\n", whgm5_SOCKA_conf_routine[1].cmdstr);
    printf("whgm5_SOCKA_conf_routine[1].keystr=%s\n", whgm5_SOCKA_conf_routine[1].keystr);
    printf("\n-------------------------------------------------------------\n");
    printf("whgm5_SOCKB_conf_routine[0].cmdstr=%s\n", whgm5_SOCKB_conf_routine[0].cmdstr);
    printf("whgm5_SOCKB_conf_routine[0].keystr=%s\n", whgm5_SOCKB_conf_routine[0].keystr);
    printf("whgm5_SOCKB_conf_routine[1].cmdstr=%s\n", whgm5_SOCKB_conf_routine[1].cmdstr);
    printf("whgm5_SOCKB_conf_routine[1].keystr=%s\n", whgm5_SOCKB_conf_routine[1].keystr);
    printf("\n-------------------------------------------------------------\n");
    printf("whgm5_SOCKC_conf_routine[0].cmdstr=%s\n", whgm5_SOCKC_conf_routine[0].cmdstr);
    printf("whgm5_SOCKC_conf_routine[0].keystr=%s\n", whgm5_SOCKC_conf_routine[0].keystr);
    printf("whgm5_SOCKC_conf_routine[1].cmdstr=%s\n", whgm5_SOCKC_conf_routine[1].cmdstr);
    printf("whgm5_SOCKC_conf_routine[1].keystr=%s\n", whgm5_SOCKC_conf_routine[1].keystr);
    printf("\n-------------------------------------------------------------\n");
    printf("whgm5_SOCKD_conf_routine[0].cmdstr=%s\n", whgm5_SOCKD_conf_routine[0].cmdstr);
    printf("whgm5_SOCKD_conf_routine[0].keystr=%s\n", whgm5_SOCKD_conf_routine[0].keystr);
    printf("whgm5_SOCKD_conf_routine[1].cmdstr=%s\n", whgm5_SOCKD_conf_routine[1].cmdstr);
    printf("whgm5_SOCKD_conf_routine[1].keystr=%s\n", whgm5_SOCKD_conf_routine[1].keystr);
    printf("\n\n-------------------------------------------------------------------------------\n");
}

void MyWHGM5_Print_SOCKEN_conf_routine(void)
{
    printf("\n\n-------------------------------------------------------------------------------\n");
    printf("MyWHGM5_Print_SOCKEN_conf_routine()\n");
    printf("whgm5_SOCKAEN_conf_routine[0].cmdstr=%s\n", whgm5_SOCKAEN_conf_routine[0].cmdstr);
    printf("whgm5_SOCKAEN_conf_routine[0].keystr=%s\n", whgm5_SOCKAEN_conf_routine[0].keystr);
    printf("whgm5_SOCKAEN_conf_routine[1].cmdstr=%s\n", whgm5_SOCKAEN_conf_routine[1].cmdstr);
    printf("whgm5_SOCKAEN_conf_routine[1].keystr=%s\n", whgm5_SOCKAEN_conf_routine[1].keystr);
    printf("\n-------------------------------------------------------------\n");
    printf("whgm5_SOCKBEN_conf_routine[0].cmdstr=%s\n", whgm5_SOCKBEN_conf_routine[0].cmdstr);
    printf("whgm5_SOCKBEN_conf_routine[0].keystr=%s\n", whgm5_SOCKBEN_conf_routine[0].keystr);
    printf("whgm5_SOCKBEN_conf_routine[1].cmdstr=%s\n", whgm5_SOCKBEN_conf_routine[1].cmdstr);
    printf("whgm5_SOCKBEN_conf_routine[1].keystr=%s\n", whgm5_SOCKBEN_conf_routine[1].keystr);
    printf("\n-------------------------------------------------------------\n");
    printf("whgm5_SOCKCEN_conf_routine[0].cmdstr=%s\n", whgm5_SOCKCEN_conf_routine[0].cmdstr);
    printf("whgm5_SOCKCEN_conf_routine[0].keystr=%s\n", whgm5_SOCKCEN_conf_routine[0].keystr);
    printf("whgm5_SOCKCEN_conf_routine[1].cmdstr=%s\n", whgm5_SOCKCEN_conf_routine[1].cmdstr);
    printf("whgm5_SOCKCEN_conf_routine[1].keystr=%s\n", whgm5_SOCKCEN_conf_routine[1].keystr);
    printf("\n-------------------------------------------------------------\n");
    printf("whgm5_SOCKDEN_conf_routine[0].cmdstr=%s\n", whgm5_SOCKDEN_conf_routine[0].cmdstr);
    printf("whgm5_SOCKDEN_conf_routine[0].keystr=%s\n", whgm5_SOCKDEN_conf_routine[0].keystr);
    printf("whgm5_SOCKDEN_conf_routine[1].cmdstr=%s\n", whgm5_SOCKDEN_conf_routine[1].cmdstr);
    printf("whgm5_SOCKDEN_conf_routine[1].keystr=%s\n", whgm5_SOCKDEN_conf_routine[1].keystr);
    printf("\n\n-------------------------------------------------------------------------------\n");
}

static uint8_t MyWHGM5_LoadNetConfToATTable(my_lte_network_confs_t* confs)
{
    //TCP
    if(confs->networkmode==WHGM5_NETWORKMODE_TCP)
    {
        #if DEBUG_PRINT_LEVEL_my_whgm5_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
        MyWHGM5_Print_SOCK_conf_routine();
        #endif
        #if DEBUG_PRINT_LEVEL_my_whgm5_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
        MyWHGM5_Print_SOCKEN_conf_routine();
        #endif
        
        if(confs->tcp_channel_1!=NULL)
        {
            ATTable_ModifyItem_SOCK_Conf(whgm5_SOCKA_conf_routine, confs->tcp_channel_1->address, confs->tcp_channel_1->port);
            ATTable_ModifyItem_SOCKEN_Conf(whgm5_SOCKAEN_conf_routine, 1);
        }
        if(confs->tcp_channel_2!=NULL)
        {
            ATTable_ModifyItem_SOCK_Conf(whgm5_SOCKB_conf_routine, confs->tcp_channel_2->address, confs->tcp_channel_2->port);
            ATTable_ModifyItem_SOCKEN_Conf(whgm5_SOCKBEN_conf_routine, 1);
        }
        if(confs->tcp_channel_3!=NULL)
        {
            ATTable_ModifyItem_SOCK_Conf(whgm5_SOCKC_conf_routine, confs->tcp_channel_3->address, confs->tcp_channel_3->port);
            ATTable_ModifyItem_SOCKEN_Conf(whgm5_SOCKCEN_conf_routine, 1);
        }
        if(confs->tcp_channel_4!=NULL)
        {
            ATTable_ModifyItem_SOCK_Conf(whgm5_SOCKD_conf_routine, confs->tcp_channel_4->address, confs->tcp_channel_4->port);
            ATTable_ModifyItem_SOCKEN_Conf(whgm5_SOCKDEN_conf_routine, 1);
        }

        #if DEBUG_PRINT_LEVEL_my_whgm5_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
        MyWHGM5_Print_SOCK_conf_routine();
        #endif
        #if DEBUG_PRINT_LEVEL_my_whgm5_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
        MyWHGM5_Print_SOCKEN_conf_routine();
        #endif
    }
    //HTTP
    else if(confs->networkmode==WHGM5_NETWORKMODE_HTTP)
    {

    }

    return 0;
}

int MyWHGM5_Cmd_Mode(void)
{
    if(my_whgm5_info.workmode!=WHGM5_WORKMODE_CMD)
    {
        ESP_LOGI("MyWHGM5_Cmd_Mode()",  "WHGM5 switch to cmd mode\n");
        if(!AT_Routine(whgm5_WORKMODE_CMD_conf_routine))
        {
            my_whgm5_info.workmode = WHGM5_WORKMODE_CMD;
            ESP_LOGI("MyWHGM5_Cmd_Mode()",  "WHGM5 switch to cmd mode succeed\n");
            return 1;
        }
        return 0;
    }
    return 1;
}

int MyWHGM5_Data_Mode(void)
{
    if(my_whgm5_info.workmode!=WHGM5_WORKMODE_DATA)
    {
        ESP_LOGI("MyWHGM5_Data_Mode()",  "WHGM5 switch to data mode\n");
        AT_Routine(whgm5_WORKMODE_DATA_conf_routine);
        my_whgm5_info.workmode = WHGM5_WORKMODE_DATA;
        ESP_LOGI("MyWHGM5_Data_Mode()",  "WHGM5 switch to data mode succeed\n");
    }
    return 1;
}

uint8_t MyWHGM5_GetWorkmode(void)
{
    return my_whgm5_info.workmode;
}

void MyWHGM5_Reset_H(void);

static void MyWHGM5_GPIOInit(void)
{
    MyPowerMng_MyWHGM5_PowerMngGpioInit();
    MyWHGM5_Reset_H();
}

void MyWHGM5_Reset_H(void)
{
    MyPowerMng_MyWHGM5_PowerOFF();
    vTaskDelay(3000/portTICK_PERIOD_MS);
    MyPowerMng_MyWHGM5_PowerON();
}

int MyWHGM5_Reset_S(void)
{
    MyWHGM5_Cmd_Mode();

    return AT_Routine(whgm5_RESTART_conf_routine);
}

static uint32_t MyWHGM5_Matching_Baudrate(uint32_t current_baudrate)
{
    ESP_LOGI("LTE_Matching_Baudrate()",  "LTE_Matching_Baudrate...\n");
    vTaskDelay(3000/portTICK_PERIOD_MS);
    if(current_baudrate == 115200)
    {
        MyUartSetBaudrate(921600);
        if(MyWHGM5_Cmd_Mode())
        {
            ESP_LOGI("LTE_Matching_Baudrate()",  "LTE_Matching_Baudrate=921600\n");
            return 921600;
        }
        else
        {
            ESP_LOGE("LTE_Matching_Baudrate()",  "error 1\n");
        }
    }
    else if(current_baudrate == 921600)
    {
        MyUartSetBaudrate(115200);
        if(MyWHGM5_Cmd_Mode())
        {
            ESP_LOGI("LTE_Matching_Baudrate()",  "LTE_Matching_Baudrate=115200\n");
            return 115200;
        }
        else
        {
            ESP_LOGE("LTE_Matching_Baudrate()",  "error 2\n");
        }
    }
    else
    {
        ESP_LOGE("LTE_Matching_Baudrate()",  "error 3\n");
    }
    return current_baudrate;
}

static uint8_t MyWHGM5_CheckConfAccordingATTable(myconf_lte_t* myconf_lte)
{
    uint8_t whgm5_conf_changed;
    int64_t time_start=0, time_end=0;

    check_whgm5_config:

    whgm5_conf_changed = 0;

    ESP_LOGI("MyWHGM5_CheckConfAccordingATTable()", "start checking...");

    #if DEBUG_PRINT_LEVEL_my_whgm5_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    time_start = esp_timer_get_time();
    #endif

    if(!MyWHGM5_Cmd_Mode())
    {
        uint32_t newbaudrate = 0;
        newbaudrate = MyWHGM5_Matching_Baudrate(myconf_lte->baudrate);
        ESP_LOGI("MyWHGM5_CheckConfAccordingATTable()", "newbaudrate=%d\n", newbaudrate);
    }
    if(!my_whgm5_info.installed)
    {
        whgm5_conf_changed += AT_Routine(whgm5_UART_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_UARTFL_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_UARTFT_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_SOCKASL_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_SHORTATM_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_SOCKRSNUM_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_SOCKRSTIM_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_KEEPALIVEA_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_KEEPALIVEB_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_HEARTEN_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_HEARTTP_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_HEARTTM_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_HEARTSORT_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_NATEN_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_SDPEN_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_RSTIM_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_STMSG_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_NTPEN_conf_routine);
    }
    if(my_whgm5_info.networkmode==WHGM5_NETWORKMODE_TCP)
    {
        whgm5_conf_changed += AT_Routine(whgm5_WKMOD_NET_conf_routine);

        whgm5_conf_changed += AT_Routine(whgm5_SOCKAEN_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_SOCKBEN_conf_routine);

        whgm5_conf_changed += AT_Routine(whgm5_SOCKA_conf_routine);
        whgm5_conf_changed += AT_Routine(whgm5_SOCKB_conf_routine);

    }
    else if(my_whgm5_info.networkmode==WHGM5_NETWORKMODE_HTTP)
    {
        AT_Routine(whgm5_WKMOD_HTTPD_conf_routine);
        AT_Routine(whgm5_HTPTP_POST_conf_routine);
        AT_Routine(whgm5_HTPTIM_conf_routine);
    }

    #if DEBUG_PRINT_LEVEL_my_whgm5_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\nMyWHGM5_CheckConfAccordingATTable:whgm5_conf_changed=%d\n\n", whgm5_conf_changed);
    #endif

    #if DEBUG_PRINT_LEVEL_my_whgm5_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    time_end = esp_timer_get_time();
    printf("\n\nMyWHGM5_CheckConfAccordingATTable:time used=%lldms\n\n", (time_end-time_start)/1000);
    #endif

    if(whgm5_conf_changed)
    {
        if(!AT_Routine(whgm5_SAVING_conf_routine))
        {
            my_whgm5_info.workmode = WHGM5_WORKMODE_DATA;
            MyUartSetBaudrate(myconf_lte->baudrate);
            goto check_whgm5_config;
        }
        else
        {
            ESP_LOGE("MyWHGM5_CheckConfAccordingATTable()", "whgm5_SAVING_conf_routine error!\n");
        }
    }

    return 0;
}

#define WHGM5_INFO_RCV_TIME 100
static int MyWHGM5_RcvCSQ(void)
{
	char *start, *end;
    char* RCV_BUF = (char*)calloc(1, LTE_CMD_LENGTH_LIMIT);
    char *CSQ = my_whgm5_info.CSQ;
    memset(my_whgm5_info.CSQ, 0, sizeof(my_whgm5_info.CSQ));

    if(RCV_BUF==NULL)
    {
        return 1;
    }

    AT_SendSingleCmd_And_Rcv("AT+CSQ?\r\n", RCV_BUF, WHGM5_INFO_RCV_TIME);//超时时间不可小于400
	if(strstr((const char*)RCV_BUF,"OK\r\n")!=NULL){
	   //ESP_LOGI("", "%s",RCV_BUF);
	   if ((start = strstr( (char *)RCV_BUF, "+CSQ: "))!=NULL)
	   {
	      start += strlen( "+CSQ: " );
	      if ((end = strstr((char *) start, "," ))!=NULL )
	      {
	        	memcpy(CSQ, start, end - start);
	      }
	   }
	   //ESP_LOGI("",  "%s\n", CSQ );
	}
    free(RCV_BUF);
	return 0;
}

static int MyWHGM5_RcvLocation(int type)
{
	char *start, *end;
	char* RCV_BUF = (char*)calloc(1, LTE_CMD_LENGTH_LIMIT);
    char* location_x = (char*)calloc(1, LTE_CMD_LENGTH_LIMIT);
    char* location_y = (char*)calloc(1, LTE_CMD_LENGTH_LIMIT);
    char* location_detail = (char*)calloc(1, 1024);
    int ret = 0;

    if(RCV_BUF==NULL||location_x==NULL||location_y==NULL)
    {
        ret = 1;
        goto lte_rcv_location_clear;
    }

    if(type==2)
    {
        if(location_detail==NULL)
        {
            ret = 1;
            goto lte_rcv_location_clear;
        }
    }
    
    //LOCATION:x=121.29058838&y=31.10537910
	if(type==1)
	{
        AT_SendSingleCmd_And_Rcv("AT+LBS=1\r\n", RCV_BUF, WHGM5_INFO_RCV_TIME);//超时时间不可小于800
		if(strstr((const char*)RCV_BUF,"OK\r\n")!=NULL)
        {
            memset(my_whgm5_info.LBS, 0, sizeof(my_whgm5_info.LBS));
            //ESP_LOGI("", "%s",RCV_BUF);
            if ((start = strstr( (char *)RCV_BUF, "+LBS: LNG = "))!=NULL)
            {
                start += strlen( "+LBS: LNG = " );
                if ((end = strstr((char *) start, ", LAT" ))!=NULL )
                {
                    memcpy(location_x, start, end - start);
                }
            }
            if ((start = strstr( (char *)RCV_BUF, "LAT = "))!=NULL)
            {
                start += strlen( "LAT = " );
                if ((end = strstr((char *) start, ", TIME" ))!=NULL )
                {
                    memcpy(location_y, start, end - start);
                }
            }
            sprintf(my_whgm5_info.LBS, "x=%s&y=%s", location_x, location_y);
            //ESP_LOGI("",  "LOCATION: %s\n", my_whgm5_info.LBS );
            
		}
        ret = 0;
        goto lte_rcv_location_clear;
	}
    //LOCATION:x=121.29058838&y=31.10537910&ADDINFO: 上海市松江区泗泾镇高技公路;高技路与双施路路口东北196米
	else if(type==2)
	{
		AT_SendSingleCmd_And_Rcv("AT+LBS=2\r\n", RCV_BUF, WHGM5_INFO_RCV_TIME);//超时时间不可小于800
		if(strstr((const char*)RCV_BUF,"OK\r\n")!=NULL)
        {
            memset(my_whgm5_info.LBS, 0, sizeof(my_whgm5_info.LBS));
            //ESP_LOGI("", "%s",RCV_BUF);
            if ((start = strstr( (char *)RCV_BUF, "+LBS: LNG = "))!=NULL)
            {
                start += strlen( "+LBS: LNG = " );
                if ((end = strstr((char *) start, ", LAT" ))!=NULL )
                {
                    memcpy(location_x, start, end - start);
                }
            }
            if ((start = strstr( (char *)RCV_BUF, "LAT = "))!=NULL)
            {
                start += strlen( "LAT = " );
                if ((end = strstr((char *) start, ", TIME" ))!=NULL )
                {
                    memcpy(location_y, start, end - start);
                }
            }
            if ((start = strstr( (char *)RCV_BUF, "ADDINFO:"))!=NULL)
            {
                if ((end = strstr((char *) start, "OK" ))!=NULL )
                {
                    memcpy(location_detail, start, end - start);
                }
            }
            sprintf(my_whgm5_info.LBS, "x=%s&y=%s&%s", location_x, location_y, location_detail);
            //ESP_LOGI("",  "LOCATION:%s\n", my_whgm5_info.LBS );
            
		}
        ret = 0;
        goto lte_rcv_location_clear;
		return 0;
	}

    lte_rcv_location_clear:
    if(RCV_BUF!=NULL)free(RCV_BUF);
    if(location_x!=NULL)free(location_x);
    if(location_y!=NULL)free(location_y);
    if(location_detail!=NULL)free(location_detail);
	return ret;
}

static int MyWHGM5_RcvInfo(char* ATCMD, char* at_ack_end_symbol, char* rcv_head_symbol, char* rcv_end_symbol, char* saving, uint16_t len)
{

	char *start, *end;
	char* RCV_BUF = (char*)calloc(1, LTE_CMD_LENGTH_LIMIT);
    memset(saving, 0, sizeof(len));

    if(RCV_BUF==NULL)
    {
        return 1;
    }

    AT_SendSingleCmd_And_Rcv(ATCMD, RCV_BUF, WHGM5_INFO_RCV_TIME);//超时时间不可小于80
	if(strstr((const char*)RCV_BUF,at_ack_end_symbol)!=NULL)
	{
		//ESP_LOGI("", "%s",RCV_BUF);
		if ((start = strstr( (char *)RCV_BUF, rcv_head_symbol))!=NULL)
		{
			start += strlen( rcv_head_symbol );
			if ((end = strstr((char *) start, rcv_end_symbol ))!=NULL )
			{
                if((end-start)<len)
				    memcpy( saving, start, end - start);
                else
                {
                    memcpy( saving, start, len);
                }
			}
			else
			{
				ESP_LOGE("", "MyWHGM5_RcvInfo(): error=1\n");
                free(RCV_BUF);
				return 1;
			}
		}
		else
		{
			ESP_LOGE("", "MyWHGM5_RcvInfo(): error=2\n");
            free(RCV_BUF);
			return 1;
		}
		//ESP_LOGI("",  "%s\n", saving );
        free(RCV_BUF);
		return 0;
	}
	ESP_LOGE("", "MyWHGM5_RcvInfo(): error=3\n");
    free(RCV_BUF);
	return 1;
}

#if DEBUG_PRINT_LEVEL_my_whgm5_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
#define WHGM5_INFO_ALL      0xff
#define WHGM5_INFO_INITOK   (1<<0)
#define WHGM5_INFO_WOKMOD   (1<<1)
#define WHGM5_INFO_INSTAL   (1<<2)
#define WHGM5_INFO_NTWMOD   (1<<3)
#define WHGM5_INFO_TCPCID   (1<<4)
#define WHGM5_INFO_FIXINF   (1<<5)
#define WHGM5_INFO_VARINF   (1<<6)

//WHGM5_INFO_ALL      输出所有信息
//WHGM5_INFO_INITOK   初始化状态:1/0
//WHGM5_INFO_WOKMOD   工作模式:1-WHGM5_WORKMODE_CMD, 2-WHGM5_WORKMODE_DATA
//WHGM5_INFO_INSTAL   驱动状态:1/0
//WHGM5_INFO_NTWMOD   网络模式:1-WHGM5_NETWORKMODE_TCP, 2-WHGM5_NETWORKMODE_HTTP
//WHGM5_INFO_TCPCID   TCP通道ID
//WHGM5_INFO_FIXINF   固定不变的信息，如IMEI等
//WHGM5_INFO_VARINF   可变信息，如CSQ等
void MyWHGM5_Print_my_whgm5_info(uint8_t type)
{
    printf("\n\n-------------------------------------------------------------------------------\n");
    printf("MyWHGM5_Print_my_whgm5_info()\n");

    if(type&WHGM5_INFO_INITOK)
        printf("MyWHGM5_GetBasicInfo():my_whgm5_info.init_ok=%d\n", my_whgm5_info.init_ok);
    if(type&WHGM5_INFO_WOKMOD)
        printf("MyWHGM5_GetBasicInfo():my_whgm5_info.workmode=%d\n", my_whgm5_info.workmode);
    if(type&WHGM5_INFO_INSTAL)
        printf("MyWHGM5_GetBasicInfo():my_whgm5_info.installed=%d\n", my_whgm5_info.installed);
    if(type&WHGM5_INFO_NTWMOD)
        printf("MyWHGM5_GetBasicInfo():my_whgm5_info.networkmode=%d\n", my_whgm5_info.networkmode);
    if(type&WHGM5_INFO_TCPCID){
        printf("MyWHGM5_GetBasicInfo():my_whgm5_info.tcp_channel_1_id=%d\n", my_whgm5_info.tcp_channel_1_id);
        printf("MyWHGM5_GetBasicInfo():my_whgm5_info.tcp_channel_2_id=%d\n", my_whgm5_info.tcp_channel_2_id);
        printf("MyWHGM5_GetBasicInfo():my_whgm5_info.tcp_channel_3_id=%d\n", my_whgm5_info.tcp_channel_3_id);
        printf("MyWHGM5_GetBasicInfo():my_whgm5_info.tcp_channel_4_id=%d\n", my_whgm5_info.tcp_channel_4_id);
    }
    
    if(type&WHGM5_INFO_FIXINF){
    printf("MyWHGM5_GetBasicInfo():my_whgm5_info.firmware_version=%s\n", my_whgm5_info.firmware_version);
    printf("MyWHGM5_GetBasicInfo():my_whgm5_info.firmware_buildtime=%s\n", my_whgm5_info.firmware_buildtime);
    printf("MyWHGM5_GetBasicInfo():my_whgm5_info.SN=%s\n", my_whgm5_info.SN);
    printf("MyWHGM5_GetBasicInfo():my_whgm5_info.IMEI=%s\n", my_whgm5_info.IMEI);
    printf("MyWHGM5_GetBasicInfo():my_whgm5_info.ICCID=%s\n", my_whgm5_info.ICCID);
    }
    if(type&WHGM5_INFO_VARINF){
    printf("MyWHGM5_GetBasicInfo():my_whgm5_info.SYSINFO=%s\n", my_whgm5_info.SYSINFO);
    printf("MyWHGM5_GetBasicInfo():my_whgm5_info.APN=%s\n", my_whgm5_info.APN);
    printf("MyWHGM5_GetBasicInfo():my_whgm5_info.CSQ=%s\n", my_whgm5_info.CSQ);
    printf("MyWHGM5_GetBasicInfo():my_whgm5_info.IP=%s\n", my_whgm5_info.IP);
    printf("MyWHGM5_GetBasicInfo():my_whgm5_info.LBS=%s\n", my_whgm5_info.LBS);
    printf("MyWHGM5_GetBasicInfo():my_whgm5_info.CCLK=%s\n", my_whgm5_info.CCLK);
    }
    printf("\n\n-------------------------------------------------------------------------------\n");
}
#endif

static uint8_t MyWHGM5_GetBasicInfo(void)
{
    MyWHGM5_Cmd_Mode();

    //固件版本号
    MyWHGM5_RcvInfo("AT+VER?\r\n", "OK", "+VER:", "\r\n", my_whgm5_info.firmware_version, sizeof(my_whgm5_info.firmware_version));
    //固件编译时间
    MyWHGM5_RcvInfo("AT+BUILD?\r\n", "OK", "+BUILD:", "\r\n", my_whgm5_info.firmware_buildtime, sizeof(my_whgm5_info.firmware_buildtime));
    //SN
    MyWHGM5_RcvInfo("AT+SN?\r\n", "OK", "+SN:", "\r\n", my_whgm5_info.SN, sizeof(my_whgm5_info.SN));
    //连接制式
    MyWHGM5_RcvInfo("AT+SYSINFO?\r\n", "OK", "+SYSINFO:", "\r\n", my_whgm5_info.SYSINFO, sizeof(my_whgm5_info.SYSINFO));
    //IMEI
    MyWHGM5_RcvInfo("AT+IMEI?\r\n", "OK", "+IMEI:", "\r\n", my_whgm5_info.IMEI, sizeof(my_whgm5_info.IMEI));
    //ICCID
    MyWHGM5_RcvInfo("AT+ICCID?\r\n", "OK", "+ICCID:", "\r\n", my_whgm5_info.ICCID, sizeof(my_whgm5_info.ICCID));
    //APN
    MyWHGM5_RcvInfo("AT+APN?\r\n", "OK", "+APN:", "\r\n", my_whgm5_info.APN, sizeof(my_whgm5_info.APN));
    //CSQ
    MyWHGM5_RcvCSQ();
    //IP
    MyWHGM5_RcvInfo("AT+IP?\r\n", "OK", "+IP:", "\r\n", my_whgm5_info.IP, sizeof(my_whgm5_info.IP));
    //LBS
    MyWHGM5_RcvLocation(1);
    //CCLK
    MyWHGM5_RcvInfo("AT+CCLK?\r\n", "OK", "+CCLK:", "\r\n", my_whgm5_info.CCLK, sizeof(my_whgm5_info.CCLK));

    #if DEBUG_PRINT_LEVEL_my_whgm5_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    MyWHGM5_Print_my_whgm5_info(WHGM5_INFO_ALL);
    #endif

    return 0;
}

uint8_t MyWHGM5_RefreshDynamicInfo(void)
{
    if(my_whgm5_info.init_ok)
    {
        //获取信号量
        xSemaphoreTake(MyWHGM5_CmdModeSendMutexSemaphore, portMAX_DELAY);
        xSemaphoreTake(MyWHGM5_CmdModeRcvMutexSemaphore, portMAX_DELAY);

        if(MyWHGM5_Cmd_Mode())
        {
            //CSQ
            MyWHGM5_RcvCSQ();
            #if DEBUG_PRINT_LEVEL_my_whgm5_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
            MyWHGM5_Print_my_whgm5_info(WHGM5_INFO_VARINF);
            #endif
        }

        MyWHGM5_Data_Mode();

        //释放信号量
        xSemaphoreGive(MyWHGM5_CmdModeSendMutexSemaphore);
        xSemaphoreGive(MyWHGM5_CmdModeRcvMutexSemaphore);

        return 0;
    }
    return 1;
}

uint8_t MyWHGM5_GetInitState(void)
{
    return my_whgm5_info.init_ok;
}

int MyWHGM5_TcpDataStationInit(void);
uint8_t MyWHGM5_Init(my_lte_network_confs_t* confs, myconf_lte_t* myconf_lte, uint8_t checkconfig)
{
    #if DEBUG_PRINT_LEVEL_my_whgm5_PRINT >= MY_DEBUG_PRINT_LEVEL_1_SIMPLE
    time_t start_time = esp_timer_get_time();
    #endif

    my_whgm5_info.init_ok = 0;
    //my_whgm5_info.workmode = WHGM5_WORKMODE_DATA;

    #if !MYTEST_CONFIG_UART_IOT_SERVER
    //加载基础配置
    if(!my_whgm5_info.installed)MyWHGM5_LoadBasicConf();
    //将网络参数confs加载到at表中
    if(confs!=NULL&&checkconfig)
    {
        MyWHGM5_LoadNetConfToATTable(confs);
        my_whgm5_info.networkmode = confs->networkmode;
        if(confs->tcp_channel_1!=NULL)my_whgm5_info.tcp_channel_1_id = confs->tcp_channel_1->id;
        if(confs->tcp_channel_2!=NULL)my_whgm5_info.tcp_channel_2_id = confs->tcp_channel_2->id;
        if(confs->tcp_channel_3!=NULL)my_whgm5_info.tcp_channel_3_id = confs->tcp_channel_3->id;
        if(confs->tcp_channel_4!=NULL)my_whgm5_info.tcp_channel_4_id = confs->tcp_channel_4->id;
    }
    //加载baudrate参数
    if(checkconfig)
    {
        if(myconf_lte!=NULL)
        {
            if( myconf_lte->baudrate==115200||myconf_lte->baudrate==921600 )
            {
                myconf_lte_info.baudrate = myconf_lte->baudrate;
            }
            else
            {
                myconf_lte_info.baudrate = WHGM5_BAUDRATE_DEFAULT;
            }
        }
        else
            myconf_lte_info.baudrate = WHGM5_BAUDRATE_DEFAULT;
    }
    

    ATTable_ModifyItem_UartBaudrate(whgm5_UART_conf_routine, myconf_lte_info.baudrate);
    
    //LTE上电
    if(!my_whgm5_info.installed)MyWHGM5_GPIOInit();
    else MyWHGM5_Reset_H();
    //等待LTE进入工作状态
    ESP_LOGI("MyWHGM5_Init()",  "wait 15s for lte run...\n");
    vTaskDelay(15000/portTICK_PERIOD_MS);
    //初始化串口
    if(!my_whgm5_info.installed)MyUartInit(myconf_lte_info.baudrate);
    #if MYTEST_CONFIG_ESP32_IOT_SERVER==0
    //根据at表检查配置
    if(confs!=NULL&&checkconfig)MyWHGM5_CheckConfAccordingATTable(&myconf_lte_info);
    //获取LTE基础参数
    if(confs!=NULL&&checkconfig)MyWHGM5_GetBasicInfo();
    //进入通讯模式
    if(confs!=NULL&&checkconfig)MyWHGM5_Data_Mode();
    #else
    my_whgm5_info.workmode = WHGM5_WORKMODE_DATA;
    #endif
    #endif

    if(!my_whgm5_info.installed)MyWHGM5_TcpDataStationInit();

    my_whgm5_info.installed = 1;
    my_whgm5_info.init_ok = 1;

    #if DEBUG_PRINT_LEVEL_my_whgm5_PRINT >= MY_DEBUG_PRINT_LEVEL_1_SIMPLE
    time_t end_time = esp_timer_get_time();
    printf("MyWHGM5_Init():func end, MyTime_GetRuntimeSinceReset=%ld, timeuse=%ldms\n", MyTime_GetRuntimeSinceReset(), (end_time-start_time)/1000);
    #endif

    return 0;
}

uint8_t MyWHGM5_Init_SimpleTcpMode(my_tcp_conf_t* confs, myconf_lte_t* myconf_lte, uint8_t checkconfig)
{
    int ret = 0;

    my_lte_network_confs_t* lte_net_work_confs = (my_lte_network_confs_t*)calloc(1, sizeof(my_lte_network_confs_t));
    #if DEBUG_PRINT_LEVEL_my_whgm5_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
	// printf("app_main():sizeof(my_lte_network_confs_t)=%d\n", sizeof(my_lte_network_confs_t));
    #endif
	lte_net_work_confs->http=NULL;
	lte_net_work_confs->tcp_channel_1 = (my_tcp_conf_t*)calloc(1, sizeof(my_tcp_conf_t));
	lte_net_work_confs->tcp_channel_2 = NULL;
	lte_net_work_confs->tcp_channel_3 = NULL;
	lte_net_work_confs->tcp_channel_4 = NULL;

	memcpy(lte_net_work_confs->tcp_channel_1->address, confs->address, strlen(confs->address));
	lte_net_work_confs->tcp_channel_1->port = confs->port;
	lte_net_work_confs->tcp_channel_1->id = 1;
	lte_net_work_confs->networkmode = WHGM5_NETWORKMODE_DEFAULT;
	ret = MyWHGM5_Init(lte_net_work_confs, myconf_lte, checkconfig);

    if(lte_net_work_confs->tcp_channel_1!=NULL)free(lte_net_work_confs->tcp_channel_1);
    if(lte_net_work_confs->tcp_channel_2!=NULL)free(lte_net_work_confs->tcp_channel_2);
    if(lte_net_work_confs->tcp_channel_3!=NULL)free(lte_net_work_confs->tcp_channel_3);
    if(lte_net_work_confs->tcp_channel_4!=NULL)free(lte_net_work_confs->tcp_channel_4);
    if(lte_net_work_confs!=NULL)free(lte_net_work_confs);

    return ret;
}

void MyWHGM5_RestartOnly(void)
{
    #if DEBUG_PRINT_LEVEL_my_whgm5_PRINT >= MY_DEBUG_PRINT_LEVEL_1_SIMPLE
    printf("MyWHGM5_RestartOnly()\n");
    #endif
    MyWHGM5_Init(NULL, NULL, 0);
}

void MyWHGM5_Restart_Reconfig_SimpleTcpMode(my_tcp_conf_t* confs, myconf_lte_t* myconf_lte)
{
    #if DEBUG_PRINT_LEVEL_my_whgm5_PRINT >= MY_DEBUG_PRINT_LEVEL_1_SIMPLE
    printf("MyWHGM5_Restart_Reconfig_SimpleTcpMode()\n");
    #endif
    MyWHGM5_Init_SimpleTcpMode(confs, myconf_lte, 1);
}

#include <stdio.h>
#include <time.h>
#include <stdlib.h>
#include <string.h>

time_t string_to_timestamp(const char *datetime_str) {
    struct tm tm = {0};
    int year, month, day, hour, min, sec;
    
    // 解析输入字符串
    if (sscanf(datetime_str, "%d/%d/%d,%d:%d:%d", 
               &year, &month, &day, &hour, &min, &sec) != 6) {
        return -1; // 解析失败返回-1
    }

    // 填充tm结构体（注意月份从0开始）
    tm.tm_year = year - 1900;
    tm.tm_mon = month - 1;
    tm.tm_mday = day;
    tm.tm_hour = hour;
    tm.tm_min = min;
    tm.tm_sec = sec;

    // 转换为时间戳（自动处理时区转换）
    time_t timestamp = mktime(&tm);
    
    // 如果输入时间是UTC时间，需要补偿时区偏移
    // time_t utc_timestamp = time(NULL) - timezone + timestamp;
    
    return timestamp;
}

// 测试用例
time_t cclk_to_timestamp(const char *datetime_str) {
    time_t ts = string_to_timestamp(datetime_str);
    
    if (ts != (time_t)-1) {
        printf("时间戳: %ld\n", ts);
        printf("验证时间: %s", ctime(&ts));
    } else {
        printf("时间格式解析失败");
    }
    return ts;
}

// void convert_date_time(const char* input, char* output) {
//     // 提取年份部分（最后两位）
//     char year_str[3];
//     strncpy(year_str, input+1, 2);
//     year_str[2] = '\0';
//     printf("year_str: %s\n", year_str);
//     int year = atoi(year_str) + 2000;
    
//     // 构建输出字符串
//     // 格式为 dd/mm/yy,HH:MM:SS+xx -> yyyy/mm/dd,HH:MM:SS
//     sprintf(output, "%04d/%s/%s%s", 
//             year, 
//             input + 4,  // mm/
//             input,     // dd/
//             input + 9); // ,HH:MM:SS (跳过秒后的+xx部分)
    
//     // 去掉时间部分最后的+32（如果有）
//     char* comma = strchr(output, ',');
//     if (comma) {
//         char* plus = strchr(comma + 1, '+');
//         if (plus) *plus = '\0';
//     }
// }

void convert_date_time(const char* input, char* output) {
    // 将25/05/14转换为2025/05/14
    output[0] = '2';
    output[1] = '0';
    output[2] = input[1];
    output[3] = input[2];
    output[4] = '/';
    output[5] = input[4];
    output[6] = input[5];
    output[7] = '/';
    output[8] = input[7];
    output[9] = input[8];
    
    // 复制时间部分直到+号
    int i;
    for (i = 0; i < 12; i++) { // 12个字符: ,HH:MM:SS
        if (input[9 + i] == '+' || input[9 + i] == '\0') break;
        output[10 + i] = input[9 + i];
    }
    output[10 + i] = '\0';
}

int MyWHGM5_timesync(void)
{
    int ret = -1;
    xSemaphoreTake(MyWHGM5_CmdModeSendMutexSemaphore, portMAX_DELAY);
    xSemaphoreTake(MyWHGM5_CmdModeRcvMutexSemaphore, portMAX_DELAY);

    MyWHGM5_Cmd_Mode();
    MyWHGM5_RcvInfo("AT+CCLK?\r\n", "OK", "+CCLK: ", "\r\n", my_whgm5_info.CCLK, sizeof(my_whgm5_info.CCLK));

    char output[32];
    memset(output, 0, sizeof(output));
    
    convert_date_time(my_whgm5_info.CCLK, output);
    printf("转换后: %s\n", output); // 输出 "2025/05/14,15:03:52"

    my_whgm5_info.cclk_timestamp = cclk_to_timestamp(output);

    if(my_whgm5_info.cclk_timestamp!=-1)
    {
        ret = 0;
        MyTime_SetTimeAndMark(my_whgm5_info.cclk_timestamp);
    }

    MyWHGM5_Data_Mode();

    xSemaphoreGive(MyWHGM5_CmdModeSendMutexSemaphore);
    xSemaphoreGive(MyWHGM5_CmdModeRcvMutexSemaphore);

    return ret;
}
int MyWHGM5_CheckRTI(void)
{
    xSemaphoreTake(MyWHGM5_CmdModeSendMutexSemaphore, portMAX_DELAY);
    xSemaphoreTake(MyWHGM5_CmdModeRcvMutexSemaphore, portMAX_DELAY);

    MyWHGM5_Cmd_Mode();

    //连接制式
    MyWHGM5_RcvInfo("AT+SYSINFO?\r\n", "OK", "+SYSINFO:", "\r\n", my_whgm5_info.SYSINFO, sizeof(my_whgm5_info.SYSINFO));
    //APN
    MyWHGM5_RcvInfo("AT+APN?\r\n", "OK", "+APN:", "\r\n", my_whgm5_info.APN, sizeof(my_whgm5_info.APN));
    //CSQ
    MyWHGM5_RcvCSQ();
    //IP
    // MyWHGM5_RcvInfo("AT+IP?\r\n", "OK", "+IP:", "\r\n", my_whgm5_info.IP, sizeof(my_whgm5_info.IP));
    // //LBS
    // MyWHGM5_RcvLocation(1);
    //CCLK
    // MyWHGM5_RcvInfo("AT+CCLK?\r\n", "OK", "+CCLK: ", "\r\n", my_whgm5_info.CCLK, sizeof(my_whgm5_info.CCLK));


    #if DEBUG_PRINT_LEVEL_my_whgm5_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    MyWHGM5_Print_my_whgm5_info(WHGM5_INFO_VARINF);
    #endif

    MyWHGM5_Data_Mode();

    xSemaphoreGive(MyWHGM5_CmdModeSendMutexSemaphore);
    xSemaphoreGive(MyWHGM5_CmdModeRcvMutexSemaphore);

    return 0;
}

uint8_t MyWHGM5_GetInfo(my_whgm5_info_t* saving)
{
    if(saving==NULL)return 1;

    memcpy(saving, &my_whgm5_info, sizeof(my_whgm5_info));

    return 0;
}

time_t MyWHGM5_Getcclktimestamp(void)
{
    return my_whgm5_info.cclk_timestamp;
}

uint8_t MyWHGM5_GetCSQ(void)
{
    return atoi(my_whgm5_info.CSQ);
}

uint8_t MyWHGM5_GetIMEI(char* saving, uint8_t len)
{
    if(saving==NULL)return 1;

    uint8_t len_saving = strlen(my_whgm5_info.IMEI);

    if(len_saving>len)memcpy(saving, my_whgm5_info.IMEI, len);
    else memcpy(saving, my_whgm5_info.IMEI, len_saving);

    return 0;
}

uint8_t MyWHGM5_GetICCID(char* saving, uint8_t len)
{
    if(saving==NULL)return 1;

    uint8_t len_saving = strlen(my_whgm5_info.ICCID);

    if(len_saving>len)memcpy(saving, my_whgm5_info.ICCID, len);
    else memcpy(saving, my_whgm5_info.ICCID, len_saving);

    return 0;
}

QueueHandle_t my_whgm5_rcv_queue = NULL;
QueueHandle_t my_whgm5_send_queue = NULL;

TaskHandle_t MyWHGM5_TcpDataRcv_TaskHandle = NULL;
my_whgm5_queue_item_t* my_whgm5_rcv_queue_item_array[WHGM5_QUEUE_LEN];
#define UART_RCV_HEAD   0xAAFD5500UL
void MyWHGM5_TcpDataRcv_Task(void* param)
{
    int rxbytes = 0;
    char* uart_rcv_buf = (char*)calloc(WHGM5_QUEUE_LEN, WHGM5_DATA_PACK_SIZE);
    int i = 0;
    int find_empty_count = 0;
    int queue_send_ret = pdTRUE;

    if(uart_rcv_buf==NULL)
    {
        ESP_LOGE("MyWHGM5_TcpDataRcv_Task()", "uart_rcv_buf=NULL\n");
        while(1)
        {
            vTaskDelay(10000 / portTICK_PERIOD_MS);
        }
    }

    for(;;)
    {
        //串口接收
        rxbytes = 0;
        if(my_whgm5_info.init_ok)
        {
            //获取信号量
            xSemaphoreTake(MyWHGM5_CmdModeRcvMutexSemaphore, portMAX_DELAY);
            #if MYTEST_CONFIG_UART_IOT_SERVER
            rxbytes = Mytest_uart_read_bytes(LTE_UART_NUM, (void*)uart_rcv_buf, WHGM5_DATA_PACK_SIZE, 40 / portTICK_PERIOD_MS);
            #else
            rxbytes = uart_read_bytes(LTE_UART_NUM, (void*)uart_rcv_buf, WHGM5_DATA_PACK_SIZE, 40 / portTICK_PERIOD_MS);
            #endif
            //释放信号量
            xSemaphoreGive(MyWHGM5_CmdModeRcvMutexSemaphore);
        }
        else
        {
            vTaskDelay(1000 / portTICK_PERIOD_MS);
        }

        if(rxbytes>0)
        {
            #if DEBUG_PRINT_LEVEL_my_whgm5_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
            printf("MyWHGM5_TcpDataRcv_Task():rcv(%d)=\n", rxbytes);
            for(int j=0; j<rxbytes; j++)
            {
                printf("%02X ", uart_rcv_buf[j]);
            }
            printf("\n\n");
            #endif
            find_empty_count = 0;
            do
            {
                for(i=0; i<WHGM5_QUEUE_LEN; i++)
                {
                    if(!my_whgm5_rcv_queue_item_array[i]->valid)
                    {
                        memcpy(my_whgm5_rcv_queue_item_array[i]->data, uart_rcv_buf, rxbytes);
                        my_whgm5_rcv_queue_item_array[i]->len = rxbytes;
                        my_whgm5_rcv_queue_item_array[i]->valid = 1;

                        queue_send_ret = xQueueSend(my_whgm5_rcv_queue, &my_whgm5_rcv_queue_item_array[i], 1000 / portTICK_PERIOD_MS);
                        if(queue_send_ret!=pdTRUE)
                        {
                            ESP_LOGE("MyWHGM5_TcpDataRcv_Task()", "xQueueSend ERROR, queue_send_ret=%d\n", queue_send_ret);
                        }
                        break;
                    }
                }
                if(i>=WHGM5_QUEUE_LEN)
                {
                    ESP_LOGE("MyWHGM5_TcpDataRcv_Task()", "not find empty in rcv queue count=%d\n", find_empty_count);
                    vTaskDelay(50 / portTICK_PERIOD_MS);
                }
                else
                {
                    break;
                }
            } while ((++find_empty_count<3)&&(i>=WHGM5_QUEUE_LEN));
            if(find_empty_count>=3)
            {
                ESP_LOGE("MyWHGM5_TcpDataRcv_Task()", "not find empty in rcv queue finally!!!\n");
            }
            else
            {
                find_empty_count = 0;
            }
        }
    }
}

uint8_t MyWHGM5_TcpRcvData(my_whgm5_queue_item_t** const saving, uint32_t timeout)
{
    int queue_rcv_ret = pdFALSE;

    if(saving!=NULL)
    {
        queue_rcv_ret = xQueueReceive(my_whgm5_rcv_queue, saving, timeout/portTICK_PERIOD_MS);
        if(queue_rcv_ret!=pdTRUE)
        {
            ESP_LOGE("MyWHGM5_TcpRcvData()", "error: recv timeout\n");
            return 1;
        }
        return 0;
    }
    else
    {
        ESP_LOGE("MyWHGM5_TcpRcvData()", "error: saving==NULL\n");
        return 2;
    }
    return 0;
}

TaskHandle_t MyWHGM5_TcpDataSend_TaskHandle = NULL;
my_whgm5_queue_item_t* my_whgm5_send_queue_item_array[WHGM5_QUEUE_LEN];
void MyWHGM5_TcpDataSend_Task(void* param)
{
    char* uart_send_buf = (char*)calloc(1, 5120+WHGM5_DATA_PACK_SIZE);
    // my_whgm5_queue_item_t* queue_rcv_item = (my_whgm5_queue_item_t*)calloc(1, sizeof(my_whgm5_queue_item_t));
    my_whgm5_queue_item_t* queue_rcv_item = NULL;
    int queue_rcv_ret = pdFALSE;

    if(uart_send_buf==NULL)
    {
        ESP_LOGE("MyWHGM5_TcpDataSend_Task()", "uart_send_buf==NULL\n");
    }

    // if(queue_rcv_item==NULL)
    // {
    //     ESP_LOGE("MyWHGM5_TcpDataSend_Task()", "queue_rcv_item==NULL\n");
    // }

    for(;;)
    {
        //队列接收
        queue_rcv_ret = xQueueReceive(my_whgm5_send_queue, &queue_rcv_item, portMAX_DELAY);
        if(queue_rcv_ret==pdTRUE)
        {
            if(queue_rcv_item!=NULL)
            {
                if(queue_rcv_item->data!=NULL)
                {
                    memcpy(uart_send_buf, queue_rcv_item->data, queue_rcv_item->len);
                    //获取信号量
                    xSemaphoreTake(MyWHGM5_CmdModeSendMutexSemaphore, portMAX_DELAY);
                    
                    ESP_LOGI("#####", "=====");
                    #if MYTEST_CONFIG_UART_IOT_SERVER
                    Mytest_UartSendData(uart_send_buf, queue_rcv_item->len);
                    #else
                    UartSendData(uart_send_buf, queue_rcv_item->len);
                    #endif
                    //释放信号量
                    xSemaphoreGive(MyWHGM5_CmdModeSendMutexSemaphore);

                    queue_rcv_item->valid = 0;
                }
            }
            else
            {
                ESP_LOGE("MyWHGM5_TcpDataSend_Task()", "queue_rcv_item!=NULL is false\n");
            }
        }
    }
}

SemaphoreHandle_t MyWHGM5_TcpSendDataMutexSemaphore = NULL;
int MyWHGM5_TcpSendData(void const* data, uint32_t len, uint32_t timeout, uint8_t tcp_channel)
{
    int queue_send_ret = pdTRUE;
    int i = 0;
    int retry_count = 0;

    //获取信号量
    MyWHGM5_TcpSendData_retry:
    xSemaphoreTake(MyWHGM5_TcpSendDataMutexSemaphore, 10000/portTICK_PERIOD_MS);
    for(i=0; i<WHGM5_QUEUE_LEN; i++)
    {
        if(!my_whgm5_send_queue_item_array[i]->valid)
        {
            my_whgm5_send_queue_item_array[i]->id = tcp_channel;
            my_whgm5_send_queue_item_array[i]->len = len;
            my_whgm5_send_queue_item_array[i]->valid = 1;
            //my_whgm5_send_queue_item_array[i]->data = data;
            if(my_whgm5_send_queue_item_array[i]->data!=NULL)
            {
                memcpy(my_whgm5_send_queue_item_array[i]->data, data, len);
                my_whgm5_send_queue_item_array[i]->data[len]=0;
            }

            break;
        }
    }
    //释放信号量
    xSemaphoreGive(MyWHGM5_TcpSendDataMutexSemaphore);
    if(i>=WHGM5_QUEUE_LEN&&retry_count<10)
    {
        vTaskDelay(timeout / portTICK_PERIOD_MS);
        retry_count++;
        goto MyWHGM5_TcpSendData_retry;
    }
    else if(i<WHGM5_QUEUE_LEN)
    {
        #if DEBUG_PRINT_LEVEL_my_whgm5_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
        //printf("MyWHGM5_TcpSendData():i=%d\n", i);
        #endif
        if(my_whgm5_send_queue!=NULL)
            queue_send_ret = xQueueSend(my_whgm5_send_queue, &my_whgm5_send_queue_item_array[i], timeout / portTICK_PERIOD_MS);
        return 0;
    }
    else
    {
        ESP_LOGE("MyWHGM5_TcpSendData()", "error, i=%d\n", i);
        return 1;
    }

    return 0;
}

static int MyWHGM5_Queue_Init(my_whgm5_queue_item_t** p, uint8_t data_area)
{
    if(data_area)
    {
        char* p_temp = (char*)calloc(WHGM5_QUEUE_LEN, WHGM5_DATA_PACK_SIZE);
        if(p_temp!=NULL)
        { 
            for(int i=0; i<WHGM5_QUEUE_LEN; i++)
            {
                p[i] = (my_whgm5_queue_item_t*)calloc(1, sizeof(my_whgm5_queue_item_t));
                if(p[i]!=NULL)
                {
                    p[i]->data = p_temp+(i*(WHGM5_DATA_PACK_SIZE));
                }
                else
                {
                    ESP_LOGE("MyWHGM5_Queue_Init()", "error1\n");
                    return 1;
                }
            }
        }
        else
        {
            ESP_LOGE("MyWHGM5_Queue_Init()", "error2\n");
            return 2;
        }
    }
    else
    {
        for(int i=0; i<WHGM5_QUEUE_LEN; i++)
        {
            p[i] = (my_whgm5_queue_item_t*)calloc(1, sizeof(my_whgm5_queue_item_t));
            if(p[i]==NULL)
            {
                ESP_LOGE("MyWHGM5_Queue_Init()", "error3, i=%d\n", i);
                return i;
            }
        }
    }
    return WHGM5_QUEUE_LEN;
}

int MyWHGM5_TcpDataStationInit(void)
{
    MyWHGM5_TcpSendDataMutexSemaphore = xSemaphoreCreateMutex();
    MyWHGM5_CmdModeSendMutexSemaphore = xSemaphoreCreateMutex();
    MyWHGM5_CmdModeRcvMutexSemaphore = xSemaphoreCreateMutex();

    MyWHGM5_Queue_Init(my_whgm5_send_queue_item_array, 1);
    MyWHGM5_Queue_Init(my_whgm5_rcv_queue_item_array, 1);

    my_whgm5_rcv_queue = xQueueGenericCreate(WHGM5_QUEUE_LEN, sizeof(my_whgm5_queue_item_t*), 1);
    if(my_whgm5_rcv_queue==NULL)ESP_LOGE("MyWHGM5_TcpDataStationInit()", "my_whgm5_rcv_queue=NULL\n");
    my_whgm5_send_queue = xQueueGenericCreate(WHGM5_QUEUE_LEN, sizeof(my_whgm5_queue_item_t*), 1);
    if(my_whgm5_send_queue==NULL)ESP_LOGE("MyWHGM5_TcpDataStationInit()", "my_whgm5_send_queue=NULL\n");

    BaseType_t ret = pdPASS;
    #if MyWHGM5_TcpDataRcv_Task_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    p_task_stack = calloc(1, MyWHGM5_TcpDataRcv_Task_task_stack_size*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
			if(xTaskCreateStaticPinnedToCore(MyWHGM5_TcpDataRcv_Task, "MyWHGM5_TcpDataRcv_Task", MyWHGM5_TcpDataRcv_Task_task_stack_size, NULL, MyWHGM5_TcpDataRcv_Task_priority, p_task_stack, p_task_data, MyWHGM5_TcpDataRcv_Task_COREID)!=p_task_data)
			{
				ESP_LOGE("MyEspChipInfoMng_Init()", "creat MyWHGM5_TcpDataRcv_Task use xTaskCreateStaticPinnedToCore() error\n");
                return 1;
			}
        }else return 1;
    }else return 1;
	#elif MyWHGM5_TcpDataRcv_Task_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
	ret = xTaskCreatePinnedToCore(MyWHGM5_TcpDataRcv_Task, "MyWHGM5_RcvTcpData_Task", MyWHGM5_TcpDataRcv_Task_task_stack_size, NULL, MyWHGM5_TcpDataRcv_Task_priority, &MyWHGM5_TcpDataRcv_TaskHandle, MyWHGM5_TcpDataRcv_Task_COREID);
	if(ret!=pdPASS)
	{
		ESP_LOGE("MyEspChipInfoMng_Init()", "creat MyWHGM5_TcpDataRcv_Task use xTaskCreatePinnedToCore() error=%d\n", ret);
        return 1;
	}
	#endif

    #if MyWHGM5_TcpDataSend_Task_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    p_task_stack = calloc(1, MyWHGM5_TcpDataSend_Task_task_stack_size*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
			if(xTaskCreateStaticPinnedToCore(MyWHGM5_TcpDataSend_Task, "MyWHGM5_TcpDataSend_Task", MyWHGM5_TcpDataSend_Task_task_stack_size, NULL, MyWHGM5_TcpDataSend_Task_priority, p_task_stack, p_task_data, MyWHGM5_TcpDataSend_Task_COREID)!=p_task_data)
			{
				ESP_LOGE("MyEspChipInfoMng_Init()", "creat MyWHGM5_TcpDataSend_Task use xTaskCreateStaticPinnedToCore() error\n");
                return 1;
			}
        }else return 1;
    }else return 1;
	#elif MyWHGM5_TcpDataSend_Task_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
	ret = xTaskCreatePinnedToCore(MyWHGM5_TcpDataSend_Task, "MyWHGM5_SendTcpData_Task", MyWHGM5_TcpDataSend_Task_task_stack_size, NULL, MyWHGM5_TcpDataSend_Task_priority, &MyWHGM5_TcpDataSend_TaskHandle, MyWHGM5_TcpDataSend_Task_COREID);
	if(ret!=pdPASS)
	{
		ESP_LOGE("MyEspChipInfoMng_Init()", "creat MyWHGM5_TcpDataSend_Task use xTaskCreatePinnedToCore() error=%d\n", ret);
        return 1;
	}
	#endif
    return 0;
}

#endif