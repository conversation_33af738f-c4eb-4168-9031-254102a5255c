#include "my_aux_485_port.h"

#include "e104_bt53c3.h"

#include "my_gpio.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"
#include "freertos/semphr.h"
#include "freertos/timers.h"

#include "esp_system.h"
#include "esp_log.h"
#include "driver/uart.h"
#include "driver/gpio.h"

#include "uart_to_uart.h"

#include "string.h"
#include "stdlib.h"




TaskHandle_t UsartAUX_PORTRcvTask_Handle = NULL;

#define AUX_PORT_RX_BUF_SIZE   1024

#define AUX_PORT_BAUDRATE 9600UL

#define AUX_PORT_TXD_PIN (GPIO_NUM_20)
#define AUX_PORT_RXD_PIN (GPIO_NUM_19)

static void Uart_AUX_PORT_Init(void)
{
    const uart_config_t uart_config = {
        .baud_rate = AUX_PORT_BAUDRATE,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE
    };
    uart_param_config(UART_NUM_1, &uart_config);
    uart_set_pin(UART_NUM_1, AUX_PORT_TXD_PIN, AUX_PORT_RXD_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    // We won't use a buffer for sending data.
    uart_driver_install(UART_NUM_1, AUX_PORT_RX_BUF_SIZE, 0, 0, NULL, 0);
}

SemaphoreHandle_t AUX_PORTsendDataMutexSemaphore = NULL;
int AUX_PORTsendData(const char* logName, const char* data, uint16_t len)
{
    xSemaphoreTake(AUX_PORTsendDataMutexSemaphore, portMAX_DELAY);
    const int txBytes = uart_write_bytes(UART_NUM_1, data, len);
    xSemaphoreGive(AUX_PORTsendDataMutexSemaphore);
    ESP_LOGI(logName, "Wrote %d bytes", txBytes);
    return txBytes;
}

void UsartAUX_PORTRcvTask( void *pvParameters )
{
    static const char *RX_TASK_TAG = "UsartAUX_PORTRcvTask";
    char* data = (char*)calloc(AUX_PORT_RX_BUF_SIZE+1, sizeof(char));
    uint32_t rxbytes = 0;
    
    for(;;)
    {
        rxbytes = uart_read_bytes(UART_NUM_1, (void*)data, AUX_PORT_RX_BUF_SIZE, 100 / portTICK_RATE_MS);
        if(rxbytes)
        {
            // if(AsendData(RX_TASK_TAG, data, rxbytes))
            {
                rxbytes = 0;
                printf("UsartAUX_PORTRcvTask:rcv:%s\n", data);
                AUX_PORTsendData("UsartAUX_PORTRcvTask", "aux rcved\r\n", strlen("aux rcved\r\n"));

                char* start = strstr(data, "BT_set_adv_name:");
                if(start != NULL)
                {
                    char* data_start = start + strlen("BT_set_adv_name:");
                    if(!e104_bt53c3_set_adv_name(data_start, strlen(data_start)))
                    {
                        printf("set adv name success\n");
                    }
                }

                start = strstr(data, "BT_set_adv_data:");
                if(start != NULL)
                {
                    char* data_start = start + strlen("BT_set_adv_data:");
                    if(!e104_bt53c3_set_adv_data(data_start, strlen(data_start)))
                    {
                        printf("set adv data success\n");
                    }
                }

                start = strstr(data, "BT_send_data_to_master:");
                if(start != NULL)
                {
                    char* data_start = start + strlen("BT_send_data_to_master:");
                    if(!e104_bt53c3_send_data_to_master(data_start, strlen(data_start)))
                    {
                        printf("send data to master success\n");
                    }
                }

                memset(data, 0, AUX_PORT_RX_BUF_SIZE);
            }
        }
    }
    free(data);
}


TaskHandle_t UsartAUX_PORTSendTask_Handle = NULL;
void UsartAUX_PORTSendTask( void *pvParameters )
{
    vTaskDelay(5000 / portTICK_PERIOD_MS);
    AUX_PORTsendData("UsartAUX_PORTRcvTask", "aux run\r\n", strlen("aux run\r\n"));
    for(;;)
    {
        vTaskDelay(2000 / portTICK_PERIOD_MS);
    }
}



int my_aux_485_port_init(void)
{
    AUX_PORTsendDataMutexSemaphore = xSemaphoreCreateMutex();

    Uart_AUX_PORT_Init();
    xTaskCreate(UsartAUX_PORTRcvTask,  "UsartAUX_PORTRcvTask",    8192,    NULL, 5, &UsartAUX_PORTRcvTask_Handle);
    xTaskCreate(UsartAUX_PORTSendTask,  "UsartAUX_PORTSendTask",    8192,    NULL, 5, &UsartAUX_PORTSendTask_Handle);


    vTaskDelay(1000 / portTICK_PERIOD_MS);

    return 0;
}