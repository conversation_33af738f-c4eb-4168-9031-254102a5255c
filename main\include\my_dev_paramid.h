#ifndef MY_DEV_PARAMID_H
#define MY_DEV_PARAMID_H


typedef enum
{
    devPI_asl_appId=0x0100,
    devPI_asl_loginFlag,
    devPI_asl_dic,
    devPI_asl_token,
    devPI_asl_last_sync_param_timestamp,
    devPI_asl_sync_param_effect_time
}asl_dev_param_id_asl_t;

typedef enum
{
    devPI_tsfc_frame_max_size_send=0x0200,
    devPI_tsfc_frame_max_size_rcv,
    devPI_tsfc_sendFail_max_repeat,
    devPI_tsfc_aesKey
}asl_dev_param_id_tsfc_t;

typedef enum
{
    devPI_net_server_ipv4_address=0x0300,
    devPI_net_server_ipv4_address_type,
    devPI_net_server_port,
    devPI_net_server_ota_ipv4_address,
    devPI_net_server_ota_port
}asl_dev_param_id_net_t;

typedef enum
{
    devPI_workmode_curMode=0x0400,
    devPI_workmode_worktimeList_1_start_time=0x0410,
    devPI_workmode_worktimeList_1_keep_time,
    devPI_workmode_worktimeList_2_start_time,
    devPI_workmode_worktimeList_2_keep_time,
    devPI_workmode_worktimeList_3_start_time,
    devPI_workmode_worktimeList_3_keep_time,
    devPI_workmode_worktimeList_4_start_time,
    devPI_workmode_worktimeList_4_keep_time,
    devPI_workmode_worktimeList_5_start_time,
    devPI_workmode_worktimeList_5_keep_time,
    devPI_workmode_timermodeSleepTime=0x0440,
    devPI_workmode_imageReportInterval,
    devPI_workmode_measReportInterval,
    devPI_workmode_measReportmode,
    devPI_workmode_vrc1_start_time=0x0460,
    devPI_workmode_vrc1_keep_time,
    devPI_workmode_vrc2_start_time,
    devPI_workmode_vrc2_keep_time,
    devPI_workmode_vrc3_start_time,
    devPI_workmode_vrc3_keep_time,
    devPI_workmode_vrc4_start_time,
    devPI_workmode_vrc4_keep_time,
    devPI_workmode_vrc5_start_time,
    devPI_workmode_vrc5_keep_time,
    devPI_workmode_debugmode=0x04FF,
    devPI_workmode_relay1_mode=0x04FE,
    devPI_workmode_relay1_Xmode_keep_time=0x04FD,
    devPI_workmode_channeldoor_mode=0x04FC,
}asl_dev_param_id_workmode_t;

typedef enum
{
    devPI_ble_enable_state=0x0500,
    devPI_ble_cur_state,
    devPI_ble_name=0x0510,
    devPI_ble_advType,
    devPI_ble_advData_len,
    devPI_ble_advData,
    devPI_ble_service_uuid_a,
    devPI_ble_char_uuid_a,
    devPI_ble_service_uuid_b,
    devPI_ble_char_uuid_b,
    devPI_ble_mac,
}asl_dev_param_id_ble_t;

typedef enum
{
    devPI_cam_model=0x0600,
    devPI_cam_support_photo_size_list,
    devPI_cam_support_compress_list,
    devPI_cam_flip_list,
    devPI_cam_format_list,
    devPI_cam_pickpicture_switch,

    devPI_cam_cur_format=0x0610,

    devPI_cam_cur_photo_size_x=0x0620,
    devPI_cam_cur_photo_size_y,

    devPI_cam_cur_photo_compress=0x0630,

    devPI_cam_cur_photo_flip_h=0x0640,
    devPI_cam_cur_photo_flip_v,
    devPI_cam_cur_photo_rotate_90,
    devPI_cam_cur_photo_rotate_180,
    devPI_cam_cur_photo_rotate_270,

    devPI_cam_cur_photo_contrast=0x0650,
    devPI_cam_cur_photo_brightness,
    devPI_cam_cur_photo_saturation,
    devPI_cam_cur_wb_mode,
    devPI_cam_cur_special_effect
}dev_param_id_cam_t;

typedef enum
{
    devPI_power_state=0x0700,
    devPI_power_poweroff_alarm_enable_state,
    devPI_power_shutdown_after_poweroffAlarm
}dev_param_id_power_t;

typedef enum
{
    devPI_factory_devstate=0x0800,
    devPI_factory_factoryDate,
    devPI_factory_poweron_reason=0x0810,
    devPI_factory_poweronCount,
    devPI_factory_runtime_since_poweron,
    devPI_factory_mac=0x0820,
    devPI_factory_imei,
    devPI_factory_iccid,
    devPI_factory_ota_info_local=0x0830,
    devPI_factory_ota_info_net,
    devPI_factory_ota_check_type,
    devPI_factory_ota_vercode=0x0834,
    devPI_factory_ota_vername,
    devPI_factory_ota_filename,
    devPI_factory_devType=0x0840,
    devPI_factory_devModel,
    devPI_factory_devId,
    devPI_factory_httpota_url=0x08FF,
}dev_param_id_factory_t;

typedef enum
{
    devPI_wifi_enable_state=0x0900,
    devPI_wifi_cur_state,
    devPI_wifi_mode,
    devPI_wifi_cur_mode,
    devPI_wifi_station_ssid=0x0910,
    devPI_wifi_station_password,
    devPI_wifi_ap_ssid,
    devPI_wifi_ap_password,
}dev_param_id_wifi_t;

typedef enum
{
    devPI_lte_baudrate=0x0A00,
}dev_param_id_lte_t;

typedef enum
{
    devPI_gnss_longitude=0x0B00,
    devPI_gnss_latitude,
    devPI_gnss_auto_report_switch,
    devPI_gnss_auto_report_interval,
}dev_param_id_gnss_t;

typedef enum
{
    devPI_event_reboot=0x8100,
    devPI_event_startWork,
    devPI_event_stopWork,
    devPI_event_external_poweroff,
    devPI_event_external_poweron,
}dev_param_id_event_workrun_t;

typedef enum
{
    devPI_event_ble_client_connected=0x8200,
    devPI_event_ble_client_disconnected,
}dev_param_id_event_ble_t;

typedef enum
{
    devPI_event_wlan_ap_client_connected=0x8300,
    devPI_event_wlan_ap_client_disconnected,
    devPI_event_wlan_wifi_connected,
    devPI_event_wlan_wifi_client_disconnected
}dev_param_id_event_wlan_t;

typedef enum
{
    devPI_event_attitude_dev_move=0x8400,
    devPI_event_attitude_dev_attitude_err,
}dev_param_id_event_attitude_t;

typedef enum
{
    devPI_ctrl_workrun_reboot=0xC100,
    devPI_ctrl_workrun_reload_default_configs
}dev_param_id_ctrl_workrun_t;

typedef enum
{
    devPI_ctrl_ble_open=0xC200,
    devPI_ctrl_ble_close,
}dev_param_id_ctrl_ble_t;

typedef enum
{
    devPI_ctrl_wlan_ap_open=0xC300,
    devPI_ctrl_wlan_ap_close,
    devPI_ctrl_wlan_wifi_open,
    devPI_ctrl_wlan_wifi_close,
}dev_param_id_ctrl_wlan_t;

typedef enum
{
    devPI_ctrl_cam_take_picture=0xC400,
}dev_param_id_ctrl_cam_t;

typedef enum
{
    devPI_ctrl_ota_check=0xC500,
}dev_param_id_ctrl_ota_t;

typedef enum
{
    devPI_report_type_upload_image_file=0x0100,
    devPI_report_type_upload_log_file=0x0200,
    devPI_report_type_download_package_file=0x0300,
    devPI_report_type_download_config_file=0x0400,
    devPI_report_type_radar_segment_data=0x0500,
    devPI_report_type_radar_stat_data=0x0501,
    devPI_report_type_radar_regular_data=0x0502,
}dev_report_type_t;

typedef enum
{
    devPI_reportdata_type_upload_image_file=0x01,
    devPI_reportdata_type_upload_log_file,
    devPI_reportdata_type_download_package_file,
    devPI_reportdata_type_download_config_file,
    devPI_reportdata_type_radar_segment_data,
    devPI_reportdata_type_radar_stat_data,
    devPI_reportdata_type_radar_regular_data,
}dev_reportdata_type_t;

#endif