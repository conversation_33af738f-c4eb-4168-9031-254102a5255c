#include "my_kmp.h"
#include <string.h>
#include <stdio.h>

void getnext(char const* const p, int len, int* const next)
{
	int k=-1;
	int j=0;
	next[0]=-1;
    
    
	//printf("\n j= %d next[0]%d",j,next[j]);
	while(j<len-1)
	{
		if(k==-1||p[k]==p[j])
		{
			k++;
			j++;
			next[j]=k;
			//printf("\n j= %d next[%d]%d",j,j,next[j]);
		}
		else
        {
			k=next[k];
		}
	}
}

int kmp(char const* const s, int s_len, char const* const p, int p_len, int *next)
{
	int i = 0;
	int j = 0;
	int sLen = s_len;
	int pLen = p_len;
    
    
	while(i<sLen&&j<pLen) 
	{
		if (j == -1 || s[i] == p[j])
		{
			i++;
			j++;
		}
		else
		{
			//②如果j != -1，且当前字符匹配失败（即S[i] != P[j]），则令 i 不变，j = next[j]    
			//next[j]即为j所对应的next值      
			j=next[j];
        }
	}
	if(j>=pLen)
		return(i-j);
	return 0xffff;
}

int MatchString(char const* const strings, int strings_len, char const* const object, int object_len)
{
    int next[50];
    int position = 0xffff;

    //memset(next, 0, sizeof(next));
    
    getnext(object, object_len, next);
    //printf("MatchString():next=");
    // for(int i=0; i<sizeof(next); i++)
    // {
    //     //printf("0x%X ", next[i]);
    // }
    //printf("\n");
    position=kmp(strings, strings_len, object, object_len, next);
    
    return position;
}

char* FindBinary(const char* pucSrc, int nsrcSize, const char* pucFind, int nFindSize)
{
	const char *pSrcPos = pucSrc;
	const char *pFindPos = pucFind;

  /* deliberately dumb algorithm */
  while ( pSrcPos - pucSrc <= nsrcSize )
	{
		pFindPos = pucFind;
		while ( (pSrcPos-pucSrc<nsrcSize) && (pFindPos-pucFind<nFindSize) )
		{
			if (*pSrcPos != *pFindPos)
				break;
		
			pSrcPos++;
			pFindPos++;
		}
		
		if ( (pFindPos-pucFind) >= nFindSize )
			return (char *)pSrcPos;
                //返回是查找匹配字符串的后一个字符指针
		pSrcPos++;
	}

  return NULL;
}