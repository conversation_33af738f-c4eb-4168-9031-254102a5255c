#include "uart_to_uart.h"
#include "my_gpio.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"
#include "freertos/semphr.h"
#include "esp_system.h"
#include "esp_log.h"
#include "driver/uart.h"
#include "driver/gpio.h"

#include "string.h"
#include "stdlib.h"


#if UART_TO_UART_FUNC


TaskHandle_t UsartARcvTask_Handle = NULL;
TaskHandle_t UsartBRcvTask_Handle = NULL;

#define A_RX_BUF_SIZE   1024
#define B_RX_BUF_SIZE   1024

#define A_BAUDRATE 115200UL
#define B_BAUDRATE 115200UL

#define A_TXD_PIN (GPIO_NUM_2)
#define A_RXD_PIN (GPIO_NUM_16)

#define B_TXD_PIN (GPIO_NUM_12)
#define B_RXD_PIN (GPIO_NUM_13)


#define LTE_RESET_GPIO_PIN  15
#define GPIO_LTE_RESET_PIN_SEL  ( (1ULL<<LTE_RESET_GPIO_PIN)  )


static void Uart_A_Init(void)
{
    const uart_config_t uart_config = {
        .baud_rate = A_BAUDRATE,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE
    };
    uart_param_config(UART_NUM_1, &uart_config);
    uart_set_pin(UART_NUM_1, A_TXD_PIN, A_RXD_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    // We won't use a buffer for sending data.
    uart_driver_install(UART_NUM_1, A_RX_BUF_SIZE, 0, 0, NULL, 0);
}

static void Uart_B_Init(void)
{
    const uart_config_t uart_config = {
        .baud_rate = B_BAUDRATE,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE
    };
    uart_param_config(UART_NUM_2, &uart_config);
    uart_set_pin(UART_NUM_2, B_TXD_PIN, B_RXD_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    // We won't use a buffer for sending data.
    uart_driver_install(UART_NUM_2, B_RX_BUF_SIZE, 0, 0, NULL, 0);
}

int AsendData(const char* logName, const char* data, uint16_t len)
{
    const int txBytes = uart_write_bytes(UART_NUM_1, data, len);
    ESP_LOGI(logName, "Wrote %d bytes", txBytes);
    return txBytes;
}

static int BsendData(const char* logName, const char* data, uint16_t len)
{
    const int txBytes = uart_write_bytes(UART_NUM_2, data, len);
    ESP_LOGI(logName, "Wrote %d bytes", txBytes);
    return txBytes;
}

void UsartARcvTask( void *pvParameters )
{
    static const char *RX_TASK_TAG = "UsartARcvTask";
    char* data = (char*)calloc(A_RX_BUF_SIZE+1, sizeof(char));
    uint32_t rxbytes = 0;

    for(;;)
    {
        rxbytes = uart_read_bytes(UART_NUM_1, (void*)data, A_RX_BUF_SIZE, 100 / portTICK_RATE_MS);
        if(rxbytes)
        {
            if(AsendData(RX_TASK_TAG, data, rxbytes))
            {
                rxbytes = 0;
                memset(data, 0, A_RX_BUF_SIZE);
            }
        }
    }
    free(data);
}

void UsartBRcvTask( void *pvParameters )
{
    static const char *RX_TASK_TAG = "UsartBRcvTask";
    char* data = (char*)calloc(B_RX_BUF_SIZE+1, sizeof(char));
    uint32_t rxbytes = 0;
    
    for(;;)
    {
        rxbytes = uart_read_bytes(UART_NUM_2, (void*)data, B_RX_BUF_SIZE, 100 / portTICK_RATE_MS);
        if(rxbytes)
        {
            if(BsendData(RX_TASK_TAG, data, rxbytes))
            {
                rxbytes = 0;
                memset(data, 0, B_RX_BUF_SIZE);
            }
        }
    }
    free(data);
}

static void LteGpioInit(void)
{
    gpio_config_t io_conf;
    //disable interrupt
    io_conf.intr_type = GPIO_PIN_INTR_DISABLE;
    //set as output mode
    io_conf.mode = GPIO_MODE_OUTPUT;
    //bit mask of the pins that you want to set,e.g.GPIO18/19
    io_conf.pin_bit_mask = GPIO_LTE_RESET_PIN_SEL;
    //disable pull-down mode
    io_conf.pull_down_en = 0;
    //disable pull-up mode
    io_conf.pull_up_en = 0;
    //configure GPIO with the given settings
    gpio_config(&io_conf);

    MyGPIO_SET(LTE_RESET_GPIO_PIN, 0);
    vTaskDelay(3000/portTICK_PERIOD_MS);
    MyGPIO_SET(LTE_RESET_GPIO_PIN, 1);
}

void UartToUartInit(void)
{
    Uart_A_Init();
    Uart_B_Init();

    xTaskCreate(UsartARcvTask,  "UsartARcvTask",    8192,    NULL, 5, &UsartARcvTask_Handle);
    xTaskCreate(UsartBRcvTask,  "UsartBRcvTask",    8192,    NULL, 5, &UsartBRcvTask_Handle);

    LteGpioInit();
}

#endif