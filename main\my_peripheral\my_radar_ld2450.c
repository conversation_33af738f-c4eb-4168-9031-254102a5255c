#include "my_radar_ld2450.h"

#if MY_RADAR_LD2450_FUNC

#include "my_radar_uart.h"
#include <esp_log.h>
#include "esp_system.h"

const char my_radar_ld2450_cmd_enable_config_mode[] = {0xFD, 0xFC, 0xFB, 0xFA, 0x04, 0x00, 0xFF, 0x00, 0x01, 0x00, 0x04, 0x03, 0x02, 0x01};
const char my_radar_ld2450_cmd_disable_config_mode[] = {0xFD, 0xFC, 0xFB, 0xFA, 0x02, 0x00, 0xFE, 0x00, 0x04, 0x03, 0x02, 0x01};
const char my_radar_ld2450_cmd_trace_single[] = {0xFD, 0xFC, 0xFB, 0xFA, 0x02, 0x00, 0x80, 0x00, 0x04, 0x03, 0x02, 0x01};
const char my_radar_ld2450_cmd_trace_multi[] = {0xFD, 0xFC, 0xFB, 0xFA, 0x02, 0x00, 0x90, 0x00, 0x04, 0x03, 0x02, 0x01};
const char my_radar_ld2450_cmd_reboot[] = {0xFD, 0xFC, 0xFB, 0xFA, 0x02, 0x00, 0xA3, 0x00, 0x04, 0x03, 0x02, 0x01};
const char my_radar_ld2450_cmd_open_buletooth[] = {0xFD, 0xFC, 0xFB, 0xFA, 0x04, 0x00, 0xA4, 0x00, 0x01, 0x00, 0x04, 0x03, 0x02, 0x01};
const char my_radar_ld2450_cmd_close_buletooth[] = {0xFD, 0xFC, 0xFB, 0xFA, 0x04, 0x00, 0xA4, 0x00, 0x00, 0x00, 0x04, 0x03, 0x02, 0x01};
const char my_radar_ld2450_cmd_query_mac[] = {0xFD, 0xFC, 0xFB, 0xFA, 0x04, 0x00, 0xA5, 0x00, 0x01, 0x00, 0x04, 0x03, 0x02, 0x01};
const char my_radar_ld2450_cmd_rfs[] = {0xFD, 0xFC, 0xFB, 0xFA, 0x02, 0x00, 0xA2, 0x00, 0x04, 0x03, 0x02, 0x01};
const char my_radar_ld2450_cmd_query_frame_version[] = {0xFD, 0xFC, 0xFB, 0xFA, 0x02, 0x00, 0xA0, 0x00, 0x04, 0x03, 0x02, 0x01};
char my_radar_ld2450_cmd_set_baudrate[] = {0xFD, 0xFC, 0xFB, 0xFA, 0x04, 0x00, 0xA1, 0x00, 0x07, 0x00, 0x04, 0x03, 0x02, 0x01};
const char my_radar_ld2450_cmd_query_cur_area_filter[] = {0xFD, 0xFC, 0xFB, 0xFA, 0x02, 0x00, 0xC1, 0x00, 0x04, 0x03, 0x02, 0x01};
char my_radar_ld2450_cmd_set_cur_area_filter[] = {0xFD, 0xFC, 0xFB, 0xFA, 0x1C, 0x00, 0xC2, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x03, 0x02, 0x01};


int _My_Radar_LD2450_EnableConfig(void)
{
    vTaskDelay(100 / portTICK_PERIOD_MS);
    AsendData("My_Radar_LD2450_EnableConfig", my_radar_ld2450_cmd_enable_config_mode, sizeof(my_radar_ld2450_cmd_enable_config_mode));
    vTaskDelay(100 / portTICK_PERIOD_MS);

    return 0;
}

int _My_Radar_LD2450_DisableConfig(void)
{
    vTaskDelay(100 / portTICK_PERIOD_MS);
    AsendData("My_Radar_LD2450_DisableConfig", my_radar_ld2450_cmd_disable_config_mode, sizeof(my_radar_ld2450_cmd_disable_config_mode));
    vTaskDelay(100 / portTICK_PERIOD_MS);

    return 0;
}

int _My_Radar_LD2450_Config_Trace_Single(void)
{
    _My_Radar_LD2450_EnableConfig();
    AsendData("My_Radar_LD2450_Config_Trace_Single", my_radar_ld2450_cmd_trace_single, sizeof(my_radar_ld2450_cmd_trace_single));
    _My_Radar_LD2450_DisableConfig();

    return 0;
}

int _My_Radar_LD2450_Config_Trace_Multi(void)
{
    _My_Radar_LD2450_EnableConfig();
    AsendData("My_Radar_LD2450_Config_Trace_Multi", my_radar_ld2450_cmd_trace_multi, sizeof(my_radar_ld2450_cmd_trace_multi));
    _My_Radar_LD2450_DisableConfig();

    return 0;
}

int _My_Radar_LD2450_Config_Query_FrameVersion(void)
{
    _My_Radar_LD2450_EnableConfig();
    AsendData("My_Radar_LD2450_Config_Query_FrameVersion", my_radar_ld2450_cmd_query_frame_version, sizeof(my_radar_ld2450_cmd_query_frame_version));
    _My_Radar_LD2450_DisableConfig();

    return 0;
}

int _My_Radar_LD2450_Config_Set_Baudrate(uint32_t newvalue)
{
    _My_Radar_LD2450_EnableConfig();
    switch(newvalue)
    {
        case(9600):
            my_radar_ld2450_cmd_set_baudrate[8] = 0x01;
            break;
        case(19200):
            my_radar_ld2450_cmd_set_baudrate[8] = 0x02;
            break;
        case(38400):
            my_radar_ld2450_cmd_set_baudrate[8] = 0x03;
            break;
        case(57600):
            my_radar_ld2450_cmd_set_baudrate[8] = 0x04;
            break;
        case(115200):
            my_radar_ld2450_cmd_set_baudrate[8] = 0x05;
            break;
        case(230400):
            my_radar_ld2450_cmd_set_baudrate[8] = 0x06;
            break;
        case(256000):
            my_radar_ld2450_cmd_set_baudrate[8] = 0x07;
            break;
        case(460800):
            my_radar_ld2450_cmd_set_baudrate[8] = 0x08;
            break;
        default:
            my_radar_ld2450_cmd_set_baudrate[8] = 0x07;
    }
    AsendData("My_Radar_LD2450_Config_Set_Baudrate", my_radar_ld2450_cmd_set_baudrate, sizeof(my_radar_ld2450_cmd_set_baudrate));
    _My_Radar_LD2450_DisableConfig();

    return 0;
}

int _My_Radar_LD2450_Config_RFS(void)
{
    _My_Radar_LD2450_EnableConfig();
    AsendData("My_Radar_LD2450_Config_RFS", my_radar_ld2450_cmd_rfs, sizeof(my_radar_ld2450_cmd_rfs));
    _My_Radar_LD2450_DisableConfig();

    return 0;
}

int _My_Radar_LD2450_Config_Reboot(void)
{
    _My_Radar_LD2450_EnableConfig();
    AsendData("My_Radar_LD2450_Config_Reboot", my_radar_ld2450_cmd_reboot, sizeof(my_radar_ld2450_cmd_reboot));
    _My_Radar_LD2450_DisableConfig();

    return 0;
}

int _My_Radar_LD2450_Config_Open_Bluetooth(void)
{
    _My_Radar_LD2450_EnableConfig();
    AsendData("My_Radar_LD2450_Config_Open_Bluetooth", my_radar_ld2450_cmd_open_buletooth, sizeof(my_radar_ld2450_cmd_open_buletooth));
    _My_Radar_LD2450_DisableConfig();

    return 0;
}

int _My_Radar_LD2450_Config_Close_Bluetooth(void)
{
    _My_Radar_LD2450_EnableConfig();
    AsendData("My_Radar_LD2450_Config_Close_Bluetooth", my_radar_ld2450_cmd_close_buletooth, sizeof(my_radar_ld2450_cmd_close_buletooth));
    _My_Radar_LD2450_DisableConfig();

    return 0;
}

int _My_Radar_LD2450_Config_Query_Mac(void)
{
    _My_Radar_LD2450_EnableConfig();
    AsendData("My_Radar_LD2450_Config_Query_Mac", my_radar_ld2450_cmd_query_mac, sizeof(my_radar_ld2450_cmd_query_mac));
    _My_Radar_LD2450_DisableConfig();

    return 0;
}

int _My_Radar_LD2450_Config_Query_CurAreaFilterConfig(void)
{
    _My_Radar_LD2450_EnableConfig();
    AsendData("My_Radar_LD2450_Config_Query_CurAreaFilterConfig", my_radar_ld2450_cmd_query_cur_area_filter, sizeof(my_radar_ld2450_cmd_query_cur_area_filter));
    _My_Radar_LD2450_DisableConfig();

    return 0;
}

int _My_Radar_LD2450_Config_Set_CurAreaFilterConfig(uint8_t filter_type, void* filter_data)
{
    _My_Radar_LD2450_EnableConfig();
    char* p_filter_data = (char*)my_radar_ld2450_cmd_set_cur_area_filter;
    my_radar_ld2450_cmd_set_cur_area_filter[8]=filter_type;
    memcpy(p_filter_data+10, filter_data, 24);
    ESP_LOGI("My_Radar_LD2450_Config_Set_CurAreaFilterConfig()", "p_filter_data=%d", sizeof(my_radar_ld2450_cmd_set_cur_area_filter));
    for(int i=0; i<sizeof(my_radar_ld2450_cmd_set_cur_area_filter); i++)
    {
        printf("%02X ", *(p_filter_data+i));
    }
    printf("\n####\n");
    AsendData("My_Radar_LD2450_Config_Set_CurAreaFilterConfig", my_radar_ld2450_cmd_set_cur_area_filter, sizeof(my_radar_ld2450_cmd_set_cur_area_filter));
    _My_Radar_LD2450_DisableConfig();

    return 0;
}

int My_Radar_LD2450_StartDetect(void)
{
    return _My_Radar_LD2450_EnableConfig();
}

int My_Radar_LD2450_StopDetect(void)
{
    return _My_Radar_LD2450_DisableConfig();
}

int My_Radar_LD2450_Config_Trace_Single(void)
{
    return _My_Radar_LD2450_Config_Trace_Single();
}

int My_Radar_LD2450_Config_Trace_Multi(void)
{
    return _My_Radar_LD2450_Config_Trace_Multi();
}

int My_Radar_LD2450_Config_Query_FrameVersion(void)
{
    return _My_Radar_LD2450_Config_Query_FrameVersion();
}

int My_Radar_LD2450_Config_Set_Baudrate(uint32_t newvalue)
{
    return _My_Radar_LD2450_Config_Set_Baudrate(newvalue);
}

int My_Radar_LD2450_Config_RFS(void)
{
    return _My_Radar_LD2450_Config_RFS();
}

int My_Radar_LD2450_Config_Reboot(void)
{
    return _My_Radar_LD2450_Config_Reboot();
}

int My_Radar_LD2450_Config_Open_Bluetooth(void)
{
    return _My_Radar_LD2450_Config_Open_Bluetooth();
}

int My_Radar_LD2450_Config_Close_Bluetooth(void)
{
    return _My_Radar_LD2450_Config_Close_Bluetooth();
}

int My_Radar_LD2450_Config_Query_Mac(void)
{
    return _My_Radar_LD2450_Config_Query_Mac();
}

int My_Radar_LD2450_Config_Query_CurAreaFilterConfig(void)
{
    return _My_Radar_LD2450_Config_Query_CurAreaFilterConfig();
}

int My_Radar_LD2450_Config_Set_CurAreaFilterConfig(uint8_t filter_type, void* filter_data)
{
    return _My_Radar_LD2450_Config_Set_CurAreaFilterConfig(filter_type, filter_data);
}


#endif