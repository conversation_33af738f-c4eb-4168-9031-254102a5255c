#include "my_relay.h"

#if MY_RELAY_FUNC

#include "my_gpio.h"
#include "esp_log.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"
#include "freertos/semphr.h"

#include "my_config.h"

#include "my_led_mng.h"

#define MY_RELAY1_CTRL_GPIO_PIN  38
#define MY_RELAY1_GPIO_PIN_SEL  ( 1ULL<<MY_RELAY1_CTRL_GPIO_PIN )

TaskHandle_t My_Relay_task_Handle = NULL;
TimerHandle_t   MyRelayTimer_Handle;

bool my_relay1_status = false;

void my_relay_on(int channel)
{
    MyGPIO_SET(MY_RELAY1_CTRL_GPIO_PIN, 1);
    my_relay1_status = true;
}

void my_relay_off(int channel)
{
    MyGPIO_SET(MY_RELAY1_CTRL_GPIO_PIN, 0);
    my_relay1_status = false;
}

bool My_Relay_GetState(int channel)
{
    return my_relay1_status;
}

void MyRelayTimer(xTimerHandle xTimer)
{
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
	if(p_my_conf->workmode.relay1_mode==2)
    {
        my_relay_off(1);
    }
    else if(p_my_conf->workmode.relay1_mode==3)
    {
        my_relay_on(1);
    }
    else
    {
        my_relay_off(1);
    }
    xTimerStop(MyRelayTimer_Handle,portMAX_DELAY);
}

void My_Channeldoor_Update_Handle(void)
{
    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();
    if(p_my_conf->workmode.channeldoor_mode==0)
    {
        MyConfig_Modify_Workmode_Relay1_mode(3, 0);
        My_Relay_On_Supper();
        My_Relay_Lock();
    }
    if(p_my_conf->workmode.channeldoor_mode==1)
    {
        MyConfig_Modify_Workmode_Relay1_mode(2, 0);
        My_Relay_Off_Supper();
        My_Relay_Unlock();
    }
    if(p_my_conf->workmode.channeldoor_mode==2)
    {
        if(MyWorkmode_Check_WhetherInWorktime(&p_my_conf->workmode.conf))
        {
            MyConfig_Modify_Workmode_Relay1_mode(2, 0);
            My_Relay_Off_Supper();
            My_Relay_Unlock();
        }
        else
        {
            MyConfig_Modify_Workmode_Relay1_mode(3, 0);
            My_Relay_On_Supper();
            My_Relay_Lock();
        }
    }
}

void My_Channeldoor_Update(void)
{
    if(My_Relay_task_Handle!=NULL)
    {
        xTaskNotify(My_Relay_task_Handle, 0xff, eSetValueWithOverwrite);
    }
}

void My_Relay_task( void *pvParameters )
{
    uint32_t runtimes = 0;
    uint32_t notifyValue = 0;
    bool relay_timer_created = false;

    My_Channeldoor_Update_Handle();

    my_config_nvs_t* p_my_conf = MyConfig_GetGCT_Cur_p();

    switch(p_my_conf->workmode.relay1_mode)
    {
        case 0:
            my_relay_off(1);
        break;
        case 1:
            my_relay_on(1);
        break;
        case 2:
            my_relay_off(1);
        break;
        case 3:
            my_relay_on(1);
        break;
    }

    for(;;)
    {
        notifyValue = 0;
        xTaskNotifyWait(0x0, 0xffffffff, &notifyValue, 200/portTICK_PERIOD_MS);
        if(notifyValue==0xff)
        {
            My_Channeldoor_Update_Handle();
        }
        if(notifyValue==1)
        {
            if(p_my_conf->workmode.relay1_mode<=1)
            {
                my_relay_on(1);
            }
            if(p_my_conf->workmode.relay1_mode==2)
            {
                if(relay_timer_created==false)
                {
                    relay_timer_created = true;
                    MyRelayTimer_Handle = xTimerCreate("LedMngfinddevTimer", (p_my_conf->workmode.relay1_Xmode_keep_time*1000)/portTICK_PERIOD_MS, pdTRUE, (void*)1, MyRelayTimer);
                }
                
                my_relay_on(1);
                xTimerStart(MyRelayTimer_Handle,portMAX_DELAY);
            }
            if(p_my_conf->workmode.relay1_mode==3)
            {
                my_relay_on(1);
            }
        }
        else if(notifyValue==2)
        {
            if(p_my_conf->workmode.relay1_mode<=1)
            {
                my_relay_off(1);
            }
            if(p_my_conf->workmode.relay1_mode==2)
            {
                my_relay_off(1);
            }
            if(p_my_conf->workmode.relay1_mode==3)
            {
                if(relay_timer_created==false)
                {
                    relay_timer_created = true;
                    MyRelayTimer_Handle = xTimerCreate("LedMngfinddevTimer", (p_my_conf->workmode.relay1_Xmode_keep_time*1000)/portTICK_PERIOD_MS, pdTRUE, (void*)1, MyRelayTimer);
                }
                
                my_relay_off(1);
                xTimerStart(MyRelayTimer_Handle,portMAX_DELAY);
            }
        }
    }
}

bool Relay_lock = false;

void My_Relay_Lock(void)
{
    Relay_lock = true;
}

void My_Relay_Unlock(void)
{
    Relay_lock = false;
}
void My_Relay_On(void)
{
    if(Relay_lock==false)
    {
        if(My_Relay_task_Handle!=NULL)
        {
            xTaskNotify(My_Relay_task_Handle, 1, eSetValueWithOverwrite);
        }
    }
}

void My_Relay_Off(void)
{
    if(Relay_lock==false)
    {
        if(My_Relay_task_Handle!=NULL)
        {
            xTaskNotify(My_Relay_task_Handle, 2, eSetValueWithOverwrite);
        }
    }
}

void My_Relay_On_Supper(void)
{
    if(My_Relay_task_Handle!=NULL)
    {
        xTaskNotify(My_Relay_task_Handle, 1, eSetValueWithOverwrite);
    }
}

void My_Relay_Off_Supper(void)
{
    if(My_Relay_task_Handle!=NULL)
    {
        xTaskNotify(My_Relay_task_Handle, 2, eSetValueWithOverwrite);
    }
}

void my_relay_init(void)
{
    gpio_config_t io_conf;
    io_conf.intr_type = GPIO_PIN_INTR_DISABLE;
    io_conf.mode = GPIO_MODE_OUTPUT;
    io_conf.pin_bit_mask = MY_RELAY1_GPIO_PIN_SEL;
    io_conf.pull_down_en = 0;
    io_conf.pull_up_en = 0;
    gpio_config(&io_conf);

    my_relay_off(1);

    xTaskCreate(My_Relay_task,  "My_Relay_task",    3072,    NULL, 5, &My_Relay_task_Handle);
}

#endif