#include "main.h"
#include "mysocket.h"

#include <stdio.h>
#include <threads.h>
#include <time.h>
#include <string.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <errno.h>
#include <stdlib.h>
#include <unistd.h>


#define MAX_SOCKET  100
typedef struct
{
    int fd;
    int exit;
    thrd_t recv_thread_id;
    thrd_t send_thread_id;
    mtx_t senddata_mtx;
    serverdata_t* rcvbuf;
    serverdata_t* sendbuf;
    int sendbuf_empty;
    int packmode;
    int eos;
    int sendlen;
}mysocket_list_t;


static const uint32_t serverdata_headersize = 8;

typedef struct 
{
    unsigned int upgrade_check;
    unsigned int binfile_size;
    unsigned int binfile_sec_num;
    unsigned int binfile_sec_size;
    unsigned int end_sec_offset;
}ota_info_t;

typedef struct
{
    unsigned int binfile_sec_count;
    unsigned int trans_finish;
}ota_trans_format_t;


void PrintTime(void)
{
    time_t now ;
    struct tm *tm_now ;
    time(&now) ;
    tm_now = localtime(&now) ;//get date
    printf("\ntime: %d-%d-%d, %d:%d:%d\n",\
    tm_now->tm_year+1900, tm_now->tm_mon+1, tm_now->tm_mday, \
    tm_now->tm_hour, tm_now->tm_min, tm_now->tm_sec) ;
}

int senddatatoclient(mysocket_list_t* const p, int id, char* data, int len, int timeout)
{
    if(len<SERVER_DATA_SIZE)
    {
        mtx_lock(&(p->senddata_mtx));
        p->eos = 0;
        p->sendbuf->id = id;
        
        if(data!=NULL)
        {
            p->sendbuf->len = len+SERVER_DATA_HEAD_SIZE;
            memcpy(p->sendbuf->data, data, len);
        }
        else
        {
            p->sendbuf->len = SERVER_DATA_HEAD_SIZE;
        }
        p->sendbuf_empty = 0;
        mtx_unlock(&(p->senddata_mtx));

        if(timeout==0)
        {
            return p->sendbuf->len;
        }

        while(!p->eos&&timeout)
        {
            usleep(1000);
            timeout--;
        }
        return p->eos;
    }
    else
    {
        puts("client pack format error!");
    }

    return 0;
}

void senddatatoclient_test(mysocket_list_t* const p)
{
    if(p->rcvbuf->len<SERVER_DATA_SIZE)
    {
        mtx_lock(&(p->senddata_mtx));
        p->sendbuf_empty = 0;
        p->sendbuf->id = 0;
        p->sendbuf->len = p->rcvbuf->len;
        memcpy(p->sendbuf->data, p->rcvbuf->data, p->rcvbuf->len);
        mtx_unlock(&(p->senddata_mtx));
    }
    else
    {
        puts("client pack format error!");
    }
}

void MySocketThrd_Exit(mysocket_list_t* const p)
{
    p->fd = 0;
    if(p->rcvbuf!=NULL)
    {
        free(p->rcvbuf);
        p->rcvbuf = NULL;
    }
    if(p->sendbuf!=NULL)
    {
        free(p->sendbuf);
        p->sendbuf = NULL;
    }
    
    mtx_destroy(&(p->senddata_mtx));

    memset(p, 0, sizeof(mysocket_list_t));
}

void OTA_Server(mysocket_list_t* p)
{
    int numbytes = 0;
    serverdata_t* serverdata = (serverdata_t*)calloc(1, sizeof(serverdata_t));
    if(serverdata==NULL)
    {
        printf("\033[31merror:%s,in line %d\033[0m\n", __FILE__, __LINE__);
    }

    p->packmode = 1;
    printf("ok101\n");
    numbytes = senddatatoclient(p, 101, NULL, 0, 0);
    printf("send numbytes:%d\n", numbytes);
    sleep(1);

    FILE *fp = NULL;
    if((fp=fopen("simple_ota.bin","rb"))==NULL)
    {
        printf("connot open or creat simple_ota.bin\n");
        printf("\033[31merror:%s,in line %d\033[0m\n", __FILE__, __LINE__);
    }
    ota_info_t* ota_info = (ota_info_t*)calloc(1, sizeof(ota_info_t));
    if(ota_info==NULL)
    {
        printf("\033[31merror:%s,in line %d\033[0m\n", __FILE__, __LINE__);
    }
    memset(ota_info, 0, sizeof(ota_info_t));
    fseek(fp, 0L, SEEK_END);
    ota_info->binfile_size = ftell(fp);
    rewind(fp);

    printf("binfilesize:%d\n", ota_info->binfile_size);

    numbytes = senddatatoclient(p, 102, (void*)ota_info, sizeof(ota_info_t), 0);
    printf("send numbytes:%d\n", numbytes);
    memset(serverdata, 0, sizeof(serverdata_t));
    do
    {
        numbytes = recv(p->fd, serverdata, sizeof(serverdata_t),0);//接收服务器端信息
    } while(serverdata->id!=102);

    printf("recv numbytes:%d\n", numbytes);
    memcpy(ota_info, serverdata->data, sizeof(ota_info_t));
    numbytes = senddatatoclient(p, 102, (void*)ota_info, sizeof(ota_info_t), 0);
    printf("ota_info.upgrade_check:%d\n", ota_info->upgrade_check);
    printf("ota_info.binfile_size:%d\n", ota_info->binfile_size);
    printf("ota_info.binfile_sec_size:%d\n", ota_info->binfile_sec_size);
    printf("ota_info.binfile_sec_num:%d\n", ota_info->binfile_sec_num);
    printf("ota_info.end_sec_offset:%d\n", ota_info->end_sec_offset);

    memset(serverdata, 0, sizeof(serverdata_t));

    if(ota_info->upgrade_check)
    {
        ota_trans_format_t* ota_trans_format = (ota_trans_format_t*)calloc(1, sizeof(ota_trans_format_t));
        if(ota_trans_format==NULL)
        {
            printf("\033[31merror:%s,in line %d\033[0m\n", __FILE__, __LINE__);
        }
        memset(ota_trans_format, 0, sizeof(ota_trans_format_t));
        numbytes = recv(p->fd, serverdata, sizeof(serverdata_t), 0);
        if(serverdata->id!=103)
        {
            printf("error:2\n");
            while(1);
        }
        memcpy(ota_trans_format, serverdata->data, sizeof(ota_trans_format_t));
        printf("ota_trans_format.binfile_sec_count:%d\n", ota_trans_format->binfile_sec_count);
        printf("ota_trans_format.trans_finish:%d\n", ota_trans_format->trans_finish);
        unsigned char *p_ota_bin_sec = (unsigned char*) calloc(sizeof(unsigned char), ota_info->binfile_sec_size);
        if(p_ota_bin_sec==NULL)
        {
            printf("\033[31merror:%s,in line %d\033[0m\n", __FILE__, __LINE__);
        }
        printf("start to send binfile...\n");
        while(!ota_trans_format->trans_finish)
        {
            memset(p_ota_bin_sec, 0, ota_info->binfile_sec_size);
            if(ota_trans_format->binfile_sec_count+1<ota_info->binfile_sec_num)
            {
                fread(p_ota_bin_sec, sizeof(unsigned char), ota_info->binfile_sec_size, fp);
                printf("send binfile_sec:%d\n",ota_trans_format->binfile_sec_count);
                numbytes = senddatatoclient(p, 104, p_ota_bin_sec, ota_info->binfile_sec_size, 0);
            }
            else
            {
                fread(p_ota_bin_sec, sizeof(unsigned char), ota_info->end_sec_offset, fp);
                printf("send binfile_sec:%d\n",ota_trans_format->binfile_sec_count);
                numbytes = senddatatoclient(p, 104, p_ota_bin_sec, ota_info->end_sec_offset, 0);
            }
            
            
            printf("send numbytes:%d\n", numbytes);
            memset(ota_trans_format, 0, sizeof(ota_trans_format_t));
            memset(serverdata, 0, sizeof(serverdata_t));
            numbytes = recv(p->fd, serverdata, sizeof(serverdata_t), 0);
            printf("recv numbytes:%d\n", numbytes);
            memcpy(ota_trans_format, serverdata->data, sizeof(ota_trans_format_t));
            if(serverdata->id!=103)
            {
                printf("error:3\n");
                while(1);
            }
        }
        memset(ota_trans_format, 0, sizeof(ota_trans_format_t));
        memset(serverdata, 0, sizeof(serverdata_t));
        printf("send binfile finished\n");
        if(p_ota_bin_sec!=NULL) free(p_ota_bin_sec);
        if(ota_trans_format!=NULL) free(ota_trans_format);
    }
    printf("ota finish\n");

    if(serverdata!=NULL) free(serverdata);
    if(ota_info!=NULL) free(ota_info);
    if(fp!=NULL) fclose(fp);
}

char response_client[] = "NL,0,60000,NL_00001_xyz,0,10,11,0,END";
char response_client_ota[] = "NL,0,60000,NL_00001_xyz,0,10,11,1,END";
int mysocket_recv(void* param)
{
    mysocket_list_t* const p = (mysocket_list_t*)param;
    printf("mysocket_recv ok, fd=%d\n", p->fd);
    int numbytes = 0;
    int runtimes = 0;
    void* p_rcv_buf = NULL;
    
    while(!p->exit)
    {
        #if SERVERDATAPACK_MODE
        p_rcv_buf = p->rcvbuf;
        while((numbytes = recv(p->fd, p_rcv_buf, sizeof(serverdata_t), 0)) > 0)
        {
            PrintTime();
            printf("fd=%d,recv(%d):id=%d, %s\n", p->fd, numbytes, p->rcvbuf->id, p->rcvbuf->data);

            if(strstr(p->rcvbuf->data, "NLTSVX")!=NULL)
            {
                runtimes++;
                printf("mysocket_recv(): runtimes=%d\n", runtimes);
                if(runtimes>=3)
                {
                    runtimes = 0;
                    p->packmode = 0;
                    senddatatoclient(p, 0, response_client_ota, strlen(response_client_ota), 0);
                }
                else
                {
                    p->packmode = 0;
                    senddatatoclient(p, 0, response_client, strlen(response_client), 0);
                }
            }
            if(p->rcvbuf->id==101)
            {
                OTA_Server(p);
            }
            if(strstr(p->rcvbuf->data, "OTA_READY_FAILED")!=NULL)
            {
                printf("ok102\n");
            }
            if(p->rcvbuf->id==100)
            {
                p->packmode = 1;
                senddatatoclient(p, 101, NULL, serverdata_headersize, 0);
            }
            memset(p->rcvbuf, 0, numbytes);
        }
        #else
        while((numbytes = recv(p->fd, p->rcvbuf->data, sizeof(serverdata_t), 0)) > 0)
        {
            PrintTime();
            printf("fd=%d,recv(%d):%s\n", p->fd, numbytes, p->rcvbuf->data);
            if(strstr(p->rcvbuf->data, "U:12312312")!=NULL)
            {
                senddatatoclient(p, 0, "connection success", strlen("connection success"));
            }
            else
            {
                p->rcvbuf->len = numbytes;
                senddatatoclient_test(p);
            }
            memset(p->rcvbuf->data, 0, numbytes);
        }
        #endif
        p->exit = 1;
    }
    PrintTime();
    printf("mysocket_recv exit, fd=%d\n", p->fd);

    thrd_exit(0);
}

int mysocket_send(void* param)
{
    mysocket_list_t* const p = (mysocket_list_t*)param;

    printf("mysocket_send ok, fd=%d\n", p->fd);

    while(!p->exit)
    {
        mtx_lock(&p->senddata_mtx);
        if(!p->sendbuf_empty)
        {
            p->sendbuf_empty = 1;
            #if SERVERDATAPACK_MODE
            
            if(p->packmode)
            {
                p->eos = send(p->fd, p->sendbuf, p->sendbuf->len, 0);
                if( p->eos < 0)
                {
                    perror("send error");
                }
            }
            else
            {
                if(p->sendbuf->len>SERVER_DATA_HEAD_SIZE)
                {
                    p->sendbuf->len -= SERVER_DATA_HEAD_SIZE;
                }
                p->eos = send(p->fd, p->sendbuf->data, p->sendbuf->len, 0);
                if( p->eos < 0 )
                {
                    perror("send error");
                }
            }
            #else
            if(send(p->fd, p->sendbuf->data, p->sendbuf->len, 0) < 0)
            {
                perror("write error");
            }
            #endif
        }
        mtx_unlock(&p->senddata_mtx);
        usleep(100);
    }
    printf("mysocket_send exit, fd=%d\n", p->fd);

    shutdown(p->fd, SHUT_RDWR);
    
    sleep(1);

    MySocketThrd_Exit(p);

    thrd_exit(0);
}

int CreateMySockThread(mysocket_list_t* const p, int fd)
{
    p->exit = 0;
    p->fd = fd;
    p->sendbuf_empty = 1;
    mtx_init(&p->senddata_mtx, mtx_plain);

    p->rcvbuf = (serverdata_t*)calloc(1, sizeof(serverdata_t));
    p->sendbuf = (serverdata_t*)calloc(1, sizeof(serverdata_t));
    if(p->sendbuf==NULL)
    {
        if(p->rcvbuf!=NULL)
        {
            free(p->rcvbuf);
            p->rcvbuf = NULL;
        }
        printf("\033[31merror:%s,in line %d:calloc memory failed\033[0m\n", __FILE__, __LINE__);
    }

    if(thrd_create(&(p->recv_thread_id), &mysocket_recv, p)!=thrd_success)
    {
        printf("\033[31merror:%s,in line %d:create mysocket_recv thread failed:fd=%d\033[0m\n", __FILE__, __LINE__, fd);
        //memset(&mysocket_list[idle_pos], 0, sizeof(mysocket_list_t));
        goto create_thread_failed;
    }
    if(thrd_create(&(p->send_thread_id), &mysocket_send, p)!=thrd_success)
    {
        printf("\033[31merror:%s,in line %d:create mysocket_send thread failed:fd=%d\033[0m\n", __FILE__, __LINE__, fd);
        p->exit = 1;
        //memset(&mysocket_list[idle_pos], 0, sizeof(mysocket_list_t));
        goto create_thread_failed;
    }
    return 0;

    create_thread_failed:
    if(p->rcvbuf!=NULL)
    {
        free(p->rcvbuf);
        p->rcvbuf = NULL;
    }
    if(p->sendbuf!=NULL)
    {
        free(p->sendbuf);
        p->sendbuf = NULL;
    }
    return 1;
}

int FindIdleSeat(mysocket_list_t* const p_list)
{
    int i = 0;
    for(i=0; i<MAX_SOCKET; i++)
    {
        if(p_list[i].fd==0)
        {
            return i;
        }
    }
    return -1;
}

#if MYSOCKET_STATISTICS
typedef struct
{
    int state;
    int seat_number;
    int fd;
    char creat_time[16];
}mysocket_seat_info_t;

typedef struct
{
    int seat_amount;
    int used_seat_count;
    int empty_seat_count;
    mysocket_seat_info_t info[MAX_SOCKET];
}mysocket_statistic_t;
#endif

int StatisticMySockets_SeatUsed(mysocket_list_t* const p)
{
    int count = 0;
    int i = 0;

    for(i=0; i<MAX_SOCKET; i++)
    {
        if(p[i].fd>0)
        {
            count++;
        }
    }

    return count;
}

#if MYSOCKET_STATISTICS
//#define StatisticMySockets_SeatUse(mysocketlist, count) for(int i=0; i<MAX_SOCKET; mysocketlist[i++].fd>0?count++:0);

int StatisticMySockets(mysocket_list_t* const p_mysockets, mysocket_statistic_t* const p_statistics)
{
    if(MAX_SOCKET>0)
    {
        p_statistics->seat_amount = MAX_SOCKET;
        p_statistics->used_seat_count = StatisticMySockets_SeatUsed(p_mysockets);
        p_statistics->empty_seat_count = MAX_SOCKET - p_statistics->used_seat_count;
        
        for(int i=0; i<MAX_SOCKET; i++)
        {
            p_statistics->info[i].fd = p_mysockets[i].fd;
            p_statistics->info[i].state = p_mysockets[i].fd>0?1:0;
            p_statistics->info[i].seat_number = i;
            sprintf(p_statistics->info[i].creat_time, "%s", __TIME__);
        }
    }
    
    return 0;
}
#endif

int MySockThread(void* param)
{
    int idle_pos = 0;
    int fd, new_fd, struct_len, client_count=0;
    struct sockaddr_in server_addr;
    struct sockaddr_in client_addr[10];

    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(50001);
    server_addr.sin_addr.s_addr = INADDR_ANY;
    bzero(&(server_addr.sin_zero), 8);
    struct_len = sizeof(struct sockaddr_in);

    fd = socket(AF_INET, SOCK_STREAM, 0);
    while(bind(fd, (struct sockaddr *)&server_addr, struct_len) == -1);
    puts("Bind Success!");
    printf("fd:%d\n", fd);
    while(listen(fd, 10) == -1);
    puts("Listening....");

    mysocket_list_t* mysocket_list = (mysocket_list_t*)calloc(MAX_SOCKET, sizeof(mysocket_list_t));
    
    #if MYSOCKET_STATISTICS
    mysocket_statistic_t* mysocket_statistics = (mysocket_statistic_t*)calloc(1, sizeof(mysocket_statistic_t));
    

    if(mysocket_statistics==NULL)
    {
        if(mysocket_list==NULL)
        {
            free(mysocket_list);
            mysocket_list = NULL;
        }
        printf("\033[31merror:%s,in line %d:calloc memory failed\033[0m\n", __FILE__, __LINE__);
    }
    #endif

    printf("mysockets used statistics: %d/%d\n", StatisticMySockets_SeatUsed(mysocket_list), MAX_SOCKET);

    while(1)
    {
        idle_pos=FindIdleSeat(mysocket_list);
        if(idle_pos != -1)
        {
            printf("idle_pos=%d\n", idle_pos);
            puts("waiting client...");
            new_fd = accept(fd, (struct sockaddr *)&client_addr[client_count], &struct_len);
            PrintTime();
            printf("new_fd=%d\n", new_fd);

            if(CreateMySockThread(&mysocket_list[idle_pos], new_fd))
            {
                shutdown(new_fd, SHUT_RDWR);
            }
            else
            {
                idle_pos=FindIdleSeat(mysocket_list);
                if(idle_pos == -1)
                {
                    sleep(1);
                    puts("client appearance ulimit");
                }
                printf("mysockets used statistics: %d/%d\n", StatisticMySockets_SeatUsed(mysocket_list), MAX_SOCKET);
                #if MYSOCKET_STATISTICS
                StatisticMySockets(mysocket_list, mysocket_statistics);
                #endif
            }
        }
        else
        {
            sleep(1);
        }
    }

    thrd_exit(0);
}

int CreateMySocketServer(void)
{
    thrd_t my_socket_thrd_id;

    PrintTime();
    
    if(thrd_create(&my_socket_thrd_id, &MySockThread, NULL)!=thrd_success)
    {
        printf("\033[31merror:%s,in line %d:create MySockThread failed\033[0m\n", __FILE__, __LINE__);
        return 1;
    }
    return 0;
}