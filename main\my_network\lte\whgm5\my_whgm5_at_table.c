#include "my_whgm5_at_table.h"

#if NETWORK_TYPE&NETWORK_TYPE_LTE_WHGM5

#include "string.h"

/*
WH-GM5固件版本：V1.2.00.000000.0000(支持LBS)
WH-GM5命令相应时间
查询命令：80ms之内
配置命令：80ms之内
*/

#define WHGM5_ACK_RCV_TIME_NOR  80//不可小于80
#define WHGM5_CMD_LAGDLEAY_TIME_NOR 10//可以为0
AT_Config_Routine_t whgm5_UART_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+UART?\r\n",
        .keystr = "115200,8,1,NONE,NONE",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+UART=115200,8,1,NONE,NONE\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

AT_Config_Routine_t whgm5_UARTFL_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+UARTFL?\r\n",
        .keystr = "UARTFL:4096",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+UARTFL=4096\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

AT_Config_Routine_t whgm5_UARTFT_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+UARTFT?\r\n",
        .keystr = "UARTFT:100",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+UARTFT=100\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//连接类型
AT_Config_Routine_t whgm5_SOCKASL_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+SOCKASL?\r\n",
        .keystr = "LONG",
        .sucnext = 2,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+SOCKASL=LONG\r\n",
        .keystr = "LONG",
        .sucnext = 2,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//短连接超时
AT_Config_Routine_t whgm5_SHORTATM_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+SHORTATM?\r\n",
        .keystr = "SHORTATM:60",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+SHORTATM=60\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//socket最大重连次数
AT_Config_Routine_t whgm5_SOCKRSNUM_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+SOCKRSNUM?\r\n",
        .keystr = "SOCKRSNUM:60",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+SOCKRSNUM=60\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//socket重连间隔
AT_Config_Routine_t whgm5_SOCKRSTIM_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+SOCKRSTIM?\r\n",
        .keystr = "SOCKRSTIM:10",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+SOCKRSTIM=10\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//keepaliveA
AT_Config_Routine_t whgm5_KEEPALIVEA_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+KEEPALIVEA?\r\n",
        .keystr = "KEEPALIVEA:1,60,15,3",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+KEEPALIVEA=1,60,15,3\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//keepaliveB
AT_Config_Routine_t whgm5_KEEPALIVEB_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+KEEPALIVEB?\r\n",
        .keystr = "KEEPALIVEB:1,60,15,3",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+KEEPALIVEB=1,60,15,3\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//keepaliveC
AT_Config_Routine_t whgm5_KEEPALIVEC_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+KEEPALIVEC?\r\n",
        .keystr = "KEEPALIVEC:1,60,15,3",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+KEEPALIVEC=1,60,15,3\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//keepaliveD
AT_Config_Routine_t whgm5_KEEPALIVED_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+KEEPALIVED?\r\n",
        .keystr = "KEEPALIVED:1,60,15,3",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+KEEPALIVED=1,60,15,3\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//心跳包使能
AT_Config_Routine_t whgm5_HEARTEN_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+HEARTEN?\r\n",
        .keystr = "HEARTEN:OFF",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+HEARTEN=OFF\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//心跳包去处
AT_Config_Routine_t whgm5_HEARTTP_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+HEARTTP?\r\n",
        .keystr = "HEARTTP:NET",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+HEARTTP=NET\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//心跳包间隔
AT_Config_Routine_t whgm5_HEARTTM_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+HEARTTM?\r\n",
        .keystr = "HEARTTM:30",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+HEARTTM=30\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//心跳包类型
AT_Config_Routine_t whgm5_HEARTSORT_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+HEARTSORT?\r\n",
        .keystr = "HEARTSORT:USER",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+HEARTSORT=USER\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//网络AT
AT_Config_Routine_t whgm5_NATEN_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+NATEN?\r\n",
        .keystr = "NATEN:OFF",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+NATEN=OFF\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//套接字分发使能
AT_Config_Routine_t whgm5_SDPEN_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+SDPEN?\r\n",
        .keystr = "SDPEN:OFF",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+SDPEN=OFF\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//无数据重启时间
AT_Config_Routine_t whgm5_RSTIM_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+RSTIM?\r\n",
        .keystr = "RSTIM:1800",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+RSTIM=1800\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//SOCKA
AT_Config_Routine_t whgm5_SOCKA_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+SOCKA?\r\n",
        //.keystr = "TCP,106.14.245.181,28288",
        .keystr = "TCP,255.255.255.255,65535",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        //.cmdstr = "AT+SOCKA=TCP,106.14.245.181.95,28288\r\n",
        .cmdstr = "AT+SOCKA=TCP,255.255.255.255,65535\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//SOCKAEN
AT_Config_Routine_t whgm5_SOCKAEN_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+SOCKAEN?\r\n",
        .keystr = "SOCKAEN:OFF",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+SOCKAEN=OFF\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//SOCKB
AT_Config_Routine_t whgm5_SOCKB_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+SOCKB?\r\n",
        //.keystr = "TCP,106.14.245.181,28288",
        .keystr = "TCP,255.255.255.255,65535",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        //.cmdstr = "AT+SOCKB=TCP,106.14.245.181.95,28288\r\n",
        .cmdstr = "AT+SOCKB=TCP,255.255.255.255,65535\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//SOCKBEN
AT_Config_Routine_t whgm5_SOCKBEN_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+SOCKBEN?\r\n",
        .keystr = "SOCKBEN:OFF",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+SOCKBEN=OFF\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//SOCKC
AT_Config_Routine_t whgm5_SOCKC_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+SOCKC?\r\n",
        //.keystr = "TCP,106.14.245.181,28288",
        .keystr = "TCP,255.255.255.255,65535",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        //.cmdstr = "AT+SOCKC=TCP,106.14.245.181.95,28288\r\n",
        .cmdstr = "AT+SOCKC=TCP,255.255.255.255,65535\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//SOCKCEN
AT_Config_Routine_t whgm5_SOCKCEN_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+SOCKCEN?\r\n",
        .keystr = "SOCKCEN:OFF",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+SOCKCEN=OFF\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//SOCKD
AT_Config_Routine_t whgm5_SOCKD_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+SOCKD?\r\n",
        //.keystr = "TCP,106.14.245.181,28288",
        .keystr = "TCP,255.255.255.255,65535",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        //.cmdstr = "AT+SOCKD=TCP,106.14.245.181.95,28288\r\n",
        .cmdstr = "AT+SOCKD=TCP,255.255.255.255,65535\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//SOCKDEN
AT_Config_Routine_t whgm5_SOCKDEN_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+SOCKDEN?\r\n",
        .keystr = "SOCKDEN:OFF",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+SOCKDEN=OFF\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 5
    },
};

//WKMOD=NET
AT_Config_Routine_t whgm5_WKMOD_NET_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+WKMOD?\r\n",
        .keystr = "NET",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+WKMOD=NET\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
};

//WKMOD=HTTPD
AT_Config_Routine_t whgm5_WKMOD_HTTPD_conf_routine[2]=
{
    
    {
        .cmd_id = 0,
        .cmdstr = "AT+WKMOD?\r\n",
        .keystr = "HTTPD",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+WKMOD=HTTPD\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
};

//AT+HTPTP=POST
AT_Config_Routine_t whgm5_HTPTP_POST_conf_routine[2]=
{
    
    {
        .cmd_id = 0,
        .cmdstr = "AT+HTPTP=?\r\n",
        .keystr = "POST",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+HTPTP=POST\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
};

//AT+HTPURL
AT_Config_Routine_t whgm5_HTPURL_conf_routine[2]=
{
    
    {
        .cmd_id = 0,
        .cmdstr = "AT+HTPURL=?\r\n",
        .keystr = "POST",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+HTPURL=xx\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
};

//AT+HTPHD
AT_Config_Routine_t whgm5_HTPHD_conf_routine[2]=
{
    
    {
        .cmd_id = 0,
        .cmdstr = "AT+HTPHD=?\r\n",
        .keystr = "POST",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+HTPHD=\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
};

//AT+HTPSV
AT_Config_Routine_t whgm5_HTPSV_conf_routine[2]=
{
    
    {
        .cmd_id = 0,
        .cmdstr = "AT+HTPSV=?\r\n",
        .keystr = "POST",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+HTPSV=\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
};

//AT+HTPPK
AT_Config_Routine_t whgm5_HTPPK_conf_routine[2]=
{
    
    {
        .cmd_id = 0,
        .cmdstr = "AT+HTPPK=?\r\n",
        .keystr = "POST",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+HTPPK=\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
};

//AT+HTPTIM
AT_Config_Routine_t whgm5_HTPTIM_conf_routine[2]=
{
    
    {
        .cmd_id = 0,
        .cmdstr = "AT+HTPTIM=?\r\n",
        .keystr = "5",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+HTPTIM=5\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
};

//STMSG=SWQZ-WC2D5
AT_Config_Routine_t whgm5_STMSG_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+STMSG?\r\n",
        .keystr = "SWQZ-WC2D5",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+STMSG=SWQZ-WC2D5\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
};

AT_Config_Routine_t whgm5_NTPEN_conf_routine[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+NTPEN?\r\n",
        .keystr = "ON",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+NTPEN=ON\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = WHGM5_CMD_LAGDLEAY_TIME_NOR,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    },
};

//WORKMODE=CMD
AT_Config_Routine_t whgm5_WORKMODE_CMD_conf_routine[3]=
{
    {   
        .cmd_id = 0,
        .cmdstr = "+++",
        .keystr = "a",
        .sucnext = 1,
        .failnext = 2,
        .pridelay = 1000,
        .lagdelay = 20,
        .faillagdelay = 5000,
        .keystrexist = 1,
        .timeout_count = 200,//不可小于200
        .failedcount = 0,
        .failedlimit = 20
    },
    {
        .cmd_id = 1,
        .cmdstr = "a",
        .keystr = "ok",
        .sucnext = 0xff,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = 500,
        .faillagdelay = 5000,
        .keystrexist = 1,
        .timeout_count = 1000,//不可小于1000
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 2,
        .cmdstr = "+++",
        .keystr = "ERROR",
        .sucnext = 0xff,
        .failnext = 0xff,
        .pridelay = 1000,
        .lagdelay = 20,
        .faillagdelay = 5000,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    }
};

//WORKMODE=DATA
AT_Config_Routine_t whgm5_WORKMODE_DATA_conf_routine[1]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+ENTM\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0,
        .pridelay = 500,
        .lagdelay = 200,
        .faillagdelay = 5000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    }
};

//saving config use AT+S
AT_Config_Routine_t whgm5_SAVING_conf_routine[1]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+S\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0xff,
        .pridelay = 2000,
        .lagdelay = 15000,
        .faillagdelay = 2000,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    }
};

//restart WHGM5 use AT+Z
AT_Config_Routine_t whgm5_RESTART_conf_routine[1]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+Z\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0xff,
        .pridelay = 500,
        .lagdelay = 6000,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = WHGM5_ACK_RCV_TIME_NOR,
        .failedcount = 0,
        .failedlimit = 10
    }
};

AT_Config_Routine_t lte_cmd_mode_routine[3]=
{
    {   
        .cmd_id = 0,
        .cmdstr = "+++",
        .keystr = "a",
        .sucnext = 1,
        .failnext = 2,
        .pridelay = 1000,
        .lagdelay = 20,
        .faillagdelay = 5000,
        .keystrexist = 1,
        .timeout_count = 200,
        .failedcount = 0,
        .failedlimit = 20
    },
    {
        .cmd_id = 1,
        .cmdstr = "a",
        .keystr = "ok",
        .sucnext = 0xff,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = 500,
        .faillagdelay = 5000,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 2,
        .cmdstr = "+++",
        .keystr = "ERROR",
        .sucnext = 0xff,
        .failnext = 0xff,
        .pridelay = 1000,
        .lagdelay = 20,
        .faillagdelay = 5000,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    }
};

AT_Config_Routine_t lte_data_mode_routine[1]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+ENTM\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0,
        .pridelay = 500,
        .lagdelay = 200,
        .faillagdelay = 5000,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    }
};

AT_Config_Routine_t lte_restart_routine[1]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+Z\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0xff,
        .pridelay = 500,
        .lagdelay = 10000,
        .faillagdelay = 10000,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 20
    }
};

AT_Config_Routine_t lte_config_check_chip_matching[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+STMSG?\r\n",
        .keystr = "SWQZ-WC2D5",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+STMSG=SWQZ-WC2D5\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
};

AT_Config_Routine_t lte_config_check_networkmode_tcp[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+WKMOD?\r\n",
        .keystr = "NET",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+WKMOD=NET\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
};

AT_Config_Routine_t lte_config_check_networkmode_http[2]=
{
    
    {
        .cmd_id = 0,
        .cmdstr = "AT+WKMOD?\r\n",
        .keystr = "HTTPD",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+WKMOD=HTTPD\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
};

AT_Config_Routine_t lte_config_check_tcp[6]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+SOCKASL?\r\n",
        .keystr = "LONG",
        .sucnext = 2,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+SOCKASL=LONG\r\n",
        .keystr = "LONG",
        .sucnext = 2,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 2,
        .cmdstr = "AT+SHORTATM?\r\n",
        .keystr = "SHORTATM:60",
        .sucnext = 4,
        .failnext = 3,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 3,
        .cmdstr = "AT+SHORTATM=60\r\n",
        .keystr = "OK",
        .sucnext = 4,
        .failnext = 2,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 4,
        .cmdstr = "AT+SOCKA?\r\n",
        //.keystr = "TCP,106.14.245.181,28288",
        .keystr = "TCP,47.96.125.253,50000",
        .sucnext = 0xff,
        .failnext = 5,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 5,
        //.cmdstr = "AT+SOCKA=TCP,106.14.245.181.95,28288\r\n",
        .cmdstr = "AT+SOCKA=TCP,47.96.125.253,50000\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 4,
        .pridelay = 0,
        .lagdelay = 1000,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
};

AT_Config_Routine_t lte_config_check_http_method_GET[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+HTPTP?\r\n",
        .keystr = "GET",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+HTPTP=GET\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
};

AT_Config_Routine_t lte_config_check_http_method_POST[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+HTPTP?\r\n",
        .keystr = "POST",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+HTPTP=POST\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
};

AT_Config_Routine_t lte_config_check_http_server[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+HTPSV?\r\n",
        .keystr = "XXX.XXX.XXX.XXX,XX",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+HTPSV=XXX.XXX.XXX.XXX,XX\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
};

AT_Config_Routine_t lte_config_check_http_url[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+HTPURL?\r\n",
        .keystr = "URLXXXX",
        .sucnext = 1,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+HTPURL=URLXXXX\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
};

AT_Config_Routine_t lte_config_check_http_head[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+HTPHD?\r\n",
        .keystr = "HEADXXXX",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+HTPHD=HEADXXXX\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
};

AT_Config_Routine_t lte_config_check_http_head_filter_enable[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+HTPPK?\r\n",
        .keystr = "ON",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+HTPPK=ON\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
};

AT_Config_Routine_t lte_config_check_http_timeout[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+HTPTIM?\r\n",
        .keystr = "10",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+HTPTIM=10\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
};

AT_Config_Routine_t lte_config_check_http_head_filter_disable[2]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+HTPPK?\r\n",
        .keystr = "OFF",
        .sucnext = 0xff,
        .failnext = 1,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
    {
        .cmd_id = 1,
        .cmdstr = "AT+HTPPK=OFF\r\n",
        .keystr = "OK",
        .sucnext = 0,
        .failnext = 0,
        .pridelay = 0,
        .lagdelay = 100,
        .faillagdelay = 100,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    },
};

AT_Config_Routine_t lte_config_check_saving[1]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+S\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0xff,
        .pridelay = 500,
        .lagdelay = 6000,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    }
};

AT_Config_Routine_t lte_config_check_restart[1]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT+Z\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0xff,
        .pridelay = 500,
        .lagdelay = 6000,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    }
};

AT_Config_Routine_t lte_config_check_test[1]=
{
    {
        .cmd_id = 0,
        .cmdstr = "AT\r\n",
        .keystr = "OK",
        .sucnext = 0xff,
        .failnext = 0xff,
        .pridelay = 100,
        .lagdelay = 100,
        .faillagdelay = 1000,
        .keystrexist = 1,
        .timeout_count = 1000,
        .failedcount = 0,
        .failedlimit = 10
    }
};


void ATTable_ModifyItem_SOCK_Conf(AT_Config_Routine_t* routinelist, char* address, uint16_t port)
{
    char portstr[8];

    itoa(port, portstr, 10);

    memset(routinelist[0].keystr, 0, strlen(routinelist[0].keystr));
    char* p_tempstr = routinelist[0].keystr;
    memcpy(p_tempstr, "TCP,", strlen("TCP,"));
    p_tempstr += strlen("TCP,");
    memcpy(p_tempstr, address, strlen(address));
    p_tempstr += strlen(address);
    memcpy(p_tempstr, ",", strlen(","));
    p_tempstr += strlen(",");
    memcpy(p_tempstr, portstr, strlen(portstr));


    char tcp_channel_str[16];
    memset(tcp_channel_str, 0, sizeof(tcp_channel_str));
    p_tempstr = routinelist[1].cmdstr;
    memcpy(tcp_channel_str, p_tempstr, strlen("AT+SOCKx=TCP,"));
    memset(p_tempstr, 0, strlen(p_tempstr));

    memcpy(p_tempstr, tcp_channel_str, strlen(tcp_channel_str));
    p_tempstr += strlen(tcp_channel_str);
    memcpy(p_tempstr, address, strlen(address));
    p_tempstr += strlen(address);
    memcpy(p_tempstr, ",", strlen(","));
    p_tempstr += strlen(",");
    memcpy(p_tempstr, portstr, strlen(portstr));
    p_tempstr += strlen(portstr);
    memcpy(p_tempstr, "\r\n", strlen("\r\n"));
}

void ATTable_ModifyItem_SOCKEN_Conf(AT_Config_Routine_t* routinelist, uint8_t newstate)
{
    char tcp_channel_str[16];
    memset(tcp_channel_str, 0, sizeof(tcp_channel_str));

    char* p_tempstr = routinelist[0].keystr;
    memcpy(tcp_channel_str, p_tempstr, strlen("SOCKxEN:"));
    memset(p_tempstr, 0, strlen(routinelist[0].keystr));

    memcpy(p_tempstr, tcp_channel_str, strlen(tcp_channel_str));
    p_tempstr += strlen(tcp_channel_str);
    if(newstate)memcpy(p_tempstr, "ON", strlen("ON"));
    else memcpy(p_tempstr, "OFF", strlen("OFF"));


    p_tempstr = routinelist[1].cmdstr;
    memset(tcp_channel_str, 0, sizeof(tcp_channel_str));
    memcpy(tcp_channel_str, p_tempstr, strlen("AT+SOCKxEN="));
    memset(p_tempstr, 0, strlen(p_tempstr));

    memcpy(p_tempstr, tcp_channel_str, strlen(tcp_channel_str));
    p_tempstr += strlen(tcp_channel_str);

    if(newstate){memcpy(p_tempstr, "ON", strlen("ON")); p_tempstr += strlen("ON");}
    else {memcpy(p_tempstr, "OFF", strlen("OFF")); p_tempstr += strlen("OFF");}

    
    memcpy(p_tempstr, "\r\n", strlen("\r\n"));
}

void ATTable_ModifyItem_UartBaudrate(AT_Config_Routine_t* routinelist, int newvalue)
{
    char newvaluestr[8];

    itoa(newvalue, newvaluestr, 10);

    char* p_tempstr = routinelist[0].keystr;
    memset(p_tempstr, 0, strlen(p_tempstr));
    memcpy(p_tempstr, newvaluestr, strlen(newvaluestr));
    p_tempstr += strlen(newvaluestr);
    memcpy(p_tempstr, ",8,1,NONE,NONE", strlen(",8,1,NONE,NONE"));

    p_tempstr = routinelist[1].cmdstr;
    memset(p_tempstr, 0, strlen(p_tempstr));
    memcpy(p_tempstr, "AT+UART=", strlen("AT+UART="));
    p_tempstr += strlen("AT+UART=");
    memcpy(p_tempstr, newvaluestr, strlen(newvaluestr));
    p_tempstr += strlen(newvaluestr);
    memcpy(p_tempstr, ",8,1,NONE,NONE\r\n", strlen(",8,1,NONE,NONE\r\n"));
}

#endif