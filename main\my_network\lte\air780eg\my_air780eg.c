#include "my_air780eg.h"

#if NETWORK_TYPE&NETWORK_TYPE_LTE_AIR780EG

#include "my_nvs.h"
#include "my_air780eg_at_table.h"
#include "my_gpio.h"
#include "my_at_ctrl.h"
#include "my_uart.h"
#include "my_time.h"
#include "my_power_mng.h"
#include "my_esp_chip_info_mng.h"
#include "mytest_uart_iot_server.h"
#include "my_debug.h"

#include "esp_log.h"

#include "driver/uart.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"
#include "freertos/semphr.h"

#if defined MY_DEBUG_PRINT_FUNC
#define DEBUG_PRINT_LEVEL_my_air780eg_PRINT   MY_DEBUG_PRINT_LEVEL_2_INFO
#else
#define DEBUG_PRINT_LEVEL_my_air780eg_PRINT   DEBUG_PRINT_LEVEL_0
#endif


my_air780eg_basic_conf_nvs_t my_air780eg_basic_conf_nvs;
my_air780eg_info_t my_air780eg_info;
SemaphoreHandle_t MyAIR780EG_CmdModeSendMutexSemaphore = NULL;
SemaphoreHandle_t MyAIR780EG_CmdModeRcvMutexSemaphore = NULL;

myconf_lte_t myconf_lte_info;

static uint8_t MyAIR780EG_LoadBaiscConfToATTable(my_air780eg_basic_conf_nvs_t* confs)
{
    //串口参数

    //TCP配置参数

    //心跳包

    //其他
    return 0;
}

static void MyAIR780EG_LoadBasicConf()
{
    memset(&my_air780eg_basic_conf_nvs, 0, sizeof(my_air780eg_basic_conf_nvs_t));

    my_air780eg_basic_conf_nvs.UART = 921600;
    my_air780eg_basic_conf_nvs.UARTFL = 1024;
    my_air780eg_basic_conf_nvs.UARTFT = 50;
    my_air780eg_basic_conf_nvs.SOCKASL = 0;
    my_air780eg_basic_conf_nvs.SHORTATM = 60;
    my_air780eg_basic_conf_nvs.SOCKRSNUM = 60;
    my_air780eg_basic_conf_nvs.SOCKRSTIM = 10;
    my_air780eg_basic_conf_nvs.KEEPALIVE = 0x0f;
    my_air780eg_basic_conf_nvs.HEARTEN = 0;
    my_air780eg_basic_conf_nvs.HEARTTP = HEART_P_TO_NET;
    my_air780eg_basic_conf_nvs.HEARTTM = 30;
    my_air780eg_basic_conf_nvs.HEARTSORT = 0;
    my_air780eg_basic_conf_nvs.NATEN = 0;
    my_air780eg_basic_conf_nvs.SDPEN = 1;
    my_air780eg_basic_conf_nvs.RSTIM = 1800;

	LoadDataFromNvs(AIR780EG_NVS_FORM_NAME, AIR780EG_NVS_KEY_NAME, (void*)&my_air780eg_basic_conf_nvs, sizeof(my_air780eg_basic_conf_nvs_t));

    MyAIR780EG_LoadBaiscConfToATTable(&my_air780eg_basic_conf_nvs);
}

void MyAIR780EG_Print_SOCK_conf_routine(void)
{
    printf("\n\n-------------------------------------------------------------------------------\n");
    printf("MyAIR780EG_Print_SOCK_conf_routine()\n");
    printf("air780eg_WORKMODE_DATA_conf_routine[2].cmdstr=%s\n", air780eg_WORKMODE_DATA_conf_routine[2].cmdstr);
    printf("air780eg_WORKMODE_DATA_conf_routine[2].keystr=%s\n", air780eg_WORKMODE_DATA_conf_routine[2].keystr);
    printf("\n\n-------------------------------------------------------------------------------\n");
}

static uint8_t MyAIR780EG_LoadNetConfToATTable(my_lte_network_confs_t* confs)
{
    //TCP
    if(confs->networkmode==AIR780EG_NETWORKMODE_TCP)
    {
        #if DEBUG_PRINT_LEVEL_my_air780eg_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
        MyAIR780EG_Print_SOCK_conf_routine();
        #endif
        
        if(confs->tcp_channel_1!=NULL)
        {
            ATTable_ModifyItem_SOCK_Conf(air780eg_WORKMODE_DATA_conf_routine, confs->tcp_channel_1->address, confs->tcp_channel_1->port);
        }
        MyAIR780EG_Print_SOCK_conf_routine();

        #if DEBUG_PRINT_LEVEL_my_air780eg_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
        MyAIR780EG_Print_SOCK_conf_routine();
        #endif
    }
    //HTTP
    else if(confs->networkmode==AIR780EG_NETWORKMODE_HTTP)
    {

    }

    return 0;
}

int MyAIR780EG_Cmd_Mode(void)
{
    if(my_air780eg_info.workmode!=AIR780EG_WORKMODE_CMD)
    {
        ESP_LOGI("MyAIR780EG_Cmd_Mode()",  "AIR780EG switch to cmd mode\n");
        if(!AT_Routine(air780eg_WORKMODE_CMD_conf_routine))
        {
            my_air780eg_info.workmode = AIR780EG_WORKMODE_CMD;
            ESP_LOGI("MyAIR780EG_Cmd_Mode()",  "AIR780EG switch to cmd mode succeed\n");
            return 1;
        }
        return 0;
    }
    return 1;
}

int MyAIR780EG_Data_Mode(void)
{
    if(my_air780eg_info.workmode!=AIR780EG_WORKMODE_DATA)
    {
        ESP_LOGI("MyAIR780EG_Data_Mode()",  "AIR780EG switch to data mode\n");
        AT_Routine(air780eg_WORKMODE_DATA_conf_routine);
        my_air780eg_info.workmode = AIR780EG_WORKMODE_DATA;
        ESP_LOGI("MyAIR780EG_Data_Mode()",  "AIR780EG switch to data mode succeed\n");
    }
    return 1;
}

uint8_t MyAIR780EG_GetWorkmode(void)
{
    return my_air780eg_info.workmode;
}

void MyAIR780EG_Reset_H(void);

static void MyAIR780EG_GPIOInit(void)
{
    MyPowerMng_MyAIR780EG_PowerMngGpioInit();
    MyAIR780EG_Reset_H();
}

void MyAIR780EG_Reset_H(void)
{
    MyPowerMng_MyAIR780EG_PowerOFF();
    vTaskDelay(3000/portTICK_PERIOD_MS);
    MyPowerMng_MyAIR780EG_PowerON();
}

int MyAIR780EG_Reset_S(void)
{
    MyAIR780EG_Cmd_Mode();

    return AT_Routine(air780eg_RESTART_conf_routine);
}

static uint32_t MyAIR780EG_Matching_Baudrate(uint32_t current_baudrate)
{
    ESP_LOGI("LTE_Matching_Baudrate()",  "LTE_Matching_Baudrate...\n");
    vTaskDelay(3000/portTICK_PERIOD_MS);
    if(current_baudrate == 115200)
    {
        MyUartSetBaudrate(921600);
        if(MyAIR780EG_Cmd_Mode())
        {
            ESP_LOGI("LTE_Matching_Baudrate()",  "LTE_Matching_Baudrate=921600\n");
            return 921600;
        }
        else
        {
            ESP_LOGE("LTE_Matching_Baudrate()",  "error 1\n");
        }
    }
    else if(current_baudrate == 921600)
    {
        MyUartSetBaudrate(115200);
        if(MyAIR780EG_Cmd_Mode())
        {
            ESP_LOGI("LTE_Matching_Baudrate()",  "LTE_Matching_Baudrate=115200\n");
            return 115200;
        }
        else
        {
            ESP_LOGE("LTE_Matching_Baudrate()",  "error 2\n");
        }
    }
    else
    {
        ESP_LOGE("LTE_Matching_Baudrate()",  "error 3\n");
    }
    return current_baudrate;
}

static uint8_t MyAIR780EG_CheckConfAccordingATTable(myconf_lte_t* myconf_lte)
{
    uint8_t air780eg_conf_changed;
    int64_t time_start=0, time_end=0;

    // check_air780eg_config:

    air780eg_conf_changed = 0;

    ESP_LOGI("MyAIR780EG_CheckConfAccordingATTable()", "start checking...");

    #if DEBUG_PRINT_LEVEL_my_air780eg_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    time_start = esp_timer_get_time();
    #endif

    if(!my_air780eg_info.installed)
    {
        air780eg_conf_changed += AT_Routine(air780eg_SIMPLE_CONFIG_conf_routine);
        // air780eg_conf_changed += AT_Routine(air780eg_WORKMODE_DATA_conf_routine);
    }

    #if DEBUG_PRINT_LEVEL_my_air780eg_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    printf("\n\nMyAIR780EG_CheckConfAccordingATTable:air780eg_conf_changed=%d\n\n", air780eg_conf_changed);
    #endif

    #if DEBUG_PRINT_LEVEL_my_air780eg_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    time_end = esp_timer_get_time();
    printf("\n\nMyAIR780EG_CheckConfAccordingATTable:time used=%lldms\n\n", (time_end-time_start)/1000);
    #endif

    my_air780eg_info.workmode = AIR780EG_WORKMODE_CMD;

    return 0;
}

#define AIR780EG_INFO_RCV_TIME 100
static int MyAIR780EG_RcvCSQ(void)
{
	char *start, *end;
    char* RCV_BUF = (char*)calloc(1, LTE_CMD_LENGTH_LIMIT);
    char *CSQ = my_air780eg_info.CSQ;
    memset(my_air780eg_info.CSQ, 0, sizeof(my_air780eg_info.CSQ));

    if(RCV_BUF==NULL)
    {
        return 1;
    }

    AT_SendSingleCmd_And_Rcv("AT+CSQ\r\n", RCV_BUF, AIR780EG_INFO_RCV_TIME);//超时时间不可小于400
	if(strstr((const char*)RCV_BUF,"OK\r\n")!=NULL){
	   //ESP_LOGI("", "%s",RCV_BUF);
	   if ((start = strstr( (char *)RCV_BUF, "+CSQ: "))!=NULL)
	   {
	      start += strlen( "+CSQ: " );
	      if ((end = strstr((char *) start, "," ))!=NULL )
	      {
	        	memcpy(CSQ, start, end - start);
	      }
	   }
	   //ESP_LOGI("",  "%s\n", CSQ );
	}
    free(RCV_BUF);
	return 0;
}

static int MyAIR780EG_RcvLocation(int type)
{
    uint8_t air780eg_conf_changed = 0;
    air780eg_conf_changed += AT_Routine(air780eg_GNSS_conf_routine);

    if(air780eg_conf_changed)
    {
        ESP_LOGI("MyAIR780EG_RcvLocation()", "waiting 30s for AIR780EG GNSS ready...");
        vTaskDelay(30000 / portTICK_PERIOD_MS);
    }

	char *start, *end;
    char *pos_s, *pos_e;
    char *pos_latitude_start;
	char* RCV_BUF = (char*)calloc(1, LTE_CMD_LENGTH_LIMIT);
    char* location_x = (char*)calloc(1, LTE_CMD_LENGTH_LIMIT);
    char* location_y = (char*)calloc(1, LTE_CMD_LENGTH_LIMIT);
    int ret = 0;

    if(RCV_BUF==NULL||location_x==NULL||location_y==NULL)
    {
        ret = 1;
        goto lte_rcv_location_clear;
    }
    
    //LOCATION:x=121.29058838&y=31.10537910
    //rcv:+CGNSINF: 1,1,20230925091453,31.119126,121.251986,62.400,0.41,0.00,3,,1.65,1.72,4.00,,11,10,,,38,,
    AT_SendSingleCmd_And_Rcv("AT+CGNSINF\r\n", RCV_BUF, AIR780EG_INFO_RCV_TIME);//超时时间不可小于800
    if(strstr((const char*)RCV_BUF,"OK\r\n")!=NULL)
    {
        memset(my_air780eg_info.LBS, 0, sizeof(my_air780eg_info.LBS));
        //ESP_LOGI("", "%s",RCV_BUF);
        if ((start = strstr( (char *)RCV_BUF, ","))!=NULL)
        {
            start += 1;
            if ((start = strstr( (char *)start, ","))!=NULL)
            {
                start += 1;
                {
                    if ((start = strstr( (char *)start, ","))!=NULL)
                    {
                        start += 1;
                        pos_s = start;
                        if ((start = strstr( (char *)start, ","))!=NULL)
                        {
                            memcpy(my_air780eg_info.LATITUDE, pos_s, start-pos_s);
                            ESP_LOGI("",  "LATITUDE: %s\n", my_air780eg_info.LATITUDE );
                            start+=1;
                            pos_latitude_start = start;
                            if ((start = strstr( (char *)start, ","))!=NULL)
                            {
                                pos_e = start;
                                memcpy(my_air780eg_info.LONGITUDE, pos_latitude_start, start-pos_latitude_start);
                                ESP_LOGI("",  "LONGITUDE: %s\n", my_air780eg_info.LONGITUDE );

                                memcpy(my_air780eg_info.LBS, pos_s, pos_e-pos_s);
                                ESP_LOGI("",  "LOCATION: %s\n", my_air780eg_info.LBS );
                            }
                        }
                    }
                }
            }
        }
    }
    ret = 0;

    lte_rcv_location_clear:
    if(RCV_BUF!=NULL)free(RCV_BUF);
    if(location_x!=NULL)free(location_x);
    if(location_y!=NULL)free(location_y);
	return ret;
}

static int MyAIR780EG_RcvInfo(char* ATCMD, char* at_ack_end_symbol, char* rcv_head_symbol, char* rcv_end_symbol, char* saving, uint16_t len)
{

	char *start, *end;
	char* RCV_BUF = (char*)calloc(1, LTE_CMD_LENGTH_LIMIT);
    memset(saving, 0, sizeof(len));

    if(RCV_BUF==NULL)
    {
        return 1;
    }

    AT_SendSingleCmd_And_Rcv(ATCMD, RCV_BUF, AIR780EG_INFO_RCV_TIME);//超时时间不可小于80
	if(strstr((const char*)RCV_BUF,at_ack_end_symbol)!=NULL)
	{
		//ESP_LOGI("", "%s",RCV_BUF);
		if ((start = strstr( (char *)RCV_BUF, rcv_head_symbol))!=NULL)
		{
			start += strlen( rcv_head_symbol );
			if ((end = strstr((char *) start, rcv_end_symbol ))!=NULL )
			{
                if((end-start)<len)
				    memcpy( saving, start, end - start);
                else
                {
                    memcpy( saving, start, len);
                }
			}
			else
			{
				ESP_LOGE("", "MyAIR780EG_RcvInfo(): error=1\n");
                free(RCV_BUF);
				return 1;
			}
		}
		else
		{
			ESP_LOGE("", "MyAIR780EG_RcvInfo(): error=2\n");
            free(RCV_BUF);
			return 1;
		}
		//ESP_LOGI("",  "%s\n", saving );
        free(RCV_BUF);
		return 0;
	}
	ESP_LOGE("", "MyAIR780EG_RcvInfo(): error=3\n");
    free(RCV_BUF);
	return 1;
}

#if DEBUG_PRINT_LEVEL_my_air780eg_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
#define AIR780EG_INFO_ALL      0xff
#define AIR780EG_INFO_INITOK   (1<<0)
#define AIR780EG_INFO_WOKMOD   (1<<1)
#define AIR780EG_INFO_INSTAL   (1<<2)
#define AIR780EG_INFO_NTWMOD   (1<<3)
#define AIR780EG_INFO_TCPCID   (1<<4)
#define AIR780EG_INFO_FIXINF   (1<<5)
#define AIR780EG_INFO_VARINF   (1<<6)

//AIR780EG_INFO_ALL      输出所有信息
//AIR780EG_INFO_INITOK   初始化状态:1/0
//AIR780EG_INFO_WOKMOD   工作模式:1-AIR780EG_WORKMODE_CMD, 2-AIR780EG_WORKMODE_DATA
//AIR780EG_INFO_INSTAL   驱动状态:1/0
//AIR780EG_INFO_NTWMOD   网络模式:1-AIR780EG_NETWORKMODE_TCP, 2-AIR780EG_NETWORKMODE_HTTP
//AIR780EG_INFO_TCPCID   TCP通道ID
//AIR780EG_INFO_FIXINF   固定不变的信息，如IMEI等
//AIR780EG_INFO_VARINF   可变信息，如CSQ等
void MyAIR780EG_Print_my_air780eg_info(uint8_t type)
{
    printf("\n\n-------------------------------------------------------------------------------\n");
    printf("MyAIR780EG_Print_my_air780eg_info()\n");

    if(type&AIR780EG_INFO_INITOK)
        printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.init_ok=%d\n", my_air780eg_info.init_ok);
    if(type&AIR780EG_INFO_WOKMOD)
        printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.workmode=%d\n", my_air780eg_info.workmode);
    if(type&AIR780EG_INFO_INSTAL)
        printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.installed=%d\n", my_air780eg_info.installed);
    if(type&AIR780EG_INFO_NTWMOD)
        printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.networkmode=%d\n", my_air780eg_info.networkmode);
    if(type&AIR780EG_INFO_TCPCID){
        printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.tcp_channel_1_id=%d\n", my_air780eg_info.tcp_channel_1_id);
        printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.tcp_channel_2_id=%d\n", my_air780eg_info.tcp_channel_2_id);
        printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.tcp_channel_3_id=%d\n", my_air780eg_info.tcp_channel_3_id);
        printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.tcp_channel_4_id=%d\n", my_air780eg_info.tcp_channel_4_id);
    }
    
    if(type&AIR780EG_INFO_FIXINF){
    printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.firmware_version=%s\n", my_air780eg_info.firmware_version);
    printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.firmware_buildtime=%s\n", my_air780eg_info.firmware_buildtime);
    printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.SN=%s\n", my_air780eg_info.SN);
    printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.IMEI=%s\n", my_air780eg_info.IMEI);
    printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.ICCID=%s\n", my_air780eg_info.ICCID);
    }
    if(type&AIR780EG_INFO_VARINF){
    printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.SYSINFO=%s\n", my_air780eg_info.SYSINFO);
    printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.APN=%s\n", my_air780eg_info.APN);
    printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.CSQ=%s\n", my_air780eg_info.CSQ);
    printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.IP=%s\n", my_air780eg_info.IP);
    printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.LBS=%s\n", my_air780eg_info.LBS);
    printf("MyAIR780EG_GetBasicInfo():my_air780eg_info.CCLK=%s\n", my_air780eg_info.CCLK);
    }
    printf("\n\n-------------------------------------------------------------------------------\n");
}
#endif

static uint8_t MyAIR780EG_GetBasicInfo(void)
{
    MyAIR780EG_Cmd_Mode();

    // //固件版本号
    // MyAIR780EG_RcvInfo("AT+VER?\r\n", "OK", "+VER:", "\r\n", my_air780eg_info.firmware_version, sizeof(my_air780eg_info.firmware_version));
    // //固件编译时间
    // MyAIR780EG_RcvInfo("AT+BUILD?\r\n", "OK", "+BUILD:", "\r\n", my_air780eg_info.firmware_buildtime, sizeof(my_air780eg_info.firmware_buildtime));
    // //SN
    // MyAIR780EG_RcvInfo("AT+SN?\r\n", "OK", "+SN:", "\r\n", my_air780eg_info.SN, sizeof(my_air780eg_info.SN));
    // //连接制式
    // MyAIR780EG_RcvInfo("AT+SYSINFO?\r\n", "OK", "+SYSINFO:", "\r\n", my_air780eg_info.SYSINFO, sizeof(my_air780eg_info.SYSINFO));
    //IMEI
    MyAIR780EG_RcvInfo("AT+CGSN\r\n", "OK", "AT+CGSN\r\n\r\n", "\r\n\r\nOK", my_air780eg_info.IMEI, sizeof(my_air780eg_info.IMEI));
    //ICCID
    MyAIR780EG_RcvInfo("AT+ICCID\r\n", "OK", "AT+ICCID\r\n\r\n+ICCID: ", "\r\n\r\nOK", my_air780eg_info.ICCID, sizeof(my_air780eg_info.ICCID));
    //APN
    // MyAIR780EG_RcvInfo("AT+APN?\r\n", "OK", "+APN:", "\r\n", my_air780eg_info.APN, sizeof(my_air780eg_info.APN));
    // //CSQ
    // MyAIR780EG_RcvCSQ();
    // //IP
    // MyAIR780EG_RcvInfo("AT+IP?\r\n", "OK", "+IP:", "\r\n", my_air780eg_info.IP, sizeof(my_air780eg_info.IP));
    //LBS
    MyAIR780EG_RcvLocation(1);
    // //CCLK
    // MyAIR780EG_RcvInfo("AT+CCLK?\r\n", "OK", "+CCLK:", "\r\n", my_air780eg_info.CCLK, sizeof(my_air780eg_info.CCLK));

    #if DEBUG_PRINT_LEVEL_my_air780eg_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    MyAIR780EG_Print_my_air780eg_info(AIR780EG_INFO_ALL);
    #endif

    return 0;
}

uint8_t MyAIR780EG_RefreshDynamicInfo(void)
{
    if(my_air780eg_info.init_ok)
    {
        //获取信号量
        xSemaphoreTake(MyAIR780EG_CmdModeSendMutexSemaphore, portMAX_DELAY);
        xSemaphoreTake(MyAIR780EG_CmdModeRcvMutexSemaphore, portMAX_DELAY);

        // if(MyAIR780EG_Cmd_Mode())
        // {
        //     //CSQ
        //     MyAIR780EG_RcvCSQ();
        // }

        // MyAIR780EG_Data_Mode();
        MyAIR780EG_CheckRTI();

        //释放信号量
        xSemaphoreGive(MyAIR780EG_CmdModeSendMutexSemaphore);
        xSemaphoreGive(MyAIR780EG_CmdModeRcvMutexSemaphore);

        return 0;
    }
    return 1;
}

uint8_t MyAIR780EG_GetInitState(void)
{
    return my_air780eg_info.init_ok;
}

int MyAIR780EG_TcpDataStationInit(void);
uint8_t MyAIR780EG_Init(my_lte_network_confs_t* confs, myconf_lte_t* myconf_lte, uint8_t checkconfig)
{
    #if DEBUG_PRINT_LEVEL_my_air780eg_PRINT >= MY_DEBUG_PRINT_LEVEL_1_SIMPLE
    time_t start_time = esp_timer_get_time();
    #endif

    my_air780eg_info.init_ok = 0;
    my_air780eg_info.workmode = AIR780EG_WORKMODE_CMD;

    #if !MYTEST_CONFIG_UART_IOT_SERVER
    //加载基础配置
    // if(!my_air780eg_info.installed)MyAIR780EG_LoadBasicConf();
    //将网络参数confs加载到at表中
    if(confs!=NULL&&checkconfig)
    {
        MyAIR780EG_LoadNetConfToATTable(confs);
        my_air780eg_info.networkmode = confs->networkmode;
        if(confs->tcp_channel_1!=NULL)my_air780eg_info.tcp_channel_1_id = confs->tcp_channel_1->id;
    }
    //加载baudrate参数
    if(checkconfig)
    {
        if(myconf_lte!=NULL)
        {
            if( myconf_lte->baudrate==115200||myconf_lte->baudrate==921600 )
            {
                myconf_lte_info.baudrate = myconf_lte->baudrate;
            }
            else
            {
                myconf_lte_info.baudrate = AIR780EG_BAUDRATE_DEFAULT;
            }
        }
        else
            myconf_lte_info.baudrate = AIR780EG_BAUDRATE_DEFAULT;
    }
    

    ATTable_ModifyItem_UartBaudrate(air780eg_UART_conf_routine, myconf_lte_info.baudrate);
    
    //LTE上电
    if(!my_air780eg_info.installed)MyAIR780EG_GPIOInit();
    else MyAIR780EG_Reset_H();
    //等待LTE进入工作状态
    ESP_LOGI("MyAIR780EG_Init()",  "wait 10s for lte run...\n");
    vTaskDelay(10000/portTICK_PERIOD_MS);
    //初始化串口
    if(!my_air780eg_info.installed)MyUartInit(myconf_lte_info.baudrate);
    #if MYTEST_CONFIG_ESP32_IOT_SERVER==0
    //根据at表检查配置
    MyAIR780EG_CheckConfAccordingATTable(&myconf_lte_info);
    //获取LTE基础参数
    MyAIR780EG_GetBasicInfo();
    //进入通讯模式
    MyAIR780EG_Data_Mode();
    #else
    my_air780eg_info.workmode = AIR780EG_WORKMODE_DATA;
    #endif
    #endif

    if(!my_air780eg_info.installed)MyAIR780EG_TcpDataStationInit();

    my_air780eg_info.installed = 1;
    my_air780eg_info.init_ok = 1;

    #if DEBUG_PRINT_LEVEL_my_air780eg_PRINT >= MY_DEBUG_PRINT_LEVEL_1_SIMPLE
    time_t end_time = esp_timer_get_time();
    printf("MyAIR780EG_Init():func end, MyTime_GetRuntimeSinceReset=%ld, timeuse=%ldms\n", MyTime_GetRuntimeSinceReset(), (end_time-start_time)/1000);
    #endif

    return 0;
}

uint8_t MyAIR780EG_Init_SimpleTcpMode(my_tcp_conf_t* confs, myconf_lte_t* myconf_lte, uint8_t checkconfig)
{
    int ret = 0;

    my_lte_network_confs_t* lte_net_work_confs = (my_lte_network_confs_t*)calloc(1, sizeof(my_lte_network_confs_t));
    #if DEBUG_PRINT_LEVEL_my_air780eg_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
	// printf("app_main():sizeof(my_lte_network_confs_t)=%d\n", sizeof(my_lte_network_confs_t));
    #endif
	lte_net_work_confs->http=NULL;
	lte_net_work_confs->tcp_channel_1 = (my_tcp_conf_t*)calloc(1, sizeof(my_tcp_conf_t));
	lte_net_work_confs->tcp_channel_2 = NULL;
	lte_net_work_confs->tcp_channel_3 = NULL;
	lte_net_work_confs->tcp_channel_4 = NULL;

	memcpy(lte_net_work_confs->tcp_channel_1->address, confs->address, strlen(confs->address));
	lte_net_work_confs->tcp_channel_1->port = confs->port;
	lte_net_work_confs->tcp_channel_1->id = 1;
	lte_net_work_confs->networkmode = AIR780EG_NETWORKMODE_DEFAULT;
	ret = MyAIR780EG_Init(lte_net_work_confs, myconf_lte, checkconfig);

    if(lte_net_work_confs->tcp_channel_1!=NULL)free(lte_net_work_confs->tcp_channel_1);
    if(lte_net_work_confs->tcp_channel_2!=NULL)free(lte_net_work_confs->tcp_channel_2);
    if(lte_net_work_confs->tcp_channel_3!=NULL)free(lte_net_work_confs->tcp_channel_3);
    if(lte_net_work_confs->tcp_channel_4!=NULL)free(lte_net_work_confs->tcp_channel_4);
    if(lte_net_work_confs!=NULL)free(lte_net_work_confs);

    return ret;
}

void MyAIR780EG_RestartOnly(void)
{
    #if DEBUG_PRINT_LEVEL_my_air780eg_PRINT >= MY_DEBUG_PRINT_LEVEL_1_SIMPLE
    printf("MyAIR780EG_RestartOnly()\n");
    #endif
    MyAIR780EG_Init(NULL, NULL, 0);
}

void MyAIR780EG_Restart_Reconfig_SimpleTcpMode(my_tcp_conf_t* confs, myconf_lte_t* myconf_lte)
{
    #if DEBUG_PRINT_LEVEL_my_air780eg_PRINT >= MY_DEBUG_PRINT_LEVEL_1_SIMPLE
    printf("MyAIR780EG_Restart_Reconfig_SimpleTcpMode()\n");
    #endif
    MyAIR780EG_Init_SimpleTcpMode(confs, myconf_lte, 1);
}

int MyAIR780EG_CheckRTI(void)
{
    MyAIR780EG_Cmd_Mode();

    //连接制式
    // MyAIR780EG_RcvInfo("AT+SYSINFO?\r\n", "OK", "+SYSINFO:", "\r\n", my_air780eg_info.SYSINFO, sizeof(my_air780eg_info.SYSINFO));
    // //APN
    // MyAIR780EG_RcvInfo("AT+APN?\r\n", "OK", "+APN:", "\r\n", my_air780eg_info.APN, sizeof(my_air780eg_info.APN));
    //CSQ
    MyAIR780EG_RcvCSQ();
    //IP
    // MyAIR780EG_RcvInfo("AT+IP?\r\n", "OK", "+IP:", "\r\n", my_air780eg_info.IP, sizeof(my_air780eg_info.IP));
    //LBS
    MyAIR780EG_RcvLocation(1);
    //CCLK
    // MyAIR780EG_RcvInfo("AT+CCLK?\r\n", "OK", "+CCLK:", "\r\n", my_air780eg_info.CCLK, sizeof(my_air780eg_info.CCLK));

    MyAIR780EG_Data_Mode();

    #if DEBUG_PRINT_LEVEL_my_air780eg_PRINT >= MY_DEBUG_PRINT_LEVEL_2_INFO
    MyAIR780EG_Print_my_air780eg_info(AIR780EG_INFO_VARINF);
    #endif

    return 0;
}

uint8_t MyAIR780EG_GetInfo(my_air780eg_info_t* saving)
{
    if(saving==NULL)return 1;

    memcpy(saving, &my_air780eg_info, sizeof(my_air780eg_info));

    return 0;
}

uint8_t MyAIR780EG_GetCSQ(void)
{
    return atoi(my_air780eg_info.CSQ);
}

uint8_t MyAIR780EG_GetIMEI(char* saving, uint8_t len)
{
    if(saving==NULL)return 1;

    memset(saving, 0, len);

    uint8_t len_saving = strlen(my_air780eg_info.IMEI);

    if(len_saving>len)memcpy(saving, my_air780eg_info.IMEI, len);
    else memcpy(saving, my_air780eg_info.IMEI, len_saving);

    return 0;
}

uint8_t MyAIR780EG_GetICCID(char* saving, uint8_t len)
{
    if(saving==NULL)return 1;

    memset(saving, 0, len);

    uint8_t len_saving = strlen(my_air780eg_info.ICCID);

    if(len_saving>len)memcpy(saving, my_air780eg_info.ICCID, len);
    else memcpy(saving, my_air780eg_info.ICCID, len_saving);

    return 0;
}

uint8_t MyAIR780EG_GetLONGITUDE(char* saving, uint8_t len)
{
    if(saving==NULL)return 1;

    memset(saving, 0, len);

    uint8_t len_saving = strlen(my_air780eg_info.LONGITUDE);

    if(len_saving>len)memcpy(saving, my_air780eg_info.LONGITUDE, len);
    else memcpy(saving, my_air780eg_info.LONGITUDE, len_saving);

    return 0;
}

uint8_t MyAIR780EG_GetLATITUDE(char* saving, uint8_t len)
{
    if(saving==NULL)return 1;

    memset(saving, 0, len);

    uint8_t len_saving = strlen(my_air780eg_info.LATITUDE);

    if(len_saving>len)memcpy(saving, my_air780eg_info.LATITUDE, len);
    else memcpy(saving, my_air780eg_info.LATITUDE, len_saving);

    return 0;
}

QueueHandle_t my_air780eg_rcv_queue = NULL;
QueueHandle_t my_air780eg_send_queue = NULL;

TaskHandle_t MyAIR780EG_TcpDataRcv_TaskHandle = NULL;
my_air780eg_queue_item_t* my_air780eg_rcv_queue_item_array[AIR780EG_QUEUE_LEN];
#define UART_RCV_HEAD   0xAAFD5500UL
void MyAIR780EG_TcpDataRcv_Task(void* param)
{
    int rxbytes = 0;
    char* uart_rcv_buf = (char*)calloc(AIR780EG_QUEUE_LEN, AIR780EG_DATA_PACK_SIZE);
    int i = 0;
    int find_empty_count = 0;
    int queue_send_ret = pdTRUE;

    if(uart_rcv_buf==NULL)
    {
        ESP_LOGE("MyAIR780EG_TcpDataRcv_Task()", "uart_rcv_buf=NULL\n");
        while(1)
        {
            vTaskDelay(10000 / portTICK_PERIOD_MS);
        }
    }

    for(;;)
    {
        //串口接收
        rxbytes = 0;
        if(my_air780eg_info.init_ok)
        {
            //获取信号量
            xSemaphoreTake(MyAIR780EG_CmdModeRcvMutexSemaphore, portMAX_DELAY);
            #if MYTEST_CONFIG_UART_IOT_SERVER
            rxbytes = Mytest_uart_read_bytes(LTE_UART_NUM, (void*)uart_rcv_buf, AIR780EG_DATA_PACK_SIZE, 40 / portTICK_PERIOD_MS);
            #else
            rxbytes = uart_read_bytes(LTE_UART_NUM, (void*)uart_rcv_buf, AIR780EG_DATA_PACK_SIZE, 40 / portTICK_PERIOD_MS);
            #endif
            //释放信号量
            xSemaphoreGive(MyAIR780EG_CmdModeRcvMutexSemaphore);
        }
        else
        {
            vTaskDelay(1000 / portTICK_PERIOD_MS);
        }

        if(rxbytes>0)
        {
            #if DEBUG_PRINT_LEVEL_my_air780eg_PRINT >= MY_DEBUG_PRINT_LEVEL_3_VERBOSE
            printf("MyAIR780EG_TcpDataRcv_Task():rcv(%d)=\n", rxbytes);
            for(int j=0; j<rxbytes; j++)
            {
                printf("%02X ", uart_rcv_buf[j]);
            }
            printf("\n\n");
            #endif
            find_empty_count = 0;
            do
            {
                for(i=0; i<AIR780EG_QUEUE_LEN; i++)
                {
                    if(!my_air780eg_rcv_queue_item_array[i]->valid)
                    {
                        memcpy(my_air780eg_rcv_queue_item_array[i]->data, uart_rcv_buf, rxbytes);
                        my_air780eg_rcv_queue_item_array[i]->len = rxbytes;
                        my_air780eg_rcv_queue_item_array[i]->valid = 1;

                        queue_send_ret = xQueueSend(my_air780eg_rcv_queue, &my_air780eg_rcv_queue_item_array[i], 1000 / portTICK_PERIOD_MS);
                        if(queue_send_ret!=pdTRUE)
                        {
                            ESP_LOGE("MyAIR780EG_TcpDataRcv_Task()", "xQueueSend ERROR, queue_send_ret=%d\n", queue_send_ret);
                        }
                        break;
                    }
                }
                if(i>=AIR780EG_QUEUE_LEN)
                {
                    ESP_LOGE("MyAIR780EG_TcpDataRcv_Task()", "not find empty in rcv queue count=%d\n", find_empty_count);
                    vTaskDelay(50 / portTICK_PERIOD_MS);
                }
                else
                {
                    break;
                }
            } while ((++find_empty_count<3)&&(i>=AIR780EG_QUEUE_LEN));
            if(find_empty_count>=3)
            {
                ESP_LOGE("MyAIR780EG_TcpDataRcv_Task()", "not find empty in rcv queue finally!!!\n");
            }
            else
            {
                find_empty_count = 0;
            }
        }
    }
}

uint8_t MyAIR780EG_TcpRcvData(my_air780eg_queue_item_t** const saving, uint32_t timeout)
{
    int queue_rcv_ret = pdFALSE;

    if(saving!=NULL)
    {
        queue_rcv_ret = xQueueReceive(my_air780eg_rcv_queue, saving, timeout/portTICK_PERIOD_MS);
        if(queue_rcv_ret!=pdTRUE)
        {
            ESP_LOGE("MyAIR780EG_TcpRcvData()", "error: recv timeout\n");
            return 1;
        }
        return 0;
    }
    else
    {
        ESP_LOGE("MyAIR780EG_TcpRcvData()", "error: saving==NULL\n");
        return 2;
    }
    return 0;
}

TaskHandle_t MyAIR780EG_TcpDataSend_TaskHandle = NULL;
my_air780eg_queue_item_t* my_air780eg_send_queue_item_array[AIR780EG_QUEUE_LEN];
void MyAIR780EG_TcpDataSend_Task(void* param)
{
    char* uart_send_buf = (char*)calloc(1, 5120+AIR780EG_DATA_PACK_SIZE);
    // my_air780eg_queue_item_t* queue_rcv_item = (my_air780eg_queue_item_t*)calloc(1, sizeof(my_air780eg_queue_item_t));
    my_air780eg_queue_item_t* queue_rcv_item = NULL;
    int queue_rcv_ret = pdFALSE;

    if(uart_send_buf==NULL)
    {
        ESP_LOGE("MyAIR780EG_TcpDataSend_Task()", "uart_send_buf==NULL\n");
    }

    // if(queue_rcv_item==NULL)
    // {
    //     ESP_LOGE("MyAIR780EG_TcpDataSend_Task()", "queue_rcv_item==NULL\n");
    // }

    for(;;)
    {
        //队列接收
        queue_rcv_ret = xQueueReceive(my_air780eg_send_queue, &queue_rcv_item, portMAX_DELAY);
        if(queue_rcv_ret==pdTRUE)
        {
            if(queue_rcv_item!=NULL)
            {
                if(queue_rcv_item->data!=NULL)
                {
                    memcpy(uart_send_buf, queue_rcv_item->data, queue_rcv_item->len);
                    //获取信号量
                    xSemaphoreTake(MyAIR780EG_CmdModeSendMutexSemaphore, portMAX_DELAY);
                    
                    ESP_LOGI("#####", "=====");
                    #if MYTEST_CONFIG_UART_IOT_SERVER
                    Mytest_UartSendData(uart_send_buf, queue_rcv_item->len);
                    #else
                    UartSendData(uart_send_buf, queue_rcv_item->len);
                    #endif
                    //释放信号量
                    xSemaphoreGive(MyAIR780EG_CmdModeSendMutexSemaphore);

                    queue_rcv_item->valid = 0;
                }
            }
            else
            {
                ESP_LOGE("MyAIR780EG_TcpDataSend_Task()", "queue_rcv_item!=NULL is false\n");
            }
        }
    }
}

SemaphoreHandle_t MyAIR780EG_TcpSendDataMutexSemaphore = NULL;
int MyAIR780EG_TcpSendData(void const* data, uint32_t len, uint32_t timeout, uint8_t tcp_channel)
{
    int queue_send_ret = pdTRUE;
    int i = 0;
    int retry_count = 0;

    //获取信号量
    MyAIR780EG_TcpSendData_retry:
    xSemaphoreTake(MyAIR780EG_TcpSendDataMutexSemaphore, 10000/portTICK_PERIOD_MS);
    for(i=0; i<AIR780EG_QUEUE_LEN; i++)
    {
        if(!my_air780eg_send_queue_item_array[i]->valid)
        {
            my_air780eg_send_queue_item_array[i]->id = tcp_channel;
            my_air780eg_send_queue_item_array[i]->len = len;
            my_air780eg_send_queue_item_array[i]->valid = 1;
            //my_air780eg_send_queue_item_array[i]->data = data;
            if(my_air780eg_send_queue_item_array[i]->data!=NULL)
            {
                memcpy(my_air780eg_send_queue_item_array[i]->data, data, len);
                my_air780eg_send_queue_item_array[i]->data[len]=0;
            }

            break;
        }
    }
    //释放信号量
    xSemaphoreGive(MyAIR780EG_TcpSendDataMutexSemaphore);
    if(i>=AIR780EG_QUEUE_LEN&&retry_count<10)
    {
        vTaskDelay(timeout / portTICK_PERIOD_MS);
        retry_count++;
        goto MyAIR780EG_TcpSendData_retry;
    }
    else if(i<AIR780EG_QUEUE_LEN)
    {
        #if DEBUG_PRINT_LEVEL_my_air780eg_PRINT >= MY_DEBUG_PRINT_LEVEL_4_BUGFIX
        //printf("MyAIR780EG_TcpSendData():i=%d\n", i);
        #endif
        if(my_air780eg_send_queue!=NULL)
            queue_send_ret = xQueueSend(my_air780eg_send_queue, &my_air780eg_send_queue_item_array[i], timeout / portTICK_PERIOD_MS);
        return 0;
    }
    else
    {
        ESP_LOGE("MyAIR780EG_TcpSendData()", "error, i=%d\n", i);
        return 1;
    }

    return 0;
}

static int MyAIR780EG_Queue_Init(my_air780eg_queue_item_t** p, uint8_t data_area)
{
    if(data_area)
    {
        char* p_temp = (char*)calloc(AIR780EG_QUEUE_LEN, AIR780EG_DATA_PACK_SIZE);
        if(p_temp!=NULL)
        { 
            for(int i=0; i<AIR780EG_QUEUE_LEN; i++)
            {
                p[i] = (my_air780eg_queue_item_t*)calloc(1, sizeof(my_air780eg_queue_item_t));
                if(p[i]!=NULL)
                {
                    p[i]->data = p_temp+(i*(AIR780EG_DATA_PACK_SIZE));
                }
                else
                {
                    ESP_LOGE("MyAIR780EG_Queue_Init()", "error1\n");
                    return 1;
                }
            }
        }
        else
        {
            ESP_LOGE("MyAIR780EG_Queue_Init()", "error2\n");
            return 2;
        }
    }
    else
    {
        for(int i=0; i<AIR780EG_QUEUE_LEN; i++)
        {
            p[i] = (my_air780eg_queue_item_t*)calloc(1, sizeof(my_air780eg_queue_item_t));
            if(p[i]==NULL)
            {
                ESP_LOGE("MyAIR780EG_Queue_Init()", "error3, i=%d\n", i);
                return i;
            }
        }
    }
    return AIR780EG_QUEUE_LEN;
}

int MyAIR780EG_TcpDataStationInit(void)
{
    MyAIR780EG_TcpSendDataMutexSemaphore = xSemaphoreCreateMutex();
    MyAIR780EG_CmdModeSendMutexSemaphore = xSemaphoreCreateMutex();
    MyAIR780EG_CmdModeRcvMutexSemaphore = xSemaphoreCreateMutex();

    MyAIR780EG_Queue_Init(my_air780eg_send_queue_item_array, 1);
    MyAIR780EG_Queue_Init(my_air780eg_rcv_queue_item_array, 1);

    my_air780eg_rcv_queue = xQueueGenericCreate(AIR780EG_QUEUE_LEN, sizeof(my_air780eg_queue_item_t*), 1);
    if(my_air780eg_rcv_queue==NULL)ESP_LOGE("MyAIR780EG_TcpDataStationInit()", "my_air780eg_rcv_queue=NULL\n");
    my_air780eg_send_queue = xQueueGenericCreate(AIR780EG_QUEUE_LEN, sizeof(my_air780eg_queue_item_t*), 1);
    if(my_air780eg_send_queue==NULL)ESP_LOGE("MyAIR780EG_TcpDataStationInit()", "my_air780eg_send_queue=NULL\n");

    BaseType_t ret = pdPASS;
    #if MyAIR780EG_TcpDataRcv_Task_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    p_task_stack = calloc(1, MyAIR780EG_TcpDataRcv_Task_task_stack_size*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
			if(xTaskCreateStaticPinnedToCore(MyAIR780EG_TcpDataRcv_Task, "MyAIR780EG_TcpDataRcv_Task", MyAIR780EG_TcpDataRcv_Task_task_stack_size, NULL, MyAIR780EG_TcpDataRcv_Task_priority, p_task_stack, p_task_data, MyAIR780EG_TcpDataRcv_Task_COREID)!=p_task_data)
			{
				ESP_LOGE("MyEspChipInfoMng_Init()", "creat MyAIR780EG_TcpDataRcv_Task use xTaskCreateStaticPinnedToCore() error\n");
                return 1;
			}
        }else return 1;
    }else return 1;
	#elif MyAIR780EG_TcpDataRcv_Task_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
	ret = xTaskCreatePinnedToCore(MyAIR780EG_TcpDataRcv_Task, "MyAIR780EG_RcvTcpData_Task", MyAIR780EG_TcpDataRcv_Task_task_stack_size, NULL, MyAIR780EG_TcpDataRcv_Task_priority, &MyAIR780EG_TcpDataRcv_TaskHandle, MyAIR780EG_TcpDataRcv_Task_COREID);
	if(ret!=pdPASS)
	{
		ESP_LOGE("MyEspChipInfoMng_Init()", "creat MyAIR780EG_TcpDataRcv_Task use xTaskCreatePinnedToCore() error=%d\n", ret);
        return 1;
	}
	#endif

    #if MyAIR780EG_TcpDataSend_Task_CREATE_TYPE==TASK_CREATE_TYPE_STATIC
    StackType_t* p_task_stack = NULL;
	StaticTask_t* p_task_data = NULL;

    p_task_stack = calloc(1, MyAIR780EG_TcpDataSend_Task_task_stack_size*sizeof(StackType_t));
    if(p_task_stack!=NULL)
    {
        p_task_data = calloc(1, sizeof(StaticTask_t));
        if(p_task_data!=NULL)
        {
			if(xTaskCreateStaticPinnedToCore(MyAIR780EG_TcpDataSend_Task, "MyAIR780EG_TcpDataSend_Task", MyAIR780EG_TcpDataSend_Task_task_stack_size, NULL, MyAIR780EG_TcpDataSend_Task_priority, p_task_stack, p_task_data, MyAIR780EG_TcpDataSend_Task_COREID)!=p_task_data)
			{
				ESP_LOGE("MyEspChipInfoMng_Init()", "creat MyAIR780EG_TcpDataSend_Task use xTaskCreateStaticPinnedToCore() error\n");
                return 1;
			}
        }else return 1;
    }else return 1;
	#elif MyAIR780EG_TcpDataSend_Task_CREATE_TYPE==TASK_CREATE_TYPE_DYNAMIC
	ret = xTaskCreatePinnedToCore(MyAIR780EG_TcpDataSend_Task, "MyAIR780EG_SendTcpData_Task", MyAIR780EG_TcpDataSend_Task_task_stack_size, NULL, MyAIR780EG_TcpDataSend_Task_priority, &MyAIR780EG_TcpDataSend_TaskHandle, MyAIR780EG_TcpDataSend_Task_COREID);
	if(ret!=pdPASS)
	{
		ESP_LOGE("MyEspChipInfoMng_Init()", "creat MyAIR780EG_TcpDataSend_Task use xTaskCreatePinnedToCore() error=%d\n", ret);
        return 1;
	}
	#endif
    return 0;
}

#endif