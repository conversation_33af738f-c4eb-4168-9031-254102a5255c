#include "e104_bt53c3.h"

#include "my_aux_485_port.h"

#include "my_gpio.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"
#include "freertos/semphr.h"
#include "freertos/timers.h"

#include "esp_system.h"
#include "esp_log.h"
#include "driver/uart.h"
#include "driver/gpio.h"

#include "uart_to_uart.h"

#include "string.h"
#include "stdlib.h"
#include <stdio.h>
#include <string.h>

#include "my_relay.h"

#include "my_esp_chip_info_mng.h"

#include "my_led_mng.h"




#define BT53C3_RST_GPIO_PIN  15
#define BT53C3_RST_PIN_SEL  ( (1ULL<<BT53C3_RST_GPIO_PIN)  )

int e104_role = -1;
int e104_adv_interval = -1;
int e104_adv_power = -1;
int e104_long_range = -1;
char e104_mac[32];
bool e104_mac_got = false;
bool e104_bt53c3_init_ok = false;
bool e104_bt53c3_chip1_get_mac_ok = false;
char* e104_chip1_mac = NULL;
char* e104_chip1_mac_no_colons = NULL;
char* e104_chip2_mac = NULL;
char* e104_chip2_mac_no_colons = NULL;

int e104_bt53c3_query_connect_list(void);
int e104_bt53c3_discon_all_dev(void);

static void BT53C3_RSTGpioInit(void)
{
    gpio_config_t io_conf;
    //disable interrupt
    io_conf.intr_type = GPIO_PIN_INTR_DISABLE;
    //set as output mode
    io_conf.mode = GPIO_MODE_OUTPUT;
    //bit mask of the pins that you want to set,e.g.GPIO18/19
    io_conf.pin_bit_mask = BT53C3_RST_PIN_SEL;
    //disable pull-down mode
    io_conf.pull_down_en = 0;
    //disable pull-up mode
    io_conf.pull_up_en = 0;
    //configure GPIO with the given settings
    gpio_config(&io_conf);

    MyGPIO_SET(BT53C3_RST_GPIO_PIN, 0);
}

void BT53C3_RST(void)
{
	MyGPIO_SET(BT53C3_RST_GPIO_PIN, 0);
	vTaskDelay(2000 / portTICK_PERIOD_MS);
	MyGPIO_SET(BT53C3_RST_GPIO_PIN, 1);
}


#define BT53C3_MOD_GPIO_PIN  16
#define BT53C3_MOD_PIN_SEL  ( (1ULL<<BT53C3_MOD_GPIO_PIN)  )

static void BT53C3_ModGpioInit(void)
{
    gpio_config_t io_conf;
    //disable interrupt
    io_conf.intr_type = GPIO_PIN_INTR_DISABLE;
    //set as output mode
    io_conf.mode = GPIO_MODE_OUTPUT;
    //bit mask of the pins that you want to set,e.g.GPIO18/19
    io_conf.pin_bit_mask = BT53C3_MOD_PIN_SEL;
    //disable pull-down mode
    io_conf.pull_down_en = 0;
    //disable pull-up mode
    io_conf.pull_up_en = 0;
    //configure GPIO with the given settings
    gpio_config(&io_conf);

    MyGPIO_SET(BT53C3_MOD_GPIO_PIN, 0);
}

void BT53C3_Mod_cmd(void)
{
	MyGPIO_SET(BT53C3_MOD_GPIO_PIN, 0);
}

void BT53C3_Mod_data(void)
{
	MyGPIO_SET(BT53C3_MOD_GPIO_PIN, 1);
}


#define BT53C3_RX_BUF_SIZE   1024

#define BT53C3_BAUDRATE 115200UL

#define BT53C3_TXD_PIN (GPIO_NUM_7)
#define BT53C3_RXD_PIN (GPIO_NUM_6)

static void Uart_BT53C3_Init(void)
{
    const uart_config_t uart_config = {
        .baud_rate = BT53C3_BAUDRATE,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE
    };
    uart_param_config(UART_NUM_2, &uart_config);
    uart_set_pin(UART_NUM_2, BT53C3_TXD_PIN, BT53C3_RXD_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    // We won't use a buffer for sending data.
    uart_driver_install(UART_NUM_2, BT53C3_RX_BUF_SIZE, 0, 0, NULL, 0);
}

static int BT53C3sendData(const char* logName, const char* data, uint16_t len)
{
    const int txBytes = uart_write_bytes(UART_NUM_2, data, len);
    ESP_LOGI(logName, "Wrote %d bytes", txBytes);
    return txBytes;
}

// 函数声明
char* extractMacAddress(const char* input, const char* ref_str);

// 函数定义：从输入字符串中提取MAC地址
char* extractMacAddress(const char* input, const char* ref_str) {
    // 假设输入格式总是正确的，并且MAC地址后紧跟着" CONNECTD P"
    static char mac[18]; // MAC地址格式（包含冒号和终止符）最长为17+1（'\0'）
    
    // 查找" CONNECTD P"的位置
    const char* pos = strstr(input, ref_str);
    
    // 检查是否找到了该字符串
    if (pos == NULL) {
        printf("MAC address not found in input string.\n");
        return NULL;
    }
    
    // 计算MAC地址的起始位置
    char* macStart = pos - 17; // MAC地址长度为17个字符，所以向前回退17位

    if (macStart < input) {
        return NULL; // 如果起始位置小于0，则输入格式不符合预期
    }
    
    // 复制MAC地址到静态缓冲区
    strncpy(mac, macStart, 17);
    mac[17] = '\0'; // 确保字符串正确终止

    
    return mac;
}

int get_last_number(const char *str) {
    int length = strlen(str);
    int i = length - 1;
    int number = 0;
    int multiplier = 1;
    int found_digit = 0;
 
    // 从字符串末尾向前遍历
    while (i >= 0) {
        if (isdigit(str[i])) {
            // 如果是数字，构造数值
            number = (str[i] - '0') * multiplier + number;
            multiplier *= 10;
            found_digit = 1;
        } else if (found_digit) {
            // 如果已经找到数字但当前字符不是数字，则停止
            break;
        }
        i--;
    }
 
    return number;
}


TimerHandle_t   Bt53c3timeoutTimer_Handle;

void Bt53c3timeoutTimer(xTimerHandle xTimer)
{
    ESP_LOGI("BT53C3", "----------------------Bt53c3timeoutTimer");
    My_Relay_On();
	e104_bt53c3_discon_all_dev();
}

// 补齐MAC地址中的0并保持格式
char* fix_mac_address(const char* mac) {
    char* copy = strdup(mac);
    if (!copy) return NULL;

    char* parts[6];
    char* token = strtok(copy, ":");
    for (int i = 0; i < 6; i++) {
        if (!token) {
            free(copy);
            return NULL;
        }
        parts[i] = token;
        token = strtok(NULL, ":");
    }
    if (token) {
        free(copy);
        return NULL; // 额外部分
    }

    char* result = malloc(18);
    if (!result) {
        free(copy);
        return NULL;
    }

    sprintf(result, "%02X:%02X:%02X:%02X:%02X:%02X",
            (unsigned char)strtol(parts[0], NULL, 16),
            (unsigned char)strtol(parts[1], NULL, 16),
            (unsigned char)strtol(parts[2], NULL, 16),
            (unsigned char)strtol(parts[3], NULL, 16),
            (unsigned char)strtol(parts[4], NULL, 16),
            (unsigned char)strtol(parts[5], NULL, 16));

    free(copy);
    return result;
}

// 移除MAC地址中的冒号
char* remove_colons(const char* mac) {
    size_t len = strlen(mac);
    char* result = malloc(len + 1);
    if (!result) return NULL;

    size_t j = 0;
    for (size_t i = 0; i < len; i++) {
        if (mac[i] != ':') {
            result[j++] = mac[i];
        }
    }
    result[j] = '\0';
    return result;
}

TaskHandle_t UsartBT53C3RcvTask_Handle = NULL;
void UsartBT53C3RcvTask( void *pvParameters )
{
    bool bt53c3_timer_created = false;
    static const char *RX_TASK_TAG = "UsartBT53C3RcvTask";
    char* data = (char*)calloc(BT53C3_RX_BUF_SIZE+1, sizeof(char));
    uint32_t rxbytes = 0;

    if(bt53c3_timer_created==false)
    {
        bt53c3_timer_created = true;
        Bt53c3timeoutTimer_Handle = xTimerCreate("Bt53c3timeoutTimer", 2000/portTICK_PERIOD_MS, pdTRUE, 0, Bt53c3timeoutTimer);
    }

    vTaskDelay(1000 / portTICK_PERIOD_MS);
    e104_bt53c3_discon_all_dev();
    vTaskDelay(1000 / portTICK_PERIOD_MS);

    e104_chip1_mac = calloc(1, strlen("00:11:22:33:44:55"));
    e104_chip1_mac_no_colons = calloc(1, strlen("001122334455"));
    e104_chip2_mac = calloc(1, strlen("00:11:22:33:44:55"));
    e104_chip2_mac_no_colons = calloc(1, strlen("001122334455"));
    
    for(;;)
    {
        while(e104_bt53c3_init_ok==false)
        {
            vTaskDelay(1000 / portTICK_PERIOD_MS);
        }
        rxbytes = uart_read_bytes(UART_NUM_2, (void*)data, BT53C3_RX_BUF_SIZE, 100 / portTICK_RATE_MS);
        if(rxbytes)
        {
            ESP_LOGI(RX_TASK_TAG, "recved %d bytes: '%s'", rxbytes, data);
            // if(AsendData(RX_TASK_TAG, data, rxbytes))
            {
                rxbytes = 0;

                // Check if the data contains " CONNECTD P" and process it accordingly.
                if ((strstr(data, " CONNECTD P") != NULL)&&(strstr(data, " DISCONNECTD P") == NULL)) {
                    // Extract the MAC address from the data.
                    char* mac = extractMacAddress(data, " CONNECTD P");

                    xTimerStart(Bt53c3timeoutTimer_Handle, 0);

                    // Allocate memory for the formatted string once, using the total length needed.
                    size_t len = strlen(mac) + strlen("BT_master_con:") + 1; // +1 for the null terminator
                    char* data_pack = malloc(len); // Use malloc instead of calloc as we don't need to initialize to zero

                    if (data_pack != NULL) {
                        // Format the data pack with the extracted MAC address.
                        snprintf(data_pack, len, "BT_master_con:%s", mac);

                        ESP_LOGI("RX_TASK_TAG", "data_pack=%s", data_pack);

                        // Free the allocated memory.
                        free(data_pack);
                    } else {
                        // Handle the case where memory allocation failed.
                        // This could be logging an error or taking other appropriate actions.
                    }

                    // Free the MAC address memory.
                    // free(mac);
                }

                // Check if the data contains " DISCONNECTD P" and process it accordingly.
                if (strstr(data, " DISCONNECTD P") != NULL) {
                    // Extract the MAC address from the data.
                    char* mac = extractMacAddress(data, " DISCONNECTD P");

                    xTimerStop(Bt53c3timeoutTimer_Handle, 0);

                    // Allocate memory for the formatted string once, using the total length needed.
                    size_t len = strlen(mac) + strlen("BT_master_discon:") + 1; // +1 for the null terminator
                    char* data_pack = malloc(len); // Use malloc instead of calloc as we don't need to initialize to zero

                    if (data_pack != NULL) {
                        // Format the data pack with the extracted MAC address.
                        snprintf(data_pack, len, "BT_master_discon:%s", mac);

                        ESP_LOGI("RX_TASK_TAG", "data_pack=%s", data_pack);

                        free(data_pack);
                    } else {
                        // Handle the case where memory allocation failed.
                        // This could be logging an error or taking other appropriate actions.
                    }

                    // Free the MAC address memory.
                    // free(mac);
                }


                if(strstr(data, "+RECEIVED:")!=NULL)
                {
                    const char *input = data;

                    const char *received_prefix = "+RECEIVED:";
                    const char *ble_data_prefix = "BLE DATA\r\n";

                    // 查找 "+RECEIVED:" 和 "BLE DATA\r\n" 的位置
                    char *received_pos = strstr(input, received_prefix);
                    char *ble_data_pos = strstr(input, ble_data_prefix);

                    if (received_pos == NULL || ble_data_pos == NULL) {
                    printf("Input string format is incorrect.\n");
                    return 1;
                    }

                    // 跳过 "+RECEIVED:" 前缀
                    received_pos += strlen(received_prefix);

                    // 提取设备编号和数据长度
                    int device_id, data_length;
                    if (sscanf(received_pos, "%d,%d", &device_id, &data_length) != 2) {
                    printf("Failed to parse device ID and data length.\n");
                    return 1;
                    }

                    // 跳过 "BLE DATA\r\n" 前缀
                    ble_data_pos += strlen(ble_data_prefix);

                    // 分配内存来存储数据字符串
                    char *_data = (char *)malloc((data_length + 1) * sizeof(char));
                    if (_data == NULL) {
                    printf("Memory allocation failed.\n");
                    return 1;
                    }

                    // 复制数据到 data 字符串
                    strncpy(_data, ble_data_pos, data_length);
                    _data[data_length] = '\0'; // 确保字符串以空字符结尾

                    // 输出提取的数据
                    printf("Device ID: %d\n", device_id);
                    printf("Data Length: %d\n", data_length);
                    printf("Data: %s\n", _data);

                    char* data_pack = (char*)calloc(1, data_length+strlen("BT_rcv_data_from_master:")+1);
                    sprintf(data_pack, "BT_rcv_data_from_master:%s", _data);

                    if((strstr( (char *)data, "evg relay1 on"))!=NULL)
                    {
                        xTimerStop(Bt53c3timeoutTimer_Handle, 0);
                        ESP_LOGI("MyBlufi_CDA()", "recv cmd: evg relay1 on");
                        My_Relay_On();
                        MyLedMng_Find_Dev();
                        e104_bt53c3_send_data_to_master("success: evg relay1 on", strlen("success: evg relay1 on"));
                        e104_bt53c3_query_connect_list();
                        e104_bt53c3_discon_all_dev();

                    }
                    if((strstr( (char *)data, "evg relay1 off"))!=NULL)
                    {
                        ESP_LOGI("MyBlufi_CDA()", "recv cmd: evg relay1 off");
                        My_Relay_Off();
                        e104_bt53c3_send_data_to_master("success: evg relay1 off", strlen("success: evg relay1 off"));
                    }

                    // AUX_PORTsendData("UsartBT53C3RcvTask", data_pack, strlen(data_pack));

                    free(_data);
                    free(data_pack);
                }
                if((strstr( (char *)data, "+RLOE"))!=NULL)
                {
                    e104_role = get_last_number(data);
                    printf("e104_role: %d\n", e104_role);
                }
                if((strstr( (char *)data, "+LE_CODED"))!=NULL)
                {
                    e104_long_range = get_last_number(data);
                    printf("e104_long_range: %d\n", e104_long_range);
                }
                if((strstr( (char *)data, "+MAC"))!=NULL)
                {
                    memset(e104_mac, 0, sizeof(e104_mac));
                    memcpy(e104_mac, data+strlen("+MAC="), 17);
                    printf("e104_mac: %s\n", e104_mac);
                    e104_mac_got = true;
                    {
                        char* fixed = fix_mac_address(e104_mac);
                        if (fixed) {
                            printf("补齐后的MAC地址: %s\n", fixed);
                            char* cleaned = remove_colons(fixed);
                            memcpy(e104_chip1_mac, fixed, strlen(fixed));
                            if (cleaned) {
                                printf("去除冒号后的地址: %s\n", cleaned);
                                memcpy(e104_chip1_mac_no_colons, cleaned, strlen(cleaned));
                                free(cleaned);
                                e104_bt53c3_chip1_get_mac_ok = true;
                            }
                            free(fixed);
                        }
                    }
                }
                if((strstr( (char *)data, "+ADV"))!=NULL)
                {
                    e104_adv_interval = get_last_number(data);
                    printf("e104_adv_interval: %d\n", e104_adv_interval);
                }
                if((strstr( (char *)data, "+PWR"))!=NULL)
                {
                    e104_adv_power = get_last_number(data);
                    printf("e104_adv_power: %d\n", e104_adv_power);
                }
                

                memset(data, 0, BT53C3_RX_BUF_SIZE);
            }
        }
    }
    free(data);
}


int e104_bt53c3_mode_cmd(void)
{
    vTaskDelay(210 / portTICK_PERIOD_MS);
    BT53C3_Mod_cmd();
    vTaskDelay(210 / portTICK_PERIOD_MS);
    return 0;
}

int e104_bt53c3_mode_data(void)
{
    vTaskDelay(210 / portTICK_PERIOD_MS);
    BT53C3_Mod_data();
    vTaskDelay(210 / portTICK_PERIOD_MS);
    return 0;
}

int e104_bt53c3_rst(void)
{
    BT53C3_RST();
    
    return 0;
}

int e104_bt53c3_set_dev_role(int role)
{
    e104_bt53c3_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+ROLE=%d\r\n", role);
    BT53C3sendData("e104_bt53c3_set_dev_role", cmd, strlen(cmd));
    return 0;
}

#define e104_role_slave     0
#define e104_role_master    1
#define e104_role_slave_and_master    2
#define e104_role_beacon    3

int e104_bt53c3_query_dev_role(void)
{
    e104_bt53c3_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+ROLE?\r\n");
    BT53C3sendData("e104_bt53c3_query_dev_role", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_query_adv_interval(void)
{
    e104_bt53c3_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+ADV?\r\n");
    BT53C3sendData("e104_bt53c3_query_adv_interval", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_query_power(void)
{
    e104_bt53c3_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+PWR?\r\n");
    BT53C3sendData("e104_bt53c3_query_power", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_query_dev_mac(void)
{
    e104_bt53c3_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+MAC?\r\n");
    BT53C3sendData("e104_bt53c3_query_dev_mac", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_query_long_range(void)
{
    e104_bt53c3_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+LE_CODED?\r\n");
    BT53C3sendData("e104_bt53c3_query_lone_range", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_query_connect_list(void)
{
    e104_bt53c3_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+CONNECT_LIST?\r\n");
    BT53C3sendData("e104_bt53c3_query_connect_list", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_discon_all_dev(void)
{
    e104_bt53c3_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+DISCON\r\n");
    BT53C3sendData("e104_bt53c3_discon_all_dev", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_set_adv_name(char *name, int len)
{
    if(!name)
    {
        return -1;
    }
    if(len > 31)
    {
        return -1;
    }
    if(strlen(name)>len)
    {
        name[len] = '\0';
    }
    if(len == 0)
    {
        return 0;
    }

    e104_bt53c3_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+NAME=%s\r\n", name);
    ESP_LOGI("e104_bt53c3_set_adv_name", "cmd:%s", cmd);
    BT53C3sendData("e104_bt53c3_set_adv_name", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_set_adv_data(char *data, int len)
{
    if(!data)
    {
        return -1;
    }
    if(len > 31)
    {
        return -1;
    }
    if(strlen(data)>len)
    {
        data[len] = '\0';
    }
    if(len == 0)
    {
        return 0;
    }

    e104_bt53c3_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+ADVDATA=0,%s\r\n", data);
    ESP_LOGI("e104_bt53c3_set_adv_data", "cmd:%s", cmd);
    BT53C3sendData("e104_bt53c3_set_adv_data", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_set_long_range(bool long_range)
{
    e104_bt53c3_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    if(long_range==true)
        sprintf(cmd, "AT+LE_CODED=%d\r\n", 1);
    else
        sprintf(cmd, "AT+LE_CODED=%d\r\n", 0);
    ESP_LOGI("e104_bt53c3_set_adv_name", "cmd:%s", cmd);
    BT53C3sendData("e104_bt53c3_set_adv_name", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_set_adv_interval(int adv_interval)
{
    e104_bt53c3_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    if((adv_interval>=20)&&(adv_interval<=10240))
        sprintf(cmd, "AT+ADV=1,1,%d\r\n", adv_interval);
    else
        sprintf(cmd, "AT+ADV=1,1,%d\r\n", 200);
    ESP_LOGI("e104_bt53c3_set_adv_interval", "cmd:%s", cmd);
    BT53C3sendData("e104_bt53c3_set_adv_interval", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_restore(void)
{
    e104_bt53c3_mode_cmd();
    char cmd[64];
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "AT+RESTORE\r\n");
    ESP_LOGI("e104_bt53c3_restore", "cmd:%s", cmd);
    BT53C3sendData("e104_bt53c3_restore", cmd, strlen(cmd));
    return 0;
}

int e104_bt53c3_send_data_to_master(char *data, int len)
{
    e104_bt53c3_mode_data();
    BT53C3sendData("e104_bt53c3_send_data_to_master", data, len);
    e104_bt53c3_mode_cmd();

    return 0;
}

int e104_bt53c3_reinit(void)
{
    e104_bt53c3_init_ok = false;
	vTaskDelay(1000 / portTICK_PERIOD_MS);
	BT53C3_RST();
	BT53C3_Mod_cmd();
	vTaskDelay(5000 / portTICK_PERIOD_MS);

    char esp_mac[16];
    memset(esp_mac, 0, sizeof(esp_mac));
    MyEspChipInfoMng_GetMacStr(esp_mac);

    char c104_adv_name[32];
    memset(c104_adv_name, 0, sizeof(c104_adv_name));

    sprintf(c104_adv_name, "EVG-%s", esp_mac);

    ESP_LOGI("e104_bt53c3_init", "c104_adv_name: %s", c104_adv_name);

    vTaskDelay(1000 / portTICK_PERIOD_MS);

    e104_bt53c3_set_adv_name(c104_adv_name, strlen(c104_adv_name));
    vTaskDelay(200 / portTICK_PERIOD_MS);
    e104_bt53c3_set_adv_interval(100);

    xTaskCreate(UsartBT53C3RcvTask,  "UsartBT53C3RcvTask",    3072,    NULL, 5, &UsartBT53C3RcvTask_Handle);

    // e104_bt53c3_set_adv_name("test_01234", strlen("test_01234"));
    // vTaskDelay(1000 / portTICK_PERIOD_MS);
    // e104_bt53c3_set_adv_data("1234", strlen("1234"));

    e104_bt53c3_init_ok = true;

    vTaskDelay(3000 / portTICK_PERIOD_MS);
    e104_bt53c3_query_dev_role();
    vTaskDelay(500 / portTICK_PERIOD_MS);
    e104_bt53c3_query_dev_mac();
    vTaskDelay(500 / portTICK_PERIOD_MS);
    e104_bt53c3_query_adv_interval();
    vTaskDelay(500 / portTICK_PERIOD_MS);
    e104_bt53c3_query_power();

    return 0;
}

int e104_bt53c3_init(void)
{
    e104_bt53c3_init_ok = false;

    BT53C3_ModGpioInit();
	BT53C3_RSTGpioInit();
	vTaskDelay(1000 / portTICK_PERIOD_MS);
	BT53C3_RST();
	BT53C3_Mod_cmd();
	vTaskDelay(5000 / portTICK_PERIOD_MS);

    Uart_BT53C3_Init();

    xTaskCreate(UsartBT53C3RcvTask,  "UsartBT53C3RcvTask",    3072,    NULL, 5, &UsartBT53C3RcvTask_Handle);

    // e104_bt53c3_set_adv_name("test_01234", strlen("test_01234"));
    // vTaskDelay(1000 / portTICK_PERIOD_MS);
    // e104_bt53c3_set_adv_data("1234", strlen("1234"));

    e104_bt53c3_init_ok = true;

    vTaskDelay(3000 / portTICK_PERIOD_MS);
    e104_bt53c3_query_dev_mac();

    while(!e104_bt53c3_chip1_get_mac_ok)
    {
        vTaskDelay(100 / portTICK_PERIOD_MS);
    }
    char c104_adv_name[32];
    memset(c104_adv_name, 0, sizeof(c104_adv_name));

    sprintf(c104_adv_name, "EVG-%s", e104_chip1_mac_no_colons+4);

    ESP_LOGI("e104_bt53c3_init", "c104_adv_name: %s", c104_adv_name);

    vTaskDelay(1000 / portTICK_PERIOD_MS);

    e104_bt53c3_set_adv_name(c104_adv_name, strlen(c104_adv_name));
    vTaskDelay(1000 / portTICK_PERIOD_MS);
    e104_bt53c3_set_adv_interval(100);
    vTaskDelay(500 / portTICK_PERIOD_MS);

    e104_bt53c3_query_dev_role();
    vTaskDelay(500 / portTICK_PERIOD_MS);
    e104_bt53c3_query_adv_interval();
    vTaskDelay(500 / portTICK_PERIOD_MS);
    e104_bt53c3_query_power();

    return 0;
}

