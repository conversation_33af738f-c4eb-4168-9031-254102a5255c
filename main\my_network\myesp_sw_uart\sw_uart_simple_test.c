#include "myesp_sw_uart.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "string.h"

static const char* TAG = "SW_UART_SIMPLE_TEST";

// 简单测试用的引脚定义
#define SIMPLE_TEST_TX_PIN    (GPIO_NUM_4)
#define SIMPLE_TEST_RX_PIN    (GPIO_NUM_5)

/**
 * @brief 简单的软串口测试
 * 
 * 这个测试使用基于定时器的新实现，应该解决任务超时和乱码问题
 */

static void simple_rx_task(void *pvParameters) {
    char rx_buffer[128];
    int bytes_received;
    uint32_t rx_count = 0;
    
    ESP_LOGI(TAG, "Simple RX task started");
    
    while (1) {
        // 接收数据，超时时间200ms
        bytes_received = sw_uart_read_bytes(SW_UART_NUM_0, rx_buffer, 
                                           sizeof(rx_buffer) - 1, 
                                           200 / portTICK_PERIOD_MS);
        
        if (bytes_received > 0) {
            rx_buffer[bytes_received] = '\0';
            rx_count++;
            
            ESP_LOGI(TAG, "[%d] RX %d bytes:", rx_count, bytes_received);
            
            // 打印十六进制
            printf("HEX: ");
            for (int i = 0; i < bytes_received; i++) {
                printf("%02X ", (uint8_t)rx_buffer[i]);
            }
            printf("\n");
            
            // 打印ASCII（如果可打印）
            printf("ASCII: ");
            for (int i = 0; i < bytes_received; i++) {
                if (rx_buffer[i] >= 32 && rx_buffer[i] <= 126) {
                    printf("%c", rx_buffer[i]);
                } else {
                    printf(".");
                }
            }
            printf("\n");
            
            // 简单回显
            char echo[150];
            snprintf(echo, sizeof(echo), "ECHO[%d]: %s\r\n", rx_count, rx_buffer);
            int sent = sw_uart_write_bytes(SW_UART_NUM_0, echo, strlen(echo));
            ESP_LOGI(TAG, "Echoed %d bytes", sent);
        }
    }
}

static void simple_tx_task(void *pvParameters) {
    int counter = 0;
    char test_msg[64];
    
    ESP_LOGI(TAG, "Simple TX task started");
    
    // 等待系统稳定
    vTaskDelay(5000 / portTICK_PERIOD_MS);
    
    while (1) {
        // 每15秒发送一次测试数据
        snprintf(test_msg, sizeof(test_msg), "TEST_%04d\r\n", counter++);
        
        int bytes_sent = sw_uart_write_bytes(SW_UART_NUM_0, test_msg, strlen(test_msg));
        ESP_LOGI(TAG, "TX: %s (%d bytes)", test_msg, bytes_sent);
        
        vTaskDelay(15000 / portTICK_PERIOD_MS);
    }
}

int simple_test_init(void) {
    ESP_LOGI(TAG, "Initializing simple SW_UART test...");
    ESP_LOGI(TAG, "TX Pin: GPIO%d, RX Pin: GPIO%d", SIMPLE_TEST_TX_PIN, SIMPLE_TEST_RX_PIN);
    
    // 配置软串口 - 使用较低的波特率确保稳定性
    sw_uart_config_t config = {
        .baud_rate = 9600,
        .data_bits = SW_UART_DATA_8_BITS,
        .parity = SW_UART_PARITY_DISABLE,
        .stop_bits = SW_UART_STOP_BITS_1,
        .flow_ctrl_enable = false
    };
    
    // 应用配置
    int ret = sw_uart_param_config(SW_UART_NUM_0, &config);
    if (ret != SW_UART_OK) {
        ESP_LOGE(TAG, "Failed to configure SW_UART: %d", ret);
        return ret;
    }
    
    // 设置引脚
    ret = sw_uart_set_pin(SW_UART_NUM_0, SIMPLE_TEST_TX_PIN, SIMPLE_TEST_RX_PIN, 
                         SW_UART_PIN_NO_CHANGE, SW_UART_PIN_NO_CHANGE);
    if (ret != SW_UART_OK) {
        ESP_LOGE(TAG, "Failed to set SW_UART pins: %d", ret);
        return ret;
    }
    
    // 安装驱动
    ret = sw_uart_driver_install(SW_UART_NUM_0, 512, 0, 0, NULL, 0);
    if (ret != SW_UART_OK) {
        ESP_LOGE(TAG, "Failed to install SW_UART driver: %d", ret);
        return ret;
    }
    
    ESP_LOGI(TAG, "SW_UART initialized successfully");
    return SW_UART_OK;
}

void simple_test_start(void) {
    ESP_LOGI(TAG, "=== SW_UART Simple Test ===");
    ESP_LOGI(TAG, "This test uses timer-based RX to avoid task timeout");
    ESP_LOGI(TAG, "Baud rate: 9600, 8N1");
    ESP_LOGI(TAG, "");
    
    // 初始化
    if (simple_test_init() != SW_UART_OK) {
        ESP_LOGE(TAG, "Initialization failed");
        return;
    }
    
    // 创建任务
    BaseType_t ret;
    
    ret = xTaskCreate(simple_rx_task, "simple_rx", 3072, NULL, 6, NULL);
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create RX task");
        return;
    }
    
    ret = xTaskCreate(simple_tx_task, "simple_tx", 2048, NULL, 5, NULL);
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create TX task");
        return;
    }
    
    ESP_LOGI(TAG, "Test started successfully");
    ESP_LOGI(TAG, "Connect your device and send data to test");
    ESP_LOGI(TAG, "Expected: No more task timeouts or 0xFF errors");
}

void simple_test_send_manual(const char* data) {
    if (data == NULL) return;
    
    int sent = sw_uart_write_bytes(SW_UART_NUM_0, data, strlen(data));
    ESP_LOGI(TAG, "Manual send: %s (%d bytes)", data, sent);
}

/*
使用方法：

1. 在main.c中调用：
   ```c
   #include "sw_uart_simple_test.c"
   
   void app_main(void) {
       simple_test_start();
   }
   ```

2. 硬件连接：
   - ESP32 GPIO4 -> 外部设备 RX
   - ESP32 GPIO5 -> 外部设备 TX
   - 共地连接

3. 测试内容：
   - 基于定时器的接收，避免任务超时
   - 9600波特率，提高稳定性
   - 详细的HEX和ASCII输出
   - 自动回显功能

4. 预期改进：
   - 不再有看门狗超时
   - 接收数据不再是乱码
   - 正确的时序控制
*/
