#ifndef MY_DS_H
#define MY_DS_H

#include <lwip/netdb.h>


struct my_ds_item
{
    int id;
    int index;
    int item_type;
    int byte_order;
    int data_len;
    void* data;

    void* parent_node;
    struct my_ds_item* pri_item;
    struct my_ds_item* next_item;
};

typedef struct my_ds_item my_ds_item_t;

typedef struct
{
    int id;
    void* parent_node;
    int index;
    int data_len;
    my_ds_item_t* first_item;
    my_ds_item_t* end_item;
}my_ds_node_t;

/**
 * 实现如下一个结构的打包流程
 * struct 0A
 * {
 *      int a;
 *      char b;
 *      struct 1A
 *      {
 *          int a;
 *          char b;
 *          struct 2A
 *          {
 *              int a;
 *              char b;
 *          }
 *          char c
 *      }
 *      char c;
 * }
 * 
 * 建立一个顶端节点0A， 0A没有父节点
 * @note 此时只有一个空的 my_ds_node_t 
 * 向顶端节点添加一个项，这个项是一个最小项
 * 向顶端节点添加一个项，这个项是一个最小项
 * @note 此时， 顶端节点 my_ds_node_t_A0 的 first_item 与 end_item 都有效 data_len=5
 * 
 * 向顶端节点0A添加一个项，这个项是一个一级节点，命名为1A， 1A的父节点是0A
 * 向这个一级节点1A添加一个项，这个项是一个最小项
 * 向这个一级节点1A添加一个项，这个项是一个最小项
 * @note 此时， 节点1A my_ds_node_t_1A 的 first_item 与 end_item 都有效 data_len=5
 * 
 * 向一级节点1A添加一个项，这个项是一个二级节点，命名为2A， 2A的父节点是1A
 * 向二级节点2A添加一个项，这个项是一个最小项
 * 向二级节点2A添加一个项，这个项是一个最小项
 * @note 此时， 节点2A my_ds_node_t_2A 的 first_item 与 end_item 都有效 data_len=5
 * @note 此时， 节点1A my_ds_node_t_1A 的 data_len=10
 * @note 此时， 节点0A my_ds_node_t_0A 的 data_len=15
 * 向一级节点1A添加一个项，这个项是一个最小项
 * @note 此时， 节点1A my_ds_node_t_1A 的 data_len=11
 * @note 此时， 节点0A my_ds_node_t_0A 的 data_len=16
 * @note 此时， 节点2A my_ds_node_t_2A 的 data_len=5（不变）
 * 
 * 向顶端节点添加一个项，这个项是一个最小项
 * @note 此时， 节点0A my_ds_node_t_0A 的 data_len=17
 * @note 此时， 节点1A my_ds_node_t_1A 的 data_len=11（不变）
 * @note 此时， 节点2A my_ds_node_t_2A 的 data_len=5（不变）
 * 
 * 
 * @note 每建立一个节点，都必须指明这个节点的父节点
 * @note 每向一个节点中添加一个最小项，都必须更新这个节点的大小 data_len ，都必须更新这个节点的父节点的大小 data_len
 */

//服务器命令解析-解析函数表-76byte

#define MY_D2LINK_LIST_ITEM_ITEM_NAME_USED  0
struct my_d2link_list_item
{
    int id;
    #if MY_D2LINK_LIST_ITEM_ITEM_NAME_USED
    char item_name[32];
    #endif
    uint8_t data_type;
    uint32_t data_len;
    uint64_t data_uint64;
    uint8_t p_data_type;
    void* p_data;
    struct my_d2link_list_item* next_item;
    struct my_d2link_list_item* pri_item;
};
typedef struct my_d2link_list_item my_d2link_list_item_t;

#define MY_D2LINK_LIST_LIST_NAME_USED  1
typedef struct
{
    int list_item_num;
    uint32_t list_data_len;
    #if MY_D2LINK_LIST_LIST_NAME_USED
    char list_name[32];
    #endif
    my_d2link_list_item_t* first_item;
    my_d2link_list_item_t* end_item;
}my_d2link_list_t;

int My_D2Link_Append_Item(my_d2link_list_t* p_list, int id, void* p_data, uint8_t p_data_type, uint64_t data_uint64, uint32_t data_len, char* service_name);
int My_D2Link_Delete_Item(my_d2link_list_t* p_list, int id);
int My_D2Link_Insert_Item(my_d2link_list_t* p_list, int insert_id, int insert_direct, int id, void* p_data, uint8_t p_data_type, uint64_t data_uint64, uint8_t data_len, char* service_name);
int My_D2Link_Delete(my_d2link_list_t** p_list);
int My_D2Link_match_id_count_statistic(my_d2link_list_t* p_list, int id_begin, int id_end);
int My_D2Link_Data_Packer(my_d2link_list_t* p_list, void* saving, uint32_t saving_len);
int My_D2Link_Data_Packer_accordingID(my_d2link_list_t* p_list, void* saving, uint32_t saving_len, uint8_t direct);
int My_D2Link_GetItem_ContinousMaxId_next(my_d2link_list_t* p_list, int start_id);
int My_D2Link_GetItem_ContinousDistribution_8_ByCurId(my_d2link_list_t* p_list, int start_id);
my_d2link_list_t* My_D2Link_Creat(char* list_name);
int My_D2Link_PrintList(my_d2link_list_t* p_list);
void* My_D2Link_GetItem_Storage_ById(my_d2link_list_t* p_list, int id);
int My_D2Link_Data_Sort_Data_accordingID(my_d2link_list_t* p_list, uint8_t direct);
void* My_D2Link_GetItem_IdGreaterThan(my_d2link_list_t* p_list, my_d2link_list_item_t* p_item);


#endif