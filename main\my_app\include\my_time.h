#ifndef MY_TIME_H
#define MY_TIME_H

#define MY_TIME_FUNC    1

#if MY_TIME_FUNC

#include <lwip/netdb.h>


#define MY_TIME_FUNC_DEBUG  0

typedef struct
{
    time_t mark_prehistoric_since_zero;//对时之前的时间，以0为参考
    time_t mark_last_sync;//最后一次对时时间
    time_t mark_last_reset;//最后一次复位时间（复位时的开机时间）
}mytime_timemark_t;

typedef struct
{
    time_t runtime_since_poweron;     //本次上电以来运行时间,s
    time_t runtime_since_reset;       //本次复位以来运行时间,s
    
    time_t runtime_total;             //出厂以来总运行时间,s
    uint32_t poweron_count;           //上电次数（不包含软件复位）
}mytime_info_t;

typedef struct
{
    uint32_t poweron_count;
    time_t mark_prehistoric_since_zero;
    time_t mark_last_sync;
}mytime_info_nvs_t;

#define MTM_NVS_FORM_NAME	"NVS_MTM"
#define MTM_NVS_KEY_NAME	"NVS_MTM_1"



/**
  * @brief 获取当前时间，使用time()获取，未对时前以0为参考
  * @return Time in Second
  */
time_t MyTime_GetTime(void);

/**
  * @brief 设置时间，立即生效，标记时间同步点
  * @param second  Time in Second
  * @return 0:success, -1:Function not implemented
  */
int MyTime_SetTimeAndMark(time_t second);

/**
  * @brief 设置时间，立即生效，标记时间同步点
  * @param 格式化的时间
  * @return now time
  */
time_t MyTime_SetTime_Format(int year, int month, int day, int hour, int minute, int second);

/**
  * @brief 打印当前时间：实际时间，开机以来时间，复位以来时间
  */
void MyTime_PrintTime(void);

/**
  * @brief 获取开机以来运行时间
  * @param second  my_esp_chip_info_mng_info.runtime_since_poweron
  */
time_t MyTime_GetRuntimeSincePoweron(void);

/**
  * @brief 获取软件复位以来运行时间
  * @param second  my_esp_chip_info_mng_info.runtime_since_reset
  */
time_t MyTime_GetRuntimeSinceReset(void);

/**
  * @brief 获取开机次数
  */
uint32_t MyTime_GetPoweronCount(void);

/**
  * @brief 刷新时间
  */
void MyTime_Refresh(void);

void MyTime_SavingForReboot(void);

void MyTime_Init(uint8_t ispoweron, uint8_t reload_mark);

#if MY_TIME_FUNC_DEBUG
//-----------------------------------------------------
//debug
void MyDebug_PrintMemInfo(char* name);
//-----------------------------------------------------
#endif

#endif

#endif