#ifndef MY_LOWPOWER_H
#define MY_LOWPOWER_H 

#define MY_LOWPOWER_FUNC 1


#if MY_LOWPOWER_FUNC

#include <lwip/netdb.h>

#define MY_LOWPOWER_FUNC_DEBUG 0

typedef struct
{
    uint32_t count;
    uint32_t total_time_s;
    uint8_t wakeup_type;
}my_lowpower_info_t;

typedef struct
{
    uint32_t count;
    uint32_t total_time_s;
    uint8_t wakeup_type;
}my_lowpower_nvs_t;
#define LPWR_NVS_FORM_NAME	"NVS_LPWR"
#define LPWR_NVS_KEY_NAME	"NVS_LPWR_1"



#define MYLOWPOWER_WAKEUP_TYPE_OTHER   0
#define MYLOWPOWER_WAKEUP_TYPE_TIMER   1
#define MYLOWPOWER_WAKEUP_TYPE_PIN   2
#define MYLOWPOWER_WAKEUP_TYPE_VALID_MAX    MYLOWPOWER_WAKEUP_TYPE_PIN
#define MY<PERSON>OWPOWER_WAKEUP_TYPE_NO_SLEEP   0xff
#define MYLOWPOWER_START_ERROR_START_FAILED 101
#define MYLOWPOWER_START_ERROR_INVALID_ARG  102
#define MYLOWPOWER_WAKEUP_TYPE_PAUSE_MOMENT 201

#define MYLOWPOWER_HOLD_GPIO_2 12


#define MYLOWPOWER_MODE_LIGHTSLEEP  1
#define MYLOWPOWER_MODE_DEEPSLEEP   2

/**
 * @brief 获取睡眠总时间
 * @return my_light_sleep_info.total_time_s:已睡眠总时间，单位s
 */
uint32_t MyLowPower_GetSleepTotalTime(void);

/**
 * @brief 获取睡眠总次数
 * @return my_light_sleep_info.count:已睡眠次数
 */
uint32_t MyLowPower_GetSleepTotalCount(void);

/**
 * @brief 进入light sleep模式
 * @param 睡眠时长
 * @return 唤醒原因
 */
uint8_t MyLowPower_EnterLightSleepFor(uint32_t second);

/**
 * @brief 进入light sleep模式
 * @param 唤醒时间
 * @return 唤醒原因
 */
uint8_t MyLowPower_EnterLightSleepUntil(time_t wakeup_time);

/**
 * @brief 进入deep sleep模式
 * @param 睡眠时长
 * @return 唤醒原因
 */
uint8_t MyLowPower_EnterDeepSleepFor(uint32_t second);

/**
 * @brief 进入deep sleep模式
 * @param 唤醒时间
 * @return 唤醒原因
 */
uint8_t MyLowPower_EnterDeepSleepUntil(time_t wakeup_time);

/**
 * @brief 加载低功耗记录信息，软件复位后调用
 */
void MyLowPower_Info_Init(void);

/**
 * @brief 保存低功耗记录信息，My_esp_restart()中调用
 * @param 1:立即重启，0:不重启
 * @return 0
 */
int SaveMyLowPowerConfig(int reboot);


#if MY_LOWPOWER_FUNC_DEBUG
//-----------------------------------------------------
//debug
void MyDebug_PrintLowPowerInfo(char* name);
//-----------------------------------------------------
#endif


#endif
#endif
