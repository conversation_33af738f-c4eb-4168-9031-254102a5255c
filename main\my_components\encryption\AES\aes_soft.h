#ifndef AES_H
#define AES_H
/*
 * Advanced Encryption Standard
 * <AUTHOR>
 * @email <EMAIL>
 *
 * Based on the document FIPS PUB 197
 */

#include <stdint.h>
#include <stdlib.h>

/*
 * Multiplication in GF(2^8)
 * http://en.wikipedia.org/wiki/Finite_field_arithmetic
 * Irreducible polynomial m(x) = x8 + x4 + x3 + x + 1
 * 
 * NOTE: we are using the look up table instead of the (slower) gmult function
 */
#define gmult(a,b) gmult_aes[256*a + b]

uint8_t *aes_init(size_t key_size);

void aes_key_expansion(uint8_t *key, uint8_t *w);

void aes_inv_cipher(uint8_t *in, uint8_t *out, uint8_t *w);

void aes_cipher(uint8_t *in, uint8_t *out, uint8_t *w);

#endif
