#include "mytest_uart_iot_server.h"

#if MYTEST_CONFIG_UART_IOT_SERVER

#include "my_tsfc.h"
#include "my_asl.h"
#include "my_endian.h"
#include "checksum.h"

#include "esp_log.h"

#include "stdio.h"

QueueHandle_t mytest_uart_read_queue = NULL;
my_whgm5_queue_item_t* my_test_uart_read_queue_item_array[WHGM5_QUEUE_LEN];
int Mytest_SendData_to_uart_read(void const* data, uint32_t len, uint32_t timeout, uint8_t tcp_channel);
void Mytest_Print(void);


char mytest_dev_ack[]={0x03, 0x01 , 0x00 , 0x00 , 0x12 , 0x34 , 0x56 , 0x78 , 0x47 , 0x4F , 0x4F , 0x44 };
char mytest_dev_register_res[]={0x01, 0x01 , 0x00 , 0x00 , 0x00 , 0x01 , 0x00 , 0x10 , /**/
                                0x02 , 0x01 , 0x00 , 0x08 , /*asl_head*/
                                0x00 , 0x00 , /*ret_code*/
                                0x11 , 0x22 , 0x33 , 0x44 , 0x00 , 0x00 , 
                                0x00 , 0x00 , 0x00 , 0x00 , 
                                0x12 , 0x34 , 0x56 , 0x78 , 0x47 , 0x4F , 0x4F , 0x44 };
char mytest_dev_login_res[]={0x01 , 0x01 , 0x00 , 0x00 , 0x00 , 0x01 , 0x00 , 0x20 , /**/
                                0x04 , 0x01 , 0x00 , 0x18 , /*asl_head*/
                                0x00 , 0x00 , /*ret_code*/
                                0xAA , 0xBB , 0xCC , 0xDD , 
                                0x00 , 0x00 , 
                                0x00 , 0x00 , 0x00 , 0x00 , 0x05 , 0x39 , 0x7F , 0xB1 , 0x00 , 0x00 , 0x00 , 0x00 , 0x05 , 0x39 , 0x7F , 0xB3 , /*time_sync*/
                                0x00, 0x00, 0x00, 0x00, 
                                0x12 , 0x34 , 0x56 , 0x78 , 0x47 , 0x4F , 0x4F , 0x44 };

char mytest_dev_fileupload_res[]={0x01 , 0x01 , 0x00 , 0x00 , 0x00 , 0x01 , 0x00 , 0x10 , /**/
                                0x32 , 0x01 , 0x00 , 0x0C , /*asl_head*/
                                0x00 , 0x00 , /*ret_code*/
                                0x04 , 0x00 , 0x00 , 0x00 , 0x60 , 0x00 , /*file_base_info*/
                                0x00 , 0x14 , 0x00 , 0x00 , /*file_bpct_info*/
                                0x12 , 0x34 , 0x56 , 0x78 , 0x47 , 0x4F , 0x4F , 0x44};
char mytest_dev_fileupload_finish[]={0x01 , 0x01 , 0x00 , 0x00 , 0x00 , 0x01 , 0x00 , 0x10 , 
                                0x37 , 0x01 , 0x00 , 0x0C , 
                                0x01 , 
                                0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 
                                0x00 , 
                                0x00 , 0x00 , 
                                0x12 , 0x34 , 0x56 , 0x78 , 0x47 , 0x4F , 0x4F , 0x44 };

char mytest_dev_filedownload_res[]={0x01 , 0x01 , 0x00 , 0x00 , 0x00 , 0x01 , 0x00 , 0x50 , /**/
                                0x34 , 0x01 , 0x00 , 0x44 , /*asl_head*/
                                0x00 , 0x00 , /*ret_code*/
                                0x03 , 0x00 , 0x00 , 0x01 , 0x80 , 0x14 , /*file_base_info*/

                                0x00 , 0x01 , 0x00 , 0x38 , 


                                0x00 , 0x12 , /*file_additional_info*/

                                0x53 , 0x57 , 0x51 , 0x5A , 0x42 , 0x4F , 0x58 , 0x32 , 0x2E , 0x37 , 0x35, 
                                0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 
                                0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 

                                0x56 , 0x31 , 0x2E , 0x30 ,
                                0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 


                                0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 

                                0x12 , 0x34 , 0x56 , 0x78 , 0x47 , 0x4F , 0x4F , 0x44};

char mytest_dev_filedownload_db_pack[]={
    0x02 , 0x01 , 0x00 , 0x00 , 0x00 , 0x01 , 0x03 , 0xE0 , /**/
    0x38 , 0x01 , 0x00 , 0x44 , /*asl_head*/
    0x00 , 
    0x11 , 0x22 , 0x33 , 0x44 , 0xAA , 0xBB , 0xCC , 0xDD , 
    0x03 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 , 0x00 ,

    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,

    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,
    0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x70,

    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

    0x12 , 0x34 , 0x56 , 0x78 , 0x47 , 0x4F , 0x4F , 0x44
};

uint8_t sn_counter = 0;
my_asl_file_base_info_t mytest_file_base_info;
my_asl_file_base_info_t mytest_file_base_info_rcv_mark;
uint32_t datablock_rcved_len = 0;
int Mytest_UartSendData(char* data, int len)
{
    printf("\n\n++++Mytest_UartSendData()\n\n");

    Mytest_Print();

    my_tsf_frame_head_t* p_rcv_frame_head = (my_tsf_frame_head_t*)data;
    if(p_rcv_frame_head->type==msg_frame)
    {
        my_tsf_msgframe_t* msgframe = (my_tsf_msgframe_t*)data;
        char tsfc_frame_info_str[256]={0};
        sprintf(tsfc_frame_info_str, "frame_type=%d\npd=%d\nsn=%d\nappid=%04x\ndata_len=%d\n\n", \
        msgframe->head.type, msgframe->head.pd, msgframe->sn, my_endian_conversion_16(msgframe->app_id), my_endian_conversion_16(msgframe->data_len));
        // printf("Mytest_UartSendData():tsfc_frame_info_str=\n%s\n", tsfc_frame_info_str);

        my_asl_head_t* p_rcv_my_asl_head = (my_asl_head_t*)msgframe->data;
        char asl_info_str[256]={0};
        sprintf(asl_info_str, "msg_type=%d\nvercode=%d\ndata_len=%d\n\n", \
        p_rcv_my_asl_head->type, p_rcv_my_asl_head->vercode, my_endian_conversion_16(p_rcv_my_asl_head->len));
        // printf("Mytest_UartSendData():asl_info_str=\n%s\n", asl_info_str);

        mytest_dev_ack[2] = msgframe->sn;
        uint32_t crc_calcu = my_endian_conversion_32(crc_32((void*)mytest_dev_ack, sizeof(mytest_dev_ack)-8));
        memcpy(&mytest_dev_ack[4], &crc_calcu, 4);
        Mytest_SendData_to_uart_read(mytest_dev_ack, sizeof(mytest_dev_ack), 1000, 1);

        if(p_rcv_my_asl_head->type==dev_reg_req)
        {
            mytest_dev_register_res[2] = sn_counter;
            sn_counter++;
            printf("Mytest_UartSendData():sizeof(mytest_dev_register_res)=\n%d\n", sizeof(mytest_dev_register_res));
            Mytest_SendData_to_uart_read(mytest_dev_register_res, sizeof(mytest_dev_register_res), 1000, 1);
        }
        else if(p_rcv_my_asl_head->type==dev_login_req)
        {
            mytest_dev_login_res[2] = sn_counter;
            sn_counter++;
            printf("Mytest_UartSendData(msgframe):sizeof(mytest_dev_login_res)=\n%d\n", sizeof(mytest_dev_login_res));
            Mytest_SendData_to_uart_read(mytest_dev_login_res, sizeof(mytest_dev_login_res), 1000, 1);
        }
        else if(p_rcv_my_asl_head->type==file_upload_req)
        {
            printf("Mytest_UartSendData(msgframe):mytest_dev_fileupload_res(%d)=\n", sizeof(mytest_dev_fileupload_res));
            for(int i=0; i<sizeof(mytest_dev_fileupload_res); i++)
            {
                printf("0x%02x ", mytest_dev_fileupload_res[i]);
            }
            printf("\n\n");
            vTaskDelay(100 / portTICK_PERIOD_MS);

            mytest_dev_fileupload_res[2] = sn_counter;
            char* p_byte_msg_frame = msgframe;
            memcpy(&mytest_dev_fileupload_res[14], p_byte_msg_frame+20, 6);

            //debug 断点续传
            // mytest_dev_fileupload_res[20] = 0;
            // mytest_dev_fileupload_res[21] = 20;

            // mytest_dev_fileupload_res[22] = 0;
            // mytest_dev_fileupload_res[23] = 0xb4;
            mytest_dev_fileupload_res[20] = 0;
            mytest_dev_fileupload_res[21] = 0;

            mytest_dev_fileupload_res[22] = 0;
            mytest_dev_fileupload_res[23] = 0;

            //end of debug 断点续传
            sn_counter++;

            mytest_file_base_info.file_type = *((uint8_t*)(p_byte_msg_frame+20));
            mytest_file_base_info.file_sn = *((uint8_t*)(p_byte_msg_frame+20+1));
            mytest_file_base_info.file_len = my_endian_conversion_32(*((uint32_t*)(p_byte_msg_frame+20+1+1)));
            char fileupload_info_str[256]={0};
            sprintf(fileupload_info_str, "fileupload_filetype=%d\nfileupload_fileno=%d\nfileupload_filelen=%d\n\n", \
            mytest_file_base_info.file_type, mytest_file_base_info.file_sn, mytest_file_base_info.file_len);
            printf("Mytest_UartSendData(dataframe):fileupload_info_str=\n%s\n", fileupload_info_str);

            printf("Mytest_UartSendData(msgframe):mytest_dev_fileupload_res(%d)=\n", sizeof(mytest_dev_fileupload_res));
            for(int i=0; i<sizeof(mytest_dev_fileupload_res); i++)
            {
                printf("0x%02x ", mytest_dev_fileupload_res[i]);
            }
            printf("\n\n");
            vTaskDelay(100 / portTICK_PERIOD_MS);

            Mytest_SendData_to_uart_read(mytest_dev_fileupload_res, sizeof(mytest_dev_fileupload_res), 1000, 1);

            datablock_rcved_len = mytest_dev_fileupload_res[21]*3072;
            uint8_t rcved_block_cache = mytest_dev_fileupload_res[23];
            int rcved_block_cache_count = 0;
            for(int j=0; j<8; j++)
            {
                if(rcved_block_cache&0x01)
                {
                    rcved_block_cache_count++;
                }
                rcved_block_cache>>=1;
            }
            datablock_rcved_len+=rcved_block_cache_count*3072;
            printf("Mytest_UartSendData(msgframe):bpct_offset=%d, rcved_block_cache_count=%d, datablock_rcved_len=%d\n", \
                    mytest_dev_fileupload_res[21], rcved_block_cache_count, datablock_rcved_len);
        }
        else if(p_rcv_my_asl_head->type==file_down_req)
        {
            printf("Mytest_UartSendData(msgframe):mytest_dev_filedownload_res(%d)=\n", sizeof(mytest_dev_filedownload_res));
            for(int i=0; i<sizeof(mytest_dev_filedownload_res); i++)
            {
                printf("0x%02x ", mytest_dev_filedownload_res[i]);
            }
            printf("\n\n");
            vTaskDelay(100 / portTICK_PERIOD_MS);

            mytest_dev_filedownload_res[2] = sn_counter;
            sn_counter++;
            int download_file_size = 32*960;
            *(uint32_t*)(&mytest_dev_filedownload_res[16]) = my_endian_conversion_32(download_file_size);

            printf("Mytest_UartSendData(msgframe):mytest_dev_filedownload_res(%d)=\n", sizeof(mytest_dev_filedownload_res));
            for(int i=0; i<sizeof(mytest_dev_filedownload_res); i++)
            {
                printf("0x%02x ", mytest_dev_filedownload_res[i]);
            }
            printf("\n\n");
            vTaskDelay(100 / portTICK_PERIOD_MS);

            Mytest_SendData_to_uart_read(mytest_dev_filedownload_res, sizeof(mytest_dev_filedownload_res), 1000, 1);

            vTaskDelay(1000 / portTICK_PERIOD_MS);
            printf("############\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n");

            //发送数据块
            // vTaskDelay(100 / portTICK_PERIOD_MS);
            char* send_test_data = mytest_dev_filedownload_db_pack;
            int send_db_total_num = download_file_size/960;
            int send_db_end_size = download_file_size%960;

            if(send_db_end_size==0)
            {
                if(send_db_total_num>0)
                {
                    send_db_total_num--;
                    send_db_end_size = 960;
                }
            }

            char* send_test_data_pack = send_test_data;


            int j=0; 
            for(; j<send_db_total_num; j++)
            {
                send_test_data_pack[2] = sn_counter;
                sn_counter++;
                *(uint16_t*)(&mytest_dev_filedownload_db_pack[24]) = my_endian_conversion_16(j);
                *(uint16_t*)(&mytest_dev_filedownload_db_pack[26]) = my_endian_conversion_16(960);
                Mytest_SendData_to_uart_read(send_test_data, sizeof(mytest_dev_filedownload_db_pack), 1000, 1);
                vTaskDelay(200 / portTICK_PERIOD_MS);
            }
            if(send_db_end_size>0)
            {
                send_test_data_pack[2] = sn_counter;
                sn_counter++;
                *(uint16_t*)(&mytest_dev_filedownload_db_pack[24]) = my_endian_conversion_16(j);
                *(uint16_t*)(&mytest_dev_filedownload_db_pack[26]) = my_endian_conversion_16(960);
                Mytest_SendData_to_uart_read(send_test_data+j*960, sizeof(mytest_dev_filedownload_db_pack), 1000, 1);
                vTaskDelay(200 / portTICK_PERIOD_MS);
            }
        }
    }
    else if(p_rcv_frame_head->type==data_frame)
    {
        my_tsf_dataframe_t* dataframe = (my_tsf_dataframe_t*)data;
        char tsfc_frame_info_str[256]={0};
        sprintf(tsfc_frame_info_str, "frame_type=%d\npd=%d\nsn=%d\nappid=%04x\ndata_len=%d\n\n", \
        dataframe->head.type, dataframe->head.pd, dataframe->sn, my_endian_conversion_16(dataframe->app_id), my_endian_conversion_16(dataframe->data_len));
        // printf("Mytest_UartSendData(dataframe):tsfc_frame_info_str=\n%s\n", tsfc_frame_info_str);

        my_asl_head_t* p_rcv_my_asl_head = (my_asl_head_t*)dataframe->data;
        char asl_info_str[256]={0};
        sprintf(asl_info_str, "msg_type=%d\nvercode=%d\ndata_len=%d\n\n", \
        p_rcv_my_asl_head->type, p_rcv_my_asl_head->vercode, my_endian_conversion_16(p_rcv_my_asl_head->len));
        // printf("Mytest_UartSendData(dataframe):asl_info_str=\n%s\n", asl_info_str);

        char* p_byte_asl_head = p_rcv_my_asl_head;
        uint8_t datablock_ud = *((uint8_t*)(p_byte_asl_head+4));
        //占位：设备识别信息8字节
        uint8_t datablock_filetype = *((uint8_t*)(p_byte_asl_head+4+1+8));
        uint8_t datablock_fileno = *((uint8_t*)(p_byte_asl_head+4+1+8+1));
        uint8_t datablock_endflag = *((uint8_t*)(p_byte_asl_head+4+1+8+1+1));
        uint16_t datablock_blockno = my_endian_conversion_16(*((uint16_t*)(p_byte_asl_head+4+1+8+1+1+1)));
        uint16_t datablock_datalen = my_endian_conversion_16(*((uint16_t*)(p_byte_asl_head+4+1+8+1+1+1+2)));
        datablock_rcved_len+=datablock_datalen;
        printf("############# datablock_rcved_len=%d\n", datablock_rcved_len);
        char datablock_info_str[256]={0};
        sprintf(datablock_info_str, "datablock_ud=%d,datablock_filetype=%d,datablock_fileno=%d,datablock_endflag=%d,datablock_blockno=%d,datablock_datalen=%d\n\n", \
        datablock_ud, datablock_filetype, datablock_fileno, datablock_endflag, datablock_blockno, datablock_datalen);
        printf("Mytest_UartSendData(dataframe):datablock_info_str=\n%s\n", datablock_info_str);

        mytest_dev_ack[2] = dataframe->sn;
        uint32_t crc_calcu = my_endian_conversion_32(crc_32((void*)mytest_dev_ack, sizeof(mytest_dev_ack)-8));
        memcpy(&mytest_dev_ack[4], &crc_calcu, 4);
        Mytest_SendData_to_uart_read(mytest_dev_ack, sizeof(mytest_dev_ack), 1000, 1);


        if((datablock_rcved_len>=mytest_file_base_info.file_len))
        {
            if((datablock_endflag==1))
            {
                mytest_dev_fileupload_finish[21] = mytest_file_base_info.file_sn;
                printf("Mytest_UartSendData(msgframe):mytest_dev_fileupload_finish(%d)=\n", sizeof(mytest_dev_fileupload_finish));
                for(int i=0; i<sizeof(mytest_dev_fileupload_finish); i++)
                {
                    printf("0x%02x ", mytest_dev_fileupload_finish[i]);
                }
                printf("\n\n");
                mytest_dev_fileupload_finish[2] = sn_counter;
                sn_counter++;
                vTaskDelay(100 / portTICK_PERIOD_MS);
                Mytest_SendData_to_uart_read(mytest_dev_fileupload_finish, sizeof(mytest_dev_fileupload_finish), 1000, 1);
            }
            else
            {
                printf("Mytest_UartSendData(msgframe):datablock_endflag==1 is false\n");
            }
        }
    }
    else if(p_rcv_frame_head->type==ack_frame)
    {
        printf("Mytest_UartSendData():rcv ack_frame\n");
    }
    else if(p_rcv_frame_head->type==cd_frame)
    {
        
    }

    Mytest_Print();

    return 0;
}

int Mytest_uart_read_bytes(int uart_num, char* rcv_buf, int rcv_buf_len, int wait)
{
    printf("\n\n++++Mytest_uart_read_bytes()\n\n");

    Mytest_Print();

    int ret = 0;
    int queue_rcv_ret = pdFALSE;
    my_whgm5_queue_item_t* queue_rcv_item = NULL;
    // if(queue_rcv_item==NULL)
    // {
    //     ESP_LOGE("Mytest_uart_read_bytes()", "queue_rcv_item==NULL");
    //     return -1;
    // }

    queue_rcv_ret = xQueueReceive(mytest_uart_read_queue, &queue_rcv_item, portMAX_DELAY);

    if(queue_rcv_ret==pdTRUE)
    {
        if(queue_rcv_item->data!=NULL)
        {
            if(rcv_buf!=NULL)
            {
                if(queue_rcv_item->len<=rcv_buf_len)
                {
                    memcpy(rcv_buf, queue_rcv_item->data, queue_rcv_item->len);
                    printf("Mytest_uart_read_bytes(), queue_rcv_item->len=%d, test success!\n\n", queue_rcv_item->len);
                    ret = queue_rcv_item->len;
                }
                else
                {
                    ESP_LOGE("Mytest_uart_read_bytes()", "queue_rcv_item->len<=rcv_buf_len is false");
                }
            }
            else
            {
                ESP_LOGE("Mytest_uart_read_bytes()", "rcv_buf!=NULL is false");
                ret = -2;
            }
        }
        else
        {
            ESP_LOGE("Mytest_uart_read_bytes()", "queue_rcv_item->data!=NULL is false");
            ret = -3;
        }
    }
    else
    {
        ESP_LOGE("Mytest_uart_read_bytes()", "queue_rcv_ret==pdTRUE is false");
        return -4;
    }
    queue_rcv_item->valid = 0;

    //if(queue_rcv_item!=NULL)free(queue_rcv_item);

    Mytest_Print();

    return ret;
}

int Mytest_SendData_to_uart_read(void const* data, uint32_t len, uint32_t timeout, uint8_t tcp_channel)
{
    int queue_send_ret = pdTRUE;
    int i = 0;
    int retry_count = 0;

    //获取信号量
    Mytest_SendData_to_uart_read_retry:
    // xSemaphoreTake(MyWHGM5_TcpSendDataMutexSemaphore, 10000/portTICK_PERIOD_MS);
    for(i=0; i<WHGM5_QUEUE_LEN; i++)
    {
        if(!my_test_uart_read_queue_item_array[i]->valid)
        {
            my_test_uart_read_queue_item_array[i]->id = tcp_channel;
            my_test_uart_read_queue_item_array[i]->len = len;
            my_test_uart_read_queue_item_array[i]->valid = 1;
            //my_test_uart_read_queue_item_array[i]->data = data;
            printf("%p\n", my_test_uart_read_queue_item_array[i]->data);
            if(my_test_uart_read_queue_item_array[i]->data!=NULL)
            {
                memcpy(my_test_uart_read_queue_item_array[i]->data, data, len);
                my_test_uart_read_queue_item_array[i]->data[len]=0;
            }
            else
            {
                ESP_LOGE("Mytest_SendData_to_uart_read()", "my_test_uart_read_queue_item_array[i]->data!=NULL is false!\n");
            }

            break;
        }
    }
    //释放信号量
    // xSemaphoreGive(MyWHGM5_TcpSendDataMutexSemaphore);
    if(i>=WHGM5_QUEUE_LEN&&retry_count<10)
    {
        vTaskDelay(timeout / portTICK_PERIOD_MS);
        retry_count++;
        goto Mytest_SendData_to_uart_read_retry;
    }
    else if(i<WHGM5_QUEUE_LEN)
    {
        //printf("Mytest_SendData_to_uart_read():i=%d\n", i);
        if(mytest_uart_read_queue!=NULL)
            queue_send_ret = xQueueSend(mytest_uart_read_queue, &my_test_uart_read_queue_item_array[i], timeout / portTICK_PERIOD_MS);
        return 0;
    }
    else
    {
        printf("Mytest_SendData_to_uart_read():error, i=%d\n", i);
        return 1;
    }

    return 0;
}

static int Mytest_Queue_Init(my_whgm5_queue_item_t** p, uint8_t data_area)
{
    if(data_area)
    {
        char* p_temp = (char*)calloc(WHGM5_QUEUE_LEN, WHGM5_DATA_PACK_SIZE);
        if(p_temp!=NULL)
        { 
            for(int i=0; i<WHGM5_QUEUE_LEN; i++)
            {
                p[i] = (my_whgm5_queue_item_t*)calloc(1, sizeof(my_whgm5_queue_item_t));
                if(p[i]!=NULL)
                {
                    p[i]->data = p_temp+(i*(WHGM5_DATA_PACK_SIZE));
                }
                else
                {
                    ESP_LOGE("Mytest_Queue_Init()", "error1\n");
                    return 1;
                }
            }
        }
        else
        {
            ESP_LOGE("Mytest_Queue_Init()", "error2\n");
            return 2;
        }
    }
    else
    {
        for(int i=0; i<WHGM5_QUEUE_LEN; i++)
        {
            p[i] = (my_whgm5_queue_item_t*)calloc(1, sizeof(my_whgm5_queue_item_t));
            if(p[i]==NULL)
            {
                ESP_LOGE("Mytest_Queue_Init()", "error3, i=%d\n", i);
                return i;
            }
        }
    }
    return WHGM5_QUEUE_LEN;
}

int Mytest_Init(void)
{
    Mytest_Queue_Init(my_test_uart_read_queue_item_array, 1);
    Mytest_Print();
    mytest_uart_read_queue = xQueueGenericCreate(WHGM5_QUEUE_LEN, sizeof(my_whgm5_queue_item_t*), 1);
    if(mytest_uart_read_queue==NULL)ESP_LOGE("Mytest_Init()", "mytest_uart_read_queue=NULL\n");

    return 0;
}

// int Mytest_Print_counter = 0;
void Mytest_Print(void)
{
    // vTaskDelay(50 / portTICK_PERIOD_MS);
    // Mytest_Print_counter++;
    // printf("Mytest_Print_counter=%d\n", Mytest_Print_counter);
    // for(int i=0; i<WHGM5_QUEUE_LEN; i++)
    // {
    //     printf("%p\n", my_test_uart_read_queue_item_array[i]->data);
    // }
    // printf("\n\n");
    // vTaskDelay(50 / portTICK_PERIOD_MS);
}

#endif