#include "e104_bt53c3_at_table.h"

#include "lte_at_table.h"

// AT_Config_Routine_t e104bt53c3_set_advname_conf_routine[2]=
// {
//     {
//         .cmd_id = 0,
//         .cmdstr = "AT+SOCKCEN?\r\n",
//         .keystr = "SOCKCEN:OFF",
//         .sucnext = 0xff,
//         .failnext = 1,
//         .pridelay = 0,
//         .lagdelay = AIR780EG_CMD_LAGDLEAY_TIME_NOR,
//         .faillagdelay = 100,
//         .keystrexist = 1,
//         .timeout_count = AIR780EG_ACK_RCV_TIME_NOR,
//         .failedcount = 0,
//         .failedlimit = 5
//     },
//     {
//         .cmd_id = 1,
//         .cmdstr = "AT+SOCKCEN=OFF\r\n",
//         .keystr = "OK",
//         .sucnext = 0,
//         .failnext = 0,
//         .pridelay = 0,
//         .lagdelay = AIR780EG_CMD_LAGDLEAY_TIME_NOR,
//         .faillagdelay = 1000,
//         .keystrexist = 1,
//         .timeout_count = AIR780EG_ACK_RCV_TIME_NOR,
//         .failedcount = 0,
//         .failedlimit = 5
//     },
// };