#include "my_nvs.h"

#include "my_nvs_ds.h"

#include "esp_system.h"
#include "esp_log.h"

#include "nvs.h"
#include "nvs_flash.h"

#include "string.h"


typedef struct
{
	int init_ok;
}mynvs_t;

mynvs_t mynvs_info;



int mynvs_init_blob(char* formname, char* keyname, char* data, int len){
	nvs_handle_t my_handle;
	int store_state = 0;
	esp_err_t err = nvs_open(formname, NVS_READWRITE, &my_handle);

	if(err !=ESP_OK){
        // printf("Error (%s) opening NVS handle!\n", esp_err_to_name(err));
	}else {
		// printf("Done\n");


		size_t size = len;
		err=nvs_get_blob(my_handle, keyname, data, &size);
		if(err==ESP_OK){
			store_state = 1;
			// printf("get store ok\n");
		}else {
			store_state = 2;
			nvs_set_blob(my_handle,keyname,data,size);
		}	

		// printf("Committing updates in NVS ... ");
		err = nvs_commit(my_handle);
		// printf((err != ESP_OK) ? "Failed!\n" : "Done\n");

		// Close
		nvs_close(my_handle);

		if(err!=ESP_OK)
		{
			return 0;
		}
	}
	return store_state;
}

int mynvs_save_blob(char* formname, char* keyname, char* data, int len, int reboot){
	nvs_handle_t my_handle;
	esp_err_t err = nvs_open(formname, NVS_READWRITE, &my_handle);

	if(err !=ESP_OK)
	{
        // printf("Error (%s) opening NVS handle!\n", esp_err_to_name(err));
		return 1;
	}
	else
	{
		//ble name
		size_t size = len;
		nvs_set_blob(my_handle,keyname,data,size);

		// printf("Committing updates in NVS ... ");
		err = nvs_commit(my_handle);
		// printf((err != ESP_OK) ? "Failed!\n" : "Done\n");

		// Close
		nvs_close(my_handle);

		if(reboot)
		{
			esp_restart();
		}
		else
		{
			return 0;
		}
	}
	return 1;
}

int SaveDataToNvs(char* formname, char* keyname, char* data, int len, int reboot)
{
	if(mynvs_info.init_ok)
	{
		return mynvs_save_blob(formname, keyname, data, len, reboot);
	}
    return 0;
}

int LoadDataFromNvs(char* formname, char* keyname, char* data, int len)
{
	int err = 0;
	if(mynvs_info.init_ok)
	{
		err = mynvs_init_blob(formname, keyname, data, len);
	}
	return err;
}

int Save_KVP_To_Nvs_FromListItem(my_nvs_d2link_list_t* p_list, int id)
{
	int err = 0;

	if(mynvs_info.init_ok)
	{
		my_nvs_d2link_list_item_t* p_item = MyNvs_D2Link_GetItem_ById(p_list, id);
		/* printf("Save_KVP_List_To_Nvs():p_list->list_name=%s, item_id=%#x(%d), item_name=%s, nvs_form_name=%s, nvs_key_name=%s\n",\
				p_list->list_name, id, id, p_item->item_name, p_item->form_name, p_item->key_name); */
		if(p_item!=NULL)
		{
			if(p_item->saving_to_nvs)
			{
				if(p_item->form_name!=NULL)
				{
					if(p_item->key_name!=NULL)
					{
						if(p_item->data_type==0)
						{
							err = mynvs_save_blob(p_item->form_name, p_item->key_name, &p_item->data_uint64, p_item->data_len, 0);
							if(err)
							{
								// printf("Save_KVP_To_Nvs_FromListItem():mynvs_save_blob failed, p_item->data_type==0\n");
							}
						}
						else if(p_item->data_type==1)
						{
							err = mynvs_save_blob(p_item->form_name, p_item->key_name, p_item->p_data, p_item->data_len, 0);
							if(err)
							{
								// printf("Save_KVP_To_Nvs_FromListItem():mynvs_save_blob failed, p_item->data_type==1\n");
							}
						}
					}
					else
					{
						ESP_LOGE("Save_KVP_To_Nvs_FromListItem():", "p_item->key_name!=NULL is false, item_id=%d", p_item->id);
					}
				}
				else
				{
					ESP_LOGE("Save_KVP_To_Nvs_FromListItem():", "p_item->form_name!=NULL is false, item_id=%d", p_item->id);
				}
			}
			else
			{
				// ESP_LOGI("Save_KVP_To_Nvs_FromListItem():", "p_item->saving_to_nvs is false, item_id=%d", p_item->id);
			}
		}
		
	}
    return 0;
}

int Save_KVP_List_To_Nvs(my_nvs_d2link_list_t* p_list)
{
	if(p_list==NULL)
    {
        ESP_LOGE("Save_KVP_List_To_Nvs():", "p_list==NULL");
        return -1;
    }

	int err = 0;
	if(mynvs_info.init_ok)
	{
		// printf("Save_KVP_List_To_Nvs():p_list->list_name=%s\n", p_list->list_name);
		my_nvs_d2link_list_item_t* p_item = p_list->first_item;

		for(int i=0; (i<p_list->list_item_num)&&(p_item!=NULL); i++,p_item=p_item->next_item)
		{
			if(p_item->saving_to_nvs)
			{
				if(p_item->form_name!=NULL)
				{
					if(p_item->key_name!=NULL)
					{
						if(p_item->data_type==0)
						{
							err = mynvs_save_blob(p_item->form_name, p_item->key_name, &p_item->data_uint64, p_item->data_len, 0);
							if(err)
							{
								// printf("Save_KVP_List_To_Nvs():mynvs_save_blob failed, p_item->data_type==0\n");
							}
						}
						else if(p_item->data_type==1)
						{
							err = mynvs_save_blob(p_item->form_name, p_item->key_name, p_item->p_data, p_item->data_len, 0);
							if(err)
							{
								// printf("Save_KVP_List_To_Nvs():mynvs_save_blob failed, p_item->data_type==1\n");
							}
						}
					}
					else
					{
						ESP_LOGE("Save_KVP_List_To_Nvs():", "p_item->key_name!=NULL is false, item_id=%d", p_item->id);
					}
				}
				else
				{
					ESP_LOGE("Save_KVP_List_To_Nvs():", "p_item->form_name!=NULL is false, item_id=%d", p_item->id);
				}
			}
			else
			{
				// ESP_LOGI("Save_KVP_List_To_Nvs():", "p_item->saving_to_nvs is false, item_id=%d", p_item->id);
			}
		}
		
	}
	return err;
}

int Load_KVP_List_FromNvs(my_nvs_d2link_list_t* p_list)
{
	if(p_list==NULL)
    {
        ESP_LOGE("Load_KVP_List_FromNvs():", "p_list==NULL");
        return -1;
    }

	int err = 0;
	if(mynvs_info.init_ok)
	{
		my_nvs_d2link_list_item_t* p_item = p_list->first_item;

		for(int i=0; (i<p_list->list_item_num)&&(p_item!=NULL); i++,p_item=p_item->next_item)
		{
			if(p_item->saving_to_nvs)
			{
				if(p_item->form_name!=NULL)
				{
					if(p_item->key_name!=NULL)
					{
						if(p_item->data_type==0)
						{
							err = mynvs_init_blob(p_item->form_name, p_item->key_name, &p_item->data_uint64, p_item->data_len);
							if(err==1)
							{
								// printf("Load_KVP_List_FromNvs():load exist item, form_name=%s, key_name=%s, p_item->data_type==0\n", p_item->form_name, p_item->key_name);
							}
							else if(err==2)
							{
								// printf("Load_KVP_List_FromNvs():first load item, form_name=%s, key_name=%s, p_item->data_type==0\n", p_item->form_name, p_item->key_name);
							}
						}
						else if(p_item->data_type==1)
						{
							err = mynvs_init_blob(p_item->form_name, p_item->key_name, p_item->p_data, p_item->data_len);
							if(err==1)
							{
								// printf("Load_KVP_List_FromNvs():load exist item, form_name=%s, key_name=%s, p_item->data_type==1\n", p_item->form_name, p_item->key_name);
							}
							else if(err==2)
							{
								// printf("Load_KVP_List_FromNvs():first load item, form_name=%s, key_name=%s, p_item->data_type==1\n", p_item->form_name, p_item->key_name);
							}
						}
					}
					else
					{
						ESP_LOGE("Load_KVP_List_FromNvs():", "p_item->key_name!=NULL is false, item_id=%d", p_item->id);
					}
				}
				else
				{
					ESP_LOGE("Load_KVP_List_FromNvs():", "p_item->form_name!=NULL is false, item_id=%d", p_item->id);
				}
			}
			else
			{
				// ESP_LOGE("Load_KVP_List_FromNvs():", "p_item->saving_to_nvs is false, item_id=%d", p_item->id);
			}
		}
		
	}
	return err;
}

int Erase_KVP_List_FromNvs(my_nvs_d2link_list_t* p_list)
{
	return 0;
}

int My_nvs_flash_init(void)
{
	if(!mynvs_info.init_ok)
	{
		esp_err_t err = nvs_flash_init();
		if (err != ESP_OK)
		{
			ESP_LOGE("", "- NVS init failed will erase\n");
			ESP_ERROR_CHECK( nvs_flash_erase() );
			ESP_ERROR_CHECK( nvs_flash_init() );
			return 0;
		}else
		{
			ESP_LOGI("", "- NVS init ok\n");
			mynvs_info.init_ok = 1;
			return 1;
		}
	}
	return 1;
}

int My_nvs_flash_erase(void)
{
	ESP_LOGI("My_nvs_flash_erase()", "- NVS will erase");
	ESP_ERROR_CHECK( nvs_flash_erase() );
	esp_err_t err = nvs_flash_init();

	if (err != ESP_OK)
	{
		ESP_LOGI("My_nvs_flash_erase()", "- NVS init failed");
	}
	else
	{
		ESP_LOGI("My_nvs_flash_erase()", "- NVS init ok\n");
	}

	return err;
}

void MyNvs_Print_Statistic(void)
{
	nvs_stats_t nvs_stats;
    nvs_get_stats(NULL, &nvs_stats);
    /* printf("\n\nMyNvs_Print_Statistic():\nCount: UsedEntries = (%d), FreeEntries = (%d), AllEntries = (%d), NameSpaceCount = (%d)\n\n",
        nvs_stats.used_entries, nvs_stats.free_entries, nvs_stats.total_entries, nvs_stats.namespace_count); */
}
